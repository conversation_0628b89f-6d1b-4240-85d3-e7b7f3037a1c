# Auth Token Issue: Cookie Options Review and Adjustments

**Issue:** The access token is not being saved after login which could be caused by the current cookie configuration settings (COOKIE_OPTIONS).

## Plan Outline

1. **Review COOKIE_OPTIONS:**
   - Ensure secure and domain options are correctly set.
   - In development, `secure` should be `false`.

2. **Verify AUTH_CONFIG:**
   - Validate token keys and routes.

3. **Testing Adjustments:**
   - Temporarily set `secure: false` and remove `domain` for development tests.
   - Check if token is present in the login response.

4. **Library Behavior:**
   - Confirm that `setCookie` (from cookies-next) functions as expected.

5. **Testing and Validation:**
   - Test the login flow after the modifications.

## Proposed Changes Example

For development, modify COOKIE_OPTIONS in `config.ts` like so:
```js
export const COOKIE_OPTIONS = {
    maxAge: 60 * 60 * 24 * 7,
    path: "/",
    secure: false, // Force non-secure for testing
    sameSite: "lax"
};
```

## Workflow Diagram

```mermaid
flowchart TD
    A[Start Login Process] --> B[Call Login API]
    B --> C{Receive Valid Token?}
    C -- No --> D[Throw AuthError]
    C -- Yes --> E[Call setCookie with COOKIE_OPTIONS]
    E --> F{Cookie Saved?}
    F -- No --> G[Check secure flag & domain]
    G --> H[Adjust settings for development/production]
    H --> F
    F -- Yes --> I[Proceed with Authenticated Session]
```

## Next Steps

- Review the above plan.
- Switch to Code mode to implement changes in `config.ts`.