# Collaboration Suite Usage Guide

## Overview

The Collaboration Suite provides comprehensive real-time collaboration, workflow management, and team productivity features for legal document analysis. This guide covers how to use all collaboration features effectively.

## 🚀 Getting Started

### Accessing the Collaboration Suite

1. Navigate to `/collaboration` in your dashboard
2. The collaboration page provides six main tabs:
   - **Dashboard**: Overview of all collaboration activities
   - **Real-time Editor**: Live collaborative document editing
   - **Comments**: Threaded discussions and document annotations
   - **Workflows**: Custom approval and review workflows
   - **Tasks**: Task management and assignment
   - **Analytics**: Collaboration metrics and team insights

### Prerequisites

- **Law Student Tier**: Access to threaded discussions
- **Lawyer Tier**: All collaboration features except team analytics
- **Law Firm Tier**: Full access to all collaboration features

## 📝 Real-time Collaborative Editing

### Starting a Collaboration Session

```typescript
import { useCollaborationFeatures } from '@/hooks/use-collaboration';

const collaboration = useCollaborationFeatures(documentId);

// Start a new collaboration session
await collaboration.startCollaboration({
  maxParticipants: 5,
  allowAnonymous: false,
  recordChanges: true
});
```

### Features

- **Live Cursor Tracking**: See other users' cursors and selections in real-time
- **Presence Awareness**: View who's currently editing the document
- **Operational Transformation**: Automatic conflict resolution for simultaneous edits
- **Change History**: Complete audit trail of all document modifications

### Usage Tips

1. **Start Session**: Click "Start Collaboration" to begin a live editing session
2. **Invite Participants**: Share the document with team members
3. **Monitor Activity**: Watch real-time cursors and participant list
4. **Save Changes**: All changes are automatically synchronized

## 💬 Threaded Comments & Discussions

### Creating Comment Threads

```typescript
// Create a new comment thread
await collaboration.createCommentThread({
  title: "Review Payment Terms",
  type: "review", // 'discussion' | 'review' | 'approval' | 'question'
  anchor: {
    type: "text",
    context: "Payment shall be made within 30 days",
    startPosition: 450,
    endPosition: 485
  },
  initialComment: "Should we extend payment terms to 45 days?"
});
```

### Comment Types

- **Discussion**: General discussion threads
- **Review**: Document review and feedback
- **Approval**: Approval requests and decisions
- **Question**: Questions requiring answers

### Advanced Features

- **@Mentions**: Tag team members with `@username`
- **Reactions**: Add emoji reactions to comments
- **Thread Resolution**: Mark discussions as resolved
- **Nested Replies**: Create threaded conversations

## 🔄 Workflow Management

### Creating Custom Workflows

Workflows automate document review and approval processes:

1. **Template Creation**: Define workflow steps and participants
2. **Step Configuration**: Set assignees, deadlines, and requirements
3. **Approval Chains**: Create multi-step approval processes
4. **Conditional Logic**: Add branching based on decisions

### Workflow Types

- **Document Review**: Standard review process
- **Legal Approval**: Multi-tier legal approval
- **Client Review**: External client review workflow
- **Compliance Check**: Regulatory compliance verification

### Example Workflow

```typescript
// Start a document review workflow
await collaboration.startWorkflow('document-review-template', {
  priority: 'high',
  deadline: '2024-02-15',
  reviewers: ['<EMAIL>', '<EMAIL>']
});
```

## ✅ Task Management

### Task Features

- **Kanban Board**: Visual task management with drag-and-drop
- **List View**: Traditional task list with filtering
- **Priority Levels**: Urgent, High, Medium, Low
- **Status Tracking**: Pending, In Progress, Completed, Overdue
- **Deadline Management**: Due date tracking with notifications

### Task Assignment

```typescript
// Update task status
await collaboration.updateTaskStatus(taskId, 'in_progress', 'Started review');

// Assign task to team member
await collaboration.assignTask(taskId, '<EMAIL>');
```

### Task Types

- **Review**: Document review tasks
- **Approval**: Approval requests
- **Research**: Legal research assignments
- **Drafting**: Document drafting tasks
- **Client Communication**: Client interaction tasks

## 📊 Analytics & Metrics

### Available Metrics (Law Firm Tier)

- **Active Collaborations**: Current live sessions
- **Total Participants**: Team member engagement
- **Comments & Discussions**: Communication activity
- **Tasks Completed**: Productivity metrics
- **Average Session Duration**: Collaboration efficiency
- **Workflow Completion Rate**: Process effectiveness

### Accessing Analytics

```typescript
// Get collaboration metrics
const metrics = await collaboration.getCollaborationMetrics({
  start: '2024-01-01',
  end: '2024-01-31'
});
```

## 🔐 Document Sharing

### External Sharing

Share documents with external parties with granular permissions:

```typescript
await collaboration.shareDocument(documentId, {
  emails: ['<EMAIL>'],
  permissions: {
    canView: true,
    canEdit: false,
    canComment: true,
    canShare: false
  },
  expiresAt: '2024-02-15T23:59:59Z',
  message: 'Please review the attached contract'
});
```

### Permission Levels

- **View Only**: Read-only access
- **Comment**: Can add comments and discussions
- **Edit**: Full editing capabilities
- **Share**: Can share with others

## 💰 Credit Usage

### Credit Costs

- **Real-time Collaboration**: 2 credits per session
- **Workflow Instance**: 1 credit per workflow
- **Comment Thread**: 0.5 credits per thread
- **Document Sharing**: 1 credit per share
- **Team Analytics**: 3 credits per report

### Optimization Tips

1. **Batch Operations**: Group related activities
2. **Efficient Workflows**: Design streamlined processes
3. **Targeted Sharing**: Share only when necessary
4. **Monitor Usage**: Track credit consumption

## 🛠️ Integration Examples

### Document Analysis with Collaboration

```typescript
import { 
  CollaborationDashboard,
  RealTimeEditor,
  ThreadedComments,
  WorkflowManager 
} from '@/components/collaboration';

function DocumentAnalysisPage({ documentId }) {
  return (
    <div className="grid grid-cols-3 gap-6">
      <div className="col-span-2">
        <RealTimeEditor 
          documentId={documentId}
          content={documentContent}
          onContentChange={setDocumentContent}
        />
      </div>
      <div className="space-y-4">
        <ThreadedComments documentId={documentId} />
        <WorkflowManager documentId={documentId} />
      </div>
    </div>
  );
}
```

### Custom Hook Usage

```typescript
import { useCollaborationFeatures } from '@/hooks/use-collaboration';

function MyComponent({ documentId }) {
  const {
    isConnected,
    participants,
    tasks,
    commentThreads,
    startCollaboration,
    createCommentThread,
    updateTaskStatus
  } = useCollaborationFeatures(documentId);

  // Use collaboration features...
}
```

## 🔧 Troubleshooting

### Common Issues

1. **Connection Problems**
   - Check WebSocket URL configuration
   - Verify authentication tokens
   - Ensure network connectivity

2. **Sync Issues**
   - Refresh the page
   - Rejoin collaboration session
   - Check for conflicting changes

3. **Permission Errors**
   - Verify subscription tier access
   - Check document permissions
   - Confirm user roles

### Support

For technical support or feature requests:
- Email: <EMAIL>
- Documentation: [docs.docgic.com](https://docs.docgic.com)
- Status Page: [status.docgic.com](https://status.docgic.com)

## 🎯 Best Practices

### Team Collaboration

1. **Establish Workflows**: Create standardized review processes
2. **Use Comments Effectively**: Provide clear, actionable feedback
3. **Monitor Tasks**: Keep track of deadlines and progress
4. **Regular Check-ins**: Use analytics to improve processes

### Document Management

1. **Version Control**: Use collaboration history for tracking
2. **Access Control**: Set appropriate sharing permissions
3. **Backup Strategy**: Maintain document backups
4. **Audit Compliance**: Use audit trails for compliance

### Performance Optimization

1. **Limit Participants**: Keep sessions focused and efficient
2. **Close Unused Sessions**: End sessions when complete
3. **Optimize Workflows**: Streamline approval processes
4. **Monitor Credits**: Track usage and optimize costs

---

*This guide covers the core collaboration features. For advanced configurations and enterprise features, contact our support team.*
