# Chat Negotiation Backend Integration Requirements

## Overview

The chat negotiation feature requires backend endpoints to bridge the conversational chat interface with the existing negotiation simulator. This document outlines the required API endpoints and data models.

## Required Backend Endpoints

### 1. Chat Negotiation Session Management

#### Create Chat Negotiation Session
```http
POST /api/chat-negotiation/sessions
```

**Request Body:**
```json
{
  "scenarioId": "string",
  "aiPersonality": {
    "aggressiveness": 0.6,
    "flexibility": 0.7,
    "communicationStyle": "diplomatic"
  }
}
```

**Response:**
```json
{
  "id": "chat-neg-session-uuid",
  "negotiationSessionId": "neg-session-uuid",
  "chatSessionId": "chat-session-uuid",
  "scenarioId": "scenario-uuid",
  "status": "active",
  "currentRound": 1,
  "extractedTerms": {},
  "relationshipMetrics": {
    "trust": 50,
    "respect": 50,
    "pressure": 20
  },
  "score": 5.0,
  "createdAt": "2024-01-15T10:00:00Z",
  "updatedAt": "2024-01-15T10:00:00Z"
}
```

#### Get Chat Negotiation Session
```http
GET /api/chat-negotiation/sessions/{sessionId}
```

#### Update Chat Negotiation Session
```http
PUT /api/chat-negotiation/sessions/{sessionId}
```

#### List User's Chat Negotiation Sessions
```http
GET /api/chat-negotiation/sessions
```

### 2. Chat Move Processing

#### Send Chat Move
```http
POST /api/chat-negotiation/sessions/{sessionId}/moves
```

**Request Body:**
```json
{
  "content": "I'm thinking around $50k for the software license",
  "extractedData": {
    "offer": {
      "price": 50000,
      "currency": "USD",
      "terms": ["annual", "support included"]
    },
    "strategy": "collaborative",
    "sentiment": "neutral",
    "confidence": 0.8
  }
}
```

**Response:**
```json
{
  "userMessage": {
    "id": "msg-uuid",
    "content": "I'm thinking around $50k for the software license",
    "type": "user",
    "timestamp": "2024-01-15T10:05:00Z"
  },
  "aiResponse": {
    "id": "msg-uuid-2",
    "content": "That's in a reasonable range. For $50,000, we could look at different tiers...",
    "type": "ai",
    "timestamp": "2024-01-15T10:05:30Z",
    "suggestions": [
      "We expect to double our team size",
      "Can you offer scalable pricing?",
      "What about a pilot program?"
    ]
  },
  "sessionUpdate": {
    "currentRound": 2,
    "relationshipMetrics": {
      "trust": 52,
      "respect": 51,
      "pressure": 18
    },
    "score": 5.2,
    "extractedTerms": {
      "price": 50000,
      "currency": "USD"
    }
  }
}
```

### 3. AI Response Generation

#### Generate AI Response
```http
POST /api/chat-negotiation/sessions/{sessionId}/ai-response
```

**Request Body:**
```json
{
  "userMove": {
    "content": "I'm thinking around $50k",
    "extractedData": {
      "offer": { "price": 50000 },
      "strategy": "collaborative",
      "sentiment": "neutral"
    }
  },
  "negotiationContext": {
    "scenarioId": "software_licensing",
    "currentRound": 2,
    "previousMoves": [...],
    "aiPersonality": {...}
  },
  "currentMetrics": {
    "trust": 50,
    "respect": 50,
    "pressure": 20
  }
}
```

**Response:**
```json
{
  "content": "That's in a reasonable range. For $50,000, we could look at different tiers. What's your expected user growth over the next 2 years?",
  "extractedData": {
    "strategy": "collaborative",
    "sentiment": "positive"
  },
  "suggestions": [
    "We expect to double our team size",
    "Growth will be gradual",
    "Can you offer scalable pricing?",
    "What about a pilot program?"
  ],
  "relationshipUpdate": {
    "trust": 52,
    "respect": 51,
    "pressure": 18
  },
  "scoreUpdate": 5.2
}
```

### 4. Natural Language Processing

#### Extract Data from Message
```http
POST /api/chat-negotiation/extract-data
```

**Request Body:**
```json
{
  "message": "I'm thinking around $50k for the software license, paid annually",
  "context": {
    "scenarioType": "software_licensing",
    "previousTerms": {...}
  }
}
```

**Response:**
```json
{
  "offer": {
    "price": 50000,
    "currency": "USD",
    "terms": ["annual", "software license"]
  },
  "strategy": "collaborative",
  "sentiment": "neutral",
  "confidence": 0.85,
  "extractedEntities": [
    {
      "type": "MONEY",
      "value": 50000,
      "text": "$50k"
    },
    {
      "type": "FREQUENCY",
      "value": "annually",
      "text": "annually"
    }
  ]
}
```

#### Get Contextual Suggestions
```http
POST /api/chat-negotiation/sessions/{sessionId}/suggestions
```

**Request Body:**
```json
{
  "currentMessage": "What about the payment terms?"
}
```

**Response:**
```json
{
  "suggestions": [
    "We prefer quarterly payments",
    "Annual upfront works for us",
    "Can we get a discount for early payment?",
    "What are the standard terms?"
  ]
}
```

### 5. Session Control

#### Pause Session
```http
PUT /api/chat-negotiation/sessions/{sessionId}/pause
```

#### Resume Session
```http
PUT /api/chat-negotiation/sessions/{sessionId}/resume
```

#### Complete Session
```http
POST /api/chat-negotiation/sessions/{sessionId}/complete
```

**Response:**
```json
{
  "session": {
    "id": "session-uuid",
    "status": "completed",
    "finalScore": 7.8,
    "totalRounds": 6
  },
  "evaluation": {
    "overallPerformance": "Good",
    "strengths": ["Collaborative approach", "Clear communication"],
    "improvements": ["Could have been more assertive on key terms"],
    "scoreBreakdown": {
      "communication": 8.5,
      "strategy": 7.2,
      "outcome": 7.8
    }
  }
}
```

## Data Models

### ChatNegotiationSession
```typescript
interface ChatNegotiationSession {
  id: string;
  negotiationSessionId: string;  // Links to existing negotiation session
  chatSessionId: string;         // Links to chat session
  scenarioId: string;
  status: 'active' | 'paused' | 'completed' | 'abandoned';
  currentRound: number;
  extractedTerms: Record<string, any>;
  relationshipMetrics: {
    trust: number;      // 0-100
    respect: number;    // 0-100
    pressure: number;   // 0-100
  };
  score: number;
  aiPersonality: {
    aggressiveness: number;
    flexibility: number;
    communicationStyle: string;
    traits: string[];
  };
  createdAt: string;
  updatedAt: string;
  userId: string;
}
```

### ExtractedData
```typescript
interface ExtractedData {
  offer?: {
    price?: number;
    currency?: string;
    terms?: string[];
    conditions?: string[];
  };
  strategy?: 'collaborative' | 'competitive' | 'accommodating' | 'analytical' | 'relationship';
  sentiment?: 'positive' | 'neutral' | 'negative';
  confidence?: number;  // 0-1
  urgency?: 'low' | 'medium' | 'high';
  extractedEntities?: Array<{
    type: string;
    value: any;
    text: string;
    confidence: number;
  }>;
}
```

## Integration Points

### 1. Existing Services to Leverage
- **Negotiation Simulator Service**: Session management, move processing, scoring
- **Chat Service**: Message storage, session management
- **Gamification Service**: XP, achievements, leaderboards
- **WebSocket Service**: Real-time updates

### 2. New Services Required
- **NLP Service**: Extract structured data from natural language
- **AI Response Generator**: Generate contextual AI responses
- **Relationship Tracker**: Track trust, respect, pressure metrics
- **Chat-Negotiation Bridge**: Coordinate between chat and negotiation systems

## Implementation Priority

### Phase 1 (MVP)
1. Basic chat negotiation session management
2. Simple data extraction (client-side fallback)
3. Basic AI response generation
4. Integration with existing negotiation simulator

### Phase 2 (Enhanced)
1. Advanced NLP for data extraction
2. Sophisticated AI response generation
3. Real-time relationship tracking
4. WebSocket integration for live updates

### Phase 3 (Advanced)
1. Multi-language support
2. Voice-to-text integration
3. Advanced analytics and insights
4. Integration with external AI services

## Security Considerations

- All endpoints require authentication
- Rate limiting on AI response generation
- Input validation and sanitization
- Audit logging for all negotiation moves
- Data encryption for sensitive negotiation terms

## Performance Requirements

- AI response generation: < 2 seconds
- Data extraction: < 500ms
- Session updates: < 200ms
- Support for 100+ concurrent chat negotiations
- Message history retention: 90 days
