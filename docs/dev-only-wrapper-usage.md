# Development-Only Wrapper Usage Guide

## Overview

The `DevOnlyWrapper` system provides a clean way to hide features, pages, or components from production while keeping them available during development. This is useful for:

- Features under development
- Experimental functionality
- Internal tools
- Beta features not ready for production

## 🚀 Components Available

### 1. `DevOnlyWrapper`
Conditionally renders content based on environment.

```typescript
import { DevOnlyWrapper } from '@/components/dev-only-wrapper';

// Hide in production
<DevOnlyWrapper>
  <ExperimentalFeature />
</DevOnlyWrapper>

// Show fallback in production
<DevOnlyWrapper fallback={<ProductionFeature />}>
  <DevelopmentFeature />
</DevOnlyWrapper>

// Force show in production (override)
<DevOnlyWrapper showInProduction={true}>
  <AlwaysVisibleFeature />
</DevOnlyWrapper>
```

### 2. `DevOnlyRoute`
Wrapper for entire pages that should only be available in development.

```typescript
import { DevOnlyRoute } from '@/components/dev-only-wrapper';

export default function ExperimentalPage() {
  return (
    <DevOnlyRoute>
      <YourPageContent />
    </DevOnlyRoute>
  );
}
```

### 3. `FeatureFlag`
Environment variable-based feature toggling.

```typescript
import { FeatureFlag } from '@/components/dev-only-wrapper';

// Using environment variable NEXT_PUBLIC_FEATURE_COLLABORATION
<FeatureFlag feature="collaboration">
  <CollaborationFeatures />
</FeatureFlag>

// Manual override
<FeatureFlag feature="collaboration" enabled={true}>
  <CollaborationFeatures />
</FeatureFlag>

// With fallback
<FeatureFlag 
  feature="collaboration" 
  fallback={<ComingSoonMessage />}
>
  <CollaborationFeatures />
</FeatureFlag>
```

### 4. `EnvWrapper`
Convenience wrappers for specific environments.

```typescript
import { EnvWrapper } from '@/components/dev-only-wrapper';

// Development only
<EnvWrapper.Development>
  <DebugPanel />
</EnvWrapper.Development>

// Production only
<EnvWrapper.Production>
  <ProductionOnlyFeature />
</EnvWrapper.Production>

// Staging only
<EnvWrapper.Staging>
  <StagingTestFeature />
</EnvWrapper.Staging>
```

## 🎯 Current Implementation

### Pages Wrapped with DevOnlyRoute

#### 1. Collaboration Suite (`/collaboration`)
```typescript
// src/app/(dashboard)/collaboration/page.tsx
export default function CollaborationPage() {
  return (
    <DevOnlyRoute>
      <CollaborationPageContent />
    </DevOnlyRoute>
  );
}
```

#### 2. Document Organization (`/document-organization`)
```typescript
// src/app/(dashboard)/document-organization/page.tsx
export default function DocumentOrganizationPage() {
  return (
    <DevOnlyRoute>
      <div className="container py-6 max-w-6xl">
        <h1 className="text-2xl font-bold mb-6">Document Organization</h1>
        <DocumentOrganizationIntegration showFullInterface={true} />
      </div>
    </DevOnlyRoute>
  );
}
```

### Sidebar Navigation
Removed from production sidebar:
- ❌ Collaboration section
- ❌ Document Organization link

## 🔧 Environment Variables

Set these in your `.env.local` for feature flags:

```bash
# Feature flags (true/false)
NEXT_PUBLIC_FEATURE_COLLABORATION=false
NEXT_PUBLIC_FEATURE_DOCUMENT_ORGANIZATION=false
NEXT_PUBLIC_FEATURE_ADVANCED_ANALYTICS=false

# Environment override
NEXT_PUBLIC_ENVIRONMENT=staging
```

## 📱 Production Behavior

### DevOnlyRoute Pages
When accessed in production, shows:
```
🚧
Development Only
This page is only available in development mode.
[Go Back]
```

### DevOnlyWrapper Components
- **Default**: Hidden in production
- **With fallback**: Shows fallback content
- **With showInProduction=true**: Always visible

## 🎮 Use Cases

### 1. Experimental Features
```typescript
// Hide experimental chat features in production
<DevOnlyWrapper>
  <ExperimentalChatBot />
</DevOnlyWrapper>
```

### 2. Debug Tools
```typescript
// Development debugging panel
<DevOnlyWrapper>
  <div className="fixed bottom-4 right-4 bg-red-500 text-white p-2 rounded">
    Debug: {JSON.stringify(debugInfo)}
  </div>
</DevOnlyWrapper>
```

### 3. Beta Features with Fallback
```typescript
// Show beta feature in dev, coming soon in production
<DevOnlyWrapper 
  fallback={
    <Card>
      <CardContent className="text-center py-8">
        <h3>Coming Soon</h3>
        <p>This feature is under development</p>
      </CardContent>
    </Card>
  }
>
  <BetaFeature />
</DevOnlyWrapper>
```

### 4. Conditional Navigation
```typescript
// Sidebar items that only show in development
{process.env.NODE_ENV === 'development' && (
  <SidebarItem href="/experimental-features">
    Experimental Features
  </SidebarItem>
)}
```

### 5. Feature Flag Integration
```typescript
// Environment-controlled features
<FeatureFlag feature="collaboration">
  <CollaborationSuite />
</FeatureFlag>
```

## 🚀 Best Practices

### 1. Use DevOnlyRoute for Entire Pages
```typescript
// ✅ Good - Wrap entire page
export default function ExperimentalPage() {
  return (
    <DevOnlyRoute>
      <PageContent />
    </DevOnlyRoute>
  );
}

// ❌ Avoid - Wrapping individual elements in page
export default function Page() {
  return (
    <div>
      <DevOnlyWrapper>
        <SomeComponent />
      </DevOnlyWrapper>
    </div>
  );
}
```

### 2. Provide Meaningful Fallbacks
```typescript
// ✅ Good - Clear fallback
<DevOnlyWrapper 
  fallback={
    <div className="text-center py-8">
      <h3>Feature Coming Soon</h3>
      <p>This feature is currently in development</p>
    </div>
  }
>
  <BetaFeature />
</DevOnlyWrapper>

// ❌ Avoid - No context for users
<DevOnlyWrapper>
  <BetaFeature />
</DevOnlyWrapper>
```

### 3. Use Feature Flags for Gradual Rollout
```typescript
// ✅ Good - Controlled rollout
<FeatureFlag feature="new_dashboard" fallback={<OldDashboard />}>
  <NewDashboard />
</FeatureFlag>
```

### 4. Document Wrapped Features
Always document which features are development-only and why.

## 🔍 Debugging

### Check Environment
```typescript
import { useEnvironment } from '@/components/dev-only-wrapper';

function MyComponent() {
  const { isDevelopment, isProduction, environment } = useEnvironment();
  
  console.log('Environment:', environment);
  console.log('Is Development:', isDevelopment);
  console.log('Is Production:', isProduction);
}
```

### Test Production Behavior
```bash
# Build and test production locally
npm run build
npm run start

# Or set NODE_ENV manually
NODE_ENV=production npm run dev
```

## 🎯 Migration Strategy

When ready to release wrapped features:

1. **Remove DevOnlyRoute wrapper**
2. **Add back to sidebar navigation**
3. **Update feature flags to true**
4. **Test in staging environment**
5. **Deploy to production**

This system provides a clean, maintainable way to manage feature visibility across environments! 🚀
