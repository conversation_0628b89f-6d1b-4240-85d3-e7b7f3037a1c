# **🎮 Gamification API Documentation**

## **📋 Overview**

The Gamification API provides a comprehensive RPG-like progression system for the negotiation simulator, including XP/levels, achievements, AI characters, relationships, and leaderboards.

## **🚀 Quick Start**

### **Prerequisites**
- Valid JWT authentication token
- Gamification system enabled (automatically available)

### **Basic Usage**
```bash
# Get user gamification profile
curl -X GET "http://localhost:4000/api/gamification/profile" \
  -H "Authorization: Bearer <your-jwt-token>"

# Get available characters
curl -X GET "http://localhost:4000/api/gamification/characters" \
  -H "Authorization: Bearer <your-jwt-token>"

# Award experience points
curl -X POST "http://localhost:4000/api/gamification/experience" \
  -H "Authorization: Bearer <your-jwt-token>" \
  -H "Content-Type: application/json" \
  -d '{"amount": 100, "source": "negotiation_completion"}'
```

## **📚 API Endpoints**

### **🎯 Gamification Profile**

#### **GET /api/gamification/profile**
Get current user's gamification profile.

**Response:**
```json
{
  "profile": {
    "level": {
      "current": 3,
      "title": "Senior Negotiator",
      "currentXP": 1100,
      "totalXP": 2600,
      "xpToNext": 900
    },
    "statistics": {
      "totalSessions": 15,
      "completedSessions": 12,
      "averageScore": 7.2,
      "winRate": 0.73,
      "currentStreak": 3,
      "bestStreak": 5,
      "totalXPEarned": 2600,
      "achievementsCount": 8,
      "totalTimeSpent": 3600
    },
    "unlockedContent": {
      "characters": ["default_character", "sarah_chen"],
      "scenarios": ["basic_contract", "software_license"],
      "features": ["advanced_hints"],
      "achievements": ["speed_demon", "win_win_master"]
    }
  },
  "achievements": {
    "achievements": [...],
    "totalUnlocked": 8,
    "totalAvailable": 25,
    "completionRate": 0.32
  },
  "relationships": [...]
}
```

#### **GET /api/gamification/profile/:userId**
Get public profile of another user (limited data).

#### **POST /api/gamification/experience**
Award experience points to current user.

**Request:**
```json
{
  "amount": 150,
  "source": "negotiation_completion",
  "metadata": {
    "sessionId": "session-123",
    "performance": "excellent"
  }
}
```

**Response:**
```json
{
  "success": true,
  "levelUpdate": {
    "previousLevel": 2,
    "newLevel": 3,
    "xpGained": 150,
    "totalXP": 1650,
    "leveledUp": true,
    "newUnlocks": ["character_sarah_chen"],
    "title": "Senior Negotiator"
  }
}
```

### **👥 Characters**

#### **GET /api/gamification/characters**
Get all available characters with unlock status.

**Query Parameters:**
- `difficulty` (1-5): Filter by difficulty level
- `unlocked` (boolean): Filter by unlock status
- `specialty` (string): Filter by character specialty

**Response:**
```json
[
  {
    "id": "default_character",
    "name": "Alex Johnson",
    "title": "Junior Procurement Manager",
    "company": "StartupCorp",
    "difficulty": 1,
    "specialties": ["friendly", "collaborative"],
    "personality": {
      "aggressiveness": 0.3,
      "flexibility": 0.8,
      "riskTolerance": 0.6,
      "communicationStyle": "DIPLOMATIC"
    },
    "unlocked": true,
    "relationship": {
      "respectLevel": 0.4,
      "trustLevel": 0.3,
      "status": "acquaintance"
    }
  }
]
```

#### **GET /api/gamification/characters/unlocked**
Get only unlocked characters for current user.

#### **GET /api/gamification/characters/default**
Get the default character (always unlocked).

#### **GET /api/gamification/characters/difficulty/:level**
Get characters by difficulty level (1-5).

#### **GET /api/gamification/characters/:characterId**
Get detailed character information including relationship.

#### **POST /api/gamification/characters/:characterId/unlock**
Attempt to unlock a character (if requirements met).

#### **POST /api/gamification/characters/seed**
Seed initial characters (admin only).

### **🏆 Achievements**

#### **GET /api/gamification/achievements**
Get all achievements with user progress.

**Query Parameters:**
- `category`: Filter by category (efficiency, collaboration, persistence, mastery)
- `rarity`: Filter by rarity (common, rare, epic, legendary)
- `unlocked`: Filter by unlock status

**Response:**
```json
{
  "achievements": [
    {
      "id": "speed_demon",
      "title": "Speed Demon",
      "description": "Close a deal in 3 rounds or less",
      "badge": "⚡",
      "rarity": "rare",
      "category": "efficiency",
      "requirements": {
        "type": "session_completion",
        "conditions": {
          "maxRounds": 3,
          "dealClosed": true,
          "minScore": 7
        }
      },
      "rewards": {
        "xp": 150,
        "credits": 50
      },
      "unlocked": false,
      "progress": 0,
      "progressPercentage": 0
    }
  ],
  "summary": {
    "total": 25,
    "unlocked": 8,
    "completionRate": 0.32,
    "categories": {
      "efficiency": 6,
      "collaboration": 8,
      "persistence": 5,
      "mastery": 6
    }
  }
}
```

#### **GET /api/gamification/achievements/categories**
Get achievement categories with counts.

#### **GET /api/gamification/achievements/rare**
Get rare and epic achievements only.

#### **GET /api/gamification/achievements/:achievementId**
Get detailed achievement information.

#### **GET /api/gamification/achievements/leaderboard/:achievementId**
Get leaderboard for specific achievement.

#### **POST /api/gamification/achievements/seed**
Seed initial achievements (admin only).

### **🏅 Leaderboards**

#### **GET /api/gamification/leaderboards**
Get leaderboard rankings.

**Query Parameters:**
- `type`: weekly, monthly, all_time (default: weekly)
- `scope`: global, organization (default: organization)
- `limit`: Number of results (default: 20, max: 100)

**Response:**
```json
{
  "rankings": [
    {
      "rank": 1,
      "userId": "user-123",
      "score": 2600,
      "totalDeals": 15,
      "winRate": 0.73,
      "averageRounds": 4.2,
      "totalXP": 2600,
      "metadata": {
        "name": "User 123",
        "title": "Senior Negotiator",
        "organization": "org-456"
      }
    }
  ],
  "userRank": 5,
  "totalParticipants": 150,
  "period": {
    "start": "2025-06-01T00:00:00.000Z",
    "end": "2025-06-07T23:59:59.999Z",
    "type": "weekly"
  }
}
```

#### **GET /api/gamification/leaderboards/around-user**
Get leaderboard centered around current user.

**Query Parameters:**
- `range`: Number of users above/below (default: 5)

#### **GET /api/gamification/leaderboards/user-rank**
Get current user's rank in leaderboard.

#### **GET /api/gamification/leaderboards/global**
Get global leaderboard (all organizations).

#### **GET /api/gamification/leaderboards/organization**
Get organization-specific leaderboard.

#### **GET /api/gamification/leaderboards/summary**
Get leaderboard summary across timeframes.

## **🔌 WebSocket Events**

### **Connection**
```javascript
const socket = io('/gamification', {
  auth: { token: 'your-jwt-token' }
});
```

### **Events to Emit**
- `join_session`: Join negotiation session for updates
- `leave_session`: Leave session
- `request_live_score`: Request real-time score calculation
- `request_hints`: Request context-aware hints

### **Events to Listen**
- `initial_data`: User profile data on connection
- `achievement_unlocked`: New achievement earned
- `level_up`: User leveled up
- `pressure_event`: Dynamic pressure event triggered
- `relationship_update`: Character relationship changed
- `game_state_update`: Session state updated
- `live_score_update`: Real-time score calculated
- `hints_update`: New hints available

## **🎯 Level System**

### **Levels & Titles**
1. **Rookie Negotiator** (0-499 XP)
2. **Junior Professional** (500-1499 XP)
3. **Senior Negotiator** (1500-3499 XP)
4. **Expert Dealmaker** (3500-6999 XP)
5. **Master Negotiator** (7000-12999 XP)
6. **Elite Professional** (13000-22999 XP)
7. **Legendary Dealmaker** (23000-39999 XP)
8. **Grandmaster** (40000+ XP)

### **XP Sources**
- Session start: 10 XP
- Successful move: 15-25 XP
- Session completion: 50-100 XP
- Achievement unlock: 25-300 XP
- Relationship milestone: 20-50 XP

## **🔐 Authentication & Security**

All endpoints require JWT authentication:
```bash
Authorization: Bearer <your-jwt-token>
```

Organization-level data isolation ensures users only see their organization's data.

## **📊 Error Handling**

### **Common Error Responses**
```json
{
  "statusCode": 400,
  "message": "Validation error",
  "error": "Bad Request",
  "details": {
    "organizationId": ["Path `organizationId` is required."]
  }
}
```

### **Error Codes**
- `400`: Bad Request - Invalid input data
- `401`: Unauthorized - Invalid or missing JWT token
- `403`: Forbidden - Insufficient permissions
- `404`: Not Found - Resource doesn't exist
- `500`: Internal Server Error - Server-side error

---

**Last Updated**: June 2025  
**API Version**: 1.0.0  
**Gamification System**: 100% Complete
