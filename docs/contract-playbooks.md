# Contract Playbooks API Documentation

## Overview

The Contract Playbooks API provides comprehensive contract analysis capabilities using customizable rule-based playbooks. This feature allows organizations to create, manage, and execute automated contract reviews against predefined compliance rules.

## Base URL

```
/api/contract-playbooks
```

## Authentication

All endpoints require JWT authentication via the `Authorization` header:

```
Authorization: Bearer <jwt_token>
```

## Feature Gating

This feature requires the `contract_playbooks` subscription feature. Users without this feature will receive a 403 Forbidden response.

## Data Models

### ContractPlaybook

```typescript
interface ContractPlaybook {
  id: string;
  organizationId: string;
  name: string;
  contractType:
    | 'nda'
    | 'employment'
    | 'service_agreement'
    | 'lease'
    | 'purchase'
    | 'partnership'
    | 'licensing'
    | 'other';
  description?: string;
  version: string;
  rules: PlaybookRule[];
  metadata: PlaybookMetadata;
  isActive: boolean;
  isTemplate: boolean;
  createdBy: string;
  updatedBy: string;
  createdAt: string;
  updatedAt: string;
}
```

### PlaybookRule

```typescript
interface PlaybookRule {
  id: string;
  name: string;
  category: string;
  ruleType:
    | 'required_clause'
    | 'prohibited_clause'
    | 'conditional_clause'
    | 'formatting_rule'
    | 'calculation_rule';
  severity: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  criteria: RuleCriteria;
  acceptableLanguage: AcceptableLanguage;
  unacceptableTerms: UnacceptableTerms;
  description: string;
  isActive: boolean;
}
```

### ContractAnalysis

```typescript
interface ContractAnalysis {
  id: string;
  organizationId: string;
  contractId: string;
  playbookId: string;
  playbookName: string;
  overallScore: number; // 0-100
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  status: 'PENDING' | 'IN_PROGRESS' | 'COMPLETED' | 'FAILED';
  deviations: Deviation[];
  summary: AnalysisSummary;
  metrics: AnalysisMetrics;
  metadata: AnalysisMetadata;
  analyzedBy: string;
  analyzedAt: string;
  createdAt: string;
  updatedAt: string;
}
```

## Endpoints

### 1. List Contract Playbooks

**GET** `/api/contract-playbooks`

Retrieve all contract playbooks for the organization.

#### Query Parameters

| Parameter    | Type    | Required | Description                                |
| ------------ | ------- | -------- | ------------------------------------------ |
| query        | string  | No       | Search query for playbook name/description |
| contractType | string  | No       | Filter by contract type                    |
| isActive     | boolean | No       | Filter by active status                    |
| isTemplate   | boolean | No       | Filter by template status                  |
| page         | number  | No       | Page number (default: 1)                   |
| limit        | number  | No       | Items per page (default: 20)               |

#### Response

```typescript
{
  playbooks: ContractPlaybook[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
```

#### Example

```bash
curl -X GET "http://localhost:4000/api/contract-playbooks?contractType=nda&page=1&limit=10" \
  -H "Authorization: Bearer <token>"
```

### 2. Create Contract Playbook

**POST** `/api/contract-playbooks`

Create a new contract playbook.

#### Request Body

```typescript
{
  name: string;
  contractType: string;
  description?: string;
  version: string;
  rules: CreatePlaybookRule[];
  metadata: PlaybookMetadata;
  isActive?: boolean;
  isTemplate?: boolean;
}
```

#### Response

```typescript
ContractPlaybook;
```

#### Example

```bash
curl -X POST "http://localhost:4000/api/contract-playbooks" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Standard NDA Playbook",
    "contractType": "nda",
    "description": "Standard playbook for reviewing Non-Disclosure Agreements",
    "version": "1.0.0",
    "rules": [
      {
        "name": "Confidentiality Definition",
        "category": "Definitions",
        "ruleType": "required_clause",
        "severity": "HIGH",
        "criteria": {
          "keywords": ["confidential information", "proprietary"],
          "semanticConcepts": ["confidentiality definition"]
        },
        "description": "Ensures proper definition of confidential information",
        "isActive": true
      }
    ],
    "metadata": {
      "industry": "Technology",
      "jurisdiction": "United States",
      "riskProfile": "Medium"
    }
  }'
```

### 3. Get Contract Playbook by ID

**GET** `/api/contract-playbooks/{id}`

Retrieve a specific contract playbook.

#### Path Parameters

| Parameter | Type   | Required | Description |
| --------- | ------ | -------- | ----------- |
| id        | string | Yes      | Playbook ID |

#### Response

```typescript
ContractPlaybook;
```

### 4. Update Contract Playbook

**PUT** `/api/contract-playbooks/{id}`

Update an existing contract playbook.

#### Path Parameters

| Parameter | Type   | Required | Description |
| --------- | ------ | -------- | ----------- |
| id        | string | Yes      | Playbook ID |

#### Request Body

```typescript
{
  name?: string;
  description?: string;
  version?: string;
  rules?: UpdatePlaybookRule[];
  metadata?: PlaybookMetadata;
  isActive?: boolean;
  isTemplate?: boolean;
}
```

#### Response

```typescript
ContractPlaybook;
```

### 5. Delete Contract Playbook

**DELETE** `/api/contract-playbooks/{id}`

Delete a contract playbook.

#### Path Parameters

| Parameter | Type   | Required | Description |
| --------- | ------ | -------- | ----------- |
| id        | string | Yes      | Playbook ID |

#### Response

```
204 No Content
```

## Contract Analysis Endpoints

### 6. Analyze Contract

**POST** `/api/contract-playbooks/analyze`

Analyze a contract against a specific playbook.

#### Request Body

```typescript
{
  contractId: string;
  playbookId: string;
  options?: {
    includeRecommendations?: boolean;
    riskThreshold?: number; // 1-5
    aiAnalysis?: boolean;
    detailedReport?: boolean;
  };
}
```

#### Response

```typescript
ContractAnalysis;
```

#### Example

```bash
curl -X POST "http://localhost:4000/api/contract-playbooks/analyze" \
  -H "Authorization: Bearer <token>" \
  -H "Content-Type: application/json" \
  -d '{
    "contractId": "43ee5001-9a05-4934-9005-d33537231cf6",
    "playbookId": "71a7c189-1b57-4729-8028-b835bf76e1bd",
    "options": {
      "includeRecommendations": true,
      "riskThreshold": 2,
      "aiAnalysis": true,
      "detailedReport": true
    }
  }'
```

### 7. List Contract Analyses

**GET** `/api/contract-playbooks/analyses`

Retrieve all contract analyses for the organization.

#### Query Parameters

| Parameter  | Type   | Required | Description                  |
| ---------- | ------ | -------- | ---------------------------- |
| contractId | string | No       | Filter by contract ID        |
| playbookId | string | No       | Filter by playbook ID        |
| riskLevel  | string | No       | Filter by risk level         |
| status     | string | No       | Filter by status             |
| startDate  | string | No       | Start date filter (ISO 8601) |
| endDate    | string | No       | End date filter (ISO 8601)   |
| page       | number | No       | Page number (default: 1)     |
| limit      | number | No       | Items per page (default: 20) |

#### Response

```typescript
{
  analyses: ContractAnalysis[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
}
```

### 8. Get Contract Analysis by ID

**GET** `/api/contract-playbooks/analyses/{id}`

Retrieve a specific contract analysis.

#### Path Parameters

| Parameter | Type   | Required | Description |
| --------- | ------ | -------- | ----------- |
| id        | string | Yes      | Analysis ID |

#### Response

```typescript
ContractAnalysis;
```

### 9. Delete Contract Analysis

**DELETE** `/api/contract-playbooks/analyses/{id}`

Delete a contract analysis.

#### Path Parameters

| Parameter | Type   | Required | Description |
| --------- | ------ | -------- | ----------- |
| id        | string | Yes      | Analysis ID |

#### Response

```
204 No Content
```

## Utility Endpoints

### 10. Get Playbook Analytics

**GET** `/api/contract-playbooks/{id}/analytics`

Get analytics and usage statistics for a specific playbook.

#### Response

```typescript
{
  playbookId: string;
  totalAnalyses: number;
  averageScore: number;
  riskDistribution: {
    LOW: number;
    MEDIUM: number;
    HIGH: number;
    CRITICAL: number;
  }
  commonDeviations: Array<{
    ruleId: string;
    ruleName: string;
    frequency: number;
    averageSeverity: string;
  }>;
  performanceMetrics: {
    averageProcessingTime: number;
    averageConfidenceScore: number;
  }
  timeSeriesData: Array<{
    date: string;
    analysisCount: number;
    averageScore: number;
  }>;
}
```

### 11. Duplicate Playbook

**POST** `/api/contract-playbooks/{id}/duplicate`

Create a copy of an existing playbook.

#### Path Parameters

| Parameter | Type   | Required | Description              |
| --------- | ------ | -------- | ------------------------ |
| id        | string | Yes      | Playbook ID to duplicate |

#### Request Body

```typescript
{
  name: string; // New name for the duplicated playbook
}
```

#### Response

```typescript
ContractPlaybook;
```

### 12. Export Playbook

**GET** `/api/contract-playbooks/{id}/export`

Export a playbook in a portable format.

#### Response

```typescript
{
  playbook: ContractPlaybook;
  exportFormat: string;
  exportedAt: string;
  version: string;
}
```

### 13. Import Playbook

**POST** `/api/contract-playbooks/import`

Import a playbook from an exported format.

#### Request Body

```typescript
{
  playbookData: any; // Exported playbook data
  options?: {
    overwriteExisting?: boolean;
    validateRules?: boolean;
  };
}
```

#### Response

```typescript
ContractPlaybook;
```

## Error Responses

All endpoints may return the following error responses:

### 400 Bad Request

```typescript
{
  statusCode: 400;
  message: string;
  error: "Bad Request";
  details?: any;
  path: string;
  timestamp: string;
}
```

### 401 Unauthorized

```typescript
{
  statusCode: 401;
  message: 'Unauthorized';
  error: 'Unauthorized';
}
```

### 403 Forbidden

```typescript
{
  statusCode: 403;
  message: "Feature 'contract_playbooks' is not available in your current subscription plan";
  error: 'Forbidden';
  details: null;
  path: string;
  timestamp: string;
}
```

### 404 Not Found

```typescript
{
  statusCode: 404;
  message: string;
  error: "Not Found";
  details?: any;
  path: string;
  timestamp: string;
}
```

### 409 Conflict

```typescript
{
  statusCode: 409;
  message: string;
  error: "Conflict";
  details?: any;
  path: string;
  timestamp: string;
}
```

### 500 Internal Server Error

```typescript
{
  statusCode: 500;
  message: 'Internal server error';
  error: 'Internal Server Error';
}
```

## Frontend Implementation Guide

### React/TypeScript Integration

#### 1. API Client Setup

```typescript
// api/contractPlaybooks.ts
import { apiClient } from './client';

export const contractPlaybooksApi = {
  // Playbook Management
  getPlaybooks: (params?: PlaybookSearchParams) =>
    apiClient.get('/contract-playbooks', { params }),

  createPlaybook: (data: CreatePlaybookRequest) =>
    apiClient.post('/contract-playbooks', data),

  getPlaybook: (id: string) => apiClient.get(`/contract-playbooks/${id}`),

  updatePlaybook: (id: string, data: UpdatePlaybookRequest) =>
    apiClient.put(`/contract-playbooks/${id}`, data),

  deletePlaybook: (id: string) => apiClient.delete(`/contract-playbooks/${id}`),

  // Analysis
  analyzeContract: (data: AnalyzeContractRequest) =>
    apiClient.post('/contract-playbooks/analyze', data),

  getAnalyses: (params?: AnalysisSearchParams) =>
    apiClient.get('/contract-playbooks/analyses', { params }),

  getAnalysis: (id: string) =>
    apiClient.get(`/contract-playbooks/analyses/${id}`),

  // Utilities
  getAnalytics: (id: string) =>
    apiClient.get(`/contract-playbooks/${id}/analytics`),

  duplicatePlaybook: (id: string, name: string) =>
    apiClient.post(`/contract-playbooks/${id}/duplicate`, { name }),

  exportPlaybook: (id: string) =>
    apiClient.get(`/contract-playbooks/${id}/export`),

  importPlaybook: (data: any) =>
    apiClient.post('/contract-playbooks/import', data),
};
```

#### 2. React Hooks

```typescript
// hooks/useContractPlaybooks.ts
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { contractPlaybooksApi } from '../api/contractPlaybooks';

export const usePlaybooks = (params?: PlaybookSearchParams) => {
  return useQuery({
    queryKey: ['playbooks', params],
    queryFn: () => contractPlaybooksApi.getPlaybooks(params),
  });
};

export const useCreatePlaybook = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: contractPlaybooksApi.createPlaybook,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['playbooks'] });
    },
  });
};

export const useAnalyzeContract = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: contractPlaybooksApi.analyzeContract,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ['analyses'] });
    },
  });
};
```

#### 3. Component Examples

```typescript
// components/PlaybookList.tsx
import React from 'react';
import { usePlaybooks } from '../hooks/useContractPlaybooks';

export const PlaybookList: React.FC = () => {
  const { data, isLoading, error } = usePlaybooks();

  if (isLoading) return <div>Loading playbooks...</div>;
  if (error) return <div>Error loading playbooks</div>;

  return (
    <div>
      <h2>Contract Playbooks ({data?.total})</h2>
      {data?.playbooks.map((playbook) => (
        <div key={playbook.id} className="playbook-card">
          <h3>{playbook.name}</h3>
          <p>{playbook.description}</p>
          <span className="contract-type">{playbook.contractType}</span>
          <span className="rule-count">{playbook.rules.length} rules</span>
        </div>
      ))}
    </div>
  );
};
```

### State Management

#### Redux Toolkit Example

```typescript
// store/contractPlaybooksSlice.ts
import { createSlice, createAsyncThunk } from '@reduxjs/toolkit';

export const fetchPlaybooks = createAsyncThunk(
  'playbooks/fetchPlaybooks',
  async (params?: PlaybookSearchParams) => {
    const response = await contractPlaybooksApi.getPlaybooks(params);
    return response.data;
  },
);

const contractPlaybooksSlice = createSlice({
  name: 'contractPlaybooks',
  initialState: {
    playbooks: [],
    analyses: [],
    loading: false,
    error: null,
  },
  reducers: {},
  extraReducers: (builder) => {
    builder
      .addCase(fetchPlaybooks.pending, (state) => {
        state.loading = true;
      })
      .addCase(fetchPlaybooks.fulfilled, (state, action) => {
        state.loading = false;
        state.playbooks = action.payload.playbooks;
      })
      .addCase(fetchPlaybooks.rejected, (state, action) => {
        state.loading = false;
        state.error = action.error.message;
      });
  },
});
```

## Testing Examples

### Jest/React Testing Library

```typescript
// __tests__/PlaybookList.test.tsx
import { render, screen } from '@testing-library/react';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { PlaybookList } from '../components/PlaybookList';

const mockPlaybooks = {
  playbooks: [
    {
      id: '1',
      name: 'Standard NDA Playbook',
      contractType: 'nda',
      rules: [{ id: '1', name: 'Confidentiality' }],
    },
  ],
  total: 1,
};

jest.mock('../api/contractPlaybooks', () => ({
  contractPlaybooksApi: {
    getPlaybooks: jest.fn().mockResolvedValue({ data: mockPlaybooks }),
  },
}));

test('renders playbook list', async () => {
  const queryClient = new QueryClient();

  render(
    <QueryClientProvider client={queryClient}>
      <PlaybookList />
    </QueryClientProvider>,
  );

  expect(await screen.findByText('Standard NDA Playbook')).toBeInTheDocument();
  expect(screen.getByText('Contract Playbooks (1)')).toBeInTheDocument();
});
```

## Performance Considerations

1. **Pagination**: Always use pagination for large datasets
2. **Caching**: Implement proper caching strategies for playbooks and analyses
3. **Debouncing**: Debounce search inputs to avoid excessive API calls
4. **Loading States**: Show appropriate loading states during analysis (can take 10-30 seconds)
5. **Error Handling**: Implement robust error handling for failed analyses

## Security Notes

1. All endpoints require valid JWT authentication
2. Feature gating is enforced at the API level
3. Users can only access data within their organization
4. Sensitive analysis data should be handled securely in the frontend
