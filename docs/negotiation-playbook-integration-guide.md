# Negotiation Playbook - Frontend Integration Guide

## 🎯 Overview

This guide helps frontend developers integrate the Negotiation Playbook feature into the DocGic application.

## 🚦 Prerequisites

- User must have **PRO subscription**
- Document must be uploaded and processed
- Valid JWT authentication token

## 📱 UI/UX Recommendations

### 1. Entry Points

Add negotiation playbook access in these locations:

- Document detail page (primary action button)
- Document list (context menu)
- Analysis results page (related action)

### 2. User Flow

```
Document View → Generate Playbook → Loading State → Results Display
                     ↓
              Check Existing Playbook First
```

### 3. Loading States

- **Initial Check**: "Checking for existing playbook..."
- **Generation**: "Generating negotiation strategy... (15-30 seconds)"
- **Progress**: Show spinner with estimated time remaining

### 4. Error Handling

- **No Subscription**: Show upgrade modal with pricing
- **Document Not Ready**: "Please wait for document processing to complete"
- **Generation Failed**: "Unable to generate playbook. Please try again."

## 🎨 Component Structure

### Main Component

```tsx
<NegotiationPlaybookContainer>
  <PlaybookHeader />
  <PlaybookActions />
  {loading && <LoadingState />}
  {error && <ErrorState />}
  {playbook && <PlaybookDisplay />}
</NegotiationPlaybookContainer>
```

### Playbook Display Sections

1. **Executive Summary** - Overview card
2. **Key Negotiation Points** - Priority-sorted list
3. **Strategic Recommendations** - Categorized advice
4. **Risk Assessment** - Risk level indicators
5. **Negotiation Simulations** - Scenario planning (if included)

## 🔧 Implementation Examples

### Service Layer

```typescript
// services/negotiationPlaybook.ts
export class NegotiationPlaybookService {
  private baseUrl = '/api/documents';

  async getPlaybook(documentId: string): Promise<NegotiationPlaybook | null> {
    try {
      const response = await apiClient.get(
        `${this.baseUrl}/${documentId}/negotiation-playbook`,
      );
      return response.data.data;
    } catch (error) {
      if (error.response?.status === 404) {
        return null; // No playbook exists
      }
      throw error;
    }
  }

  async generatePlaybook(
    documentId: string,
    options: GeneratePlaybookOptions,
  ): Promise<NegotiationPlaybook> {
    const response = await apiClient.post(
      `${this.baseUrl}/${documentId}/negotiation-playbook`,
      options,
    );
    return response.data.data;
  }
}
```

### React Hook

```typescript
// hooks/useNegotiationPlaybook.ts
export const useNegotiationPlaybook = (documentId: string) => {
  const [state, setState] = useState({
    playbook: null,
    loading: false,
    error: null,
    hasExisting: false,
  });

  const checkExisting = useCallback(async () => {
    setState((prev) => ({ ...prev, loading: true, error: null }));

    try {
      const existing = await playbookService.getPlaybook(documentId);
      setState((prev) => ({
        ...prev,
        playbook: existing,
        hasExisting: !!existing,
        loading: false,
      }));
    } catch (error) {
      setState((prev) => ({
        ...prev,
        error: error.message,
        loading: false,
      }));
    }
  }, [documentId]);

  const generate = useCallback(
    async (options: GeneratePlaybookOptions) => {
      setState((prev) => ({ ...prev, loading: true, error: null }));

      try {
        const playbook = await playbookService.generatePlaybook(
          documentId,
          options,
        );
        setState((prev) => ({
          ...prev,
          playbook,
          hasExisting: true,
          loading: false,
        }));
      } catch (error) {
        setState((prev) => ({
          ...prev,
          error: error.message,
          loading: false,
        }));
      }
    },
    [documentId],
  );

  useEffect(() => {
    checkExisting();
  }, [checkExisting]);

  return {
    ...state,
    generate,
    refresh: checkExisting,
  };
};
```

### Main Component

```tsx
// components/NegotiationPlaybook.tsx
const NegotiationPlaybook: React.FC<{ documentId: string }> = ({
  documentId,
}) => {
  const { playbook, loading, error, hasExisting, generate } =
    useNegotiationPlaybook(documentId);
  const [showGenerateModal, setShowGenerateModal] = useState(false);

  const handleGenerate = async (options: GeneratePlaybookOptions) => {
    await generate(options);
    setShowGenerateModal(false);
  };

  if (loading) {
    return (
      <LoadingState message="Analyzing document for negotiation opportunities..." />
    );
  }

  if (error) {
    return (
      <ErrorState error={error} onRetry={() => window.location.reload()} />
    );
  }

  return (
    <div className="negotiation-playbook">
      <div className="playbook-header">
        <h2>Negotiation Playbook</h2>
        <div className="actions">
          {!hasExisting ? (
            <Button
              onClick={() => setShowGenerateModal(true)}
              variant="primary"
              icon="magic"
            >
              Generate Playbook
            </Button>
          ) : (
            <Button
              onClick={() => setShowGenerateModal(true)}
              variant="secondary"
              icon="refresh"
            >
              Regenerate
            </Button>
          )}
        </div>
      </div>

      {playbook && <PlaybookDisplay playbook={playbook} />}

      <GeneratePlaybookModal
        isOpen={showGenerateModal}
        onClose={() => setShowGenerateModal(false)}
        onGenerate={handleGenerate}
      />
    </div>
  );
};
```

## 🎨 Styling Guidelines

### Priority Indicators

```css
.negotiation-point {
  border-left: 4px solid var(--priority-color);
  padding: 16px;
  margin-bottom: 12px;
  border-radius: 8px;
  background: var(--card-background);
}

.priority-critical {
  --priority-color: #dc2626;
}
.priority-high {
  --priority-color: #ea580c;
}
.priority-medium {
  --priority-color: #ca8a04;
}
.priority-low {
  --priority-color: #65a30d;
}
```

### Risk Level Indicators

```css
.risk-level {
  display: inline-flex;
  align-items: center;
  padding: 4px 12px;
  border-radius: 20px;
  font-size: 12px;
  font-weight: 600;
  text-transform: uppercase;
}

.risk-critical {
  background: #fef2f2;
  color: #dc2626;
}
.risk-high {
  background: #fff7ed;
  color: #ea580c;
}
.risk-medium {
  background: #fefce8;
  color: #ca8a04;
}
.risk-low {
  background: #f0fdf4;
  color: #65a30d;
}
```

## 📊 Analytics Integration

Track these events for product analytics:

```typescript
// Analytics events
analytics.track('negotiation_playbook_viewed', {
  documentId,
  documentType,
  hasExistingPlaybook: !!playbook,
});

analytics.track('negotiation_playbook_generated', {
  documentId,
  documentType,
  focusAreas,
  includeSimulations,
  generationTime: endTime - startTime,
});

analytics.track('negotiation_point_expanded', {
  documentId,
  pointTitle,
  priority,
});
```

## 🔒 Security Considerations

1. **Subscription Validation**: Always check subscription status before showing generate button
2. **Token Refresh**: Handle expired tokens gracefully
3. **Data Sanitization**: Sanitize playbook content before rendering
4. **Error Logging**: Log errors without exposing sensitive data

## 📱 Mobile Considerations

- Use collapsible sections for better mobile experience
- Implement swipe gestures for navigating between sections
- Optimize loading states for slower connections
- Consider offline caching for generated playbooks

## 🧪 Testing Checklist

### Unit Tests

- [ ] Service methods handle all response types
- [ ] Hook manages state correctly
- [ ] Components render with different data states

### Integration Tests

- [ ] Generate playbook flow works end-to-end
- [ ] Error handling displays appropriate messages
- [ ] Subscription checks prevent unauthorized access

### E2E Tests

- [ ] User can generate playbook from document page
- [ ] Generated playbook displays all sections
- [ ] Regeneration updates existing playbook

## 🚀 Performance Optimization

1. **Lazy Loading**: Load playbook components only when needed
2. **Caching**: Cache generated playbooks in local storage
3. **Debouncing**: Debounce regeneration requests
4. **Pagination**: Paginate large lists of negotiation points

## 📞 Support & Troubleshooting

### Common Issues

1. **"Feature not available"**: User needs PRO subscription
2. **"Document not found"**: Document may be processing or deleted
3. **Generation timeout**: AI service may be overloaded

### Debug Information

Include these details when reporting issues:

- Document ID
- User subscription tier
- Request payload
- Response status and body
- Browser console errors

---

For additional help, refer to:

- [Full API Documentation](./negotiation-playbook.md)
- [Quick Reference Guide](./negotiation-playbook-quick-reference.md)
- [Postman Collection](./postman/negotiation-playbook.postman_collection.json)
