# Backend Requirements: Negotiation Simulator Feature

## Overview

This document outlines the complete backend implementation requirements for the Negotiation Simulator feature. The feature enables users to practice negotiation skills through AI-powered interactive scenarios.

## MongoDB Collections Schema

### New Collections Required

#### 1. Negotiation Scenarios Collection

```javascript
// Collection: negotiation_scenarios
{
  _id: ObjectId,
  name: String, // required, min: 3, max: 255
  description: String, // required, min: 10, max: 2000
  industry: String, // enum: ['TECHNOLOGY', 'HEALTHCARE', 'FINANCE', 'LEGAL', 'REAL_ESTATE', 'MANUFACTURING', 'RETAIL', 'EDUCATION', 'OTHER']
  contractType: String, // enum: ['SOFTWARE_LICENSE', 'EMPLOYMENT_AGREEMENT', 'SERVICE_AGREEMENT', 'PURCHASE_AGREEMENT', 'PARTNERSHIP_AGREEMENT', 'NDA', 'LEASE_AGREEMENT', 'CONSULTING_AGREEMENT', 'OTHER']
  difficulty: String, // enum: ['BEGINNER', 'INTERMEDIATE', 'EXPERT']
  parties: [
    {
      role: String, // enum: ['BUYER', 'SELLER', 'VENDOR', 'CLIENT', 'CONTRACTOR', 'LICENSOR', 'LICENSEE', 'EMPLOYER', 'EMPLOYEE']
      priorities: [String],
      negotiationStyle: String, // enum: ['AGGRESSIVE', 'COLLABORATIVE', 'ANALYTICAL', 'COMPETITIVE', 'ACCOMMODATING', 'DIPLOMATIC']
      constraints: Object,
      budget: {
        min: Number,
        max: Number,
        currency: String
      },
      timeline: {
        urgency: String, // enum: ['LOW', 'MEDIUM', 'HIGH']
        deadline: Date
      }
    }
  ],
  initialOffer: {
    price: Number,
    currency: String,
    paymentTerms: String,
    deliveryDate: Date,
    warranties: [String],
    liabilities: [String],
    terminationClauses: [String],
    intellectualProperty: [String],
    confidentiality: [String],
    customTerms: Object
  },
  constraints: {
    maxRounds: Number, // required, min: 1, max: 20
    mustHaveTerms: [String],
    dealBreakers: [String],
    flexibleTerms: [String],
    timeLimit: Number // in minutes
  },
  timeline: {
    expectedDuration: Number, // in minutes
    maxDuration: Number // in minutes
  },
  isTemplate: Boolean, // default: false
  tags: [String],
  // Integration fields for playbook-simulator connection
  sourcePlaybookId: ObjectId, // ref: negotiation_playbooks (optional)
  sourceDocumentId: ObjectId, // ref: documents (optional)
  extractedFromDocument: Boolean, // default: false
  recommendedStrategies: [String], // strategies recommended from playbook analysis
  playbookAnalysisContext: {
    negotiationPoints: [String],
    riskFactors: [String],
    strategicRecommendations: [String],
    documentComplexity: String, // enum: ['LOW', 'MEDIUM', 'HIGH']
    estimatedDifficulty: String, // enum: ['BEGINNER', 'INTERMEDIATE', 'EXPERT']
    keyTerms: [String],
    potentialConcessions: [String]
  },
  createdBy: ObjectId, // ref: users
  organizationId: ObjectId, // ref: organizations
  createdAt: Date, // default: Date.now
  updatedAt: Date // default: Date.now
}
```

#### 2. Negotiation Sessions Collection

```javascript
// Collection: negotiation_sessions
{
  _id: ObjectId,
  scenarioId: ObjectId, // ref: negotiation_scenarios
  userId: ObjectId, // ref: users
  status: String, // enum: ['ACTIVE', 'COMPLETED', 'PAUSED', 'ABANDONED'], default: 'ACTIVE'
  rounds: [
    {
      roundNumber: Number,
      userMove: {
        id: String,
        timestamp: Date,
        offer: Object, // Terms structure
        message: String,
        strategy: String, // enum: ['COLLABORATIVE', 'COMPETITIVE', 'VALUE_BASED', 'CONCESSION', 'ANCHORING', 'DEADLINE_PRESSURE']
        reasoning: String
      },
      aiMove: {
        id: String,
        timestamp: Date,
        offer: Object, // Terms structure
        message: String,
        strategy: String,
        reasoning: String,
        aiResponse: {
          responseTime: Number,
          confidenceScore: Number,
          reasoning: String
        }
      },
      feedback: {
        score: Number,
        strengths: [String],
        improvements: [String],
        suggestions: [String]
      }
    }
  ],
  metrics: {
    totalRounds: Number,
    averageResponseTime: Number,
    finalScore: Number,
    dealClosed: Boolean,
    userSatisfaction: Number,
    aiSatisfaction: Number,
    keyMetrics: {
      communicationEffectiveness: Number,
      strategicThinking: Number,
      flexibilityScore: Number,
      timeManagement: Number
    }
  },
  aiPersonality: {
    aggressiveness: Number, // 0-1
    flexibility: Number, // 0-1
    riskTolerance: Number, // 0-1
    communicationStyle: String, // enum: ['FORMAL', 'CASUAL', 'TECHNICAL', 'DIPLOMATIC']
    decisionSpeed: String, // enum: ['FAST', 'MODERATE', 'DELIBERATE']
    concessionPattern: String // enum: ['EARLY', 'GRADUAL', 'LATE', 'MINIMAL']
  },
  currentTerms: Object, // Current negotiation state
  // Integration fields for playbook-simulator connection
  sourcePlaybookId: ObjectId, // ref: negotiation_playbooks (optional)
  sourceDocumentId: ObjectId, // ref: documents (optional)
  userPerformanceContext: {
    averageScore: Number,
    strongStrategies: [String],
    weakAreas: [String],
    preferredStyle: String,
    skillLevel: String // enum: ['BEGINNER', 'INTERMEDIATE', 'EXPERT']
  },
  startedAt: Date, // default: Date.now
  completedAt: Date,
  pausedAt: Date,
  lastActivity: Date // default: Date.now
}
```

#### 3. User Negotiation Profiles Collection

```javascript
// Collection: user_negotiation_profiles
{
  _id: ObjectId,
  userId: ObjectId, // ref: users, unique: true
  totalSessions: Number, // default: 0
  completedSessions: Number, // default: 0
  averageScore: Number, // default: 0.0
  averageRounds: Number, // default: 0.0
  averageDuration: Number, // in minutes, default: 0
  strongStrategies: [String],
  weakAreas: [String],
  preferredStyle: String,
  performanceHistory: [
    {
      date: Date,
      score: Number,
      rounds: Number,
      duration: Number, // in minutes
      strategiesUsed: [String],
      sessionId: ObjectId
    }
  ],
  lastUpdated: Date // default: Date.now
}
```

### MongoDB Indexes for Performance

```javascript
// Scenario collection indexes
db.negotiation_scenarios.createIndex({ createdBy: 1, organizationId: 1 });
db.negotiation_scenarios.createIndex({ industry: 1, difficulty: 1 });
db.negotiation_scenarios.createIndex({ contractType: 1 });
db.negotiation_scenarios.createIndex({ isTemplate: 1 });
db.negotiation_scenarios.createIndex({ tags: 1 });
db.negotiation_scenarios.createIndex({ createdAt: -1 });

// Session collection indexes
db.negotiation_sessions.createIndex({ userId: 1, status: 1 });
db.negotiation_sessions.createIndex({ scenarioId: 1 });
db.negotiation_sessions.createIndex({ lastActivity: -1 });
db.negotiation_sessions.createIndex({ startedAt: -1 });
db.negotiation_sessions.createIndex({ status: 1, lastActivity: -1 });

// User profiles collection indexes
db.user_negotiation_profiles.createIndex({ userId: 1 }, { unique: true });
db.user_negotiation_profiles.createIndex({ "performanceHistory.date": -1 });
db.user_negotiation_profiles.createIndex({ lastUpdated: -1 });

// Compound indexes for common queries
db.negotiation_scenarios.createIndex({
	organizationId: 1,
	isTemplate: 1,
	industry: 1,
});
db.negotiation_sessions.createIndex({ userId: 1, completedAt: -1 });
```

## API Endpoints Implementation

### Scenario Management

```typescript
// POST /api/negotiation-simulator/scenarios
interface CreateScenarioRequest {
	name: string;
	description: string;
	industry: Industry;
	contractType: ContractType;
	difficulty: Difficulty;
	parties: PartyProfile[];
	initialOffer: Terms;
	constraints: NegotiationConstraints;
	timeline: TimelineOptions;
	tags?: string[];
}

// GET /api/negotiation-simulator/scenarios
interface GetScenariosQuery {
	industry?: string;
	difficulty?: string;
	contractType?: string;
	page?: number;
	limit?: number;
	isTemplate?: boolean;
}

// Additional endpoints:
// GET /api/negotiation-simulator/scenarios/:id
// PUT /api/negotiation-simulator/scenarios/:id
// DELETE /api/negotiation-simulator/scenarios/:id
// POST /api/negotiation-simulator/scenarios/:id/clone
// GET /api/negotiation-simulator/scenarios/templates
```

### Session Management

```typescript
// POST /api/negotiation-simulator/sessions
interface StartSessionRequest {
	scenarioId: string;
	aiPersonality?: Partial<AIPersonalityProfile>;
}

// POST /api/negotiation-simulator/sessions/:id/moves
interface MakeMoveRequest {
	offer: Terms;
	message: string;
	strategy: MoveStrategy;
	reasoning?: string;
}

// Additional endpoints:
// GET /api/negotiation-simulator/sessions
// GET /api/negotiation-simulator/sessions/:id
// PUT /api/negotiation-simulator/sessions/:id/pause
// PUT /api/negotiation-simulator/sessions/:id/resume
// PUT /api/negotiation-simulator/sessions/:id/abandon
```

### Analytics & Evaluation

```typescript
// GET /api/negotiation-simulator/analytics/overview
interface AnalyticsResponse {
	totalSessions: number;
	completedSessions: number;
	completionRate: number;
	averageScore: number;
	averageRounds: number;
	averageDuration: number;
	recentSessions: NegotiationSession[];
	performanceTrends: PerformanceTrend[];
}

// POST /api/negotiation-simulator/sessions/:id/evaluate
// Triggers comprehensive session evaluation and scoring
```

### Integration Endpoints (Playbook-Simulator Bridge)

```typescript
// POST /api/negotiation-playbook/{playbookId}/create-scenario
interface CreateScenarioFromPlaybookRequest {
	difficulty?: Difficulty;
	focusAreas?: string[];
	aiPersonality?: Partial<AIPersonalityProfile>;
	customizations?: {
		maxRounds?: number;
		timeLimit?: number;
		specificTerms?: string[];
	};
}

// GET /api/negotiation-simulator/scenarios/from-playbook/{playbookId}
interface GetScenariosFromPlaybookQuery {
	page?: number;
	limit?: number;
}

// POST /api/negotiation-simulator/scenarios/from-document/{documentId}
interface CreateScenarioFromDocumentRequest {
	name: string;
	description?: string;
	difficulty?: Difficulty;
	extractionOptions?: {
		focusOnRisks?: boolean;
		includeRecommendations?: boolean;
		targetRole?: "buyer" | "seller" | "vendor" | "client";
	};
}

// Enhanced session creation with integration context
interface EnhancedStartSessionRequest extends StartSessionRequest {
	sourcePlaybookId?: string;
	sourceDocumentId?: string;
	userPerformanceContext?: {
		averageScore?: number;
		strongStrategies?: string[];
		weakAreas?: string[];
		preferredStyle?: string;
		skillLevel?: Difficulty;
	};
}
```

### User Profile & Performance Endpoints

```typescript
// GET /api/users/negotiation-profile
interface UserNegotiationProfileResponse {
	userId: string;
	totalSessions: number;
	completedSessions: number;
	averageScore: number;
	averageRounds: number;
	averageDuration: number;
	strongStrategies: string[];
	weakAreas: string[];
	preferredStyle: string;
	skillLevel: Difficulty;
	performanceHistory: PerformanceHistoryEntry[];
	lastUpdated: Date;
}

// PUT /api/users/negotiation-profile
interface UpdateUserNegotiationProfileRequest {
	strongStrategies?: string[];
	weakAreas?: string[];
	preferredStyle?: string;
	skillLevel?: Difficulty;
}

// GET /api/users/{userId}/performance-history
interface GetPerformanceHistoryQuery {
	timeframe?: "7d" | "30d" | "90d" | "1y";
	page?: number;
	limit?: number;
}

// POST /api/users/{userId}/skill-gap-analysis
interface SkillGapAnalysisRequest {
	targetScenarioId?: string;
	targetDifficulty?: Difficulty;
	focusAreas?: string[];
}

interface SkillGapAnalysisResponse {
	currentSkillLevel: Difficulty;
	targetSkillLevel: Difficulty;
	identifiedGaps: string[];
	recommendedPractice: {
		scenarios: string[];
		focusAreas: string[];
		estimatedPracticeTime: number; // in hours
		priorityOrder: string[];
	};
	improvementPlan: {
		shortTerm: string[]; // 1-2 weeks
		mediumTerm: string[]; // 1-2 months
		longTerm: string[]; // 3+ months
	};
}
```

### Cross-Feature Analytics Endpoints

```typescript
// GET /api/analytics/cross-feature-usage
interface CrossFeatureUsageQuery {
	timeframe?: "7d" | "30d" | "90d" | "1y";
	userId?: string;
	organizationId?: string;
}

interface CrossFeatureUsageResponse {
	playbookToSimulatorConversion: {
		totalPlaybooks: number;
		playbooksWithSimulation: number;
		conversionRate: number;
	};
	simulatorToPlaybookUsage: {
		totalSessions: number;
		sessionsWithPlaybookContext: number;
		contextUsageRate: number;
	};
	userEngagement: {
		averageSessionsPerPlaybook: number;
		averagePlaybooksPerUser: number;
		crossFeatureRetention: number;
	};
}

// GET /api/analytics/playbook-simulator-correlation
interface PlaybookSimulatorCorrelationQuery {
	userId?: string;
	timeframe?: "30d" | "90d" | "1y";
}

interface PlaybookSimulatorCorrelationResponse {
	performanceImprovement: {
		beforePlaybookAnalysis: number;
		afterPlaybookAnalysis: number;
		improvementPercentage: number;
	};
	strategicAlignment: {
		recommendedStrategiesUsed: number;
		totalRecommendations: number;
		alignmentScore: number;
	};
	learningEffectiveness: {
		practiceToRealWorldCorrelation: number;
		skillTransferRate: number;
		confidenceImprovement: number;
	};
}

// POST /api/analytics/generate-personalized-recommendations
interface GenerateRecommendationsRequest {
	userId: string;
	context: "playbook" | "simulator" | "general";
	targetArea?: string;
	currentDocumentId?: string;
	currentSessionId?: string;
}

interface PersonalizedRecommendationsResponse {
	recommendations: {
		type:
			| "practice_scenario"
			| "skill_focus"
			| "strategy_improvement"
			| "document_analysis";
		title: string;
		description: string;
		priority: "high" | "medium" | "low";
		estimatedTime: number; // in minutes
		actionUrl?: string;
		metadata?: Record<string, any>;
	}[];
	reasoning: string;
	confidenceScore: number;
}
```

## Core Services Implementation (MongoDB)

### 1. Scenario Service

```typescript
import { MongoClient, ObjectId, Collection } from "mongodb";
import Joi from "joi";

class ScenarioService {
	private collection: Collection<NegotiationScenario>;

	constructor(db: MongoClient) {
		this.collection = db.db().collection("negotiation_scenarios");
	}

	// Validation using Joi schema
	validateScenario(data: CreateScenarioRequest): ValidationResult {
		const schema = Joi.object({
			name: Joi.string().min(3).max(255).required(),
			description: Joi.string().min(10).max(2000).required(),
			industry: Joi.string()
				.valid(...INDUSTRY_OPTIONS)
				.required(),
			contractType: Joi.string()
				.valid(...CONTRACT_TYPE_OPTIONS)
				.required(),
			difficulty: Joi.string()
				.valid("BEGINNER", "INTERMEDIATE", "EXPERT")
				.required(),
			parties: Joi.array().min(1).max(4).required(),
			constraints: Joi.object({
				maxRounds: Joi.number().min(1).max(20).required(),
				mustHaveTerms: Joi.array().items(Joi.string()),
				dealBreakers: Joi.array().items(Joi.string()),
			}).required(),
		});
		return schema.validate(data);
	}

	// CRUD operations with MongoDB
	async createScenario(
		data: CreateScenarioRequest,
		userId: string
	): Promise<NegotiationScenario> {
		const validation = this.validateScenario(data);
		if (validation.error) {
			throw new ValidationError(validation.error.message);
		}

		const scenario = {
			...data,
			_id: new ObjectId(),
			createdBy: new ObjectId(userId),
			organizationId: new ObjectId(data.organizationId),
			isTemplate: false,
			createdAt: new Date(),
			updatedAt: new Date(),
		};

		const result = await this.collection.insertOne(scenario);
		return this.transformDocument(scenario);
	}

	async getScenarios(
		filters: GetScenariosQuery,
		userId: string
	): Promise<PaginatedResult<NegotiationScenario>> {
		const query: any = {
			$or: [{ createdBy: new ObjectId(userId) }, { isTemplate: true }],
		};

		// Apply filters
		if (filters.industry) query.industry = filters.industry;
		if (filters.difficulty) query.difficulty = filters.difficulty;
		if (filters.contractType) query.contractType = filters.contractType;
		if (filters.isTemplate !== undefined) query.isTemplate = filters.isTemplate;

		const page = filters.page || 1;
		const limit = Math.min(filters.limit || 20, 100); // Max 100 per page
		const skip = (page - 1) * limit;

		const [scenarios, total] = await Promise.all([
			this.collection
				.find(query)
				.sort({ createdAt: -1 })
				.skip(skip)
				.limit(limit)
				.toArray(),
			this.collection.countDocuments(query),
		]);

		return {
			data: scenarios.map((s) => this.transformDocument(s)),
			total,
			page,
			limit,
			totalPages: Math.ceil(total / limit),
		};
	}

	async getScenario(id: string, userId: string): Promise<NegotiationScenario> {
		if (!ObjectId.isValid(id)) {
			throw new ValidationError("Invalid scenario ID");
		}

		const scenario = await this.collection.findOne({
			_id: new ObjectId(id),
			$or: [{ createdBy: new ObjectId(userId) }, { isTemplate: true }],
		});

		if (!scenario) {
			throw new NotFoundError("Scenario not found or access denied");
		}

		return this.transformDocument(scenario);
	}

	// Helper method to transform MongoDB document
	private transformDocument(doc: any): NegotiationScenario {
		return {
			...doc,
			id: doc._id.toString(),
			createdBy: doc.createdBy.toString(),
			organizationId: doc.organizationId.toString(),
		};
	}
}
```

### 2. Session Service

```typescript
class SessionService {
	// Session lifecycle
	startSession(
		data: StartSessionRequest,
		userId: string
	): Promise<NegotiationSession>;
	getSession(id: string, userId: string): Promise<NegotiationSession>;
	getSessions(
		filters: GetSessionsQuery,
		userId: string
	): Promise<PaginatedResult<NegotiationSession>>;

	// Session control
	pauseSession(id: string, userId: string): Promise<NegotiationSession>;
	resumeSession(id: string, userId: string): Promise<NegotiationSession>;
	abandonSession(id: string, userId: string): Promise<void>;

	// Move processing
	makeMove(
		sessionId: string,
		move: MakeMoveRequest,
		userId: string
	): Promise<NegotiationSession>;
	processAIResponse(
		sessionId: string,
		userMove: NegotiationMove
	): Promise<NegotiationMove>;

	// Evaluation
	evaluateSession(id: string): Promise<NegotiationSession>;
	calculateSessionMetrics(
		session: NegotiationSession
	): Promise<NegotiationMetrics>;

	// Authorization
	canAccessSession(sessionId: string, userId: string): Promise<boolean>;
}
```

### 3. AI Service (Critical Component)

```typescript
class AIService {
	// Core AI functionality
	generateResponse(
		sessionContext: NegotiationSession,
		userMove: NegotiationMove,
		aiPersonality: AIPersonalityProfile
	): Promise<{
		message: string;
		offer: Terms;
		strategy: MoveStrategy;
		reasoning: string;
		confidenceScore: number;
		responseTime: number;
	}>;

	// Personality adaptation
	adaptPersonality(
		basePersonality: AIPersonalityProfile,
		sessionHistory: NegotiationMove[],
		roundNumber: number
	): Promise<AIPersonalityProfile>;

	// Evaluation algorithms
	evaluateUserPerformance(session: NegotiationSession): Promise<{
		finalScore: number;
		keyMetrics: {
			communicationEffectiveness: number;
			strategicThinking: number;
			flexibilityScore: number;
			timeManagement: number;
		};
		feedback: {
			strengths: string[];
			improvements: string[];
			suggestions: string[];
		};
	}>;

	// Decision making
	shouldAcceptOffer(
		offer: Terms,
		constraints: NegotiationConstraints,
		personality: AIPersonalityProfile
	): boolean;
	generateCounterOffer(
		currentOffer: Terms,
		targetTerms: Terms,
		personality: AIPersonalityProfile
	): Terms;
	selectStrategy(
		sessionContext: NegotiationSession,
		personality: AIPersonalityProfile
	): MoveStrategy;
}
```

### 4. Integration Service (Playbook-Simulator Bridge)

```typescript
class PlaybookIntegrationService {
	private scenarioService: ScenarioService;
	private userProfileService: UserProfileService;
	private playbookCollection: Collection;
	private documentCollection: Collection;

	constructor(
		scenarioService: ScenarioService,
		userProfileService: UserProfileService,
		db: MongoClient
	) {
		this.scenarioService = scenarioService;
		this.userProfileService = userProfileService;
		this.playbookCollection = db.db().collection("negotiation_playbooks");
		this.documentCollection = db.db().collection("documents");
	}

	// Create scenario from playbook analysis
	async createScenarioFromPlaybook(
		playbookId: string,
		request: CreateScenarioFromPlaybookRequest,
		userId: string
	): Promise<NegotiationScenario> {
		// Fetch playbook analysis
		const playbook = await this.playbookCollection.findOne({
			_id: new ObjectId(playbookId),
		});

		if (!playbook) {
			throw new NotFoundError("Playbook not found");
		}

		// Extract negotiation context from playbook
		const playbookContext = this.extractNegotiationContext(playbook);

		// Create scenario with playbook context
		const scenarioData: CreateScenarioRequest = {
			name: `Practice: ${playbook.title}`,
			description: `Negotiation practice scenario based on ${playbook.title} analysis`,
			industry: this.mapDocumentToIndustry(playbook.metadata?.industry),
			contractType: this.mapDocumentToContractType(playbook.documentType),
			difficulty: request.difficulty || playbookContext.estimatedDifficulty,
			parties: this.generatePartiesFromPlaybook(playbook, playbookContext),
			initialOffer: this.generateInitialOfferFromPlaybook(playbookContext),
			constraints: this.generateConstraintsFromPlaybook(
				playbookContext,
				request.customizations
			),
			timeline: {
				expectedDuration: request.customizations?.timeLimit || 30,
				maxDuration: request.customizations?.maxRounds
					? request.customizations.maxRounds * 5
					: 60,
			},
			tags: ["playbook-generated", playbook.documentType],
			// Integration fields
			sourcePlaybookId: new ObjectId(playbookId),
			extractedFromDocument: true,
			recommendedStrategies: playbookContext.strategicRecommendations,
			playbookAnalysisContext: playbookContext,
		};

		return await this.scenarioService.createScenario(scenarioData, userId);
	}

	// Get scenarios linked to a playbook
	async getScenariosFromPlaybook(
		playbookId: string,
		query: GetScenariosFromPlaybookQuery,
		userId: string
	): Promise<PaginatedResult<NegotiationScenario>> {
		const scenarios = await this.scenarioService.getScenarios(
			{
				...query,
				sourcePlaybookId: playbookId,
			},
			userId
		);

		return scenarios;
	}

	// Enhanced session creation with context
	async createSessionWithContext(
		request: EnhancedStartSessionRequest,
		userId: string
	): Promise<NegotiationSession> {
		// Get user performance context if not provided
		let userContext = request.userPerformanceContext;
		if (!userContext) {
			const profile = await this.userProfileService.getUserProfile(userId);
			userContext = {
				averageScore: profile.averageScore,
				strongStrategies: profile.strongStrategies,
				weakAreas: profile.weakAreas,
				preferredStyle: profile.preferredStyle,
				skillLevel: this.determineSkillLevel(profile),
			};
		}

		// Adapt AI personality based on user performance
		const adaptedPersonality = this.adaptAIPersonalityForUser(
			request.aiPersonality,
			userContext
		);

		const sessionData = {
			scenarioId: request.scenarioId,
			aiPersonality: adaptedPersonality,
			sourcePlaybookId: request.sourcePlaybookId
				? new ObjectId(request.sourcePlaybookId)
				: undefined,
			sourceDocumentId: request.sourceDocumentId
				? new ObjectId(request.sourceDocumentId)
				: undefined,
			userPerformanceContext: userContext,
		};

		return await this.sessionService.startSession(sessionData, userId);
	}

	// Helper methods for context extraction
	private extractNegotiationContext(playbook: any): PlaybookAnalysisContext {
		return {
			negotiationPoints: playbook.analysis?.keyTerms || [],
			riskFactors: playbook.analysis?.risks || [],
			strategicRecommendations: playbook.analysis?.recommendations || [],
			documentComplexity: this.assessComplexity(playbook),
			estimatedDifficulty: this.estimateDifficulty(playbook),
			keyTerms: playbook.analysis?.importantClauses || [],
			potentialConcessions: playbook.analysis?.negotiableTerms || [],
		};
	}

	private adaptAIPersonalityForUser(
		basePersonality: Partial<AIPersonalityProfile> | undefined,
		userContext: any
	): AIPersonalityProfile {
		const defaultPersonality: AIPersonalityProfile = {
			aggressiveness: 0.5,
			flexibility: 0.5,
			riskTolerance: 0.5,
			communicationStyle: "DIPLOMATIC",
			decisionSpeed: "MODERATE",
			concessionPattern: "GRADUAL",
		};

		// Adapt based on user's weak areas
		if (userContext.weakAreas?.includes("time_management")) {
			defaultPersonality.decisionSpeed = "FAST";
		}

		if (userContext.weakAreas?.includes("assertiveness")) {
			defaultPersonality.aggressiveness = 0.7;
		}

		return { ...defaultPersonality, ...basePersonality };
	}
}
```

### 5. Cross-Feature Analytics Service

```typescript
class AnalyticsService {
	// User analytics
	getUserAnalytics(userId: string): Promise<SessionAnalytics>;
	getPerformanceTrends(
		userId: string,
		timeframe: string
	): Promise<PerformanceTrend[]>;
	updateUserProfile(
		userId: string,
		sessionMetrics: NegotiationMetrics
	): Promise<void>;

	// Insights generation
	generatePersonalizedInsights(userId: string): Promise<string[]>;
	identifySkillGaps(userId: string): Promise<string[]>;
	recommendPracticeAreas(userId: string): Promise<string[]>;

	// Aggregated analytics
	getOrganizationAnalytics(orgId: string): Promise<OrganizationAnalytics>;
	getFeatureUsageMetrics(): Promise<FeatureMetrics>;
}
```

## Authorization & Subscription Integration

### Permission Checks

```typescript
// Add to existing middleware
const NEGOTIATION_SIMULATOR_PERMISSIONS = {
	view_scenarios: ["free", "pro"],
	create_scenarios: ["pro"],
	start_sessions: ["pro"],
	advanced_analytics: ["pro"],
	unlimited_sessions: ["pro"],
};

class NegotiationPermissionService {
	canAccessFeature(user: User, feature: string): boolean;
	canCreateScenarios(user: User): boolean;
	canStartSessions(user: User): boolean;
	hasSessionLimit(user: User): {
		limited: boolean;
		maxSessions: number;
		currentCount: number;
	};
}
```

### Rate Limiting

```typescript
// Implement rate limiting for AI API calls
class RateLimitService {
	checkAIRequestLimit(userId: string): Promise<boolean>;
	incrementAIUsage(userId: string): Promise<void>;
	getUsageStats(userId: string): Promise<UsageStats>;
}
```

## Background Jobs & Processing

### Job Queue Implementation

```typescript
// Background job processing
interface JobProcessor {
	// AI response generation (async)
	processAIResponse(sessionId: string, userMoveId: string): Promise<void>;

	// Session evaluation
	evaluateCompletedSession(sessionId: string): Promise<void>;

	// Analytics updates
	updateUserAnalytics(userId: string): Promise<void>;

	// Cleanup tasks
	cleanupAbandonedSessions(): Promise<void>;
	archiveOldSessions(): Promise<void>;
}
```

## Real-time Features (WebSocket)

### WebSocket Events

```typescript
interface WebSocketEvents {
	// Session updates
	"session:ai_response_ready": { sessionId: string; move: NegotiationMove };
	"session:status_changed": { sessionId: string; status: SessionStatus };
	"session:round_completed": { sessionId: string; roundNumber: number };

	// Real-time notifications
	"notification:session_reminder": { sessionId: string; message: string };
	"notification:evaluation_complete": {
		sessionId: string;
		metrics: NegotiationMetrics;
	};
}
```

## Data Validation & Business Rules

### Validation Schemas

```typescript
// JSON Schema validation for complex objects
const ScenarioValidationSchema = {
	type: "object",
	required: ["name", "description", "industry", "contractType", "difficulty"],
	properties: {
		name: { type: "string", minLength: 3, maxLength: 255 },
		description: { type: "string", minLength: 10, maxLength: 2000 },
		parties: {
			type: "array",
			minItems: 1,
			maxItems: 4,
			items: { $ref: "#/definitions/PartyProfile" },
		},
		// ... additional validation rules
	},
};
```

### Business Rules

```typescript
class BusinessRuleService {
	// Session rules
	validateSessionStart(
		scenario: NegotiationScenario,
		user: User
	): ValidationResult;
	validateMove(
		move: MakeMoveRequest,
		session: NegotiationSession
	): ValidationResult;
	checkSessionLimits(user: User): ValidationResult;

	// Scenario rules
	validateScenarioConstraints(
		scenario: CreateScenarioRequest
	): ValidationResult;
	checkScenarioComplexity(scenario: NegotiationScenario): ComplexityLevel;
}
```

## Performance & Optimization

### Caching Strategy

```typescript
class CacheService {
	// Scenario caching
	cacheUserScenarios(userId: string, ttl: number): Promise<void>;
	cacheTemplateScenarios(ttl: number): Promise<void>;

	// Session state caching
	cacheActiveSession(sessionId: string): Promise<void>;
	invalidateSessionCache(sessionId: string): Promise<void>;

	// Analytics caching
	cacheUserAnalytics(userId: string, ttl: number): Promise<void>;
}
```

### Database Optimization (MongoDB)

```javascript
// Collection sharding for large datasets
sh.enableSharding("negotiation_simulator");
sh.shardCollection("negotiation_simulator.negotiation_sessions", {
	userId: 1,
	startedAt: 1,
});
sh.shardCollection("negotiation_simulator.user_negotiation_profiles", {
	userId: 1,
});

// Aggregation pipelines for analytics
const userPerformancePipeline = [
	{
		$match: { userId: ObjectId(userId) },
	},
	{
		$group: {
			_id: "$userId",
			totalSessions: { $sum: 1 },
			completedSessions: {
				$sum: { $cond: [{ $eq: ["$status", "COMPLETED"] }, 1, 0] },
			},
			averageScore: { $avg: "$metrics.finalScore" },
			averageRounds: { $avg: "$metrics.totalRounds" },
			averageDuration: {
				$avg: {
					$divide: [
						{ $subtract: ["$completedAt", "$startedAt"] },
						60000, // Convert to minutes
					],
				},
			},
		},
	},
];

// Time-series collections for performance tracking
db.createCollection("performance_metrics", {
	timeseries: {
		timeField: "timestamp",
		metaField: "userId",
		granularity: "hours",
	},
});
```

## Security Considerations

### Data Protection

```typescript
class SecurityService {
	// Data encryption for sensitive information
	encryptSessionData(data: any): string;
	decryptSessionData(encryptedData: string): any;

	// Audit logging
	logUserAction(userId: string, action: string, details: any): Promise<void>;
	logAIInteraction(
		sessionId: string,
		request: any,
		response: any
	): Promise<void>;

	// Input sanitization
	sanitizeUserMessage(message: string): string;
	validateOfferTerms(terms: Terms): ValidationResult;
}
```

## Monitoring & Logging

### Metrics to Track

```typescript
interface MonitoringMetrics {
	// Performance metrics
	aiResponseTime: number;
	sessionCreationTime: number;
	databaseQueryTime: number;

	// Business metrics
	dailyActiveUsers: number;
	sessionCompletionRate: number;
	averageSessionDuration: number;

	// Error metrics
	aiServiceErrors: number;
	validationErrors: number;
	authorizationFailures: number;
}
```

## Implementation Timeline

### Phase 1: Core Infrastructure (2-3 weeks)

- Database schema implementation
- Basic CRUD operations for scenarios and sessions
- Authentication and authorization integration

### Phase 2: AI Integration (3-4 weeks)

- AI service implementation
- Move processing and response generation
- Basic evaluation algorithms

### Phase 3: Advanced Features (2-3 weeks)

- Analytics and performance tracking
- Real-time updates via WebSocket
- Background job processing

### Phase 4: Optimization & Polish (1-2 weeks)

- Performance optimization
- Comprehensive testing
- Monitoring and logging implementation

## Dependencies

### External Services

- **AI/LLM Provider** (OpenAI, Anthropic, or custom model)
- **Background Job Queue** (Redis/Bull, AWS SQS, or similar)
- **WebSocket Infrastructure** (Socket.io, native WebSocket)
- **Caching Layer** (Redis)
- **MongoDB Atlas** (or self-hosted MongoDB cluster)

### Internal Dependencies

- Existing user authentication system
- Subscription management system
- Organization management
- Audit logging infrastructure
- MongoDB connection and configuration

## MongoDB-Specific Considerations

### Schema Design Best Practices

```javascript
// Use embedded documents for related data that's always accessed together
{
  _id: ObjectId,
  scenario: {
    // Embed scenario data in session for performance
    name: String,
    difficulty: String,
    constraints: Object
  },
  rounds: [
    {
      // Embed rounds as they're always accessed with the session
      roundNumber: Number,
      userMove: Object,
      aiMove: Object
    }
  ]
}

// Use references for data that can be large or accessed independently
{
  _id: ObjectId,
  userId: ObjectId, // Reference to users collection
  scenarioId: ObjectId, // Reference to scenarios collection
  // ... other session data
}
```

### Data Validation

```javascript
// MongoDB schema validation
db.createCollection("negotiation_scenarios", {
	validator: {
		$jsonSchema: {
			bsonType: "object",
			required: [
				"name",
				"description",
				"industry",
				"contractType",
				"difficulty",
			],
			properties: {
				name: { bsonType: "string", minLength: 3, maxLength: 255 },
				description: { bsonType: "string", minLength: 10, maxLength: 2000 },
				industry: {
					enum: [
						"TECHNOLOGY",
						"HEALTHCARE",
						"FINANCE",
						"LEGAL",
						"REAL_ESTATE",
						"MANUFACTURING",
						"RETAIL",
						"EDUCATION",
						"OTHER",
					],
				},
				difficulty: { enum: ["BEGINNER", "INTERMEDIATE", "EXPERT"] },
				parties: {
					bsonType: "array",
					minItems: 1,
					maxItems: 4,
					items: {
						bsonType: "object",
						required: ["role", "priorities", "negotiationStyle"],
						properties: {
							role: {
								enum: [
									"BUYER",
									"SELLER",
									"VENDOR",
									"CLIENT",
									"CONTRACTOR",
									"LICENSOR",
									"LICENSEE",
									"EMPLOYER",
									"EMPLOYEE",
								],
							},
							priorities: { bsonType: "array", items: { bsonType: "string" } },
							negotiationStyle: {
								enum: [
									"AGGRESSIVE",
									"COLLABORATIVE",
									"ANALYTICAL",
									"COMPETITIVE",
									"ACCOMMODATING",
									"DIPLOMATIC",
								],
							},
						},
					},
				},
			},
		},
	},
});
```

### Performance Optimization

```javascript
// Use aggregation pipelines for complex analytics
const getSessionAnalytics = async (userId) => {
	return await db.negotiation_sessions.aggregate([
		{ $match: { userId: ObjectId(userId) } },
		{
			$group: {
				_id: null,
				totalSessions: { $sum: 1 },
				completedSessions: {
					$sum: { $cond: [{ $eq: ["$status", "COMPLETED"] }, 1, 0] },
				},
				averageScore: { $avg: "$metrics.finalScore" },
				averageRounds: { $avg: { $size: "$rounds" } },
			},
		},
	]);
};

// Use change streams for real-time updates
const watchSessions = () => {
	const changeStream = db.negotiation_sessions.watch([
		{ $match: { "fullDocument.status": "ACTIVE" } },
	]);

	changeStream.on("change", (change) => {
		// Emit WebSocket events for real-time updates
		io.emit("session:updated", change.fullDocument);
	});
};
```

This comprehensive backend implementation will provide a robust foundation for the Negotiation Simulator feature with proper MongoDB integration, scalability, security, and performance considerations.
