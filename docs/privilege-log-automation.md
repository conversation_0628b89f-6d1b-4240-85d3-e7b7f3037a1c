# Privilege Log Automation API Documentation

## Overview

The Privilege Log Automation feature provides AI-powered detection of privileged content in legal documents, automated redaction suggestions, and a complete workflow for reviewing and applying redactions. This feature is available for PRO tier subscribers.

## Base URL

```
http://localhost:4000/api/documents
```

## Authentication

All endpoints require JWT authentication via <PERSON><PERSON> token:

```
Authorization: Bearer <jwt_token>
```

## Feature Requirements

- **Subscription**: PRO tier required
- **Feature Flag**: `privilege_log_automation`

---

## API Endpoints

### 1. Analyze Document for Privileged Content

Scans a document for attorney-client privilege, work product, and other privileged content using both pattern matching and AI analysis.

**Endpoint:** `POST /documents/:documentId/privilege-analysis`

**Parameters:**

- `documentId` (path, required): The ID of the document to analyze

**Request Body:**

```json
{
  "includeAIAnalysis": true,
  "confidenceThreshold": 0.7,
  "privilegeTypes": [
    "attorney_client",
    "work_product",
    "confidential_communication"
  ],
  "autoRedact": false,
  "requireManualReview": true
}
```

**Request Schema:**

```typescript
interface AnalyzePrivilegeRequest {
  includeAIAnalysis?: boolean; // Default: true
  confidenceThreshold?: number; // 0.0-1.0, Default: 0.7
  privilegeTypes?: PrivilegeType[]; // Optional filter
  autoRedact?: boolean; // Default: false
  requireManualReview?: boolean; // Default: true
}

enum PrivilegeType {
  ATTORNEY_CLIENT = 'attorney_client',
  WORK_PRODUCT = 'work_product',
  CONFIDENTIAL_COMMUNICATION = 'confidential_communication',
  TRADE_SECRET = 'trade_secret',
  MEDICAL_PRIVILEGE = 'medical_privilege',
  SPOUSAL_PRIVILEGE = 'spousal_privilege',
  OTHER = 'other',
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "documentId": "675a1b2c3d4e5f6789abcdef",
    "privilegedContent": [
      {
        "id": "content-123",
        "documentId": "675a1b2c3d4e5f6789abcdef",
        "startPosition": 45,
        "endPosition": 78,
        "content": "attorney-client communication",
        "privilegeType": "attorney_client",
        "confidenceScore": 0.92,
        "detectionMethod": "pattern",
        "status": "detected",
        "redactionApplied": false
      }
    ],
    "redactionSuggestions": [
      {
        "contentId": "content-123",
        "originalText": "attorney-client communication",
        "suggestedRedaction": "[REDACTED]",
        "reason": "attorney_client privilege detected with 92.0% confidence",
        "privilegeType": "attorney_client",
        "confidenceScore": 0.92,
        "requiresReview": false
      }
    ],
    "summary": {
      "totalItemsFound": 5,
      "highConfidenceItems": 3,
      "requiresManualReview": 2,
      "autoRedactable": 3
    },
    "analysisMetadata": {
      "analysisDate": "2025-01-20T10:30:00Z",
      "detectionMethods": ["pattern", "ai"],
      "aiModelUsed": "gpt-4",
      "processingTime": 2340
    }
  }
}
```

**Error Responses:**

- `404`: Document not found
- `403`: Feature not available for current subscription
- `400`: Invalid request parameters

---

### 2. Get Privilege Log for Document

Retrieves the existing privilege log and analysis results for a specific document.

**Endpoint:** `GET /documents/:documentId/privilege-log`

**Parameters:**

- `documentId` (path, required): The ID of the document

**Response:**

```json
{
  "success": true,
  "data": {
    "id": "log-123",
    "documentId": "675a1b2c3d4e5f6789abcdef",
    "documentTitle": "Employment Contract",
    "privilegedContent": [...],
    "totalPrivilegedItems": 5,
    "totalRedactions": 2,
    "analysisDate": "2025-01-20T10:30:00Z",
    "lastReviewDate": "2025-01-20T11:15:00Z",
    "status": "completed",
    "reviewedBy": "user-456",
    "organizationId": "org-789",
    "createdBy": "user-123"
  }
}
```

---

### 3. List All Privilege Logs

Retrieves all privilege logs for the organization with filtering and pagination.

**Endpoint:** `GET /documents/privilege-logs`

**Query Parameters:**

- `status` (optional): Filter by privilege log status
- `privilegeType` (optional): Filter by privilege type
- `page` (optional): Page number (default: 1)
- `limit` (optional): Items per page (default: 20, max: 100)

**Response:**

```json
{
  "success": true,
  "data": [
    {
      "id": "log-123",
      "documentId": "675a1b2c3d4e5f6789abcdef",
      "documentTitle": "Employment Contract",
      "totalPrivilegedItems": 5,
      "totalRedactions": 2,
      "analysisDate": "2025-01-20T10:30:00Z",
      "status": "completed"
    }
  ],
  "pagination": {
    "total": 25,
    "page": 1,
    "limit": 20,
    "pages": 2
  }
}
```

---

### 4. Review Privileged Content Item

Updates the status of a privileged content item after manual review.

**Endpoint:** `PUT /documents/:documentId/privilege-content/:contentId/review`

**Parameters:**

- `documentId` (path, required): The ID of the document
- `contentId` (path, required): The ID of the privileged content item

**Request Body:**

```json
{
  "status": "confirmed",
  "reason": "Confirmed attorney-client privilege",
  "applyRedaction": true
}
```

**Request Schema:**

```typescript
interface ReviewPrivilegeRequest {
  status: PrivilegeStatus;
  reason?: string;
  applyRedaction?: boolean;
}

enum PrivilegeStatus {
  DETECTED = 'detected',
  UNDER_REVIEW = 'under_review',
  CONFIRMED = 'confirmed',
  REJECTED = 'rejected',
  REDACTED = 'redacted',
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    // Updated privilege log object
  },
  "message": "Privileged content reviewed successfully"
}
```

---

### 5. Apply Single Redaction

Applies redaction to a specific privileged content item.

**Endpoint:** `POST /documents/:documentId/redactions`

**Request Body:**

```json
{
  "contentId": "content-123",
  "redactionText": "[REDACTED]",
  "reason": "Attorney-client privilege protection"
}
```

**Response:**

```json
{
  "success": true,
  "message": "Redaction applied to privileged content: attorney_client"
}
```

---

### 6. Apply Bulk Redactions

Applies redactions to multiple privileged content items at once.

**Endpoint:** `POST /documents/:documentId/bulk-redactions`

**Request Body:**

```json
{
  "contentIds": ["content-123", "content-456", "content-789"],
  "redactionText": "[REDACTED]",
  "reason": "Bulk privilege protection"
}
```

**Response:**

```json
{
  "success": true,
  "data": {
    "redactedCount": 3
  },
  "message": "Applied 3 redactions"
}
```

---

## Data Models

### PrivilegedContent

```typescript
interface PrivilegedContent {
  id: string;
  documentId: string;
  startPosition: number;
  endPosition: number;
  content: string;
  privilegeType: PrivilegeType;
  confidenceScore: number; // 0.0 to 1.0
  detectionMethod: 'pattern' | 'ai' | 'manual';
  status: PrivilegeStatus;
  reviewedBy?: string;
  reviewedAt?: Date;
  redactionApplied: boolean;
  redactionReason?: string;
}
```

### PrivilegeLogEntry

```typescript
interface PrivilegeLogEntry {
  id: string;
  documentId: string;
  documentTitle: string;
  privilegedContent: PrivilegedContent[];
  totalPrivilegedItems: number;
  totalRedactions: number;
  analysisDate: Date;
  lastReviewDate?: Date;
  status: PrivilegeLogStatus;
  reviewedBy?: string;
  organizationId: string;
  createdBy: string;
}
```

### RedactionSuggestion

```typescript
interface RedactionSuggestion {
  contentId: string;
  originalText: string;
  suggestedRedaction: string;
  reason: string;
  privilegeType: PrivilegeType;
  confidenceScore: number;
  requiresReview: boolean;
}
```

---

## Error Handling

### Common Error Responses

**400 Bad Request:**

```json
{
  "statusCode": 400,
  "message": "Failed to analyze privilege: Invalid confidence threshold",
  "error": "Bad Request"
}
```

**403 Forbidden:**

```json
{
  "statusCode": 403,
  "message": "Feature 'privilege_log_automation' not available for current subscription",
  "error": "Forbidden"
}
```

**404 Not Found:**

```json
{
  "statusCode": 404,
  "message": "No privilege log found for document 675a1b2c3d4e5f6789abcdef",
  "error": "Not Found"
}
```

---

## Usage Examples

### JavaScript/Fetch Example

```javascript
// Analyze document for privileged content
const analyzePrivilege = async (documentId, options) => {
  const response = await fetch(
    `/api/documents/${documentId}/privilege-analysis`,
    {
      method: 'POST',
      headers: {
        Authorization: `Bearer ${token}`,
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        includeAIAnalysis: true,
        confidenceThreshold: 0.8,
        privilegeTypes: ['attorney_client', 'work_product'],
        requireManualReview: true,
      }),
    },
  );

  return await response.json();
};

// Get privilege log
const getPrivilegeLog = async (documentId) => {
  const response = await fetch(`/api/documents/${documentId}/privilege-log`, {
    headers: {
      Authorization: `Bearer ${token}`,
    },
  });

  return await response.json();
};

// Apply redaction
const applyRedaction = async (documentId, contentId, reason) => {
  const response = await fetch(`/api/documents/${documentId}/redactions`, {
    method: 'POST',
    headers: {
      Authorization: `Bearer ${token}`,
      'Content-Type': 'application/json',
    },
    body: JSON.stringify({
      contentId,
      reason,
    }),
  });

  return await response.json();
};
```
