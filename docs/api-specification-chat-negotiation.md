# Chat Negotiation API Specification

## Base URL
```
https://api.docgic.com/api/chat-negotiation
```

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer {jwt_token}
```

## Endpoints

### 1. Create Chat Negotiation Session

**Endpoint:** `POST /sessions`

**Request Body:**
```json
{
  "scenarioId": "software_licensing",
  "aiPersonality": {
    "aggressiveness": 0.6,
    "flexibility": 0.7,
    "communicationStyle": "diplomatic",
    "traits": ["analytical", "collaborative"]
  }
}
```

**Response:** `201 Created`
```json
{
  "id": "chat-neg-123e4567-e89b-12d3-a456-************",
  "negotiationSessionId": "neg-123e4567-e89b-12d3-a456-426614174001",
  "chatSessionId": "chat-123e4567-e89b-12d3-a456-426614174002",
  "scenarioId": "software_licensing",
  "status": "active",
  "currentRound": 1,
  "extractedTerms": {},
  "relationshipMetrics": {
    "trust": 50,
    "respect": 50,
    "pressure": 20
  },
  "score": 5.0,
  "aiPersonality": {
    "aggressiveness": 0.6,
    "flexibility": 0.7,
    "communicationStyle": "diplomatic",
    "traits": ["analytical", "collaborative"]
  },
  "createdAt": "2024-01-15T10:00:00.000Z",
  "updatedAt": "2024-01-15T10:00:00.000Z"
}
```

**Error Responses:**
```json
// 400 Bad Request
{
  "error": "Invalid scenario ID",
  "code": "INVALID_SCENARIO"
}

// 401 Unauthorized
{
  "error": "Authentication required",
  "code": "AUTH_REQUIRED"
}

// 500 Internal Server Error
{
  "error": "Failed to create session",
  "code": "SESSION_CREATION_FAILED"
}
```

### 2. Send Chat Move

**Endpoint:** `POST /sessions/{sessionId}/moves`

**Request Body:**
```json
{
  "content": "I'm thinking around $50k for the software license, paid annually",
  "extractedData": {
    "offer": {
      "price": 50000,
      "currency": "USD",
      "terms": ["annual", "software license"]
    },
    "strategy": "collaborative",
    "sentiment": "neutral",
    "confidence": 0.8
  }
}
```

**Response:** `200 OK`
```json
{
  "userMessage": {
    "id": "msg-123e4567-e89b-12d3-a456-426614174003",
    "sessionId": "chat-123e4567-e89b-12d3-a456-426614174002",
    "content": "I'm thinking around $50k for the software license, paid annually",
    "type": "user",
    "timestamp": "2024-01-15T10:05:00.000Z",
    "metadata": {
      "extractedData": {
        "offer": {
          "price": 50000,
          "currency": "USD",
          "terms": ["annual", "software license"]
        },
        "strategy": "collaborative",
        "sentiment": "neutral",
        "confidence": 0.8
      }
    }
  },
  "aiResponse": {
    "id": "msg-123e4567-e89b-12d3-a456-426614174004",
    "sessionId": "chat-123e4567-e89b-12d3-a456-426614174002",
    "content": "That's in a reasonable range. For $50,000 annually, we could look at different tiers. What's your expected user growth over the next 2 years?",
    "type": "ai",
    "timestamp": "2024-01-15T10:05:30.000Z",
    "metadata": {
      "suggestions": [
        "We expect to double our team size",
        "Growth will be gradual",
        "Can you offer scalable pricing?",
        "What about a pilot program?"
      ],
      "extractedData": {
        "strategy": "collaborative",
        "sentiment": "positive"
      }
    }
  },
  "sessionUpdate": {
    "id": "chat-neg-123e4567-e89b-12d3-a456-************",
    "currentRound": 2,
    "extractedTerms": {
      "price": 50000,
      "currency": "USD",
      "paymentFrequency": "annual"
    },
    "relationshipMetrics": {
      "trust": 52,
      "respect": 51,
      "pressure": 18
    },
    "score": 5.2,
    "updatedAt": "2024-01-15T10:05:30.000Z"
  }
}
```

### 3. Extract Data from Message

**Endpoint:** `POST /extract-data`

**Request Body:**
```json
{
  "message": "We need this software for 500 users, budget is around $75k, and we'd prefer quarterly payments",
  "context": {
    "scenarioType": "software_licensing",
    "previousTerms": {
      "userCount": 100,
      "price": 50000
    }
  }
}
```

**Response:** `200 OK`
```json
{
  "offer": {
    "price": 75000,
    "currency": "USD",
    "userCount": 500,
    "paymentTerms": "quarterly"
  },
  "strategy": "collaborative",
  "sentiment": "neutral",
  "confidence": 0.92,
  "extractedEntities": [
    {
      "type": "MONEY",
      "value": 75000,
      "text": "$75k",
      "confidence": 0.95
    },
    {
      "type": "QUANTITY",
      "value": 500,
      "text": "500 users",
      "confidence": 0.98
    },
    {
      "type": "FREQUENCY",
      "value": "quarterly",
      "text": "quarterly payments",
      "confidence": 0.85
    }
  ],
  "processingTimeMs": 245
}
```

### 4. Get Session Details

**Endpoint:** `GET /sessions/{sessionId}`

**Response:** `200 OK`
```json
{
  "id": "chat-neg-123e4567-e89b-12d3-a456-************",
  "negotiationSessionId": "neg-123e4567-e89b-12d3-a456-426614174001",
  "chatSessionId": "chat-123e4567-e89b-12d3-a456-426614174002",
  "scenarioId": "software_licensing",
  "status": "active",
  "currentRound": 3,
  "extractedTerms": {
    "price": 75000,
    "currency": "USD",
    "userCount": 500,
    "paymentTerms": "quarterly"
  },
  "relationshipMetrics": {
    "trust": 58,
    "respect": 55,
    "pressure": 25
  },
  "score": 6.1,
  "aiPersonality": {
    "aggressiveness": 0.6,
    "flexibility": 0.7,
    "communicationStyle": "diplomatic"
  },
  "createdAt": "2024-01-15T10:00:00.000Z",
  "updatedAt": "2024-01-15T10:15:00.000Z"
}
```

### 5. Get Contextual Suggestions

**Endpoint:** `POST /sessions/{sessionId}/suggestions`

**Request Body:**
```json
{
  "currentMessage": "What about the support terms?"
}
```

**Response:** `200 OK`
```json
{
  "suggestions": [
    "We need 24/7 premium support",
    "Basic email support would work for us",
    "What support levels do you offer?",
    "Can we get dedicated account management?",
    "What's included in your standard support?"
  ],
  "context": {
    "phase": "terms_negotiation",
    "focusArea": "support_services",
    "recommendedStrategy": "collaborative"
  }
}
```

### 6. Session Control

#### Pause Session
**Endpoint:** `PUT /sessions/{sessionId}/pause`

**Response:** `200 OK`
```json
{
  "id": "chat-neg-123e4567-e89b-12d3-a456-************",
  "status": "paused",
  "pausedAt": "2024-01-15T10:20:00.000Z"
}
```

#### Resume Session
**Endpoint:** `PUT /sessions/{sessionId}/resume`

**Response:** `200 OK`
```json
{
  "id": "chat-neg-123e4567-e89b-12d3-a456-************",
  "status": "active",
  "resumedAt": "2024-01-15T10:25:00.000Z"
}
```

#### Complete Session
**Endpoint:** `POST /sessions/{sessionId}/complete`

**Response:** `200 OK`
```json
{
  "session": {
    "id": "chat-neg-123e4567-e89b-12d3-a456-************",
    "status": "completed",
    "finalScore": 7.8,
    "totalRounds": 6,
    "completedAt": "2024-01-15T10:30:00.000Z"
  },
  "evaluation": {
    "overallPerformance": "Good",
    "strengths": [
      "Collaborative approach",
      "Clear communication",
      "Good use of data"
    ],
    "improvements": [
      "Could have been more assertive on key terms",
      "Missed opportunity to explore additional value"
    ],
    "scoreBreakdown": {
      "communication": 8.5,
      "strategy": 7.2,
      "outcome": 7.8,
      "relationship": 8.0
    },
    "finalTerms": {
      "price": 72000,
      "currency": "USD",
      "userCount": 500,
      "paymentTerms": "quarterly",
      "supportLevel": "premium",
      "contractDuration": "2 years"
    }
  }
}
```

## WebSocket Events

### Connection
```javascript
socket.emit('join_chat_negotiation', {
  sessionId: 'chat-neg-123e4567-e89b-12d3-a456-************',
  userId: 'user-123e4567-e89b-12d3-a456-426614174005'
});
```

### Events Received
```javascript
// Session updates
socket.on('session_updated', (data) => {
  console.log('Session updated:', data);
  // {
  //   sessionId: 'chat-neg-123e4567-e89b-12d3-a456-************',
  //   relationshipMetrics: { trust: 55, respect: 52, pressure: 22 },
  //   score: 5.8,
  //   currentRound: 4
  // }
});

// AI typing indicator
socket.on('ai_typing', (data) => {
  console.log('AI typing:', data.isTyping);
});

// Real-time suggestions
socket.on('suggestion_update', (data) => {
  console.log('New suggestions:', data.suggestions);
});
```

## Rate Limits

- **Session Creation**: 10 per hour per user
- **Chat Moves**: 60 per minute per session
- **Data Extraction**: 100 per minute per user
- **Suggestions**: 30 per minute per session

## Error Codes

| Code | Description |
|------|-------------|
| `INVALID_SCENARIO` | Scenario ID not found |
| `SESSION_NOT_FOUND` | Chat negotiation session not found |
| `SESSION_COMPLETED` | Cannot modify completed session |
| `RATE_LIMIT_EXCEEDED` | Too many requests |
| `EXTRACTION_FAILED` | Data extraction service unavailable |
| `AI_SERVICE_UNAVAILABLE` | AI response generation failed |
| `WEBSOCKET_CONNECTION_FAILED` | Real-time updates unavailable |

## Testing

### Postman Collection
```bash
# Import this collection for testing
curl -o chat-negotiation-api.postman_collection.json \
  https://api.docgic.com/docs/postman/chat-negotiation.json
```

### Example Test Flow
```bash
# 1. Create session
SESSION_ID=$(curl -X POST /api/chat-negotiation/sessions \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"scenarioId": "software_licensing"}' | jq -r '.id')

# 2. Send first move
curl -X POST /api/chat-negotiation/sessions/$SESSION_ID/moves \
  -H "Authorization: Bearer $TOKEN" \
  -d '{"content": "I am looking for software licensing for 100 users"}'

# 3. Get session status
curl -X GET /api/chat-negotiation/sessions/$SESSION_ID \
  -H "Authorization: Bearer $TOKEN"
```
