# Subscription Management Flow

This document provides a visual representation of the subscription management flow in the Legal Document Analyzer application. Use this as a reference when implementing the frontend subscription management components.

## User Registration & Subscription Flow

```mermaid
graph TD
    A[User Registration] --> B{Create New Org?}
    B -->|Yes| C[Create Organization]
    B -->|No| D[Join Existing Organization]

    C --> E[Free Tier by Default]
    D --> F[Inherit Org's Subscription]

    E --> G[User Dashboard]
    F --> G

    G --> H{View Available Plans}
    H --> I[Select Plan]
    I --> J[Checkout with <PERSON>e]
    J --> K{Payment Successful?}

    K -->|Yes| L[Update Subscription Tier]
    K -->|No| M[Show Error]

    L --> N[Access New Features]
    M --> H
```

## Feature Access Control Flow

```mermaid
graph TD
    A[User Requests Feature] --> B{Check Tenant Context}
    B -->|Valid Context| C{Check Subscription Status}
    B -->|Invalid Context| D[Return 401 Unauthorized]

    C -->|Active| E{Check Feature in Tier}
    C -->|Inactive/Canceled/Past Due| F[Free Tier Features Only]

    E -->|Feature Available| G[Allow Access]
    E -->|Feature Not Available| H[Return 403 Forbidden]

    F --> I{Feature in Free Tier?}
    I -->|Yes| G
    I -->|No| H

    G --> J[Execute Feature]
    H --> K[Show Upgrade Prompt]
```

## Usage Tracking Flow

```mermaid
graph TD
    A[User Action] -->|Document Upload| B[Check Document Limit]
    A -->|Analysis| C[Check Analysis Limit]

    B -->|Under Limit| D[Allow Upload]
    B -->|Over Limit| E[Block Upload]

    C -->|Under Limit| F[Allow Analysis]
    C -->|Over Limit| G[Block Analysis]

    D --> H[Process Document]
    F --> I[Run Analysis]

    H -->|Success| J[Increment Usage Counter]
    I -->|Success| J

    E --> K[Show Upgrade Prompt]
    G --> K

    J --> L[Update UI with New Usage Stats]
```

## Subscription State Transitions

```mermaid
stateDiagram-v2
    [*] --> Free: Registration

    Free --> Professional: Upgrade
    Free --> Enterprise: Upgrade

    Professional --> Enterprise: Upgrade
    Professional --> Free: Downgrade

    Enterprise --> Professional: Downgrade
    Enterprise --> Free: Downgrade

    Professional --> Canceled: Cancel Subscription
    Enterprise --> Canceled: Cancel Subscription

    Canceled --> Free: Auto-transition after period end

    Professional --> PastDue: Payment Failed
    Enterprise --> PastDue: Payment Failed

    PastDue --> Professional: Payment Resolved
    PastDue --> Enterprise: Payment Resolved
    PastDue --> Canceled: Not Resolved (30 days)
```

## Frontend Component Dependencies

```mermaid
graph TD
    A[App] --> B[AuthProvider]
    B --> C[OrganizationProvider]
    C --> D[SubscriptionProvider]

    D --> E[Dashboard]
    D --> F[DocumentUpload]
    D --> G[Analysis]
    D --> H[Chat]
    D --> I[SubscriptionManagement]

    F --> J[UsageTracker]
    G --> J
    H --> J

    I --> K[PlanSelector]
    I --> L[StripeCheckout]
    I --> M[BillingHistory]

    E --> N[FeatureGuard]
    F --> N
    G --> N
    H --> N
```

## Subscription Webhook Processing Flow

```mermaid
graph TD
    A[Stripe Webhook] --> B{Event Type}

    B -->|subscription.created| C[Create Subscription]
    B -->|subscription.updated| D[Update Subscription]
    B -->|subscription.deleted| E[Mark as Canceled]
    B -->|invoice.payment_succeeded| F[Confirm Payment]
    B -->|invoice.payment_failed| G[Mark as Past Due]

    C --> H[Update User Interface]
    D --> H
    E --> H
    F --> H
    G --> H[Show Payment Issue Warning]

    G --> I[Send Email Notification]
```

## Implementing This Flow

When implementing the subscription management system in the frontend:

1. **User Registration**: Enable organization creation with default Free tier
2. **Feature Access Control**: Use FeatureGuard component to protect premium features
3. **Subscription Selection**: Show available plans based on current tier
4. **Payment Processing**: Integrate Stripe Elements for secure payment collection
5. **Usage Tracking**: Display current usage vs limits with progress indicators
6. **Subscription Management**: Allow users to upgrade, downgrade, or cancel subscriptions

For detailed implementation guidance, refer to the [Frontend Integration Guide](./FRONTEND_INTEGRATION_GUIDE.md).

For API reference, see the [Frontend API Documentation](./FRONTEND_API_DOCUMENTATION.md).

{}