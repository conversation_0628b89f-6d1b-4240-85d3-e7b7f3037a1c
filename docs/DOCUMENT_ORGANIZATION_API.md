# Document Organization API Documentation

This document provides comprehensive documentation for the Document Organization feature of the Legal Document Analyzer API, which includes document tagging, folder structure, and saved searches functionality.

**Version:** 1.0.0  
**Last Updated:** April 19, 2025  
**Status:** Implemented

## Overview

The Document Organization feature enables users to effectively organize and retrieve their legal documents through:

1. **Document Tagging and Categorization**: Create and manage custom tags for documents
2. **Basic Folder Structure**: Organize documents in a hierarchical folder structure
3. **Saved Searches and Filters**: Save complex search criteria for future use

## Authentication

All endpoints require authentication using a JWT token and organization context:

```
Authorization: Bearer <jwt_token>
X-Organization-Id: <organization_id>
```

## Feature Availability

The Document Organization feature is available in the following subscription tiers:

- Free: Basic document organization
- Professional: Document organization
- Enterprise: Document organization and advanced document organization

## 1. Tag Management

Tags allow users to categorize and filter documents based on custom criteria.

### 1.1 Create a Tag

Create a new tag for document categorization.

**Endpoint:** `POST /api/document-organization/tags`

**Request Body:**

```json
{
  "name": "Contract",
  "color": "#FF5733",
  "description": "Legal contracts and agreements"
}
```

**Response:** (201 Created)

```json
{
  "id": "680412e749a4e9ea0eb0761a",
  "name": "Contract",
  "color": "#FF5733",
  "description": "Legal contracts and agreements",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:17:26.123Z"
}
```

### 1.2 Get All Tags

Retrieve all tags for the current organization.

**Endpoint:** `GET /api/document-organization/tags`

**Response:** (200 OK)

```json
[
  {
    "id": "680412e749a4e9ea0eb0761a",
    "name": "Contract",
    "color": "#FF5733",
    "description": "Legal contracts and agreements",
    "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
    "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
    "createdAt": "2025-04-19T21:17:26.123Z",
    "updatedAt": "2025-04-19T21:17:26.123Z"
  }
]
```

### 1.3 Get Tag by ID

Retrieve a specific tag by its ID.

**Endpoint:** `GET /api/document-organization/tags/:id`

**Response:** (200 OK)

```json
{
  "id": "680412e749a4e9ea0eb0761a",
  "name": "Contract",
  "color": "#FF5733",
  "description": "Legal contracts and agreements",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:17:26.123Z"
}
```

### 1.4 Update Tag

Update an existing tag.

**Endpoint:** `PATCH /api/document-organization/tags/:id`

**Request Body:**

```json
{
  "name": "Legal Contract",
  "color": "#3366FF",
  "description": "Updated description for legal contracts"
}
```

**Response:** (200 OK)

```json
{
  "id": "680412e749a4e9ea0eb0761a",
  "name": "Legal Contract",
  "color": "#3366FF",
  "description": "Updated description for legal contracts",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:20:15.456Z"
}
```

### 1.5 Delete Tag

Delete a tag by its ID.

**Endpoint:** `DELETE /api/document-organization/tags/:id`

**Response:** (204 No Content)

### 1.6 Add Document to Tag

Associate a document with a tag.

**Endpoint:** `POST /api/document-organization/tags/:id/documents`

**Request Body:**

```json
{
  "documentId": "5f8e829e-64d2-4f8e-829e-64d2af85270e"
}
```

**Response:** (200 OK)

```json
{
  "id": "680412e749a4e9ea0eb0761a",
  "name": "Contract",
  "color": "#FF5733",
  "description": "Legal contracts and agreements",
  "documents": ["5f8e829e-64d2-4f8e-829e-64d2af85270e"],
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:21:45.789Z"
}
```

### 1.7 Remove Document from Tag

Remove a document association from a tag.

**Endpoint:** `DELETE /api/document-organization/tags/:id/documents/:documentId`

**Response:** (204 No Content)

### 1.8 Get Tags for Document

Retrieve all tags associated with a specific document.

**Endpoint:** `GET /api/document-organization/tags/document/:documentId`

**Response:** (200 OK)

```json
[
  {
    "id": "680412e749a4e9ea0eb0761a",
    "name": "Contract",
    "color": "#FF5733",
    "description": "Legal contracts and agreements",
    "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
    "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
    "createdAt": "2025-04-19T21:17:26.123Z",
    "updatedAt": "2025-04-19T21:17:26.123Z"
  }
]
```

## 2. Folder Management

Folders provide a hierarchical structure for organizing documents.

### 2.1 Create a Folder

Create a new folder for document organization.

**Endpoint:** `POST /api/document-organization/folders`

**Request Body:**

```json
{
  "name": "Contracts",
  "description": "All legal contracts and agreements",
  "parentId": null
}
```

**Response:** (201 Created)

```json
{
  "id": "6804141a91e84ac0305e0658",
  "name": "Contracts",
  "description": "All legal contracts and agreements",
  "path": "/Contracts",
  "parentId": null,
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:17:26.123Z"
}
```

### 2.2 Get All Folders

Retrieve all folders for the current organization.

**Endpoint:** `GET /api/document-organization/folders`

**Response:** (200 OK)

```json
[
  {
    "id": "6804141a91e84ac0305e0658",
    "name": "Contracts",
    "description": "All legal contracts and agreements",
    "path": "/Contracts",
    "parentId": null,
    "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
    "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
    "createdAt": "2025-04-19T21:17:26.123Z",
    "updatedAt": "2025-04-19T21:17:26.123Z"
  },
  {
    "id": "6804141b91e84ac0305e0660",
    "name": "Employment",
    "description": "Employment contracts",
    "path": "/Contracts/Employment",
    "parentId": "6804141a91e84ac0305e0658",
    "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
    "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
    "createdAt": "2025-04-19T21:17:26.123Z",
    "updatedAt": "2025-04-19T21:17:26.123Z"
  }
]
```

### 2.3 Get Folder by ID

Retrieve a specific folder by its ID.

**Endpoint:** `GET /api/document-organization/folders/:id`

**Response:** (200 OK)

```json
{
  "id": "6804141a91e84ac0305e0658",
  "name": "Contracts",
  "description": "All legal contracts and agreements",
  "path": "/Contracts",
  "parentId": null,
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:17:26.123Z"
}
```

### 2.4 Update Folder

Update an existing folder.

**Endpoint:** `PATCH /api/document-organization/folders/:id`

**Request Body:**

```json
{
  "name": "Legal Contracts",
  "description": "Updated description for legal contracts folder"
}
```

**Response:** (200 OK)

```json
{
  "id": "6804141a91e84ac0305e0658",
  "name": "Legal Contracts",
  "description": "Updated description for legal contracts folder",
  "path": "/Legal Contracts",
  "parentId": null,
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:20:15.456Z"
}
```

### 2.5 Delete Folder

Delete a folder by its ID.

**Endpoint:** `DELETE /api/document-organization/folders/:id`

**Response:** (204 No Content)

### 2.6 Add Document to Folder

Associate a document with a folder.

**Endpoint:** `POST /api/document-organization/folders/:id/documents`

**Request Body:**

```json
{
  "documentId": "5f8e829e-64d2-4f8e-829e-64d2af85270e"
}
```

**Response:** (200 OK)

```json
{
  "id": "6804141a91e84ac0305e0658",
  "name": "Contracts",
  "description": "All legal contracts and agreements",
  "path": "/Contracts",
  "documents": ["5f8e829e-64d2-4f8e-829e-64d2af85270e"],
  "parentId": null,
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:21:45.789Z"
}
```

### 2.7 Remove Document from Folder

Remove a document association from a folder.

**Endpoint:** `DELETE /api/document-organization/folders/:id/documents/:documentId`

**Response:** (204 No Content)

### 2.8 Get Folders for Document

Retrieve all folders associated with a specific document.

**Endpoint:** `GET /api/document-organization/folders/document/:documentId`

**Response:** (200 OK)

```json
[
  {
    "id": "6804141a91e84ac0305e0658",
    "name": "Contracts",
    "description": "All legal contracts and agreements",
    "path": "/Contracts",
    "parentId": null,
    "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
    "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
    "createdAt": "2025-04-19T21:17:26.123Z",
    "updatedAt": "2025-04-19T21:17:26.123Z"
  }
]
```

## 3. Saved Search Management

Saved searches allow users to store and reuse complex search criteria.

### 3.1 Create a Saved Search

Create a new saved search for document retrieval.

**Endpoint:** `POST /api/document-organization/saved-searches`

**Request Body:**

```json
{
  "name": "Recent Contracts",
  "description": "Contracts created in the last 30 days",
  "criteria": {
    "text": "contract",
    "documentType": "contract",
    "dateFrom": "2025-03-19",
    "dateTo": "2025-04-19"
  },
  "notificationsEnabled": true,
  "notificationFrequency": "daily"
}
```

**Response:** (201 Created)

```json
{
  "id": "6804141b91e84ac0305e0665",
  "name": "Recent Contracts",
  "description": "Contracts created in the last 30 days",
  "criteria": {
    "text": "contract",
    "documentType": "contract",
    "dateFrom": "2025-03-19",
    "dateTo": "2025-04-19"
  },
  "notificationsEnabled": true,
  "notificationFrequency": "daily",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:17:26.123Z"
}
```

### 3.2 Get All Saved Searches

Retrieve all saved searches for the current organization.

**Endpoint:** `GET /api/document-organization/saved-searches`

**Response:** (200 OK)

```json
[
  {
    "id": "6804141b91e84ac0305e0665",
    "name": "Recent Contracts",
    "description": "Contracts created in the last 30 days",
    "criteria": {
      "text": "contract",
      "documentType": "contract",
      "dateFrom": "2025-03-19",
      "dateTo": "2025-04-19"
    },
    "notificationsEnabled": true,
    "notificationFrequency": "daily",
    "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
    "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
    "createdAt": "2025-04-19T21:17:26.123Z",
    "updatedAt": "2025-04-19T21:17:26.123Z"
  }
]
```

### 3.3 Get Saved Search by ID

Retrieve a specific saved search by its ID.

**Endpoint:** `GET /api/document-organization/saved-searches/:id`

**Response:** (200 OK)

```json
{
  "id": "6804141b91e84ac0305e0665",
  "name": "Recent Contracts",
  "description": "Contracts created in the last 30 days",
  "criteria": {
    "text": "contract",
    "documentType": "contract",
    "dateFrom": "2025-03-19",
    "dateTo": "2025-04-19"
  },
  "notificationsEnabled": true,
  "notificationFrequency": "daily",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:17:26.123Z"
}
```

### 3.4 Update Saved Search

Update an existing saved search.

**Endpoint:** `PATCH /api/document-organization/saved-searches/:id`

**Request Body:**

```json
{
  "name": "Updated Contract Search",
  "description": "Updated search criteria for contracts",
  "criteria": {
    "text": "legal contract",
    "documentType": "contract",
    "dateFrom": "2025-03-01",
    "dateTo": "2025-04-19"
  },
  "notificationsEnabled": false
}
```

**Response:** (200 OK)

```json
{
  "id": "6804141b91e84ac0305e0665",
  "name": "Updated Contract Search",
  "description": "Updated search criteria for contracts",
  "criteria": {
    "text": "legal contract",
    "documentType": "contract",
    "dateFrom": "2025-03-01",
    "dateTo": "2025-04-19"
  },
  "notificationsEnabled": false,
  "notificationFrequency": "daily",
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:20:15.456Z"
}
```

### 3.5 Delete Saved Search

Delete a saved search by its ID.

**Endpoint:** `DELETE /api/document-organization/saved-searches/:id`

**Response:** (204 No Content)

### 3.6 Execute Saved Search

Execute a saved search to retrieve matching documents.

**Endpoint:** `POST /api/document-organization/saved-searches/:id/execute`

**Response:** (200 OK)

```json
{
  "query": {
    "text": "contract",
    "documentType": "contract",
    "dateFrom": "2025-03-19",
    "dateTo": "2025-04-19"
  },
  "results": [
    {
      "id": "5f8e829e-64d2-4f8e-829e-64d2af85270e",
      "title": "Employment Contract",
      "documentType": "contract",
      "createdAt": "2025-04-15T14:30:45.123Z"
    },
    {
      "id": "6f8e829e-64d2-4f8e-829e-64d2af85270e",
      "title": "Service Agreement Contract",
      "documentType": "contract",
      "createdAt": "2025-04-10T09:15:22.456Z"
    },
    {
      "id": "7f8e829e-64d2-4f8e-829e-64d2af85270e",
      "title": "Non-Disclosure Contract",
      "documentType": "contract",
      "createdAt": "2025-03-25T11:45:33.789Z"
    }
  ]
}
```

### 3.7 Share Saved Search

Share a saved search with other users in the organization.

**Endpoint:** `POST /api/document-organization/saved-searches/:id/share`

**Request Body:**

```json
{
  "userIds": [
    "7f8e829e-64d2-4f8e-829e-64d2af85270e",
    "8f8e829e-64d2-4f8e-829e-64d2af85270e"
  ]
}
```

**Response:** (200 OK)

```json
{
  "id": "6804141b91e84ac0305e0665",
  "name": "Recent Contracts",
  "description": "Contracts created in the last 30 days",
  "criteria": {
    "text": "contract",
    "documentType": "contract",
    "dateFrom": "2025-03-19",
    "dateTo": "2025-04-19"
  },
  "notificationsEnabled": true,
  "notificationFrequency": "daily",
  "sharedWith": [
    "7f8e829e-64d2-4f8e-829e-64d2af85270e",
    "8f8e829e-64d2-4f8e-829e-64d2af85270e"
  ],
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:22:15.456Z"
}
```

### 3.8 Unshare Saved Search

Remove sharing permissions for a saved search.

**Endpoint:** `POST /api/document-organization/saved-searches/:id/unshare`

**Request Body:**

```json
{
  "userIds": ["7f8e829e-64d2-4f8e-829e-64d2af85270e"]
}
```

**Response:** (200 OK)

```json
{
  "id": "6804141b91e84ac0305e0665",
  "name": "Recent Contracts",
  "description": "Contracts created in the last 30 days",
  "criteria": {
    "text": "contract",
    "documentType": "contract",
    "dateFrom": "2025-03-19",
    "dateTo": "2025-04-19"
  },
  "notificationsEnabled": true,
  "notificationFrequency": "daily",
  "sharedWith": ["8f8e829e-64d2-4f8e-829e-64d2af85270e"],
  "organizationId": "2b7b40e6-307a-4c72-9b45-021f78a99a12",
  "createdBy": "d0331076-bfc2-4f8e-829e-64d2af85270e",
  "createdAt": "2025-04-19T21:17:26.123Z",
  "updatedAt": "2025-04-19T21:23:45.789Z"
}
```

## Implementation Details

The Document Organization feature is implemented using the following components:

### Schemas

1. **Tag Schema**: Defines the structure for document tags
2. **Folder Schema**: Defines the structure for document folders with hierarchical relationships
3. **Saved Search Schema**: Defines the structure for saved searches with criteria and notification settings

### Services

1. **Tags Service**: Manages tag creation, retrieval, updating, and deletion
2. **Folders Service**: Manages folder creation, retrieval, updating, and deletion with hierarchical structure
3. **Saved Searches Service**: Manages saved search creation, retrieval, updating, deletion, execution, and sharing

### Controllers

1. **Tags Controller**: Handles API requests related to tags
2. **Folders Controller**: Handles API requests related to folders
3. **Saved Searches Controller**: Handles API requests related to saved searches

### Subscription Integration

The Document Organization feature is integrated with the subscription system:

- Feature availability is controlled through the FeatureAvailabilityGuard
- Different subscription tiers have access to different levels of document organization functionality

## Error Handling

All endpoints return appropriate HTTP status codes and error messages:

- **400 Bad Request**: Invalid input data
- **401 Unauthorized**: Missing or invalid authentication
- **403 Forbidden**: Insufficient permissions or feature not available in subscription
- **404 Not Found**: Resource not found
- **500 Internal Server Error**: Server-side error

## Testing

The Document Organization feature has been thoroughly tested using a comprehensive test script that validates all endpoints and functionality.

## Future Enhancements

Planned enhancements for the Document Organization feature include:

1. **Advanced Tagging**: Tag analytics, automatic tag suggestions, and tag inheritance
2. **Enhanced Folder Structure**: Drag-and-drop folder management and folder permissions
3. **Advanced Search Builder**: Visual query builder and saved search templates
