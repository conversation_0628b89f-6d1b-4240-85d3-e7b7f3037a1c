# Chat Negotiation API Documentation

## Overview

The Chat Negotiation API provides gamified negotiation simulation features where users can practice negotiation skills against AI opponents with different personalities and difficulty levels. The system tracks performance, relationship metrics, and provides real-time feedback.

## Base URL
```
/api/chat-negotiation
```

## Authentication
All endpoints require Bearer token authentication:
```
Authorization: Bearer <jwt_token>
```

## API Endpoints

### 1. Create Negotiation Session

**Endpoint:** `POST /api/chat-negotiation/sessions`

**Description:** Creates a new negotiation session with specified AI personality and scenario.

**Request Body:**
```typescript
interface CreateSessionRequest {
  scenarioId: string;           // MongoDB ObjectId of the scenario
  aiPersonality: {
    characterId: string;        // AI character identifier
    aggressiveness: number;     // 0.0 - 1.0 (0 = passive, 1 = aggressive)
    flexibility: number;        // 0.0 - 1.0 (0 = rigid, 1 = flexible)
    riskTolerance: number;      // 0.0 - 1.0 (0 = risk-averse, 1 = risk-taking)
    communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
  };
  metadata?: {
    source?: string;           // Optional: tracking source
    version?: string;          // Optional: version info
    [key: string]: any;        // Additional metadata
  };
}
```

**Response:**
```typescript
interface CreateSessionResponse {
  id: string;                  // Session ID
  scenarioId: string;
  status: 'active' | 'completed' | 'paused';
  currentRound: number;        // Starting at 1
  extractedTerms: {
    price?: number;
    currency?: string;
    terms?: string[];
  };
  relationshipMetrics: {
    trust: number;             // 0-100
    respect: number;           // 0-100
    pressure: number;          // 0-100
  };
  score: number;               // Performance score
  aiPersonality: AiPersonality;
  negotiationSessionId: string;
  totalMessages: number;
  aiResponseTime: number;      // Average response time in ms
  createdAt: string;           // ISO date
  updatedAt: string;           // ISO date
  lastActivityAt: string;      // ISO date
}
```

**Example Request:**
```javascript
const response = await fetch('/api/chat-negotiation/sessions', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    scenarioId: "68405c9d35f27e4188e67b72",
    aiPersonality: {
      characterId: "default_character",
      aggressiveness: 0.4,
      flexibility: 0.7,
      riskTolerance: 0.6,
      communicationStyle: "ANALYTICAL"
    },
    metadata: {
      source: "web_app",
      version: "1.0"
    }
  })
});
```

### 2. Get Session Details

**Endpoint:** `GET /api/chat-negotiation/sessions/:id`

**Description:** Retrieves detailed information about a specific negotiation session.

**Parameters:**
- `id` (path): Session ID

**Response:** Same as CreateSessionResponse

**Example:**
```javascript
const session = await fetch(`/api/chat-negotiation/sessions/${sessionId}`, {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 3. Get User Sessions

**Endpoint:** `GET /api/chat-negotiation/sessions`

**Description:** Retrieves all negotiation sessions for the authenticated user.

**Query Parameters:**
- `status` (optional): Filter by session status
- `limit` (optional): Number of sessions to return (default: 20)
- `offset` (optional): Number of sessions to skip (default: 0)

**Response:**
```typescript
interface GetSessionsResponse {
  sessions: CreateSessionResponse[];
  total: number;
  hasMore: boolean;
}
```

**Example:**
```javascript
const sessions = await fetch('/api/chat-negotiation/sessions?limit=10&status=active', {
  headers: {
    'Authorization': `Bearer ${token}`
  }
});
```

### 4. Send Chat Move

**Endpoint:** `POST /api/chat-negotiation/sessions/:id/moves`

**Description:** Sends a user message/move in the negotiation and receives AI response.

**Parameters:**
- `id` (path): Session ID

**Request Body:**
```typescript
interface SendMoveRequest {
  content: string;             // User's message content
  extractedData?: {            // Optional: pre-extracted data
    offer?: {
      price?: number;
      currency?: string;
      terms?: string[];
    };
    strategy?: string;
    sentiment?: string;
    confidence?: number;
  };
  context?: {
    userConfidence?: number;   // 0.0 - 1.0
    timeSpent?: number;        // Time spent composing message (seconds)
    [key: string]: any;
  };
}
```

**Response:**
```typescript
interface SendMoveResponse {
  userMove: {
    content: string;
    extractedData: ExtractedData;
    timestamp: string;
    processingTime: number;    // ms
  };
  aiResponse: {
    content: string;
    suggestions?: string[];    // Strategic suggestions
    timestamp: string;
    processingTime: number;
  };
  sessionUpdate: {
    currentRound: number;
    extractedTerms: ExtractedTerms;
    relationshipMetrics: RelationshipMetrics;
    score: number;
    status: string;
  };
  creditsConsumed: number;     // Always 3 for chat moves
}
```

**Example:**
```javascript
const move = await fetch(`/api/chat-negotiation/sessions/${sessionId}/moves`, {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    content: "I'm thinking around $50,000 for the software license with Net 30 payment terms.",
    context: {
      userConfidence: 0.8,
      timeSpent: 45
    }
  })
});
```

### 5. Extract Data from Message

**Endpoint:** `POST /api/chat-negotiation/extract-data`

**Description:** Extracts negotiation data from a message without creating a session move. Useful for real-time feedback.

**Request Body:**
```typescript
interface ExtractDataRequest {
  message: string;             // Message to analyze
  context?: {
    scenarioType?: string;     // Optional: scenario context
    currentRound?: number;     // Optional: current negotiation round
    [key: string]: any;
  };
}
```

**Response:**
```typescript
interface ExtractDataResponse {
  offer: {
    price?: number;
    currency?: string;
    terms?: string[];
  };
  strategy: string;            // e.g., "collaborative", "competitive"
  sentiment: string;           // e.g., "positive", "neutral", "negative"
  confidence: number;          // 0.0 - 1.0
  extractedEntities: string[]; // List of detected entities
  processingTime: number;      // ms
}
```

**Example:**
```javascript
const extraction = await fetch('/api/chat-negotiation/extract-data', {
  method: 'POST',
  headers: {
    'Authorization': `Bearer ${token}`,
    'Content-Type': 'application/json'
  },
  body: JSON.stringify({
    message: "I propose $75,000 with Net 60 payment terms and a 10% discount for early payment",
    context: {
      scenarioType: "software_licensing",
      currentRound: 2
    }
  })
});
```

## Data Types

### AI Personality Configuration
```typescript
interface AiPersonality {
  characterId: string;         // Character identifier
  aggressiveness: number;      // 0.0 - 1.0
  flexibility: number;         // 0.0 - 1.0  
  riskTolerance: number;       // 0.0 - 1.0
  communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
}
```

### Relationship Metrics
```typescript
interface RelationshipMetrics {
  trust: number;               // 0-100, higher is better
  respect: number;             // 0-100, higher is better  
  pressure: number;            // 0-100, lower is better
}
```

### Extracted Terms
```typescript
interface ExtractedTerms {
  price?: number;              // Numerical price value
  currency?: string;           // Currency code (e.g., "USD")
  terms?: string[];            // Payment/contract terms
}
```

## Error Handling

All endpoints return standard HTTP status codes:

- `200` - Success
- `201` - Created (for POST requests)
- `400` - Bad Request (validation errors)
- `401` - Unauthorized (invalid/missing token)
- `403` - Forbidden (insufficient permissions)
- `404` - Not Found (session doesn't exist)
- `429` - Too Many Requests (rate limiting)
- `500` - Internal Server Error

**Error Response Format:**
```typescript
interface ErrorResponse {
  statusCode: number;
  message: string;
  error: string;
  details?: any;
  path: string;
  timestamp: string;
}
```

## Credit System

- **Chat moves consume 3 credits** per message sent
- **Data extraction is free** (no credits consumed)
- Users receive error if insufficient credits
- Credit consumption is tracked in the response

## Rate Limiting

- **60 requests per minute** per user
- Rate limit headers included in responses:
  - `X-RateLimit-Limit`
  - `X-RateLimit-Remaining` 
  - `X-RateLimit-Reset`

## WebSocket Events (Future Enhancement)

The system is designed to support real-time updates via WebSocket:

```typescript
// Future WebSocket events
interface NegotiationEvents {
  'session:created': CreateSessionResponse;
  'move:sent': SendMoveResponse;
  'ai:typing': { sessionId: string };
  'session:updated': Partial<CreateSessionResponse>;
}
```
