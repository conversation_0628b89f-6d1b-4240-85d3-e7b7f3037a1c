# Redo Analysis Feature

## 🎯 **Overview**

The Redo Analysis feature allows users to re-run document analysis with fresh AI processing. This is useful when:
- Users want updated analysis with the latest AI models
- Previous analysis had issues or incomplete results
- Users want to force a new analysis instead of using cached results

## 🏗️ **Implementation Details**

### **API Endpoint**
```
POST /api/documents/{documentId}/analyze
Content-Type: application/json

{
  "documentType"?: "CONTRACT",
  "forceNew"?: true
}
```

### **Components Modified**

#### 1. **Document Service** (`src/lib/services/document-service.ts`)
- Added `redoAnalysis()` method
- Calls the analyze endpoint with `forceNew: true` to bypass cache
- Handles error cases and provides proper error messages

#### 2. **Analysis Panel** (`src/components/analysis/analysis-panel.tsx`)
- Added `documentId` prop to pass document ID to artifact components
- Updated interface and component calls

#### 3. **Analysis Artifact Router** (`src/components/analysis/analysis-artifact.tsx`)
- Added `documentId` prop to interface
- Passes `documentId` to all artifact components

#### 4. **All Artifact Components**
- Updated interfaces to accept optional `documentId` prop
- Updated component signatures to destructure `documentId`

#### 5. **General Artifact** (`src/components/analysis/artifact/general-artifact.tsx`)
- **Full implementation** of redo analysis functionality
- Added state management for loading states
- Integrated with credit usage system
- Added "Redo Analysis" button in card header

#### 6. **Document Card** (`src/components/documents/document-card.tsx`)
- Passes `document.id` to `AnalysisPanel` component

## 🎨 **User Experience**

### **Button Location**
- Located in the analysis artifact card header
- Next to the document type badge
- Only visible when `documentId` is available

### **Button States**
- **Default**: Shows "Redo Analysis" with refresh icon
- **Loading**: Shows "Redoing..." with spinning loader
- **Disabled**: When analysis is in progress

### **User Flow**
1. User views document analysis results
2. Clicks "Redo Analysis" button in artifact header
3. System checks credit availability (consumes 1 credit for basic analysis)
4. If sufficient credits, triggers new analysis with `forceNew: true`
5. Shows success toast with refresh notification
6. Page automatically refreshes after 2 seconds to show new results

## 🔧 **Technical Features**

### **Credit Integration**
- Uses `useCreditUsage` hook to check and consume credits
- Consumes 1 credit for "basic_analysis" feature
- Handles insufficient credits gracefully
- Shows appropriate error messages

### **Error Handling**
- Validates document ID availability
- Handles API errors with user-friendly messages
- Provides fallback for insufficient credits
- Logs errors for debugging

### **Loading States**
- Button shows loading spinner during analysis
- Prevents multiple simultaneous requests
- Provides visual feedback to user

### **Auto-refresh**
- Automatically refreshes page after successful analysis
- 2-second delay to allow backend processing
- Ensures user sees updated results

## 🚀 **Benefits**

1. **Fresh Analysis**: Users can get updated analysis with latest AI models
2. **Error Recovery**: Allows retry when previous analysis failed
3. **Credit-Aware**: Properly integrates with credit system
4. **User-Friendly**: Clear visual feedback and error handling
5. **Consistent UX**: Follows existing design patterns

## 📱 **Usage Examples**

### **Successful Redo**
1. User clicks "Redo Analysis" button
2. System deducts 1 credit
3. Shows "Redoing..." state
4. API call succeeds
5. Shows success toast
6. Page refreshes with new analysis

### **Insufficient Credits**
1. User clicks "Redo Analysis" button
2. System checks credits
3. Shows insufficient credits modal
4. User can purchase more credits or upgrade plan
5. Analysis is not triggered

### **API Error**
1. User clicks "Redo Analysis" button
2. System deducts credit
3. API call fails
4. Shows error toast with details
5. Button returns to normal state

## 🔮 **Future Enhancements**

1. **Selective Re-analysis**: Allow users to redo specific sections
2. **Analysis Comparison**: Show differences between old and new analysis
3. **Batch Redo**: Allow redoing analysis for multiple documents
4. **Analysis History**: Track and show previous analysis versions
5. **Smart Retry**: Automatically retry failed analyses

## 🧪 **Testing**

The feature includes:
- ✅ Credit usage validation
- ✅ Error handling for missing document ID
- ✅ API error handling
- ✅ Loading state management
- ✅ Success flow with auto-refresh
- ✅ Integration with existing analysis system

## 📋 **Implementation Status**

- ✅ **General Artifact**: Full implementation with credit integration
- ⏳ **Other Artifacts**: Interface updated, ready for implementation
- ✅ **API Integration**: Complete with proper error handling
- ✅ **Credit System**: Fully integrated
- ✅ **UI/UX**: Consistent with design system

The redo analysis feature provides users with a reliable way to refresh their document analysis while maintaining proper credit usage and error handling.
