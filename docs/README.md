# Legal Document Analyzer API Documentation

## Overview

The Legal Document Analyzer API is a multi-tenant SaaS platform providing AI-powered legal document analysis, management, and chat functionality. The system uses Gemini for document analysis and chat interactions, MongoDB for persistent data storage, and supports complete tenant isolation for enterprise-grade security.

### Key Features

- **Multi-tenant Architecture**: Complete data isolation between organizations
- **AI-powered Document Analysis**: Extract insights from legal documents
- **Interactive Chat**: Have conversations about your legal documents
- **Subscription Management**: Tiered pricing with feature availability based on subscription level
- **Document Attachments**: Upload and reference legal documents in conversations

See [Streaming and Context Management](docs/streaming-and-context.md) for detailed documentation on streaming functionality and context management implementation.

See [MVP Launch Plan](docs/MVP_LAUNCH_PLAN.md) for details on upcoming features and development roadmap.

## Base URL

```bash
http://localhost:3000
```

## Authentication and Organization Management

The API uses JWT (JSON Web Token) based authentication with multi-tenant organization support. Each user belongs to an organization, and data is isolated between organizations.

### Registration

- **URL**: `/auth/register`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Request Body**:

```json
{
  "email": "string",
  "password": "string",
  "firstName": "string",
  "lastName": "string",
  "username": "string (optional)",
  "organizationName": "string (optional)",
  "createNewOrganization": "boolean (default: true)",
  "organizationId": "string (required if createNewOrganization is false)"
}
```

- **Response**:

```json
{
  "user": {
    "id": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "role": "string",
    "organizationId": "string"
  },
  "accessToken": "string"
}
```

### Registration Options

1. **Create New Organization**: By default, users will create a new organization named "[firstName]'s Workspace" during registration
2. **Custom Organization Name**: Specify a custom organization name with the `organizationName` field
3. **Join Existing Organization**: Set `createNewOrganization` to false and provide a valid `organizationId` to join an existing organization

### Login

- **URL**: `/auth/login`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Request Body**:

```json
{
  "email": "string",
  "password": "string"
}
```

- **Response**:

```json
{
  "accessToken": "string",
  "user": {
    "id": "string",
    "email": "string",
    "firstName": "string",
    "lastName": "string",
    "role": "string",
    "organizationId": "string"
  }
}
```

### Protected Routes

To access protected routes, include the JWT token in the Authorization header:

```
Authorization: Bearer <your-jwt-token>
```

The token contains the organization context, ensuring all operations are scoped to the user's organization.

### User Profile

- **URL**: `/auth/profile`
- **Method**: `GET`
- **Headers**:
  - `Authorization`: `Bearer <your-jwt-token>`
- **Response**:

```json
{
  "id": "string",
  "email": "string",
  "firstName": "string",
  "lastName": "string",
  "role": "string",
  "organizationId": "string"
}
```

## Subscription Management

The Legal Document Analyzer offers three subscription tiers:

1. **Free Tier**
   - Limited document uploads (5 documents)
   - Basic analysis features
   - 30-day chat history retention

2. **Professional Tier**
   - Increased document limit (50 documents)
   - Enhanced analysis features
   - 90-day chat history retention
   - Priority processing

3. **Enterprise Tier**
   - Unlimited documents
   - Full feature access
   - Unlimited chat history retention
   - Priority support
   - Advanced security features

### Subscription Endpoints

#### Get Current Subscription

- **URL**: `/subscription/current`
- **Method**: `GET`
- **Headers**:
  - `Authorization`: `Bearer <your-jwt-token>`
- **Response**:

```json
{
  "organizationId": "string",
  "tier": "free|professional|enterprise",
  "status": "active|trialing|past_due|canceled",
  "currentPeriodEnd": "string (ISO date)",
  "features": {
    "maxDocuments": number,
    "maxStorageGB": number,
    "chatHistoryDays": number,
    "prioritySupport": boolean
  }
}
```

#### Update Subscription

- **URL**: `/subscription/update`
- **Method**: `POST`
- **Headers**:
  - `Authorization`: `Bearer <your-jwt-token>`
- **Request Body**:

```json
{
  "tier": "free|professional|enterprise"
}
```

- **Response**: Redirects to Stripe Checkout or returns upgraded subscription details

## Organization Management

### Get Organization Profile

- **URL**: `/organizations/profile`
- **Method**: `GET`
- **Headers**:
  - `Authorization`: `Bearer <your-jwt-token>`
- **Response**:

```json
{
  "id": "string",
  "name": "string",
  "isActive": boolean,
  "createdAt": "string (ISO date)",
  "updatedAt": "string (ISO date)"
}
```

## File Attachments and References

### Upload Chat Attachment

- **URL**: `/chat/messages/upload`
- **Method**: `POST`
- **Content-Type**: `multipart/form-data`
- **Parameters**:
  - `file`: The attachment file (PDF, DOCX, TXT)
- **Response**:

```json
{
  "success": true,
  "attachment": {
    "filename": "string",
    "path": "string",
    "mimeType": "string",
    "size": number
  }
}
```

The messages endpoint now supports attachments and document references:

```json
{
  "sessionId": "string",
  "content": "string",
  "attachments": [
    {
      "filename": "string",
      "path": "string",
      "mimeType": "string",
      "description": "string"
    }
  ],
  "references": [
    {
      "documentId": "string",
      "sectionId": "string",
      "text": "string"
    }
  ],
  "relatedDocumentIds": ["string"]
}
```

Response messages now include attachments and references:

```json
{
  "id": "string",
  "role": "user|assistant",
  "content": "string",
  "timestamp": "string",
  "attachments": [
    {
      "filename": "string",
      "path": "string",
      "mimeType": "string",
      "description": "string",
      "uploadedAt": "string"
    }
  ],
  "references": [
    {
      "documentId": "string",
      "sectionId": "string",
      "text": "string"
    }
  ]
}
```

Supported attachment types:

- PDF (application/pdf)
- Word Document (application/msword, application/vnd.openxmlformats-officedocument.wordprocessingml.document)
- Text (text/plain)

Maximum file size: 5MB per attachment

## Endpoints

### Documents

#### Upload Document

- **URL**: `/documents/upload`
- **Method**: `POST`
- **Content-Type**: `multipart/form-data`
- **Parameters**:
  - `file`: The document file (text, docx, pdf)
  - `title`: Document title
  - `author`: Document author
- **Response**: Returns the uploaded document metadata

```json
{
  "id": "string",
  "filename": "string",
  "size": number,
  "uploadDate": "string",
  "metadata": {
    "title": "string",
    "author": "string",
    "sections": [
      {
        "title": "string",
        "purpose": "string",
        "startIndex": number,
        "endIndex": number
      }
    ],
    "clauses": [
      {
        "title": "string",
        "type": "string",
        "content": "string",
        "riskLevel": "string",
        "riskDescription": "string"
      }
    ]
  }
}
```

#### Get All Documents

- **URL**: `/documents`
- **Method**: `GET`
- **Response**: Returns an array of document metadata

#### Get Document by ID

- **URL**: `/documents/:id`
- **Method**: `GET`
- **Parameters**:
  - `id`: Document ID
- **Response**: Returns the document metadata

### Chat

#### Create Chat Session

- **URL**: `/chat/sessions`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Request Body**:

```json
{
  "documentId": "string",
  "title": "string"
}
```

- **Response**:

```json
{
  "id": "string",
  "documentId": "string",
  "title": "string",
  "createdAt": "string"
}
```

#### Get All Chat Sessions

- **URL**: `/chat/sessions`
- **Method**: `GET`
- **Response**: Returns an array of chat sessions

#### Get Chat Session by ID

- **URL**: `/chat/sessions/:id`
- **Method**: `GET`
- **Parameters**:
  - `id`: Session ID
- **Response**: Returns the chat session details

#### Get Session Messages

- **URL**: `/chat/sessions/:id/messages`
- **Method**: `GET`
- **Parameters**:
  - `id`: Session ID
- **Response**: Returns an array of chat messages

#### Get Paginated Session Messages

- **URL**: `/chat/sessions/:id/paginated-messages`
- **Method**: `GET`
- **Parameters**:
  - `id`: Session ID (path parameter)
  - `page`: Page number, starts from 1 (optional, default: 1)
  - `limit`: Number of messages per page, max 100 (optional, default: 10)
  - `sort`: Sort direction, either "asc" or "desc" (optional, default: "desc")
  - `sortBy`: Field to sort by, either "timestamp" or "role" (optional, default: "timestamp")
- **Response**: Returns paginated messages with metadata

```json
{
  "items": [
    {
      "id": "string",
      "role": "user",
      "content": "string",
      "timestamp": "string"
    }
  ],
  "meta": {
    "totalItems": 24,
    "itemsPerPage": 10,
    "currentPage": 1,
    "totalPages": 3,
    "hasPreviousPage": false,
    "hasNextPage": true
  }
}
```

#### Send Message

- **URL**: `/chat/messages`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Request Body**:

```json
{
  "sessionId": "string",
  "content": "string",
  "relatedDocumentIds": ["string"] // Optional array of related document IDs for enhanced context
}
```

- **Response**:

```json
{
  "id": "string",
  "role": "assistant",
  "content": "string",
  "timestamp": "string"
}
```

#### Stream Messages

- **URL**: `/chat/stream/messages`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Accept**: `text/event-stream`
- **Request Body**:

```json
{
  "sessionId": "string",
  "content": "string",
  "relatedDocumentIds": ["string"] // Optional array of related document IDs for enhanced context
}
```

- **Response**: Server-Sent Events stream with the following event format:

```json
data: {
  "id": "string",
  "role": "user|assistant",
  "content": "string",
  "timestamp": "string",
  "contextSources": [
    {
      "documentId": "string",
      "content": "string",
      "relevanceScore": number
    }
  ]
}
```

### Chat Analytics

#### Get Session Analytics

- **URL**: `/chat/analytics/sessions/:sessionId`
- **Method**: `GET`
- **Parameters**:
  - `sessionId`: Session ID (path parameter)
- **Response**:

```json
{
  "sessionId": "string",
  "analytics": {
    "messages": {
      "total": 10,
      "userMessages": 5,
      "aiMessages": 5
    },
    "feedback": {
      "helpful": 3,
      "total": 5,
      "rating": 4.5
    },
    "topTopics": ["contract terms", "liability", "payment terms"],
    "engagementMetrics": {
      "averageResponseTime": "2.5s",
      "sessionDuration": "15m"
    }
  }
}
```

#### Submit Session Feedback

- **URL**: `/chat/analytics/sessions/:sessionId/feedback`
- **Method**: `POST`
- **Parameters**:
  - `sessionId`: Session ID (path parameter)
- **Request Body**:

```json
{
  "isHelpful": true,
  "rating": 5,
  "comments": "Very helpful analysis"
}
```

- **Response**: Returns 201 Created

#### Update Session Topics

- **URL**: `/chat/analytics/sessions/:sessionId/topics`
- **Method**: `PUT`
- **Parameters**:
  - `sessionId`: Session ID (path parameter)
- **Response**:

```json
{
  "sessionId": "string",
  "topics": ["legal terms", "contract review", "liability clauses"],
  "updatedAt": "2025-03-16T19:30:00Z"
}
```

### Chat Migration

The following endpoints manage the migration of chat data from file-based storage to MongoDB:

#### Migrate All Chat Sessions

- **URL**: `/chat/migration/all`
- **Method**: `POST`
- **Response**:

```json
{
  "success": true,
  "timestamp": "2025-03-15T17:30:21.123Z",
  "statistics": {
    "total": 10,
    "migrated": 8,
    "failed": 2,
    "successRate": "80%"
  },
  "successSessions": [{ "id": "session1" }, { "id": "session2" }],
  "failedSessions": [{ "id": "session3", "reason": "Document not found" }],
  "details": {
    "fileSessions": [{ "id": "session1" }, { "id": "session2" }],
    "dbSessions": [
      {
        "id": "session1",
        "migratedAt": "2025-03-15T17:30:21.123Z"
      }
    ]
  }
}
```

#### Migrate Single Chat Session

- **URL**: `/chat/migration/:sessionId`
- **Method**: `POST`
- **Parameters**:
  - `sessionId`: The ID of the chat session to migrate
  - `force` (optional): Boolean flag to force re-migration of existing sessions (default: false)
- **Response**:

```json
{
  "success": true,
  "timestamp": "2025-03-15T17:30:21.123Z",
  "sessionId": "session1",
  "title": "Sample Chat",
  "messageCount": 15,
  "documents": ["documentId1", "documentId2"],
  "metadata": {
    "processingTimeMs": 235,
    "migrationStatus": "completed",
    "originalFilePath": "/uploads/chat-sessions/session1.json",
    "migratedAt": "2025-03-15T17:30:21.123Z",
    "originalDocumentId": "originalDocId123",
    "originalDocuments": ["docId1", "docId2"]
  }
}
```

#### Verify Chat Session Migration

- **URL**: `/chat/migration/verify/:sessionId`
- **Method**: `GET`
- **Parameters**:
  - `sessionId`: The ID of the chat session to verify
- **Response**:

```json
{
  "isValid": true,
  "fileExists": true,
  "dbExists": true,
  "messageCountMatch": true,
  "fileMessageCount": 15,
  "dbMessageCount": 15,
  "documentsMatched": true
}
```

#### Get Migration Status

- **URL**: `/chat/migration/status`
- **Method**: `GET`
- **Query Parameters**:
  - `detailed`: Optional boolean to include detailed session information
- **Response**:

```json
{
  "timestamp": "2025-03-15T17:30:21.123Z",
  "status": "pending",
  "statistics": {
    "totalFileSessions": 10,
    "totalDbSessions": 8,
    "migrationProgress": 80
  },
  "details": {
    "availableFileSessions": ["session1", "session2", "session3"]
  }
}
```

#### List Chat Files

- **URL**: `/chat/migration/list-files`
- **Method**: `GET`
- **Response**:

```json
{
  "success": true,
  "count": 10,
  "files": ["session1.json", "session2.json", "session3.json"]
}
```

### Database Management

#### MongoDB Health Check

- **URL**: `/database/health`
- **Method**: `GET`
- **Response**:

```json
{
  "status": "connected",
  "database": "legal_documents",
  "connectionInfo": {
    "host": "mongodb://localhost:27017",
    "isConnected": true,
    "responseTime": "4ms"
  },
  "collections": ["documents", "chatsessions", "analysisresults"]
}
```

## Error Handling

The API returns standard HTTP status codes:

- `200`: Success
- `400`: Bad Request
- `404`: Not Found
- `500`: Internal Server Error

Error responses include a message describing the error:

```json
{
  "statusCode": 404,
  "message": "Document not found",
  "error": "Not Found"
}
```

## Installation

### Prerequisites

- Node.js v18+
- MongoDB v6+

### Setup

1. Clone the repository
2. Install dependencies: `npm install`
3. Set up environment variables in `.env` file:

```env
PORT=3000
GEMINI_API_KEY=your_gemini_api_key
UPLOAD_DIR=./uploads
MONGODB_URI=mongodb://localhost:27017/legal_documents
```

1. Start the server: `npm run start:dev`

## MongoDB Integration Notes

### Schema Overview

The application uses three main MongoDB collections:

- **LegalDocument**: Stores document metadata, content references, and analysis results
- **ChatSession**: Stores chat conversation history with references to legal documents
- **AnalysisResult**: Stores AI-generated analysis of legal documents

### Migration Process

To migrate existing chat data from file-based storage to MongoDB:

1. Ensure MongoDB is properly configured and running
2. Use the `/chat/migration/all` endpoint to migrate all chat sessions
3. Check migration status with the `/chat/migration/status` endpoint
4. Verify individual migrations with the `/chat/migration/verify/:sessionId` endpoint

### Known Issues

- If endpoints return 404 errors, ensure that the controller is properly registered in the chat module
- Migration integrity verification might fail if document references cannot be resolved in MongoDB
- Duplicate index errors may occur during schema initialization; these have been addressed in the current implementation
- Migration controller endpoints use the `/chat/migration/` prefix, which needs to be correctly registered in the chat module
- When testing migration endpoints, use a database connection monitor to verify proper connectivity

## Additional Features

### Document Processing Enhancements

- **Parallel Processing**: Large documents are processed in parallel for faster analysis
- **Result Caching**: Document processing results are cached to improve performance on repeated queries
- **Versioning System**: Documents maintain version history for tracking changes
- **Metadata Extraction**: Automatic extraction of document metadata including titles, authors, and sections

### AI Integration Capabilities

- **Rate Limiting**: Implemented token bucket strategy for Gemini API requests to prevent service disruptions
- **Retry Mechanisms**: Automatic retry of failed API calls with exponential backoff
- **Prompt Templates**: Configurable prompt templates optimized for different document types
- **Context Management**: Enhanced context handling for multi-document analysis and references
- **Post-Processing**: Result refinement to improve analysis quality and relevance

### Performance Optimizations

- **Connection Pooling**: Database connections are pooled for improved performance
- **Efficient Indexing**: Strategic index design for optimized query performance
- **Caching Strategy**: Multi-level caching for frequently accessed data

## API Rate Limiting

The Legal Document Analyzer API implements a token bucket rate limiting strategy to control access to the Gemini AI API and prevent exceeding usage limits:

- **Default Rate Limit**: 60 requests per minute
- **Configuration**: Customizable via `gemini.config.ts`
- **Behavior**: When limits are reached, requests will be queued or rejected based on configuration
- **Monitoring**: Rate limit status can be queried via the API

Rate limiting implementation features:

- Dynamic token replenishment based on configured window
- Configurable maximum request threshold
- Detailed logging of rate limiting events
- Optional error throwing when limits are exceeded

## Context Management System

The Legal Document Analyzer API now features an advanced context management system for improved AI responses:

- **Smart Context Window Building**: Analyzes document content and selects the most relevant sections based on user queries
- **Token Optimization**: Maintains context windows within configurable token limits (default: 8000 tokens)
- **Relevance Scoring**: Uses a sophisticated scoring system to rank document sections by:
  - Keyword matching
  - Section importance (headers, key clauses)
  - Numeric content relevance
- **Real-time Context Updates**: Continuously adjusts context based on conversation flow
- **Efficient Token Usage**: Typically uses 20-30% of available token limit for optimal response generation
- **Streaming Support**: Seamlessly integrates with SSE streaming for real-time AI responses

The system automatically handles context preservation and relevance scoring, requiring no additional configuration from API users.

## Architecture Improvements

The Legal Document Analyzer API has undergone several architectural improvements:

- **Circular Dependency Resolution**: Resolved circular dependencies between modules using NestJS forwardRef() technique
- **Improved Dependency Injection**: Enhanced service instantiation with proper module organization
- **Module Organization**: Clear separation of concerns with dedicated modules for each major feature
- **Service Interfaces**: Well-defined service interfaces for maintainability and testability

## Document Comparison

The Document Comparison API allows you to compare two legal documents and analyze their similarities and differences.

### Compare Documents

- **URL**: `/documents/comparison`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Request Body**:

```json
{
  "documentA": "string",
  "documentB": "string",
  "type": "both|similarities|differences"
}
```

Parameters:

- `documentA`: Content of the first document (required)
- `documentB`: Content of the second document (required)
- `type`: The type of comparison to perform (required)

  - `both`: Returns both similarities and differences
  - `similarities`: Returns only similarities
  - `differences`: Returns only differences

- **Response** (Status 201):

```json
{
  "status": "success",
  "data": {
    "result": {
      "comparison": [
        {
          "topic": "string",
          "documents": ["documentA", "documentB"],
          "similarities": ["string"],
          "differences": ["string"]
        }
      ],
      "metadata": {
        "comparisonType": "string",
        "timestamp": "string",
        "documentStats": {
          "documentA": {
            "length": number
          },
          "documentB": {
            "length": number
          }
        }
      }
    }
  },
  "metadata": {
    "timestamp": "string",
    "documentStats": {
      "documentA": {
        "length": number
      },
      "documentB": {
        "length": number
      }
    }
  }
}
```

- **Error Response** (Status 400):

```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "BAD_REQUEST",
  "path": "/documents/comparison",
  "timestamp": "string"
}
```

### Validation Rules

- Both `documentA` and `documentB` are required
- `documentA` and `documentB` must be non-empty strings
- `type` must be one of: "both", "similarities", or "differences"

### Notes

- The document comparison uses Gemini AI to analyze the content and identify relevant similarities and differences.
- The API is rate-limited according to the Gemini API usage policies.
- Depending on document size and complexity, processing times may vary.
- Maximum document length is subject to Gemini API token limits.

### Example Usage

```javascript
const response = await fetch('http://localhost:3000/documents/comparison', {
  method: 'POST',
  headers: {
    'Content-Type': 'application/json',
  },
  body: JSON.stringify({
    documentA: 'Content: This is document A with some content for comparison.',
    documentB: 'Content: This is document B with different content.',
    type: 'both',
  }),
});

const result = await response.json();
console.log(result);
```

### Thread Management

The API now supports thread management for organizing chat conversations:

#### Create Thread

- **URL**: `/chat/threads`
- **Method**: `POST`
- **Content-Type**: `application/json`
- **Request Body**:

```json
{
  "sessionId": "string",
  "messageId": "string",
  "title": "string",
  "parentThreadId": "string" // Optional, for nested threads
}
```

- **Response**:

```json
{
  "id": "string",
  "sessionId": "string",
  "title": "string",
  "createdAt": "string",
  "updatedAt": "string",
  "firstMessageId": "string",
  "parentThreadId": "string" // Optional
}
```

#### Get Thread

- **URL**: `/chat/threads/:threadId`
- **Method**: `GET`
- **Parameters**:
  - `threadId`: Thread ID
- **Response**: Returns thread details

#### Get Thread Messages

- **URL**: `/chat/threads/:threadId/messages`
- **Method**: `GET`
- **Parameters**:
  - `threadId`: Thread ID
- **Response**: Returns messages associated with the thread

#### Get Thread Summary

- **URL**: `/chat/threads/:threadId/summary`
- **Method**: `GET`
- **Parameters**:
  - `threadId`: Thread ID
- **Response**:

```json
{
  "id": "string",
  "title": "string",
  "messageCount": number,
  "lastMessageTimestamp": "string",
  "previewContent": "string"
}
```

#### Update Thread Title

- **URL**: `/chat/threads/:threadId/title`
- **Method**: `PUT`
- **Content-Type**: `application/json`
- **Request Body**:

```json
{
  "title": "string"
}
```

- **Response**: Returns updated thread details

#### Get Session Threads

- **URL**: `/chat/threads/session/:sessionId`
- **Method**: `GET`
- **Parameters**:
  - `sessionId`: Session ID
- **Response**: Returns array of threads for the specified session

Note: Thread management provides a way to organize conversations within chat sessions. Threads can be nested (using parentThreadId) to create hierarchical discussion structures. Each thread maintains its own message history and can be summarized for quick reference.

### Context Management Implementation

The context management system now features:

- **Smart Context Window Building**: Analyzes document content and selects the most relevant sections based on user queries
- **Token Optimization**: Maintains context windows within configurable token limits (default: 8000 tokens)
- **Relevance Scoring**: Uses a sophisticated scoring system to rank document sections by:
  - Keyword matching
  - Section importance (headers, key clauses)
  - Numeric content relevance
- **Real-time Context Updates**: Continuously adjusts context based on conversation flow
- **Efficient Token Usage**: Typically uses 20-30% of available token limit for optimal response generation
- **Streaming Support**: Seamlessly integrates with SSE streaming for real-time AI responses

The system automatically handles context preservation and relevance scoring, requiring no additional configuration from API users.

## Architecture Improvements

The Legal Document Analyzer API has undergone several architectural improvements:

- **Circular Dependency Resolution**: Resolved circular dependencies between modules using NestJS forwardRef() technique
- **Improved Dependency Injection**: Enhanced service instantiation with proper module organization
- **Module Organization**: Clear separation of concerns with dedicated modules for each major feature
- **Service Interfaces**: Well-defined service interfaces for maintainability and testability
