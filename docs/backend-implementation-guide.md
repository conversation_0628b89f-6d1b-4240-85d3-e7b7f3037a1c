# **🔧 Backend Implementation Guide: Gamified Negotiation Simulator**

## **📋 Table of Contents**
1. [System Architecture Overview](#system-architecture-overview)
2. [Database Schema Design](#database-schema-design)
3. [Core Services Implementation](#core-services-implementation)
4. [API Endpoints Implementation](#api-endpoints-implementation)
5. [Real-Time Systems](#real-time-systems)
6. [Gamification Engine](#gamification-engine)
7. [Performance & Scalability](#performance--scalability)
8. [Security Considerations](#security-considerations)
9. [Testing Strategy](#testing-strategy)
10. [Deployment Guide](#deployment-guide)

---

## **System Architecture Overview**

### **High-Level Architecture**
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   API Gateway   │    │   Auth Service  │
│   (React/Next)  │◄──►│   (Express)     │◄──►│   (JWT/OAuth)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
                                │
                                ▼
┌─────────────────────────────────────────────────────────────────┐
│                    Core Backend Services                        │
├─────────────────┬─────────────────┬─────────────────┬───────────┤
│ Negotiation     │ Gamification    │ Character       │ Analytics │
│ Engine          │ Service         │ AI Service      │ Service   │
└─────────────────┴─────────────────┴─────────────────┴───────────┘
                                │
                                ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   MongoDB       │    │   Redis Cache   │    │   WebSocket     │
│   (Primary DB)  │    │   (Sessions)    │    │   (Real-time)   │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### **Service Breakd  own**

#### **1. Negotiation Engine Service**
- **Purpose**: Core negotiation logic, session management, move processing
- **Responsibilities**: Session state, AI decision making, scoring algorithms
- **Technology**: Node.js/Express, TypeScript

#### **2. Gamification Service**
- **Purpose**: XP, achievements, levels, leaderboards
- **Responsibilities**: Progress tracking, reward calculation, unlock logic
- **Technology**: Node.js/Express, TypeScript

#### **3. Character AI Service**
- **Purpose**: AI personality simulation, dynamic behavior
- **Responsibilities**: Character responses, relationship tracking, adaptive difficulty
- **Technology**: Node.js/Express, AI/ML integration

#### **4. Analytics Service**
- **Purpose**: Performance tracking, insights, recommendations
- **Responsibilities**: Data aggregation, trend analysis, personalization
- **Technology**: Node.js/Express, data processing

#### **5. Real-Time Service**
- **Purpose**: Live updates, pressure events, notifications
- **Responsibilities**: WebSocket management, event broadcasting
- **Technology**: Socket.io, Redis pub/sub

---

## **Database Schema Design**

### **MongoDB Collections**

#### **1. Characters Collection**
```javascript
// characters
{
  _id: ObjectId,
  id: "sarah_chen",
  name: "Sarah Chen",
  title: "Senior Sales Director",
  company: "TechCorp Solutions",
  avatar: "/avatars/sarah-chen.jpg",
  difficulty: 3, // 1-5 scale
  specialties: ["cost_reduction", "data_driven"],
  personality: {
    aggressiveness: 0.6,
    flexibility: 0.7,
    riskTolerance: 0.5,
    communicationStyle: "ANALYTICAL",
    decisionSpeed: "MODERATE",
    concessionPattern: "GRADUAL"
  },
  backstory: "15 years in enterprise sales...",
  unlockRequirements: {
    level: 2,
    achievements: ["win_win_master"],
    sessionsCompleted: 5
  },
  behaviorPatterns: {
    openingStrategy: "analytical_assessment",
    concessionTriggers: ["time_pressure", "competitor_mention"],
    relationshipFactors: ["respect_data", "values_honesty"],
    weaknesses: ["deadline_pressure", "relationship_preservation"]
  },
  isActive: true,
  createdAt: Date,
  updatedAt: Date
}
```

#### **2. User Gamification Collection**
```javascript
// user_gamification
{
  _id: ObjectId,
  userId: "user-123",
  organizationId: "org-456",
  level: {
    current: 3,
    title: "Senior Negotiator",
    currentXP: 2750,
    totalXP: 2750,
    xpToNext: 750
  },
  achievements: [
    {
      achievementId: "speed_demon",
      unlockedAt: Date,
      sessionId: "session-123",
      metadata: { rounds: 3, timeSpent: 180 }
    }
  ],
  statistics: {
    totalSessions: 25,
    completedSessions: 23,
    averageScore: 8.2,
    winRate: 0.87,
    currentStreak: 5,
    bestStreak: 8,
    totalXPEarned: 2750,
    achievementsCount: 12
  },
  preferences: {
    difficulty: "INTERMEDIATE",
    favoriteCharacters: ["sarah_chen", "marcus_rodriguez"],
    preferredStrategies: ["COLLABORATIVE", "DATA_DRIVEN"]
  },
  unlockedContent: {
    characters: ["sarah_chen", "jennifer_liu"],
    scenarios: ["advanced_software", "partnership_deals"],
    features: ["team_challenges", "campaign_mode"]
  },
  createdAt: Date,
  updatedAt: Date
}
```

#### **3. Achievements Collection**
```javascript
// achievements
{
  _id: ObjectId,
  id: "speed_demon",
  title: "Speed Demon",
  description: "Close a deal in 3 rounds or less",
  badge: "⚡",
  rarity: "rare", // common, rare, epic, legendary
  category: "efficiency",
  requirements: {
    type: "session_completion",
    conditions: {
      maxRounds: 3,
      dealClosed: true,
      minScore: 7.0
    }
  },
  rewards: {
    xp: 150,
    credits: 50,
    unlocks: ["time_pressure_scenarios"]
  },
  statistics: {
    totalUnlocked: 1247,
    unlockRate: 0.23
  },
  isActive: true,
  createdAt: Date
}
```

#### **4. Character Relationships Collection**
```javascript
// character_relationships
{
  _id: ObjectId,
  userId: "user-123",
  characterId: "sarah_chen",
  relationship: {
    respectLevel: 75, // 0-100
    trustLevel: 60,   // 0-100
    relationshipStatus: "professional_respect",
    totalInteractions: 8,
    lastInteraction: Date
  },
  interactionHistory: [
    {
      sessionId: "session-123",
      outcome: "win_win",
      userApproach: "collaborative",
      aiSatisfaction: 0.85,
      userSatisfaction: 0.90,
      respectChange: 15,
      trustChange: 10,
      date: Date
    }
  ],
  bonuses: {
    betterStartingTerms: true,
    increasedFlexibility: 0.1,
    insiderInformation: false,
    relationshipDiscount: 0.05
  },
  notes: "Responds well to data-driven arguments",
  createdAt: Date,
  updatedAt: Date
}
```

#### **5. Leaderboards Collection**
```javascript
// leaderboards
{
  _id: ObjectId,
  type: "weekly", // weekly, monthly, all_time, organization, industry
  scope: "global", // global, organization, industry, team
  scopeId: null, // organizationId, industryId, teamId
  period: {
    start: Date,
    end: Date,
    year: 2024,
    week: 3
  },
  rankings: [
    {
      rank: 1,
      userId: "user-123",
      score: 8.9,
      totalDeals: 15,
      winRate: 0.87,
      averageRounds: 4.2,
      totalXP: 1250,
      metadata: {
        name: "John Smith",
        title: "Senior Associate",
        organization: "BigLaw Firm",
        avatar: "/avatars/john-smith.jpg"
      }
    }
  ],
  totalParticipants: 1247,
  lastUpdated: Date,
  createdAt: Date
}
```

#### **6. Challenges Collection**
```javascript
// challenges
{
  _id: ObjectId,
  id: "cost_reduction_masters",
  title: "Cost Reduction Masters",
  description: "Teams compete for highest average cost savings",
  type: "team", // individual, team, organization
  status: "active", // upcoming, active, completed, cancelled
  timeframe: {
    startDate: Date,
    endDate: Date,
    duration: 7 // days
  },
  rules: {
    metric: "average_cost_savings",
    minSessions: 3,
    eligibleScenarios: ["software_licensing", "vendor_agreements"],
    teamSize: { min: 3, max: 10 }
  },
  rewards: {
    first: { xp: 500, credits: 200, badge: "🏆" },
    top10: { xp: 200, credits: 100, badge: "🥉" },
    participation: { xp: 50, credits: 25 }
  },
  participants: [
    {
      userId: "user-123",
      teamId: "team-456",
      joinedAt: Date,
      currentScore: 0.23,
      sessionsCompleted: 4,
      rank: 3
    }
  ],
  leaderboard: [
    {
      teamId: "team-456",
      teamName: "Legal Eagles",
      score: 0.28,
      participants: 5,
      sessionsCompleted: 18
    }
  ],
  createdAt: Date,
  updatedAt: Date
}
```

#### **7. Pressure Events Collection**
```javascript
// pressure_events
{
  _id: ObjectId,
  id: "ceo_urgent_message",
  type: "stakeholder", // stakeholder, market, time, competitor
  title: "CEO Urgent Message",
  category: "executive_pressure",
  templates: [
    {
      message: "Board meeting moved up. Need deal closed in {timeRemaining}.",
      intensity: "high",
      variables: ["timeRemaining"]
    }
  ],
  impact: {
    userStress: 0.2,
    timeMultiplier: 1.5,
    leverageChange: -0.1,
    aiAggressiveness: 0.1
  },
  triggerConditions: {
    minRound: 2,
    maxRound: 8,
    sessionStatus: "active",
    timeRemaining: { max: 120 }, // minutes
    userScore: { min: 6.0 },
    dealGap: { min: 0.15 } // 15% gap between positions
  },
  cooldown: 300, // seconds before can trigger again
  probability: 0.3, // 30% chance when conditions met
  isActive: true,
  createdAt: Date
}
```

#### **8. Campaign Progress Collection**
```javascript
// campaign_progress
{
  _id: ObjectId,
  userId: "user-123",
  campaignId: "startup_journey",
  status: "in_progress", // not_started, in_progress, completed
  currentChapter: 3,
  chaptersCompleted: [1, 2],
  progress: {
    totalChapters: 12,
    completedChapters: 2,
    percentComplete: 16.67,
    estimatedTimeRemaining: 150 // minutes
  },
  chapterHistory: [
    {
      chapterId: 1,
      title: "The First Office Lease",
      completedAt: Date,
      score: 8.5,
      timeSpent: 25,
      attempts: 1
    }
  ],
  rewards: {
    xpEarned: 300,
    achievementsUnlocked: ["campaign_starter"],
    charactersUnlocked: ["startup_ceo"]
  },
  startedAt: Date,
  lastPlayedAt: Date,
  completedAt: null
}
```

### **Redis Cache Structure**

#### **1. Session State Cache**
```javascript
// Key: session:{sessionId}
{
  sessionId: "session-123",
  userId: "user-123",
  characterId: "sarah_chen",
  status: "active",
  currentRound: 3,
  gameState: {
    userStress: 0.65,
    aiMood: "pressured",
    activePressureEvents: ["ceo_message", "competitor_alert"],
    timeRemaining: 45,
    currentScore: 8.2
  },
  aiContext: {
    personality: { /* current AI state */ },
    relationshipBonus: 0.1,
    pressureLevel: 0.7,
    concessionThreshold: 0.6
  },
  ttl: 3600 // 1 hour
}
```

#### **2. Real-Time Updates Cache**
```javascript
// Key: updates:{sessionId}
{
  pendingEvents: [
    {
      type: "pressure_event",
      eventId: "market_news",
      scheduledFor: Date,
      data: { /* event data */ }
    }
  ],
  liveScoring: {
    lastMove: 8.5,
    trend: "improving",
    breakdown: {
      strategy: 0.9,
      communication: 0.8,
      timing: 0.85
    }
  }
}
```

#### **3. Leaderboard Cache**
```javascript
// Key: leaderboard:{type}:{scope}:{period}
{
  rankings: [ /* top 100 rankings */ ],
  lastUpdated: Date,
  nextUpdate: Date,
  totalParticipants: 1247,
  ttl: 300 // 5 minutes
}
```

---

## **Core Services Implementation**

### **1. Negotiation Engine Service**

#### **Session Management**
```typescript
// services/negotiation-engine.service.ts
export class NegotiationEngineService {

  async createSession(data: CreateSessionRequest): Promise<NegotiationSession> {
    // 1. Validate scenario and character
    const scenario = await this.scenarioService.getById(data.scenarioId);
    const character = await this.characterService.getById(data.characterId);

    // 2. Check user unlock requirements
    await this.validateUserAccess(data.userId, character);

    // 3. Initialize AI personality with relationship bonuses
    const aiPersonality = await this.initializeAIPersonality(
      character,
      data.userId,
      data.aiPersonality
    );

    // 4. Create session record
    const session = await this.sessionRepository.create({
      ...data,
      aiPersonality,
      status: 'ACTIVE',
      startedAt: new Date(),
      rounds: [],
      metrics: this.initializeMetrics()
    });

    // 5. Cache session state
    await this.cacheService.setSession(session.id, {
      ...session,
      gameState: this.initializeGameState(),
      aiContext: this.initializeAIContext(aiPersonality)
    });

    // 6. Schedule initial pressure events
    await this.pressureEventService.scheduleInitialEvents(session.id);

    return session;
  }

  async makeMove(sessionId: string, moveData: MakeMoveRequest): Promise<NegotiationSession> {
    // 1. Get cached session state
    const sessionState = await this.cacheService.getSession(sessionId);
    if (!sessionState || sessionState.status !== 'ACTIVE') {
      throw new Error('Session not active');
    }

    // 2. Validate move
    await this.validateMove(sessionState, moveData);

    // 3. Score user move in real-time
    const moveScore = await this.scoringService.scoreMove(
      moveData,
      sessionState.gameState,
      sessionState.aiContext
    );

    // 4. Update session state
    const updatedState = await this.updateSessionState(sessionState, moveData, moveScore);

    // 5. Generate AI response
    const aiResponse = await this.aiService.generateResponse(
      updatedState,
      moveData,
      sessionState.aiContext
    );

    // 6. Check for pressure events
    await this.pressureEventService.checkTriggers(sessionId, updatedState);

    // 7. Update relationship scores
    await this.relationshipService.updateRelationship(
      sessionState.userId,
      sessionState.characterId,
      moveData,
      aiResponse
    );

    // 8. Check achievements
    await this.gamificationService.checkAchievements(
      sessionState.userId,
      sessionId,
      updatedState
    );

    // 9. Save and return updated session
    const session = await this.sessionRepository.update(sessionId, updatedState);
    await this.cacheService.setSession(sessionId, updatedState);

    return session;
  }

  private async initializeAIPersonality(
    character: Character,
    userId: string,
    overrides?: Partial<AIPersonalityProfile>
  ): Promise<AIPersonalityProfile> {
    // Get relationship bonuses
    const relationship = await this.relationshipService.getRelationship(userId, character.id);

    // Apply relationship modifiers
    const personality = {
      ...character.personality,
      ...overrides
    };

    if (relationship?.bonuses.increasedFlexibility) {
      personality.flexibility += relationship.bonuses.increasedFlexibility;
    }

    return personality;
  }

  private initializeGameState(): GameState {
    return {
      userStress: 0.3,
      aiMood: 'neutral',
      activePressureEvents: [],
      timeRemaining: null,
      currentScore: 5.0,
      dealMomentum: 'neutral'
    };
  }
}
```

#### **AI Response Generation**
```typescript
// services/ai-response.service.ts
export class AIResponseService {

  async generateResponse(
    sessionState: SessionState,
    userMove: MakeMoveRequest,
    aiContext: AIContext
  ): Promise<AIResponse> {
    // 1. Analyze user move
    const moveAnalysis = await this.analyzeMoveStrategy(userMove, sessionState);

    // 2. Update AI emotional state
    const updatedAIContext = await this.updateAIEmotionalState(
      aiContext,
      moveAnalysis,
      sessionState.gameState
    );

    // 3. Determine AI strategy
    const aiStrategy = await this.selectAIStrategy(
      updatedAIContext,
      sessionState,
      moveAnalysis
    );

    // 4. Generate counter-offer
    const counterOffer = await this.generateCounterOffer(
      userMove.offer,
      aiStrategy,
      updatedAIContext
    );

    // 5. Generate response message
    const message = await this.generateResponseMessage(
      aiStrategy,
      moveAnalysis,
      updatedAIContext
    );

    return {
      offer: counterOffer,
      message,
      strategy: aiStrategy.type,
      reasoning: aiStrategy.reasoning,
      emotionalState: updatedAIContext.emotionalState,
      confidenceScore: aiStrategy.confidence
    };
  }

  private async selectAIStrategy(
    aiContext: AIContext,
    sessionState: SessionState,
    moveAnalysis: MoveAnalysis
  ): Promise<AIStrategy> {
    const { personality, pressureLevel, relationshipBonus } = aiContext;
    const { currentRound, gameState } = sessionState;

    // Base strategy selection on personality
    let strategyWeights = {
      aggressive: personality.aggressiveness,
      collaborative: personality.flexibility,
      analytical: personality.communicationStyle === 'ANALYTICAL' ? 0.8 : 0.3,
      deadline_pressure: pressureLevel
    };

    // Adjust for relationship
    if (relationshipBonus > 0.1) {
      strategyWeights.collaborative += 0.2;
      strategyWeights.aggressive -= 0.1;
    }

    // Adjust for round number (more aggressive as rounds progress)
    if (currentRound > 5) {
      strategyWeights.aggressive += 0.2;
      strategyWeights.deadline_pressure += 0.3;
    }

    // Adjust for user move analysis
    if (moveAnalysis.isAggressive) {
      strategyWeights.aggressive += 0.3;
    }

    if (moveAnalysis.isCollaborative) {
      strategyWeights.collaborative += 0.2;
    }

    // Select highest weighted strategy
    const selectedStrategy = Object.entries(strategyWeights)
      .sort(([,a], [,b]) => b - a)[0][0];

    return this.buildStrategy(selectedStrategy, strategyWeights[selectedStrategy]);
  }
}
```

### **2. Gamification Service**

#### **Achievement System**
```typescript
// services/gamification.service.ts
export class GamificationService {

  async checkAchievements(
    userId: string,
    sessionId: string,
    sessionState: SessionState
  ): Promise<Achievement[]> {
    const unlockedAchievements: Achievement[] = [];

    // Get all active achievements
    const achievements = await this.achievementRepository.findActive();

    // Get user's current achievements
    const userAchievements = await this.getUserAchievements(userId);
    const unlockedIds = userAchievements.map(a => a.achievementId);

    for (const achievement of achievements) {
      // Skip if already unlocked
      if (unlockedIds.includes(achievement.id)) continue;

      // Check if requirements are met
      if (await this.checkAchievementRequirements(achievement, userId, sessionState)) {
        // Award achievement
        await this.awardAchievement(userId, achievement.id, sessionId);
        unlockedAchievements.push(achievement);

        // Award XP and other rewards
        await this.awardAchievementRewards(userId, achievement.rewards);
      }
    }

    return unlockedAchievements;
  }

  async awardExperience(
    userId: string,
    amount: number,
    source: string,
    metadata?: any
  ): Promise<LevelUpdate> {
    // Get current user gamification data
    const userGamification = await this.userGamificationRepository.findByUserId(userId);

    // Calculate new XP
    const newTotalXP = userGamification.level.totalXP + amount;
    const currentLevel = userGamification.level.current;

    // Check for level up
    const levelRequirements = await this.getLevelRequirements();
    const newLevel = this.calculateLevel(newTotalXP, levelRequirements);

    const levelUpdate: LevelUpdate = {
      previousLevel: currentLevel,
      newLevel: newLevel.level,
      xpGained: amount,
      totalXP: newTotalXP,
      leveledUp: newLevel.level > currentLevel,
      newUnlocks: []
    };

    // If leveled up, check for new unlocks
    if (levelUpdate.leveledUp) {
      levelUpdate.newUnlocks = await this.getNewUnlocks(currentLevel, newLevel.level);

      // Update unlocked content
      await this.updateUnlockedContent(userId, levelUpdate.newUnlocks);
    }

    // Update user gamification record
    await this.userGamificationRepository.update(userId, {
      level: {
        current: newLevel.level,
        title: newLevel.title,
        currentXP: newLevel.currentXP,
        totalXP: newTotalXP,
        xpToNext: newLevel.xpToNext
      },
      statistics: {
        ...userGamification.statistics,
        totalXPEarned: newTotalXP
      }
    });

    return levelUpdate;
  }

  private async checkAchievementRequirements(
    achievement: Achievement,
    userId: string,
    sessionState: SessionState
  ): Promise<boolean> {
    const { requirements } = achievement;

    switch (requirements.type) {
      case 'session_completion':
        return this.checkSessionCompletionRequirements(requirements.conditions, sessionState);

      case 'performance_streak':
        return this.checkPerformanceStreakRequirements(requirements.conditions, userId);

      case 'total_sessions':
        return this.checkTotalSessionsRequirements(requirements.conditions, userId);

      case 'relationship_milestone':
        return this.checkRelationshipRequirements(requirements.conditions, userId);

      default:
        return false;
    }
  }
}
```

### **3. Pressure Events Service**

#### **Dynamic Event System**
```typescript
// services/pressure-events.service.ts
export class PressureEventsService {

  async checkTriggers(sessionId: string, sessionState: SessionState): Promise<void> {
    // Get all active pressure events
    const events = await this.pressureEventRepository.findActive();

    for (const event of events) {
      // Check if event can be triggered
      if (await this.canTriggerEvent(event, sessionState)) {
        // Check probability
        if (Math.random() < event.probability) {
          await this.triggerEvent(sessionId, event, sessionState);
        }
      }
    }
  }

  async triggerEvent(
    sessionId: string,
    event: PressureEvent,
    sessionState: SessionState
  ): Promise<void> {
    // Check cooldown
    const lastTriggered = await this.getLastTriggered(sessionId, event.id);
    if (lastTriggered && (Date.now() - lastTriggered.getTime()) < event.cooldown * 1000) {
      return;
    }

    // Generate event message
    const message = await this.generateEventMessage(event, sessionState);

    // Apply event impact
    const updatedGameState = await this.applyEventImpact(sessionState.gameState, event.impact);

    // Update session state
    await this.cacheService.updateSessionGameState(sessionId, updatedGameState);

    // Broadcast to frontend
    await this.websocketService.emitToSession(sessionId, 'pressure_event', {
      eventId: event.id,
      type: event.type,
      title: event.title,
      message,
      impact: event.impact,
      timestamp: new Date()
    });

    // Record event trigger
    await this.recordEventTrigger(sessionId, event.id, message);
  }

  private async canTriggerEvent(
    event: PressureEvent,
    sessionState: SessionState
  ): Promise<boolean> {
    const { triggerConditions } = event;
    const { currentRound, gameState, status } = sessionState;

    // Check basic conditions
    if (status !== 'active') return false;
    if (currentRound < triggerConditions.minRound) return false;
    if (currentRound > triggerConditions.maxRound) return false;

    // Check time remaining
    if (triggerConditions.timeRemaining?.max &&
        gameState.timeRemaining > triggerConditions.timeRemaining.max) {
      return false;
    }

    // Check user score
    if (triggerConditions.userScore?.min &&
        gameState.currentScore < triggerConditions.userScore.min) {
      return false;
    }

    // Check deal gap
    if (triggerConditions.dealGap?.min) {
      const dealGap = await this.calculateDealGap(sessionState);
      if (dealGap < triggerConditions.dealGap.min) return false;
    }

    return true;
  }
}
```

---

## **API Endpoints Implementation**

### **Character Management Endpoints**

#### **GET /api/negotiation-simulator/characters**
```typescript
// controllers/characters.controller.ts
export class CharactersController {

  @Get('/')
  async getCharacters(
    @Query('userId') userId: string,
    @Query('difficulty') difficulty?: string,
    @Query('unlocked') unlocked?: boolean
  ): Promise<CharacterResponse[]> {
    // Get all characters
    let characters = await this.characterService.findAll();

    // Filter by difficulty if specified
    if (difficulty) {
      characters = characters.filter(c => c.difficulty <= parseInt(difficulty));
    }

    // Get user's unlocked characters
    const userGamification = await this.gamificationService.getUserGamification(userId);
    const unlockedCharacterIds = userGamification.unlockedContent.characters;

    // Filter by unlocked status if specified
    if (unlocked !== undefined) {
      characters = characters.filter(c =>
        unlocked ? unlockedCharacterIds.includes(c.id) : !unlockedCharacterIds.includes(c.id)
      );
    }

    // Add unlock status to response
    const response = characters.map(character => ({
      ...character,
      unlocked: unlockedCharacterIds.includes(character.id),
      unlockRequirements: character.unlockRequirements
    }));

    return response;
  }

  @Get('/:characterId')
  async getCharacter(
    @Param('characterId') characterId: string,
    @Query('userId') userId: string
  ): Promise<CharacterDetailResponse> {
    const character = await this.characterService.findById(characterId);
    if (!character) {
      throw new NotFoundException('Character not found');
    }

    // Get user's relationship with character
    const relationship = await this.relationshipService.getRelationship(userId, characterId);

    // Get user's unlock status
    const userGamification = await this.gamificationService.getUserGamification(userId);
    const unlocked = userGamification.unlockedContent.characters.includes(characterId);

    return {
      ...character,
      unlocked,
      relationship,
      interactionHistory: relationship?.interactionHistory || []
    };
  }
}
```

#### **GET /api/users/{userId}/achievements**
```typescript
// controllers/achievements.controller.ts
export class AchievementsController {

  @Get('/users/:userId/achievements')
  async getUserAchievements(
    @Param('userId') userId: string,
    @Query('category') category?: string,
    @Query('rarity') rarity?: string
  ): Promise<UserAchievementsResponse> {
    // Get user's unlocked achievements
    const userAchievements = await this.gamificationService.getUserAchievements(userId);

    // Get all available achievements
    const allAchievements = await this.achievementService.findAll();

    // Filter if specified
    let filteredAchievements = allAchievements;
    if (category) {
      filteredAchievements = filteredAchievements.filter(a => a.category === category);
    }
    if (rarity) {
      filteredAchievements = filteredAchievements.filter(a => a.rarity === rarity);
    }

    // Combine with user progress
    const achievements = filteredAchievements.map(achievement => {
      const userAchievement = userAchievements.find(ua => ua.achievementId === achievement.id);

      return {
        ...achievement,
        unlocked: !!userAchievement,
        unlockedAt: userAchievement?.unlockedAt,
        progress: this.calculateAchievementProgress(achievement, userId)
      };
    });

    return {
      achievements,
      totalUnlocked: userAchievements.length,
      totalAvailable: allAchievements.length,
      categories: this.getAchievementCategories(allAchievements),
      rarityBreakdown: this.getRarityBreakdown(userAchievements, allAchievements)
    };
  }

  @Post('/users/:userId/experience')
  async awardExperience(
    @Param('userId') userId: string,
    @Body() data: AwardExperienceRequest
  ): Promise<LevelUpdateResponse> {
    const levelUpdate = await this.gamificationService.awardExperience(
      userId,
      data.amount,
      data.source,
      data.metadata
    );

    // Check for new achievements after XP award
    if (levelUpdate.leveledUp) {
      await this.gamificationService.checkLevelBasedAchievements(userId);
    }

    return levelUpdate;
  }
}
```

#### **GET /api/leaderboards/negotiation**
```typescript
// controllers/leaderboards.controller.ts
export class LeaderboardsController {

  @Get('/negotiation')
  async getNegotiationLeaderboard(
    @Query('timeframe') timeframe: string = 'weekly',
    @Query('scope') scope: string = 'global',
    @Query('scopeId') scopeId?: string,
    @Query('limit') limit: number = 50,
    @Query('userId') userId?: string
  ): Promise<LeaderboardResponse> {
    // Get cached leaderboard
    const cacheKey = `leaderboard:${timeframe}:${scope}:${scopeId || 'global'}`;
    let leaderboard = await this.cacheService.get(cacheKey);

    if (!leaderboard) {
      // Generate leaderboard
      leaderboard = await this.leaderboardService.generateLeaderboard(
        timeframe,
        scope,
        scopeId
      );

      // Cache for 5 minutes
      await this.cacheService.set(cacheKey, leaderboard, 300);
    }

    // Get user's rank if userId provided
    let userRank = null;
    if (userId) {
      userRank = leaderboard.rankings.findIndex(r => r.userId === userId) + 1;
      if (userRank === 0) userRank = null;
    }

    // Limit results
    const limitedRankings = leaderboard.rankings.slice(0, limit);

    return {
      rankings: limitedRankings,
      userRank,
      totalParticipants: leaderboard.totalParticipants,
      timeframe,
      scope,
      lastUpdated: leaderboard.lastUpdated,
      nextUpdate: this.calculateNextUpdate(timeframe)
    };
  }

  @Get('/organization/:organizationId')
  async getOrganizationLeaderboard(
    @Param('organizationId') organizationId: string,
    @Query('timeframe') timeframe: string = 'weekly'
  ): Promise<LeaderboardResponse> {
    return this.getNegotiationLeaderboard(timeframe, 'organization', organizationId);
  }
}
```

---

## **Real-Time Systems**

### **WebSocket Implementation**

#### **Socket.io Setup**
```typescript
// websocket/negotiation.gateway.ts
@WebSocketGateway({
  namespace: '/negotiation',
  cors: { origin: '*' }
})
export class NegotiationGateway implements OnGatewayConnection, OnGatewayDisconnect {

  constructor(
    private readonly pressureEventService: PressureEventsService,
    private readonly scoringService: ScoringService
  ) {}

  @SubscribeMessage('join_session')
  async handleJoinSession(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: string, userId: string }
  ): Promise<void> {
    // Validate session access
    const hasAccess = await this.validateSessionAccess(data.userId, data.sessionId);
    if (!hasAccess) {
      client.emit('error', { message: 'Access denied' });
      return;
    }

    // Join session room
    await client.join(`session:${data.sessionId}`);

    // Send current session state
    const sessionState = await this.cacheService.getSession(data.sessionId);
    client.emit('session_state', sessionState);

    // Send any pending events
    const pendingEvents = await this.pressureEventService.getPendingEvents(data.sessionId);
    if (pendingEvents.length > 0) {
      client.emit('pending_events', pendingEvents);
    }
  }

  @SubscribeMessage('request_live_score')
  async handleLiveScoreRequest(
    @ConnectedSocket() client: Socket,
    @MessageBody() data: { sessionId: string, moveData: any }
  ): Promise<void> {
    // Calculate live score for proposed move
    const sessionState = await this.cacheService.getSession(data.sessionId);
    const liveScore = await this.scoringService.calculateLiveScore(
      data.moveData,
      sessionState
    );

    client.emit('live_score_update', {
      score: liveScore.total,
      breakdown: liveScore.breakdown,
      suggestions: liveScore.suggestions
    });
  }

  // Broadcast pressure event to session
  async broadcastPressureEvent(sessionId: string, event: PressureEventData): Promise<void> {
    this.server.to(`session:${sessionId}`).emit('pressure_event', event);
  }

  // Broadcast achievement unlock
  async broadcastAchievement(userId: string, achievement: Achievement): Promise<void> {
    this.server.to(`user:${userId}`).emit('achievement_unlocked', achievement);
  }
}
```

### **Background Jobs**

#### **Leaderboard Updates**
```typescript
// jobs/leaderboard-update.job.ts
@Injectable()
export class LeaderboardUpdateJob {

  @Cron('0 */5 * * * *') // Every 5 minutes
  async updateLeaderboards(): Promise<void> {
    const timeframes = ['weekly', 'monthly', 'all_time'];
    const scopes = ['global', 'organization', 'industry'];

    for (const timeframe of timeframes) {
      for (const scope of scopes) {
        if (scope === 'global') {
          await this.updateGlobalLeaderboard(timeframe);
        } else {
          await this.updateScopedLeaderboards(timeframe, scope);
        }
      }
    }
  }

  private async updateGlobalLeaderboard(timeframe: string): Promise<void> {
    const period = this.calculatePeriod(timeframe);

    // Aggregate user performance data
    const userStats = await this.sessionRepository.aggregate([
      {
        $match: {
          completedAt: { $gte: period.start, $lte: period.end },
          status: 'COMPLETED'
        }
      },
      {
        $group: {
          _id: '$userId',
          totalSessions: { $sum: 1 },
          averageScore: { $avg: '$metrics.finalScore' },
          totalDeals: { $sum: { $cond: ['$metrics.dealClosed', 1, 0] } },
          averageRounds: { $avg: '$metrics.totalRounds' },
          totalXP: { $sum: '$xpEarned' }
        }
      },
      {
        $sort: { averageScore: -1, totalDeals: -1 }
      },
      {
        $limit: 1000
      }
    ]);

    // Enrich with user data
    const rankings = await this.enrichWithUserData(userStats);

    // Save leaderboard
    await this.leaderboardRepository.upsert({
      type: timeframe,
      scope: 'global',
      period,
      rankings,
      totalParticipants: userStats.length,
      lastUpdated: new Date()
    });

    // Update cache
    const cacheKey = `leaderboard:${timeframe}:global:global`;
    await this.cacheService.set(cacheKey, { rankings, totalParticipants: userStats.length }, 300);
  }
}
```

---

## **Performance & Scalability**

### **Caching Strategy**

#### **Redis Implementation**
```typescript
// services/cache.service.ts
export class CacheService {

  constructor(private readonly redis: Redis) {}

  // Session state caching
  async setSession(sessionId: string, sessionData: any, ttl: number = 3600): Promise<void> {
    const key = `session:${sessionId}`;
    await this.redis.setex(key, ttl, JSON.stringify(sessionData));
  }

  async getSession(sessionId: string): Promise<any> {
    const key = `session:${sessionId}`;
    const data = await this.redis.get(key);
    return data ? JSON.parse(data) : null;
  }

  // Leaderboard caching
  async setLeaderboard(
    type: string,
    scope: string,
    scopeId: string,
    data: any,
    ttl: number = 300
  ): Promise<void> {
    const key = `leaderboard:${type}:${scope}:${scopeId}`;
    await this.redis.setex(key, ttl, JSON.stringify(data));
  }

  // User gamification caching
  async setUserGamification(userId: string, data: any, ttl: number = 1800): Promise<void> {
    const key = `user_gamification:${userId}`;
    await this.redis.setex(key, ttl, JSON.stringify(data));
  }

  // Pressure event cooldowns
  async setPressureEventCooldown(
    sessionId: string,
    eventId: string,
    cooldownSeconds: number
  ): Promise<void> {
    const key = `pressure_cooldown:${sessionId}:${eventId}`;
    await this.redis.setex(key, cooldownSeconds, '1');
  }

  async isPressureEventOnCooldown(sessionId: string, eventId: string): Promise<boolean> {
    const key = `pressure_cooldown:${sessionId}:${eventId}`;
    const exists = await this.redis.exists(key);
    return exists === 1;
  }
}
```

### **Database Optimization**

#### **MongoDB Indexes**
```javascript
// Database indexes for optimal performance

// Characters collection
db.characters.createIndex({ "difficulty": 1, "isActive": 1 });
db.characters.createIndex({ "specialties": 1 });
db.characters.createIndex({ "unlockRequirements.level": 1 });

// User gamification collection
db.user_gamification.createIndex({ "userId": 1 }, { unique: true });
db.user_gamification.createIndex({ "organizationId": 1, "level.current": -1 });
db.user_gamification.createIndex({ "statistics.averageScore": -1 });

// Achievements collection
db.achievements.createIndex({ "isActive": 1, "category": 1 });
db.achievements.createIndex({ "rarity": 1 });

// Character relationships collection
db.character_relationships.createIndex({ "userId": 1, "characterId": 1 }, { unique: true });
db.character_relationships.createIndex({ "userId": 1 });

// Leaderboards collection
db.leaderboards.createIndex({ "type": 1, "scope": 1, "scopeId": 1, "period.start": 1 });
db.leaderboards.createIndex({ "lastUpdated": -1 });

// Challenges collection
db.challenges.createIndex({ "status": 1, "timeframe.endDate": 1 });
db.challenges.createIndex({ "type": 1, "status": 1 });

// Pressure events collection
db.pressure_events.createIndex({ "isActive": 1, "type": 1 });
db.pressure_events.createIndex({ "triggerConditions.minRound": 1, "triggerConditions.maxRound": 1 });

// Campaign progress collection
db.campaign_progress.createIndex({ "userId": 1, "campaignId": 1 }, { unique: true });
db.campaign_progress.createIndex({ "status": 1, "lastPlayedAt": -1 });

// Negotiation sessions collection (existing, add gamification indexes)
db.negotiation_sessions.createIndex({ "userId": 1, "completedAt": -1 });
db.negotiation_sessions.createIndex({ "status": 1, "startedAt": -1 });
db.negotiation_sessions.createIndex({ "metrics.finalScore": -1, "completedAt": -1 });
```

---

## **Security Considerations**

### **Authentication & Authorization**
```typescript
// middleware/auth.middleware.ts
export class AuthMiddleware {

  @UseGuards(JwtAuthGuard)
  async validateSessionAccess(
    userId: string,
    sessionId: string
  ): Promise<boolean> {
    const session = await this.sessionRepository.findById(sessionId);

    // Check if user owns the session
    if (session.userId !== userId) {
      return false;
    }

    // Check if user has access to the character
    const character = await this.characterService.findById(session.characterId);
    const userGamification = await this.gamificationService.getUserGamification(userId);

    if (!userGamification.unlockedContent.characters.includes(character.id)) {
      return false;
    }

    return true;
  }

  @UseGuards(JwtAuthGuard)
  async validateLeaderboardAccess(
    userId: string,
    scope: string,
    scopeId?: string
  ): Promise<boolean> {
    if (scope === 'global') return true;

    if (scope === 'organization') {
      const user = await this.userService.findById(userId);
      return user.organizationId === scopeId;
    }

    if (scope === 'team') {
      const teamMember = await this.teamService.isMember(userId, scopeId);
      return teamMember;
    }

    return false;
  }
}
```

### **Rate Limiting**
```typescript
// middleware/rate-limit.middleware.ts
export class RateLimitMiddleware {

  @UseGuards(ThrottlerGuard)
  @Throttle(10, 60) // 10 requests per minute
  async makeMove(): Promise<void> {
    // Rate limit negotiation moves to prevent spam
  }

  @UseGuards(ThrottlerGuard)
  @Throttle(100, 3600) // 100 requests per hour
  async getLeaderboard(): Promise<void> {
    // Rate limit leaderboard requests
  }

  @UseGuards(ThrottlerGuard)
  @Throttle(5, 60) // 5 requests per minute
  async triggerPressureEvent(): Promise<void> {
    // Rate limit pressure event triggers
  }
}
```

---

## **Testing Strategy**

### **Unit Tests**
```typescript
// tests/services/gamification.service.spec.ts
describe('GamificationService', () => {
  let service: GamificationService;
  let mockRepository: jest.Mocked<UserGamificationRepository>;

  beforeEach(async () => {
    const module = await Test.createTestingModule({
      providers: [
        GamificationService,
        {
          provide: UserGamificationRepository,
          useValue: createMockRepository()
        }
      ]
    }).compile();

    service = module.get<GamificationService>(GamificationService);
    mockRepository = module.get(UserGamificationRepository);
  });

  describe('awardExperience', () => {
    it('should award XP and level up user', async () => {
      // Arrange
      const userId = 'user-123';
      const currentGamification = {
        level: { current: 2, totalXP: 1400 },
        // ... other properties
      };
      mockRepository.findByUserId.mockResolvedValue(currentGamification);

      // Act
      const result = await service.awardExperience(userId, 200, 'session_completion');

      // Assert
      expect(result.leveledUp).toBe(true);
      expect(result.newLevel).toBe(3);
      expect(result.totalXP).toBe(1600);
    });

    it('should not level up if XP insufficient', async () => {
      // Arrange
      const userId = 'user-123';
      const currentGamification = {
        level: { current: 2, totalXP: 1400 },
        // ... other properties
      };
      mockRepository.findByUserId.mockResolvedValue(currentGamification);

      // Act
      const result = await service.awardExperience(userId, 50, 'session_completion');

      // Assert
      expect(result.leveledUp).toBe(false);
      expect(result.newLevel).toBe(2);
      expect(result.totalXP).toBe(1450);
    });
  });

  describe('checkAchievements', () => {
    it('should unlock speed demon achievement', async () => {
      // Arrange
      const sessionState = {
        metrics: {
          totalRounds: 3,
          dealClosed: true,
          finalScore: 8.5
        }
      };

      // Act
      const achievements = await service.checkAchievements('user-123', 'session-456', sessionState);

      // Assert
      expect(achievements).toContain('speed_demon');
    });
  });
});
```

### **Integration Tests**
```typescript
// tests/controllers/characters.controller.integration.spec.ts
describe('CharactersController (Integration)', () => {
  let app: INestApplication;
  let authToken: string;

  beforeAll(async () => {
    const moduleFixture = await Test.createTestingModule({
      imports: [AppModule]
    }).compile();

    app = moduleFixture.createNestApplication();
    await app.init();

    // Get auth token
    authToken = await getAuthToken(app);
  });

  describe('GET /api/negotiation-simulator/characters', () => {
    it('should return available characters for user', async () => {
      return request(app.getHttpServer())
        .get('/api/negotiation-simulator/characters')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ userId: 'test-user-123' })
        .expect(200)
        .expect((res) => {
          expect(res.body).toHaveProperty('characters');
          expect(Array.isArray(res.body.characters)).toBe(true);
          expect(res.body.characters[0]).toHaveProperty('unlocked');
        });
    });

    it('should filter by difficulty', async () => {
      return request(app.getHttpServer())
        .get('/api/negotiation-simulator/characters')
        .set('Authorization', `Bearer ${authToken}`)
        .query({ userId: 'test-user-123', difficulty: '2' })
        .expect(200)
        .expect((res) => {
          const characters = res.body.characters;
          expect(characters.every(c => c.difficulty <= 2)).toBe(true);
        });
    });
  });
});
```

---

## **Deployment Guide**

### **Environment Configuration**
```yaml
# docker-compose.yml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=production
      - MONGODB_URI=mongodb://mongo:27017/docgic
      - REDIS_URL=redis://redis:6379
      - JWT_SECRET=${JWT_SECRET}
      - WEBSOCKET_PORT=3001
    depends_on:
      - mongo
      - redis

  mongo:
    image: mongo:5.0
    ports:
      - "27017:27017"
    volumes:
      - mongo_data:/data/db

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mongo_data:
  redis_data:
```

### **Production Deployment Steps**

#### **1. Database Setup**
```bash
# Initialize MongoDB with indexes
npm run db:setup

# Seed initial data
npm run db:seed:characters
npm run db:seed:achievements
npm run db:seed:pressure-events
```

#### **2. Redis Configuration**
```bash
# Configure Redis for production
redis-cli CONFIG SET maxmemory 2gb
redis-cli CONFIG SET maxmemory-policy allkeys-lru
```

#### **3. Application Deployment**
```bash
# Build application
npm run build

# Start production server
npm run start:prod

# Start background jobs
npm run jobs:start
```

#### **4. Monitoring Setup**
```typescript
// monitoring/health.controller.ts
@Controller('health')
export class HealthController {

  @Get()
  async checkHealth(): Promise<HealthStatus> {
    const checks = await Promise.allSettled([
      this.checkDatabase(),
      this.checkRedis(),
      this.checkWebSocket()
    ]);

    return {
      status: checks.every(c => c.status === 'fulfilled') ? 'healthy' : 'unhealthy',
      timestamp: new Date(),
      services: {
        database: checks[0].status === 'fulfilled',
        redis: checks[1].status === 'fulfilled',
        websocket: checks[2].status === 'fulfilled'
      }
    };
  }
}
```

### **Performance Monitoring**
```typescript
// monitoring/metrics.service.ts
export class MetricsService {

  @Cron('0 */5 * * * *') // Every 5 minutes
  async collectMetrics(): Promise<void> {
    const metrics = {
      activeSessions: await this.getActiveSessions(),
      averageResponseTime: await this.getAverageResponseTime(),
      achievementUnlockRate: await this.getAchievementUnlockRate(),
      leaderboardUpdateTime: await this.getLeaderboardUpdateTime(),
      cacheHitRate: await this.getCacheHitRate()
    };

    // Send to monitoring service
    await this.monitoringService.recordMetrics(metrics);
  }
}
```

This comprehensive backend implementation guide provides everything needed to build the gamified negotiation simulator system, from database design to deployment strategies.
```

---

## **Gamification Engine**

### **Achievement Engine**
```typescript
// engines/achievement.engine.ts
export class AchievementEngine {

  private achievementRules: Map<string, AchievementRule> = new Map();

  constructor() {
    this.initializeRules();
  }

  private initializeRules(): void {
    // Speed Demon Achievement
    this.achievementRules.set('speed_demon', {
      id: 'speed_demon',
      check: async (context: AchievementContext) => {
        const { sessionState } = context;
        return sessionState.metrics.totalRounds <= 3 &&
               sessionState.metrics.dealClosed &&
               sessionState.metrics.finalScore >= 7.0;
      }
    });

    // Win-Win Master Achievement
    this.achievementRules.set('win_win_master', {
      id: 'win_win_master',
      check: async (context: AchievementContext) => {
        const { sessionState } = context;
        return sessionState.metrics.userSatisfaction >= 0.85 &&
               sessionState.metrics.aiSatisfaction >= 0.85;
      }
    });

    // Hot Streak Achievement
    this.achievementRules.set('hot_streak', {
      id: 'hot_streak',
      check: async (context: AchievementContext) => {
        const { userId } = context;
        const recentSessions = await this.getRecentSessions(userId, 5);
        return recentSessions.every(s => s.metrics.finalScore >= 8.0);
      }
    });

    // Pressure Cooker Achievement
    this.achievementRules.set('pressure_cooker', {
      id: 'pressure_cooker',
      check: async (context: AchievementContext) => {
        const { sessionState } = context;
        const activePressureEvents = sessionState.gameState.activePressureEvents;
        return activePressureEvents.length >= 3 &&
               sessionState.metrics.dealClosed &&
               sessionState.metrics.finalScore >= 8.0;
      }
    });
  }

  async checkAllAchievements(context: AchievementContext): Promise<string[]> {
    const unlockedAchievements: string[] = [];

    for (const [achievementId, rule] of this.achievementRules) {
      // Skip if already unlocked
      if (context.userAchievements.includes(achievementId)) continue;

      // Check rule
      if (await rule.check(context)) {
        unlockedAchievements.push(achievementId);
      }
    }

    return unlockedAchievements;
  }
}
```

### **Level Progression System**
```typescript
// engines/level.engine.ts
export class LevelEngine {

  private levelRequirements = [
    { level: 1, xp: 0, title: "Rookie Negotiator" },
    { level: 2, xp: 500, title: "Junior Professional" },
    { level: 3, xp: 1500, title: "Senior Negotiator" },
    { level: 4, xp: 3500, title: "Expert Dealmaker" },
    { level: 5, xp: 7500, title: "Master Negotiator" },
    { level: 6, xp: 15000, title: "Negotiation Guru" },
    { level: 7, xp: 30000, title: "Deal Wizard" },
    { level: 8, xp: 60000, title: "Legendary Closer" }
  ];

  calculateLevel(totalXP: number): LevelInfo {
    let currentLevel = 1;
    let currentLevelXP = 0;
    let nextLevelXP = 500;

    for (let i = this.levelRequirements.length - 1; i >= 0; i--) {
      const requirement = this.levelRequirements[i];
      if (totalXP >= requirement.xp) {
        currentLevel = requirement.level;
        currentLevelXP = requirement.xp;

        // Find next level XP
        if (i < this.levelRequirements.length - 1) {
          nextLevelXP = this.levelRequirements[i + 1].xp;
        } else {
          nextLevelXP = requirement.xp; // Max level
        }
        break;
      }
    }

    const currentXP = totalXP - currentLevelXP;
    const xpToNext = nextLevelXP - totalXP;

    return {
      level: currentLevel,
      title: this.levelRequirements[currentLevel - 1].title,
      currentXP,
      totalXP,
      xpToNext: Math.max(0, xpToNext)
    };
  }

  getUnlocksForLevel(level: number): string[] {
    const unlocks: string[] = [];

    switch (level) {
      case 2:
        unlocks.push('intermediate_scenarios', 'assertive_ai_personalities');
        break;
      case 3:
        unlocks.push('advanced_scenarios', 'aggressive_ai_personalities', 'team_challenges');
        break;
      case 4:
        unlocks.push('expert_scenarios', 'celebrity_negotiators', 'campaign_mode');
        break;
      case 5:
        unlocks.push('master_scenarios', 'crisis_negotiations', 'custom_characters');
        break;
    }

    return unlocks;
  }
}
```

---

## **Performance & Scalability**

### **Caching Strategy**

#### **Redis Implementation**
```typescript
// services/cache.service.ts
export class CacheService {

  constructor(private readonly redis: Redis) {}

  // Session state caching
  async setSession(sessionId: string, sessionData: any, ttl: number = 3600): Promise<void> {
    const key = `session:${sessionId}`;
    await this.redis.setex(key, ttl, JSON.stringify(sessionData));
  }

  async getSession(sessionId: string): Promise<any> {
    const key = `session:${sessionId}`;
    const data = await this.redis.get(key);
    return data ? JSON.parse(data) : null;
  }

  // Leaderboard caching
  async setLeaderboard(
    type: string,
    scope: string,
    scopeId: string,
    data: any,
    ttl: number = 300
  ): Promise<void> {
    const key = `leaderboard:${type}:${scope}:${scopeId}`;
    await this.redis.setex(key, ttl, JSON.stringify(data));
  }

  // User gamification caching
  async setUserGamification(userId: string, data: any, ttl: number = 1800): Promise<void> {
    const key = `user_gamification:${userId}`;
    await this.redis.setex(key, ttl, JSON.stringify(data));
  }

  // Pressure event cooldowns
  async setPressureEventCooldown(
    sessionId: string,
    eventId: string,
    cooldownSeconds: number
  ): Promise<void> {
    const key = `pressure_cooldown:${sessionId}:${eventId}`;
    await this.redis.setex(key, cooldownSeconds, '1');
  }

  async isPressureEventOnCooldown(sessionId: string, eventId: string): Promise<boolean> {
    const key = `pressure_cooldown:${sessionId}:${eventId}`;
    const exists = await this.redis.exists(key);
    return exists === 1;
  }
}
```

### **Database Optimization**

#### **MongoDB Indexes**
```javascript
// Database indexes for optimal performance

// Characters collection
db.characters.createIndex({ "difficulty": 1, "isActive": 1 });
db.characters.createIndex({ "specialties": 1 });
db.characters.createIndex({ "unlockRequirements.level": 1 });

// User gamification collection
db.user_gamification.createIndex({ "userId": 1 }, { unique: true });
db.user_gamification.createIndex({ "organizationId": 1, "level.current": -1 });
db.user_gamification.createIndex({ "statistics.averageScore": -1 });

// Achievements collection
db.achievements.createIndex({ "isActive": 1, "category": 1 });
db.achievements.createIndex({ "rarity": 1 });

// Character relationships collection
db.character_relationships.createIndex({ "userId": 1, "characterId": 1 }, { unique: true });
db.character_relationships.createIndex({ "userId": 1 });

// Leaderboards collection
db.leaderboards.createIndex({ "type": 1, "scope": 1, "scopeId": 1, "period.start": 1 });
db.leaderboards.createIndex({ "lastUpdated": -1 });

// Challenges collection
db.challenges.createIndex({ "status": 1, "timeframe.endDate": 1 });
db.challenges.createIndex({ "type": 1, "status": 1 });

// Pressure events collection
db.pressure_events.createIndex({ "isActive": 1, "type": 1 });
db.pressure_events.createIndex({ "triggerConditions.minRound": 1, "triggerConditions.maxRound": 1 });

// Campaign progress collection
db.campaign_progress.createIndex({ "userId": 1, "campaignId": 1 }, { unique: true });
db.campaign_progress.createIndex({ "status": 1, "lastPlayedAt": -1 });

// Negotiation sessions collection (existing, add gamification indexes)
db.negotiation_sessions.createIndex({ "userId": 1, "completedAt": -1 });
db.negotiation_sessions.createIndex({ "status": 1, "startedAt": -1 });
db.negotiation_sessions.createIndex({ "metrics.finalScore": -1, "completedAt": -1 });
```
```
```
```
```
```
