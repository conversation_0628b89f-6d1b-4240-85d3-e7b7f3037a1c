# Updated Credit Package Integration

## 🎯 Overview

The credit package integration has been updated to match the new structure with 5 packages and updated API endpoints.

## 📦 Updated Credit Packages

### 1. Student Credit Package
- **ID**: `student`
- **Name**: "Student Credit Package"
- **Description**: "50 credits for law students"
- **Credits**: 50 (no bonus)
- **Price**: $4.99
- **Target Tier**: Law Student

### 2. Lawyer Small Credit Package
- **ID**: `lawyer_small`
- **Name**: "Lawyer Starter Pack"
- **Description**: "200 credits + 20 bonus for practicing attorneys"
- **Credits**: 200 + 20 bonus = 220 total
- **Price**: $19.99
- **Target Tier**: Lawyer

### 3. Lawyer Large Credit Package
- **ID**: `lawyer_large`
- **Name**: "Lawyer Professional Pack"
- **Description**: "500 credits + 75 bonus for busy legal practices"
- **Credits**: 500 + 75 bonus = 575 total
- **Price**: $44.99
- **Target Tier**: Lawyer

### 4. Firm Standard Credit Package
- **ID**: `firm_standard`
- **Name**: "Law Firm Standard Pack"
- **Description**: "1000 credits + 200 bonus for growing legal teams"
- **Credits**: 1000 + 200 bonus = 1200 total
- **Price**: $79.99
- **Target Tier**: Law Firm

### 5. Firm Enterprise Credit Package
- **ID**: `firm_enterprise`
- **Name**: "Law Firm Enterprise Pack"
- **Description**: "5000 credits + 1500 bonus for large legal organizations"
- **Credits**: 5000 + 1500 bonus = 6500 total
- **Price**: $349.99
- **Target Tier**: Law Firm

## 🔧 Required Stripe Setup

Create these products and prices in your Stripe dashboard:

### Environment Variables Needed:
```bash
STRIPE_PRICE_ID_STUDENT=price_xxxxx
STRIPE_PRICE_ID_LAWYER_SMALL=price_xxxxx
STRIPE_PRICE_ID_LAWYER_LARGE=price_xxxxx
STRIPE_PRICE_ID_FIRM_STANDARD=price_xxxxx
STRIPE_PRICE_ID_FIRM_ENTERPRISE=price_xxxxx
```

**Note**: Prices are stored in cents in the frontend configuration (e.g., 499 for $4.99) and automatically converted to dollars for display.

## 🚀 API Endpoints

### Purchase Credits
```http
POST /api/credits/purchase
Authorization: Bearer <jwt_token>
Content-Type: application/json

{
  "package": "lawyer_small",
  "successUrl": "https://yourdomain.com/credits/success",
  "cancelUrl": "https://yourdomain.com/credits/cancel"
}
```

**Response:**
```json
{
  "success": true,
  "sessionUrl": "https://checkout.stripe.com/pay/cs_...",
  "package": {
    "name": "lawyer_small",
    "credits": 200,
    "bonus": 20,
    "totalCredits": 220,
    "price": 19.99,
    "description": "200 credits + 20 bonus for practicing attorneys"
  },
  "organizationId": "org-123"
}
```

### Get Available Packages
```http
GET /api/credits/packages
Authorization: Bearer <jwt_token>
```

**Response:**
```json
[
  {
    "id": "student",
    "name": "50 credits for law students",
    "credits": 50,
    "bonus": 0,
    "totalCredits": 50,
    "price": 4.99,
    "targetTier": "law_student",
    "popular": false
  },
  {
    "id": "lawyer_small",
    "name": "200 credits + 20 bonus for practicing attorneys",
    "credits": 200,
    "bonus": 20,
    "totalCredits": 220,
    "price": 19.99,
    "targetTier": "lawyer",
    "popular": true
  }
  // ... more packages
]
```

## 🔄 Updated Components

### 1. Credit Service (`src/lib/services/credit-service.ts`)
- Updated `getCreditPackages()` to use `/api/credits/packages`
- Updated `createCreditCheckoutSession()` to use `/api/credits/purchase`
- Updated category types to match new structure

### 2. Credit Package Interface (`src/lib/types/subscription.ts`)
- Added `targetTier` and `description` properties
- Updated package IDs and structure

### 3. Success/Cancel Pages
- Created `/credits/success` page for successful purchases
- Created `/credits/cancel` page for cancelled purchases

## 🎨 Frontend Integration

### Using Credit Purchase Button
```tsx
import { CreditPurchaseButton } from "@/components/subscription/credit-purchase";

// Simple purchase button
<CreditPurchaseButton packageId="lawyer_small">
  Buy Lawyer Starter Pack
</CreditPurchaseButton>

// Custom styled button
<CreditPurchaseButton 
  packageId="firm_enterprise" 
  variant="outline" 
  size="lg"
  className="w-full"
>
  Purchase Enterprise Pack
</CreditPurchaseButton>
```

### Using Credit Packages
```tsx
import { CREDIT_PACKAGES } from "@/lib/types/subscription";

// Display all packages
{CREDIT_PACKAGES.map(pkg => (
  <div key={pkg.id}>
    <h3>{pkg.name}</h3>
    <p>{pkg.description}</p>
    <p>${pkg.price} - {pkg.total} credits</p>
    <CreditPurchaseButton packageId={pkg.id}>
      Purchase
    </CreditPurchaseButton>
  </div>
))}
```

## 🔐 Backend Integration

The backend webhook should handle `checkout.session.completed` events with `mode: 'payment'` to process credit purchases. The session metadata will contain:

- `organizationId`: Target organization
- `packageId`: Package identifier
- `credits`: Base credits amount
- `bonus`: Bonus credits amount
- `totalCredits`: Total credits to add

## ✅ Testing Checklist

1. ✅ Updated credit package structure
2. ✅ Updated API endpoints
3. ✅ Updated frontend components
4. ✅ Created success/cancel pages
5. ✅ Updated service layer
6. ⏳ Set up Stripe products and prices
7. ⏳ Configure environment variables
8. ⏳ Test purchase flow end-to-end
9. ⏳ Verify webhook handling

## 🎯 Next Steps

1. **Create Stripe Products**: Set up the 5 credit packages in Stripe dashboard
2. **Configure Environment Variables**: Add the Stripe price IDs
3. **Test Purchase Flow**: Complete end-to-end testing
4. **Verify Webhook**: Ensure backend properly handles credit additions

The frontend integration is now complete and ready for the backend webhook to process credit purchases!
