"use client";

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useFeatureAccess } from '@/hooks/use-feature-access';
import { 
  negotiationSimulatorService, 
  NegotiationSimulatorError 
} from '@/lib/services/negotiation-simulator-service';
import type {
  UserNegotiationProfile,
  UpdateUserNegotiationProfileRequest,
  PerformanceHistoryEntry,
  SkillGapAnalysisRequest,
  SkillGapAnalysisResponse,
  CrossFeatureUsageResponse,
  PersonalizedRecommendationsResponse,
  NegotiationScenario
} from '@/lib/types/negotiation-simulator';

interface UseNegotiationIntegrationState {
  userProfile: UserNegotiationProfile | null;
  performanceHistory: PerformanceHistoryEntry[];
  skillGapAnalysis: SkillGapAnalysisResponse | null;
  crossFeatureUsage: CrossFeatureUsageResponse | null;
  recommendations: PersonalizedRecommendationsResponse | null;
  relatedScenarios: NegotiationScenario[];
  loading: boolean;
  error: string | null;
}

export function useNegotiationIntegration() {
  const [state, setState] = useState<UseNegotiationIntegrationState>({
    userProfile: null,
    performanceHistory: [],
    skillGapAnalysis: null,
    crossFeatureUsage: null,
    recommendations: null,
    relatedScenarios: [],
    loading: false,
    error: null,
  });

  const { toast } = useToast();
  const { canAccessFeature } = useFeatureAccess();

  const hasAccess = canAccessFeature('negotiation_simulator');

  // Get user negotiation profile
  const getUserProfile = useCallback(async () => {
    if (!hasAccess) return null;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const profile = await negotiationSimulatorService.getUserNegotiationProfile();
      setState(prev => ({
        ...prev,
        userProfile: profile,
        loading: false,
      }));
      return profile;
    } catch (error) {
      const errorMessage = error instanceof NegotiationSimulatorError 
        ? error.message 
        : 'Failed to get user profile';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
      return null;
    }
  }, [hasAccess]);

  // Update user negotiation profile
  const updateUserProfile = useCallback(async (updates: UpdateUserNegotiationProfileRequest) => {
    if (!hasAccess) {
      toast({
        title: "Subscription Required",
        description: "Negotiation simulator feature requires PRO subscription",
        variant: "destructive",
      });
      return null;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const updatedProfile = await negotiationSimulatorService.updateUserNegotiationProfile(updates);
      setState(prev => ({
        ...prev,
        userProfile: updatedProfile,
        loading: false,
      }));

      toast({
        title: "Profile Updated",
        description: "Your negotiation profile has been updated successfully.",
      });

      return updatedProfile;
    } catch (error) {
      const errorMessage = error instanceof NegotiationSimulatorError 
        ? error.message 
        : 'Failed to update profile';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));

      toast({
        title: "Update Failed",
        description: errorMessage,
        variant: "destructive",
      });

      return null;
    }
  }, [hasAccess, toast]);

  // Get performance history
  const getPerformanceHistory = useCallback(async (
    userId: string,
    params: { timeframe?: string; page?: number; limit?: number } = {}
  ) => {
    if (!hasAccess) return null;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await negotiationSimulatorService.getPerformanceHistory(userId, params);
      setState(prev => ({
        ...prev,
        performanceHistory: result.data,
        loading: false,
      }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof NegotiationSimulatorError 
        ? error.message 
        : 'Failed to get performance history';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
      return null;
    }
  }, [hasAccess]);

  // Generate skill gap analysis
  const generateSkillGapAnalysis = useCallback(async (
    userId: string,
    request: SkillGapAnalysisRequest
  ) => {
    if (!hasAccess) {
      toast({
        title: "Subscription Required",
        description: "Negotiation simulator feature requires PRO subscription",
        variant: "destructive",
      });
      return null;
    }

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const analysis = await negotiationSimulatorService.generateSkillGapAnalysis(userId, request);
      setState(prev => ({
        ...prev,
        skillGapAnalysis: analysis,
        loading: false,
      }));

      toast({
        title: "Analysis Complete",
        description: "Your skill gap analysis has been generated.",
      });

      return analysis;
    } catch (error) {
      const errorMessage = error instanceof NegotiationSimulatorError 
        ? error.message 
        : 'Failed to generate skill gap analysis';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));

      toast({
        title: "Analysis Failed",
        description: errorMessage,
        variant: "destructive",
      });

      return null;
    }
  }, [hasAccess, toast]);

  // Get cross-feature usage analytics
  const getCrossFeatureUsage = useCallback(async (params: {
    timeframe?: string;
    userId?: string;
    organizationId?: string;
  } = {}) => {
    if (!hasAccess) return null;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const usage = await negotiationSimulatorService.getCrossFeatureUsage(params);
      setState(prev => ({
        ...prev,
        crossFeatureUsage: usage,
        loading: false,
      }));
      return usage;
    } catch (error) {
      const errorMessage = error instanceof NegotiationSimulatorError 
        ? error.message 
        : 'Failed to get cross-feature usage';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
      return null;
    }
  }, [hasAccess]);

  // Generate personalized recommendations
  const generateRecommendations = useCallback(async (request: {
    userId: string;
    context: "playbook" | "simulator" | "general";
    targetArea?: string;
    currentDocumentId?: string;
    currentSessionId?: string;
  }) => {
    if (!hasAccess) return null;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const recommendations = await negotiationSimulatorService.generatePersonalizedRecommendations(request);
      setState(prev => ({
        ...prev,
        recommendations,
        loading: false,
      }));
      return recommendations;
    } catch (error) {
      const errorMessage = error instanceof NegotiationSimulatorError 
        ? error.message 
        : 'Failed to generate recommendations';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
      return null;
    }
  }, [hasAccess]);

  // Get scenarios from playbook
  const getScenariosFromPlaybook = useCallback(async (
    playbookId: string,
    params: { page?: number; limit?: number } = {}
  ) => {
    if (!hasAccess) return null;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const result = await negotiationSimulatorService.getScenariosFromPlaybook(playbookId, params);
      setState(prev => ({
        ...prev,
        relatedScenarios: Array.isArray(result?.scenarios) ? result.scenarios : [],
        loading: false,
      }));
      return result;
    } catch (error) {
      const errorMessage = error instanceof NegotiationSimulatorError 
        ? error.message 
        : 'Failed to get scenarios from playbook';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
      return null;
    }
  }, [hasAccess]);

  // Clear error
  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  return {
    ...state,
    hasAccess,
    getUserProfile,
    updateUserProfile,
    getPerformanceHistory,
    generateSkillGapAnalysis,
    getCrossFeatureUsage,
    generateRecommendations,
    getScenariosFromPlaybook,
    clearError,
  };
}
