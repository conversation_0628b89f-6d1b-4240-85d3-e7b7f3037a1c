import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
	documentAutomationService,
	DocumentAutomationServiceError,
} from "@/lib/services/document-automation-service";
import { useFeatureAccess } from "@/hooks/use-feature-access";
import type {
	AIAssistedDraftingRequest,
	AIAssistedDraftingResponse,
	RelatedDocumentsRequest,
	RelatedDocumentsResponse,
	ClauseIntelligenceRequest,
	ClauseIntelligenceResponse,
	ClauseLibraryBuildRequest,
	ClauseLibraryBuildResponse,
} from "@/lib/types/document-automation";

/**
 * Hook for AI-assisted document drafting
 */
export function useAIAssistedDrafting() {
	const queryClient = useQueryClient();

	return useMutation<
		AIAssistedDraftingResponse,
		DocumentAutomationServiceError,
		AIAssistedDraftingRequest
	>({
		mutationFn: (request) =>
			documentAutomationService.aiAssistedDrafting(request),
		onSuccess: () => {
			// Invalidate relevant queries
			queryClient.invalidateQueries({ queryKey: ["documents"] });
		},
	});
}

/**
 * Hook for generating related documents
 */
export function useGenerateRelatedDocuments() {
	const queryClient = useQueryClient();

	return useMutation<
		RelatedDocumentsResponse,
		DocumentAutomationServiceError,
		RelatedDocumentsRequest
	>({
		mutationFn: (request) =>
			documentAutomationService.generateRelatedDocuments(request),
		onSuccess: () => {
			// Invalidate relevant queries
			queryClient.invalidateQueries({ queryKey: ["documents"] });
		},
	});
}

/**
 * Hook for getting clause intelligence suggestions
 */
export function useClauseIntelligence() {
	return useMutation<
		ClauseIntelligenceResponse,
		DocumentAutomationServiceError,
		ClauseIntelligenceRequest
	>({
		mutationFn: (request) =>
			documentAutomationService.getClauseIntelligence(request),
	});
}

/**
 * Hook for building clause library from document corpus
 */
export function useBuildClauseLibrary() {
	const queryClient = useQueryClient();

	return useMutation<
		ClauseLibraryBuildResponse,
		DocumentAutomationServiceError,
		ClauseLibraryBuildRequest
	>({
		mutationFn: (request) =>
			documentAutomationService.buildClauseLibrary(request),
		onSuccess: () => {
			// Invalidate clause library queries
			queryClient.invalidateQueries({ queryKey: ["clause-library"] });
		},
	});
}

/**
 * Utility hook to check if document automation features are available
 */
export function useDocumentAutomationFeatures() {
	const { canAccessFeature } = useFeatureAccess();

	return {
		hasAIDrafting: canAccessFeature("document_automation"),
		hasRelatedDocuments: canAccessFeature("document_automation"),
		hasClauseIntelligence: canAccessFeature("document_automation"),
		hasClauseLibraryBuilder: canAccessFeature("document_automation"),
	};
}
