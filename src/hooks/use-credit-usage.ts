"use client";

import { useState, useCallback } from "react";
import { useSubscription } from "@/lib/subscription/subscription-context";

interface CreditUsageOptions {
  showToast?: boolean;
  onSuccess?: (newBalance: number) => void;
  onError?: (error: Error) => void;
  onInsufficientCredits?: () => void;
}

interface CreditUsageResult {
  success: boolean;
  newBalance?: number;
  error?: string;
}

export function useCreditUsage() {
  const {
    useCreditsForFeature,
    checkCreditsForFeature,
    getFeatureCost,
    creditBalance
  } = useSubscription();

  const [isUsing] = useState(false);

  /**
   * Check if user has enough credits for a feature
   */
  const checkCredits = useCallback(async (featureName: string): Promise<boolean> => {
    try {
      return await checkCreditsForFeature(featureName);
    } catch (error) {
      console.error("Error checking credits:", error);
      
      // Fallback to local check
      const featureCost = getFeatureCost(featureName);
      if (featureCost && creditBalance) {
        return creditBalance.current >= featureCost.credits;
      }
      
      return false;
    }
  }, [checkCreditsForFeature, getFeatureCost, creditBalance]);

  // TODO: Restructure useCredits to avoid calling hooks inside callbacks
  // For now, return the basic functions without the problematic useCredits callback
  const useCredits = useCreditsForFeature;

  /**
   * Get the cost of a feature
   */
  const getCost = useCallback((featureName: string) => {
    return getFeatureCost(featureName);
  }, [getFeatureCost]);

  /**
   * Check if user can afford a feature (local check only)
   */
  const canAfford = useCallback((featureName: string): boolean => {
    const featureCost = getFeatureCost(featureName);
    if (!featureCost || !creditBalance) return false;
    
    return creditBalance.current >= featureCost.credits;
  }, [getFeatureCost, creditBalance]);

  /**
   * Get remaining credits after using a feature
   */
  const getRemainingAfterUse = useCallback((featureName: string): number => {
    const featureCost = getFeatureCost(featureName);
    if (!featureCost || !creditBalance) return 0;
    
    return Math.max(0, creditBalance.current - featureCost.credits);
  }, [getFeatureCost, creditBalance]);

  /**
   * Batch check multiple features
   */
  const checkMultipleFeatures = useCallback(async (featureNames: string[]): Promise<Record<string, boolean>> => {
    const results: Record<string, boolean> = {};
    
    for (const featureName of featureNames) {
      try {
        results[featureName] = await checkCredits(featureName);
      } catch (error) {
        console.error(`Error checking credits for ${featureName}:`, error);
        results[featureName] = false;
      }
    }
    
    return results;
  }, [checkCredits]);

  /**
   * Calculate total cost for multiple features
   */
  const calculateTotalCost = useCallback((featureNames: string[]): number => {
    return featureNames.reduce((total, featureName) => {
      const cost = getFeatureCost(featureName);
      return total + (cost?.credits || 0);
    }, 0);
  }, [getFeatureCost]);

  return {
    // State
    isUsing,
    currentBalance: creditBalance?.current || 0,
    
    // Actions
    useCredits,
    checkCredits,
    
    // Utilities
    getCost,
    canAfford,
    getRemainingAfterUse,
    checkMultipleFeatures,
    calculateTotalCost,
  };
}

// TODO: Implement proper credit usage patterns that don't violate React Hook rules
// These functions need to be restructured to avoid calling hooks inside callbacks
