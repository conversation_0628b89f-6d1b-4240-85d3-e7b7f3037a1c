import { useQuery } from '@tanstack/react-query';
import { documentService } from '@/lib/services/document-service';

// Query key for document details
const documentDetailKey = (id: string) => ['document', id] as const;

/**
 * Hook for fetching a single document by ID using React Query
 * This is a separate hook from useDocumentQuery to avoid circular dependencies
 * and to allow for more targeted refetching
 */
export function useDocumentById(id: string | null) {
  return useQuery({
    queryKey: id ? documentDetailKey(id) : ['document', null] as const,
    queryFn: async () => {
      if (!id) return null;
      try {
        return await documentService.getDocument(id);
      } catch (error) {
        console.error(`Error fetching document ${id}:`, error);
        throw error;
      }
    },
    enabled: !!id, // Only run the query if we have an ID
    staleTime: 1000 * 60 * 5, // 5 minutes
  });
}
