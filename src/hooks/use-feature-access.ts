import { useSubscription } from "@/lib/subscription/subscription-context";

export function useFeatureAccess() {
  const { hasFeature, isInTrial, subscription } = useSubscription();

  const canAccessFeature = (featureId: string): boolean => {
    return hasFeature(featureId);
  };

  const checkDocumentLimit = (): boolean => {
    if (!subscription) return false;
    
    const { tier, usageStats } = subscription;
    const plan = SUBSCRIPTION_PLANS.find(p => p.id === tier);
    
    if (!plan) return false;
    
    // If in trial and trial tier is Pro, use Pro limits
    if (isInTrial() && subscription.trialTier === 'pro') {
      const trialPlan = SUBSCRIPTION_PLANS.find(p => p.id === 'pro');
      if (trialPlan) {
        return usageStats.documentsProcessed < trialPlan.limits.documentLimit;
      }
    }
    
    return usageStats.documentsProcessed < plan.limits.documentLimit;
  };

  // 🎯 Analysis limits have been REMOVED - now unlimited within credit allocation
  // All AI analysis operations are unlimited as long as user has credits
  const checkAnalysisLimit = (): boolean => {
    // Analysis is now unlimited for all tiers within their credit allocation
    // Credits are the only limiting factor for AI-powered features
    return true;
  };

  return {
    canAccessFeature,
    checkDocumentLimit,
    checkAnalysisLimit,
  };
}

// Import here to avoid circular dependency
import { SUBSCRIPTION_PLANS } from "@/lib/types/subscription";
