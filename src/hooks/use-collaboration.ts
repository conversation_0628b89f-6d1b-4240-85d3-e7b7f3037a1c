import { useState, useEffect, useCallback } from 'react';
import { collaborationService } from '@/lib/services/collaboration-service';
import { useCollaboration as useCollaborationContext } from '@/lib/collaboration/collaboration-context';
import type {
  CollaborationSession,
  Task,
  CommentThread,
  WorkflowInstance,
  CollaborationMetrics,
  Notification
} from '@/lib/types/collaboration';

export function useCollaborationFeatures(documentId?: string) {
  const collaborationContext = useCollaborationContext();
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Real-time collaboration
  const [activeSessions, setActiveSessions] = useState<CollaborationSession[]>([]);
  
  // Tasks
  const [tasks, setTasks] = useState<Task[]>([]);
  const [myTasks, setMyTasks] = useState<Task[]>([]);
  
  // Comments
  const [commentThreads, setCommentThreads] = useState<CommentThread[]>([]);
  
  // Workflows
  const [workflowInstances, setWorkflowInstances] = useState<WorkflowInstance[]>([]);
  
  // Metrics
  const [metrics, setMetrics] = useState<CollaborationMetrics | null>(null);
  
  // Notifications
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [unreadCount, setUnreadCount] = useState(0);

  // Load initial data
  useEffect(() => {
    if (documentId) {
      loadCollaborationData();
    }
  }, [documentId]);

  const loadCollaborationData = useCallback(async () => {
    if (!documentId) return;
    
    try {
      setLoading(true);
      setError(null);

      const [
        sessionsData,
        tasksData,
        threadsData,
        workflowsData,
        metricsData,
        notificationsData
      ] = await Promise.all([
        collaborationService.getActiveSessions(documentId),
        collaborationService.getTasks({ documentId }),
        collaborationService.getCommentThreads(documentId),
        collaborationService.getWorkflowInstances(documentId),
        collaborationService.getCollaborationMetrics(),
        collaborationService.getNotifications({ isRead: false })
      ]);

      setActiveSessions(sessionsData);
      setTasks(tasksData);
      setMyTasks(tasksData.filter(task => task.assigneeId === 'current-user-id')); // Replace with actual user ID
      setCommentThreads(threadsData);
      setWorkflowInstances(workflowsData);
      setMetrics(metricsData);
      setNotifications(notificationsData);
      setUnreadCount(notificationsData.length);

    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load collaboration data');
    } finally {
      setLoading(false);
    }
  }, [documentId]);

  // Real-time collaboration functions
  const startCollaboration = useCallback(async (settings?: any) => {
    if (!documentId) return null;
    
    try {
      const session = await collaborationService.createSession(documentId, settings);
      setActiveSessions(prev => [...prev, session]);
      return session;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start collaboration');
      return null;
    }
  }, [documentId]);

  const joinCollaboration = useCallback(async (sessionId: string) => {
    try {
      await collaborationContext.joinSession(sessionId);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join collaboration');
      return false;
    }
  }, [collaborationContext]);

  const leaveCollaboration = useCallback(async () => {
    try {
      await collaborationContext.leaveSession();
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to leave collaboration');
      return false;
    }
  }, [collaborationContext]);

  // Comment functions
  const createCommentThread = useCallback(async (threadData: {
    title?: string;
    type: 'discussion' | 'review' | 'approval' | 'question';
    anchor?: any;
    initialComment: string;
  }) => {
    if (!documentId) return null;
    
    try {
      const thread = await collaborationService.createCommentThread(documentId, threadData);
      setCommentThreads(prev => [thread, ...prev]);
      return thread;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create comment thread');
      return null;
    }
  }, [documentId]);

  const addComment = useCallback(async (threadId: string, content: string, parentId?: string) => {
    try {
      const comment = await collaborationService.addComment(threadId, content, parentId);
      setCommentThreads(prev => prev.map(thread => 
        thread.id === threadId 
          ? { ...thread, comments: [...thread.comments, comment] }
          : thread
      ));
      return comment;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to add comment');
      return null;
    }
  }, []);

  const resolveThread = useCallback(async (threadId: string) => {
    try {
      const updatedThread = await collaborationService.resolveThread(threadId);
      setCommentThreads(prev => prev.map(thread => 
        thread.id === threadId ? updatedThread : thread
      ));
      return updatedThread;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to resolve thread');
      return null;
    }
  }, []);

  // Task functions
  const updateTaskStatus = useCallback(async (taskId: string, status: string, comment?: string) => {
    try {
      const updatedTask = await collaborationService.updateTaskStatus(taskId, status, comment);
      setTasks(prev => prev.map(task => task.id === taskId ? updatedTask : task));
      setMyTasks(prev => prev.map(task => task.id === taskId ? updatedTask : task));
      return updatedTask;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to update task status');
      return null;
    }
  }, []);

  const assignTask = useCallback(async (taskId: string, assigneeId: string) => {
    try {
      const updatedTask = await collaborationService.assignTask(taskId, assigneeId);
      setTasks(prev => prev.map(task => task.id === taskId ? updatedTask : task));
      return updatedTask;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to assign task');
      return null;
    }
  }, []);

  // Workflow functions
  const startWorkflow = useCallback(async (templateId: string, metadata?: Record<string, any>) => {
    if (!documentId) return null;
    
    try {
      const instance = await collaborationService.createWorkflowInstance(templateId, documentId, metadata);
      setWorkflowInstances(prev => [...prev, instance]);
      return instance;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start workflow');
      return null;
    }
  }, [documentId]);

  const executeWorkflowStep = useCallback(async (instanceId: string, stepId: string, data?: Record<string, any>) => {
    try {
      const updatedInstance = await collaborationService.executeWorkflowStep(instanceId, stepId, data);
      setWorkflowInstances(prev => prev.map(instance => 
        instance.id === instanceId ? updatedInstance : instance
      ));
      return updatedInstance;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to execute workflow step');
      return null;
    }
  }, []);

  // Notification functions
  const markNotificationAsRead = useCallback(async (notificationId: string) => {
    try {
      await collaborationService.markNotificationAsRead(notificationId);
      setNotifications(prev => prev.map(notification => 
        notification.id === notificationId 
          ? { ...notification, isRead: true }
          : notification
      ));
      setUnreadCount(prev => Math.max(0, prev - 1));
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark notification as read');
      return false;
    }
  }, []);

  const markAllNotificationsAsRead = useCallback(async () => {
    try {
      await collaborationService.markAllNotificationsAsRead();
      setNotifications(prev => prev.map(notification => ({ ...notification, isRead: true })));
      setUnreadCount(0);
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to mark all notifications as read');
      return false;
    }
  }, []);

  // Document sharing
  const shareDocument = useCallback(async (shareData: {
    emails?: string[];
    permissions: {
      canView: boolean;
      canEdit: boolean;
      canComment: boolean;
      canShare: boolean;
    };
    expiresAt?: string;
    message?: string;
  }) => {
    if (!documentId) return null;
    
    try {
      const result = await collaborationService.shareDocument(documentId, shareData);
      return result;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to share document');
      return null;
    }
  }, [documentId]);

  // Refresh data
  const refresh = useCallback(() => {
    loadCollaborationData();
  }, [loadCollaborationData]);

  // Clear error
  const clearError = useCallback(() => {
    setError(null);
  }, []);

  return {
    // State
    loading,
    error,
    activeSessions,
    tasks,
    myTasks,
    commentThreads,
    workflowInstances,
    metrics,
    notifications,
    unreadCount,
    
    // Real-time collaboration
    isConnected: collaborationContext.isConnected,
    currentSession: collaborationContext.currentSession,
    participants: collaborationContext.participants,
    startCollaboration,
    joinCollaboration,
    leaveCollaboration,
    
    // Comments
    createCommentThread,
    addComment,
    resolveThread,
    
    // Tasks
    updateTaskStatus,
    assignTask,
    
    // Workflows
    startWorkflow,
    executeWorkflowStep,
    
    // Notifications
    markNotificationAsRead,
    markAllNotificationsAsRead,
    
    // Document sharing
    shareDocument,
    
    // Utilities
    refresh,
    clearError
  };
}
