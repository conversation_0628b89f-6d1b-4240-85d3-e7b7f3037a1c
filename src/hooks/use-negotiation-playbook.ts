"use client";

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useFeatureAccess } from '@/hooks/use-feature-access';
import { negotiationPlaybookService, NegotiationPlaybookError } from '@/lib/services/negotiation-playbook-service';
import type {
  NegotiationPlaybook,
  GeneratePlaybookOptions
} from '@/lib/types/negotiation-playbook';

interface UseNegotiationPlaybookState {
  playbook: NegotiationPlaybook | null;
  loading: boolean;
  error: string | null;
  hasExisting: boolean;
}

interface UseNegotiationPlaybookResult extends UseNegotiationPlaybookState {
  generate: (options: GeneratePlaybookOptions) => Promise<void>;
  refresh: () => Promise<void>;
  clearError: () => void;
}

export function useNegotiationPlaybook(documentId: string): UseNegotiationPlaybookResult {
  const { toast } = useToast();
  const { canAccessFeature } = useFeatureAccess();
  
  const [state, setState] = useState<UseNegotiationPlaybookState>({
    playbook: null,
    loading: false,
    error: null,
    hasExisting: false,
  });

  // Check if user has access to negotiation playbook feature
  const hasAccess = canAccessFeature('negotiation_playbook');

  const checkExisting = useCallback(async () => {
    if (!documentId || !hasAccess) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const existing = await negotiationPlaybookService.getPlaybook(documentId);
      setState(prev => ({
        ...prev,
        playbook: existing,
        hasExisting: !!existing,
        loading: false,
      }));
    } catch (error) {
      const errorMessage = error instanceof NegotiationPlaybookError 
        ? error.message 
        : 'Failed to check for existing playbook';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
    }
  }, [documentId, hasAccess]);

  const generate = useCallback(
    async (options: GeneratePlaybookOptions) => {
      if (!documentId) {
        toast({
          title: "Error",
          description: "No document ID provided",
          variant: "destructive",
        });
        return;
      }

      if (!hasAccess) {
        toast({
          title: "Subscription Required",
          description: "Negotiation playbook feature requires PRO subscription",
          variant: "destructive",
        });
        return;
      }

      setState(prev => ({ ...prev, loading: true, error: null }));

      try {
        const playbook = await negotiationPlaybookService.generatePlaybook(
          documentId,
          options
        );
        
        setState(prev => ({
          ...prev,
          playbook,
          hasExisting: true,
          loading: false,
        }));

        toast({
          title: "Success",
          description: "Negotiation playbook generated successfully",
        });
      } catch (error) {
        let errorMessage = 'Failed to generate negotiation playbook';
        
        if (error instanceof NegotiationPlaybookError) {
          errorMessage = error.message;
          
          // Handle specific error cases
          if (error.code === 'FEATURE_NOT_AVAILABLE') {
            toast({
              title: "Subscription Required",
              description: error.message,
              variant: "destructive",
            });
            return;
          }
        }
        
        setState(prev => ({
          ...prev,
          error: errorMessage,
          loading: false,
        }));

        toast({
          title: "Error",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    [documentId, hasAccess, toast]
  );

  const refresh = useCallback(async () => {
    await checkExisting();
  }, [checkExisting]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  // Check for existing playbook on mount and when documentId changes
  useEffect(() => {
    checkExisting();
  }, [checkExisting]);

  return {
    ...state,
    generate,
    refresh,
    clearError,
  };
}
