"use client";

import { useState, useEffect, useCallback } from 'react';
import { 
  teamChallengesService, 
  TeamChallenge, 
  Team, 
  ChallengeParticipation,
  ChallengeLeaderboard
} from '@/lib/services/team-challenges-service';
import { useUser } from '@/hooks/use-user';

export interface UseTeamChallengesOptions {
  autoFetch?: boolean;
  organizationId?: string;
}

export function useTeamChallenges(options: UseTeamChallengesOptions = {}) {
  const { autoFetch = true, organizationId } = options;
  const { user } = useUser();
  
  // State
  const [challenges, setChallenges] = useState<TeamChallenge[]>([]);
  const [userTeams, setUserTeams] = useState<Team[]>([]);
  const [participations, setParticipations] = useState<ChallengeParticipation[]>([]);
  const [leaderboards, setLeaderboards] = useState<Record<string, ChallengeLeaderboard>>({});
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch challenges
  const fetchChallenges = useCallback(async (filters?: any) => {
    try {
      setLoading(true);
      setError(null);
      const challengeList = await teamChallengesService.getChallenges({
        ...filters,
        organizationId,
        userId: user?.id
      });
      setChallenges(challengeList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch challenges');
    } finally {
      setLoading(false);
    }
  }, [organizationId, user?.id]);

  // Fetch user teams
  const fetchUserTeams = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setError(null);
      const teams = await teamChallengesService.getTeams({
        organizationId,
        userId: user.id
      });
      setUserTeams(teams);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch user teams');
    } finally {
      setLoading(false);
    }
  }, [user?.id, organizationId]);

  // Create a new challenge
  const createChallenge = useCallback(async (
    challengeData: Partial<TeamChallenge>
  ): Promise<TeamChallenge | null> => {
    try {
      setLoading(true);
      setError(null);
      const challenge = await teamChallengesService.createChallenge({
        ...challengeData,
        createdBy: user?.id || '',
        organizationId
      });
      
      // Add to challenges list
      setChallenges(prev => [challenge, ...prev]);
      
      return challenge;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create challenge');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user?.id, organizationId]);

  // Join a challenge
  const joinChallenge = useCallback(async (
    challengeId: string,
    participantId: string,
    participantType: 'individual' | 'team'
  ): Promise<ChallengeParticipation | null> => {
    try {
      setLoading(true);
      setError(null);
      const participation = await teamChallengesService.joinChallenge(
        challengeId,
        participantId,
        participantType
      );
      
      // Add to participations
      setParticipations(prev => [...prev, participation]);
      
      return participation;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join challenge');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Leave a challenge
  const leaveChallenge = useCallback(async (
    challengeId: string,
    participantId: string
  ): Promise<boolean> => {
    try {
      setLoading(true);
      setError(null);
      await teamChallengesService.leaveChallenge(challengeId, participantId);
      
      // Remove from participations
      setParticipations(prev => 
        prev.filter(p => !(p.challengeId === challengeId && p.participantId === participantId))
      );
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to leave challenge');
      return false;
    } finally {
      setLoading(false);
    }
  }, []);

  // Create a team
  const createTeam = useCallback(async (
    teamData: Partial<Team>
  ): Promise<Team | null> => {
    if (!user?.id) return null;
    
    try {
      setLoading(true);
      setError(null);
      const team = await teamChallengesService.createTeam({
        ...teamData,
        captainId: user.id,
        organizationId
      });
      
      // Add to user teams
      setUserTeams(prev => [...prev, team]);
      
      return team;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to create team');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user?.id, organizationId]);

  // Join a team
  const joinTeam = useCallback(async (teamId: string): Promise<Team | null> => {
    if (!user?.id) return null;
    
    try {
      setLoading(true);
      setError(null);
      const team = await teamChallengesService.joinTeam(teamId, user.id);
      
      // Update user teams
      setUserTeams(prev => {
        const existing = prev.find(t => t.id === teamId);
        if (existing) {
          return prev.map(t => t.id === teamId ? team : t);
        }
        return [...prev, team];
      });
      
      return team;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to join team');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Leave a team
  const leaveTeam = useCallback(async (teamId: string): Promise<boolean> => {
    if (!user?.id) return false;
    
    try {
      setLoading(true);
      setError(null);
      await teamChallengesService.leaveTeam(teamId, user.id);
      
      // Remove from user teams
      setUserTeams(prev => prev.filter(t => t.id !== teamId));
      
      return true;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to leave team');
      return false;
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Get challenge leaderboard
  const fetchChallengeLeaderboard = useCallback(async (
    challengeId: string,
    type: 'individual' | 'team' = 'individual'
  ) => {
    try {
      const leaderboard = await teamChallengesService.getChallengeLeaderboard(challengeId, type);
      
      // Update leaderboards cache
      setLeaderboards(prev => ({
        ...prev,
        [`${challengeId}_${type}`]: leaderboard
      }));
      
      return leaderboard;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch leaderboard');
      return null;
    }
  }, []);

  // Get challenge analytics
  const getChallengeAnalytics = useCallback(async (challengeId: string) => {
    try {
      return await teamChallengesService.getChallengeAnalytics(challengeId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get challenge analytics');
      return null;
    }
  }, []);

  // Get team analytics
  const getTeamAnalytics = useCallback(async (teamId: string) => {
    try {
      return await teamChallengesService.getTeamAnalytics(teamId);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get team analytics');
      return null;
    }
  }, []);

  // Auto-fetch data on mount
  useEffect(() => {
    if (autoFetch && user?.id) {
      Promise.all([
        fetchChallenges(),
        fetchUserTeams()
      ]);
    }
  }, [autoFetch, user?.id, fetchChallenges, fetchUserTeams]);

  // Computed values
  const activeChallenges = challenges.filter(c => c.status === 'active');
  const upcomingChallenges = challenges.filter(c => c.status === 'upcoming');
  const completedChallenges = challenges.filter(c => c.status === 'completed');

  const userParticipations = participations.filter(p => 
    p.participantType === 'individual' ? p.participantId === user?.id :
    userTeams.some(t => t.id === p.participantId)
  );

  const activeParticipations = userParticipations.filter(p => 
    p.status === 'active' || p.status === 'registered'
  );

  const teamChallenges = challenges.filter(c => 
    c.format !== 'individual_scores'
  );

  const individualChallenges = challenges.filter(c => 
    c.format === 'individual_scores'
  );

  return {
    // State
    challenges,
    userTeams,
    participations,
    leaderboards,
    loading,
    error,
    
    // Actions
    fetchChallenges,
    fetchUserTeams,
    createChallenge,
    joinChallenge,
    leaveChallenge,
    createTeam,
    joinTeam,
    leaveTeam,
    fetchChallengeLeaderboard,
    getChallengeAnalytics,
    getTeamAnalytics,
    
    // Computed values
    activeChallenges,
    upcomingChallenges,
    completedChallenges,
    userParticipations,
    activeParticipations,
    teamChallenges,
    individualChallenges,
    
    // Team info
    isTeamCaptain: (teamId: string) => {
      const team = userTeams.find(t => t.id === teamId);
      return team?.captainId === user?.id;
    },
    
    getUserTeam: (teamId: string) => {
      return userTeams.find(t => t.id === teamId);
    },
    
    getChallengeParticipation: (challengeId: string) => {
      return userParticipations.find(p => p.challengeId === challengeId);
    },
    
    // Utilities
    clearError: () => setError(null),
    refresh: () => {
      if (user?.id) {
        Promise.all([
          fetchChallenges(),
          fetchUserTeams()
        ]);
      }
    }
  };
}
