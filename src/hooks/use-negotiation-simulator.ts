"use client";

import { useState, useCallback, useEffect } from "react";
import { useToast } from "@/hooks/use-toast";
import { useFeatureAccess } from "@/hooks/use-feature-access";
import {
	negotiationSimulatorService,
	NegotiationSimulatorError,
} from "@/lib/services/negotiation-simulator-service";
import type {
	NegotiationScenario,
	NegotiationSession,
	CreateScenarioRequest,
	StartSessionRequest,
	MakeMoveRequest,
	SessionAnalytics,
} from "@/lib/types/negotiation-simulator";

// Hook for managing scenarios
export function useNegotiationScenarios() {
	const [scenarios, setScenarios] = useState<NegotiationScenario[]>([]);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const { toast } = useToast();
	const { canAccessFeature } = useFeatureAccess();

	const hasAccess = canAccessFeature("negotiation_simulator");

	const fetchScenarios = useCallback(
		async (params?: {
			industry?: string;
			difficulty?: string;
			contractType?: string;
		}) => {
			if (!hasAccess) return;

			setLoading(true);
			setError(null);

			try {
				const result = await negotiationSimulatorService.getScenarios(params);
				setScenarios(result.scenarios);
			} catch (error) {
				const errorMessage =
					error instanceof NegotiationSimulatorError
						? error.message
						: "Failed to fetch scenarios";
				setError(errorMessage);
			} finally {
				setLoading(false);
			}
		},
		[hasAccess]
	);

	const createScenario = useCallback(
		async (data: CreateScenarioRequest) => {
			if (!hasAccess) {
				toast({
					title: "Subscription Required",
					description:
						"Negotiation simulator feature requires PRO subscription",
					variant: "destructive",
				});
				return null;
			}

			setLoading(true);
			try {
				const scenario = await negotiationSimulatorService.createScenario(data);
				setScenarios((prev) => [scenario, ...prev]);

				toast({
					title: "Scenario Created",
					description: `"${scenario.name}" has been created successfully`,
				});

				return scenario;
			} catch (error) {
				const errorMessage =
					error instanceof NegotiationSimulatorError
						? error.message
						: "Failed to create scenario";

				toast({
					title: "Creation Failed",
					description: errorMessage,
					variant: "destructive",
				});

				return null;
			} finally {
				setLoading(false);
			}
		},
		[hasAccess, toast]
	);

	const deleteScenario = useCallback(
		async (scenarioId: string) => {
			try {
				await negotiationSimulatorService.deleteScenario(scenarioId);
				setScenarios((prev) => prev.filter((s) => s.id !== scenarioId));

				toast({
					title: "Scenario Deleted",
					description: "Scenario has been deleted successfully",
				});
			} catch (error) {
				const errorMessage =
					error instanceof NegotiationSimulatorError
						? error.message
						: "Failed to delete scenario";

				toast({
					title: "Deletion Failed",
					description: errorMessage,
					variant: "destructive",
				});
			}
		},
		[toast]
	);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	useEffect(() => {
		fetchScenarios();
	}, [fetchScenarios]);

	return {
		scenarios,
		loading,
		error,
		hasAccess,
		fetchScenarios,
		createScenario,
		deleteScenario,
		clearError,
	};
}

// Hook for managing active negotiation session
export function useNegotiationSession(sessionId?: string) {
	const [session, setSession] = useState<NegotiationSession | null>(null);
	const [loading, setLoading] = useState(false);
	const [makingMove, setMakingMove] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const { toast } = useToast();
	const { canAccessFeature } = useFeatureAccess();

	const hasAccess = canAccessFeature("negotiation_simulator");

	const fetchSession = useCallback(async () => {
		if (!sessionId || !hasAccess) return;

		setLoading(true);
		setError(null);

		try {
			const sessionData = await negotiationSimulatorService.getSession(
				sessionId
			);
			setSession(sessionData);
		} catch (error) {
			const errorMessage =
				error instanceof NegotiationSimulatorError
					? error.message
					: "Failed to fetch session";
			setError(errorMessage);
		} finally {
			setLoading(false);
		}
	}, [sessionId, hasAccess]);

	const startSession = useCallback(
		async (data: StartSessionRequest) => {
			if (!hasAccess) {
				toast({
					title: "Subscription Required",
					description:
						"Negotiation simulator feature requires PRO subscription",
					variant: "destructive",
				});
				return null;
			}

			setLoading(true);
			try {
				// TODO: Add credit usage at component level
				const newSession = await negotiationSimulatorService.startSession(data);
				setSession(newSession);

				toast({
					title: "Session Started",
					description: "Your negotiation session has begun!",
				});

				return newSession;
			} catch (error) {
				const errorMessage =
					error instanceof NegotiationSimulatorError
						? error.message
						: "Failed to start session";

				toast({
					title: "Session Failed",
					description: errorMessage,
					variant: "destructive",
				});

				return null;
			} finally {
				setLoading(false);
			}
		},
		[hasAccess, toast]
	);

	const makeMove = useCallback(
		async (data: MakeMoveRequest) => {
			if (!session) return;

			setMakingMove(true);
			try {
				const updatedSession = await negotiationSimulatorService.makeMove(
					session.id,
					data
				);
				setSession(updatedSession);

				toast({
					title: "Move Made",
					description:
						"Your move has been submitted. Waiting for AI response...",
				});

				return updatedSession;
			} catch (error) {
				const errorMessage =
					error instanceof NegotiationSimulatorError
						? error.message
						: "Failed to make move";

				toast({
					title: "Move Failed",
					description: errorMessage,
					variant: "destructive",
				});

				return null;
			} finally {
				setMakingMove(false);
			}
		},
		[session, toast]
	);

	const pauseSession = useCallback(async () => {
		if (!session) return;

		try {
			const updatedSession = await negotiationSimulatorService.pauseSession(
				session.id
			);
			setSession(updatedSession);

			toast({
				title: "Session Paused",
				description: "You can resume this session later",
			});
		} catch (error) {
			const errorMessage =
				error instanceof NegotiationSimulatorError
					? error.message
					: "Failed to pause session";

			toast({
				title: "Pause Failed",
				description: errorMessage,
				variant: "destructive",
			});
		}
	}, [session, toast]);

	const resumeSession = useCallback(async () => {
		if (!session) return;

		try {
			const updatedSession = await negotiationSimulatorService.resumeSession(
				session.id
			);
			setSession(updatedSession);

			toast({
				title: "Session Resumed",
				description: "Welcome back! Continue your negotiation",
			});
		} catch (error) {
			const errorMessage =
				error instanceof NegotiationSimulatorError
					? error.message
					: "Failed to resume session";

			toast({
				title: "Resume Failed",
				description: errorMessage,
				variant: "destructive",
			});
		}
	}, [session, toast]);

	const abandonSession = useCallback(async () => {
		if (!session) return;

		try {
			await negotiationSimulatorService.abandonSession(session.id);
			setSession(null);

			toast({
				title: "Session Abandoned",
				description: "The negotiation session has been ended",
			});
		} catch (error) {
			const errorMessage =
				error instanceof NegotiationSimulatorError
					? error.message
					: "Failed to abandon session";

			toast({
				title: "Error",
				description: errorMessage,
				variant: "destructive",
			});
		}
	}, [session, toast]);

	const clearError = useCallback(() => {
		setError(null);
	}, []);

	useEffect(() => {
		if (sessionId) {
			fetchSession();
		}
	}, [fetchSession]);

	return {
		session,
		loading,
		makingMove,
		error,
		hasAccess,
		startSession,
		makeMove,
		pauseSession,
		resumeSession,
		abandonSession,
		fetchSession,
		clearError,
	};
}

// Hook for analytics
export function useNegotiationAnalytics() {
	const [analytics, setAnalytics] = useState<SessionAnalytics | null>(null);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const { canAccessFeature } = useFeatureAccess();

	const hasAccess = canAccessFeature("negotiation_simulator");

	const fetchAnalytics = useCallback(async () => {
		if (!hasAccess) return;

		setLoading(true);
		setError(null);

		try {
			const data = await negotiationSimulatorService.getAnalytics();
			setAnalytics(data);
		} catch (error) {
			const errorMessage =
				error instanceof NegotiationSimulatorError
					? error.message
					: "Failed to fetch analytics";
			setError(errorMessage);
		} finally {
			setLoading(false);
		}
	}, [hasAccess]);

	useEffect(() => {
		fetchAnalytics();
	}, [fetchAnalytics]);

	return {
		analytics,
		loading,
		error,
		hasAccess,
		fetchAnalytics,
	};
}
