import { useState, useEffect } from 'react';
import { folderService } from '@/lib/services/folder-service';
import { documentService, type DocumentMetadata } from '@/lib/services/document-service';
import { useToast } from '@/hooks/use-toast';
import type { Folder } from '@/lib/types/document-organization';

export function useFolderDocuments(initialDocumentId?: string) {
  const [folders, setFolders] = useState<Folder[]>([]);
  const [documents, setDocuments] = useState<DocumentMetadata[]>([]);
  const [selectedDocument, setSelectedDocument] = useState<DocumentMetadata | null>(null);
  const [selectedFolderId, setSelectedFolderId] = useState<string | undefined>();
  const [loading, setLoading] = useState(true);
  const [loadingDocuments, setLoadingDocuments] = useState(true);
  const [addingToFolder, setAddingToFolder] = useState(false);
  const { toast } = useToast();

  // Load folders
  useEffect(() => {
    const loadFolders = async () => {
      try {
        setLoading(true);
        const allFolders = await folderService.getFolders();
        setFolders(allFolders);

        if (initialDocumentId) {
          const docFolders = await folderService.getFoldersForDocument(initialDocumentId);
          if (docFolders.length > 0) {
            setSelectedFolderId(docFolders[0].id);
          }
        }
      } catch (error) {
        console.error("Error loading folders:", error);
        toast({
          title: "Error loading folders",
          description: "Could not load folders. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoading(false);
      }
    };

    loadFolders();
  }, [initialDocumentId, toast]);

  // Load documents
  useEffect(() => {
    const loadDocuments = async () => {
      try {
        setLoadingDocuments(true);
        const docs = await documentService.getDocuments();
        setDocuments(docs);
        
        // If we have an initial document ID, select that document
        if (initialDocumentId) {
          const doc = docs.find(d => d.id === initialDocumentId);
          if (doc) {
            setSelectedDocument(doc);
          }
        }
      } catch (error) {
        console.error("Error loading documents:", error);
        toast({
          title: "Error loading documents",
          description: "Could not load documents. Please try again.",
          variant: "destructive",
        });
      } finally {
        setLoadingDocuments(false);
      }
    };

    loadDocuments();
  }, [initialDocumentId, toast]);

  // Add document to folder
  const addDocumentToFolder = async (folderId: string, docId: string | string[]) => {
    const documentIds = Array.isArray(docId) ? docId : [docId];
    
    if (!documentIds.length) {
      toast({
        title: "No document selected",
        description: "Please select at least one document before adding to a folder",
        variant: "destructive",
      });
      return;
    }
    
    try {
      setAddingToFolder(true);
      console.log(`Adding document(s) to folder ${folderId}:`, documentIds);
      
      // Make the API call
      const result = await folderService.addDocumentToFolder(folderId, documentIds);
      console.log("API response:", result);
      
      // Show success toast
      toast({
        title: "Document added to folder",
        description: documentIds.length > 1
          ? `${documentIds.length} documents successfully added to folder`
          : `Document successfully added to ${folders.find(f => f.id === folderId)?.name || "folder"}`,
        variant: "default",
      });
      
      // Refresh folders to show updated state
      const allFolders = await folderService.getFolders();
      setFolders(allFolders);
      
      // If we have a single document ID, update the selected folder
      if (!Array.isArray(docId) && docId) {
        const docFolders = await folderService.getFoldersForDocument(docId);
        console.log("Document folders after adding:", docFolders);
        if (docFolders.length > 0) {
          setSelectedFolderId(docFolders[0].id);
        }
      }
    } catch (error) {
      console.error(`Error adding document(s) to folder ${folderId}:`, error);
      
      // Show error toast with more details
      toast({
        title: "Error adding document to folder",
        description: error instanceof Error 
          ? `Error: ${error.message}. Please try again.` 
          : "Unknown error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setAddingToFolder(false);
    }
  };

  // Remove documents from folder
  const removeDocumentsFromFolder = async (folderId: string, documentIds: string[]) => {
    if (!documentIds.length) return;
    
    try {
      setAddingToFolder(true); // Reuse the loading state
      
      // Make the API call
      await folderService.removeDocumentsFromFolder(folderId, documentIds);
      
      // Show success toast
      toast({
        title: "Document removed from folder",
        description: documentIds.length > 1
          ? `${documentIds.length} documents removed from folder`
          : `Document removed from ${folders.find(f => f.id === folderId)?.name || "folder"}`,
        variant: "default",
      });
      
      // Refresh folders to show updated state
      const allFolders = await folderService.getFolders();
      setFolders(allFolders);
    } catch (error) {
      console.error(`Error removing document(s) from folder ${folderId}:`, error);
      
      // Show error toast
      toast({
        title: "Error removing document from folder",
        description: error instanceof Error 
          ? `Error: ${error.message}. Please try again.` 
          : "Unknown error occurred. Please try again.",
        variant: "destructive",
      });
    } finally {
      setAddingToFolder(false);
    }
  };

  return {
    folders,
    documents,
    selectedDocument,
    setSelectedDocument,
    selectedFolderId,
    setSelectedFolderId,
    loading,
    loadingDocuments,
    addingToFolder,
    addDocumentToFolder,
    removeDocumentsFromFolder,
    refreshFolders: async () => {
      const allFolders = await folderService.getFolders();
      setFolders(allFolders);
    }
  };
}
