import { useContext } from 'react';
import type { AuthContextType } from '@/lib/auth/auth-context';
import { AuthContext } from '@/lib/auth/auth-context';

export const useUser = () => {
  const context = useContext(AuthContext) as AuthContextType;
  
  if (!context) {
    throw new Error('useUser must be used within an AuthProvider');
  }
  
  return {
    user: context.user,
    isAuthenticated: context.isAuthenticated
  };
};