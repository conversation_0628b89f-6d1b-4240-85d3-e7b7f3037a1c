"use client";

import { useState, useCallback, useEffect } from 'react';
import { useToast } from '@/hooks/use-toast';
import { useFeatureAccess } from '@/hooks/use-feature-access';
import { privilegeLogService, PrivilegeLogError } from '@/lib/services/privilege-log-service';
import type {
  PrivilegeAnalysisResult,
  PrivilegeLogEntry,
  AnalysisOptions,
  PrivilegeStatus
} from '@/lib/types/privilege-log';

interface UsePrivilegeLogState {
  privilegeLog: PrivilegeLogEntry | null;
  analysisResult: PrivilegeAnalysisResult | null;
  loading: boolean;
  analyzing: boolean;
  error: string | null;
  hasExisting: boolean;
}

interface UsePrivilegeLogResult extends UsePrivilegeLogState {
  analyzeDocument: (options: AnalysisOptions) => Promise<void>;
  reviewContent: (contentId: string, status: PrivilegeStatus, reason?: string, applyRedaction?: boolean) => Promise<void>;
  applyRedaction: (contentId: string, reason: string, redactionText?: string) => Promise<void>;
  applyBulkRedaction: (contentIds: string[], reason: string, redactionText?: string) => Promise<void>;
  refresh: () => Promise<void>;
  clearError: () => void;
  clearAnalysisResult: () => void;
}

export function usePrivilegeLog(documentId: string): UsePrivilegeLogResult {
  const { toast } = useToast();
  const { canAccessFeature } = useFeatureAccess();
  
  const [state, setState] = useState<UsePrivilegeLogState>({
    privilegeLog: null,
    analysisResult: null,
    loading: false,
    analyzing: false,
    error: null,
    hasExisting: false,
  });

  // Check if user has access to privilege log automation feature
  const hasAccess = canAccessFeature('privilege_log_automation');

  const fetchPrivilegeLog = useCallback(async () => {
    if (!documentId || !hasAccess) return;

    setState(prev => ({ ...prev, loading: true, error: null }));

    try {
      const log = await privilegeLogService.getPrivilegeLog(documentId);
      setState(prev => ({
        ...prev,
        privilegeLog: log,
        hasExisting: !!log,
        loading: false,
      }));
    } catch (error) {
      const errorMessage = error instanceof PrivilegeLogError 
        ? error.message 
        : 'Failed to fetch privilege log';
      
      setState(prev => ({
        ...prev,
        error: errorMessage,
        loading: false,
      }));
    }
  }, [documentId, hasAccess]);

  const analyzeDocument = useCallback(
    async (options: AnalysisOptions) => {
      if (!documentId) {
        toast({
          title: "Error",
          description: "No document ID provided",
          variant: "destructive",
        });
        return;
      }

      if (!hasAccess) {
        toast({
          title: "Subscription Required",
          description: "Privilege log automation feature requires PRO subscription",
          variant: "destructive",
        });
        return;
      }

      setState(prev => ({ ...prev, analyzing: true, error: null }));

      try {
        const result = await privilegeLogService.analyzeDocument(documentId, options);
        
        setState(prev => ({
          ...prev,
          analysisResult: result,
          analyzing: false,
        }));

        toast({
          title: "Analysis Complete",
          description: `Found ${result.summary.totalItemsFound} privileged items`,
        });

        // Refresh the privilege log to get updated data
        await fetchPrivilegeLog();
      } catch (error) {
        let errorMessage = 'Failed to analyze document for privilege';
        
        if (error instanceof PrivilegeLogError) {
          errorMessage = error.message;
          
          // Handle specific error cases
          if (error.code === 'FEATURE_NOT_AVAILABLE') {
            toast({
              title: "Subscription Required",
              description: error.message,
              variant: "destructive",
            });
            return;
          }
        }
        
        setState(prev => ({
          ...prev,
          error: errorMessage,
          analyzing: false,
        }));

        toast({
          title: "Analysis Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    [documentId, hasAccess, toast, fetchPrivilegeLog]
  );

  const reviewContent = useCallback(
    async (contentId: string, status: PrivilegeStatus, reason?: string, applyRedaction?: boolean) => {
      if (!documentId) return;

      try {
        await privilegeLogService.reviewContent(documentId, contentId, {
          status,
          reason,
          applyRedaction
        });

        toast({
          title: "Review Complete",
          description: `Content marked as ${status}`,
        });

        // Refresh the privilege log
        await fetchPrivilegeLog();
      } catch (error) {
        const errorMessage = error instanceof PrivilegeLogError 
          ? error.message 
          : 'Failed to review content';

        setState(prev => ({ ...prev, error: errorMessage }));
        
        toast({
          title: "Review Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    [documentId, toast, fetchPrivilegeLog]
  );

  const applyRedaction = useCallback(
    async (contentId: string, reason: string, redactionText?: string) => {
      if (!documentId) return;

      try {
        await privilegeLogService.applyRedaction(documentId, {
          contentId,
          reason,
          redactionText
        });

        toast({
          title: "Redaction Applied",
          description: "Content has been redacted successfully",
        });

        // Refresh the privilege log
        await fetchPrivilegeLog();
      } catch (error) {
        const errorMessage = error instanceof PrivilegeLogError 
          ? error.message 
          : 'Failed to apply redaction';

        setState(prev => ({ ...prev, error: errorMessage }));
        
        toast({
          title: "Redaction Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    [documentId, toast, fetchPrivilegeLog]
  );

  const applyBulkRedaction = useCallback(
    async (contentIds: string[], reason: string, redactionText?: string) => {
      if (!documentId || contentIds.length === 0) return;

      try {
        const result = await privilegeLogService.applyBulkRedaction(documentId, {
          contentIds,
          reason,
          redactionText
        });

        toast({
          title: "Bulk Redaction Complete",
          description: `Applied ${result.data.redactedCount} redactions`,
        });

        // Refresh the privilege log
        await fetchPrivilegeLog();
      } catch (error) {
        const errorMessage = error instanceof PrivilegeLogError 
          ? error.message 
          : 'Failed to apply bulk redactions';

        setState(prev => ({ ...prev, error: errorMessage }));
        
        toast({
          title: "Bulk Redaction Failed",
          description: errorMessage,
          variant: "destructive",
        });
      }
    },
    [documentId, toast, fetchPrivilegeLog]
  );

  const refresh = useCallback(async () => {
    await fetchPrivilegeLog();
  }, [fetchPrivilegeLog]);

  const clearError = useCallback(() => {
    setState(prev => ({ ...prev, error: null }));
  }, []);

  const clearAnalysisResult = useCallback(() => {
    setState(prev => ({ ...prev, analysisResult: null }));
  }, []);

  // Fetch privilege log on mount and when documentId changes
  useEffect(() => {
    fetchPrivilegeLog();
  }, [fetchPrivilegeLog]);

  return {
    ...state,
    analyzeDocument,
    reviewContent,
    applyRedaction,
    applyBulkRedaction,
    refresh,
    clearError,
    clearAnalysisResult,
  };
}
