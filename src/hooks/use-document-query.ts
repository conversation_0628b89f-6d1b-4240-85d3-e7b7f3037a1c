import { useQuery, useQueryClient } from '@tanstack/react-query';
import { documentService } from '@/lib/services/document-service';
import { useToast } from './use-toast';
import { useState, useCallback, useMemo } from 'react';

// Query keys for document-related queries
export const documentKeys = {
  all: ['documents'] as const,
  lists: () => [...documentKeys.all, 'list'] as const,
  list: (filters: Record<string, unknown>) => [...documentKeys.lists(), { ...filters }] as const,
  details: () => [...documentKeys.all, 'detail'] as const,
  detail: (id: string) => [...documentKeys.details(), id] as const,
};

/**
 * Hook for fetching and managing documents with React Query
 */
export function useDocumentQuery() {
  const queryClient = useQueryClient();
  const { toast } = useToast();
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [isDeleting, setIsDeleting] = useState(false);

  // Fetch documents with pagination
  const documentsQuery = useQuery({
    queryKey: [...documentKeys.lists(), { page, limit }],
    queryFn: async () => {
      try {
        return await documentService.getDocuments(page, limit);
      } catch (error) {
        console.error('Error fetching documents:', error);
        throw error;
      }
    },
    staleTime: 1000 * 60 * 5, // 5 minutes
  });

  // Custom delete function with manual state management
  const deleteDocument = useCallback(async (documentId: string) => {
    setIsDeleting(true);

    try {
      await documentService.deleteDocument(documentId);

      // Show success message
      toast({
        title: "Document Deleted",
        description: "The document has been successfully deleted.",
      });

      // Invalidate and refetch documents list
      queryClient.invalidateQueries({ queryKey: documentKeys.lists() });

    } catch (error) {
      toast({
        title: "Delete Failed",
        description: error instanceof Error ? error.message : "Failed to delete document",
        variant: "destructive"
      });
    } finally {
      setIsDeleting(false);
    }
  }, [queryClient, toast]);

  const nextPage = useCallback(() => {
    // Check against data from the query directly inside the callback
    if (documentsQuery.data?.hasNextPage) {
      setPage(prev => prev + 1);
    }
  }, [documentsQuery.data?.hasNextPage]); // Dependency: only need to know if next page exists

  const previousPage = useCallback(() => {
    // Check against data from the query directly inside the callback
    if (documentsQuery.data?.hasPreviousPage) {
      setPage(prev => prev - 1);
    }
  }, [documentsQuery.data?.hasPreviousPage]); // Dependency: only need to know if previous page exists

  const goToPage = useCallback((pageNumber: number) => {
    // Check against data from the query directly inside the callback
    const totalPages = documentsQuery.data?.totalPages || 1;
    if (pageNumber >= 1 && pageNumber <= totalPages) {
      setPage(pageNumber);
    }
  }, [documentsQuery.data?.totalPages]); // Dependency: totalPages

  const changeLimit = useCallback((newLimit: number) => {
    // No external dependencies needed here if logic is self-contained
    setLimit(newLimit);
    setPage(1); // Reset to first page when changing limit
  }, []); // Empty dependency array as setLimit/setPage are stable

  // Memoize the pagination object
  const pagination = useMemo(() => ({
    page: documentsQuery.data?.page || 1,
    limit: documentsQuery.data?.limit || 10,
    total: documentsQuery.data?.total || 0,
    totalPages: documentsQuery.data?.totalPages || 1,
    hasNextPage: documentsQuery.data?.hasNextPage || false,
    hasPreviousPage: documentsQuery.data?.hasPreviousPage || false,
    nextPage,
    previousPage,
    goToPage,
    changeLimit
  }), [
    documentsQuery.data?.page,
    documentsQuery.data?.limit,
    documentsQuery.data?.total,
    documentsQuery.data?.totalPages,
    documentsQuery.data?.hasNextPage,
    documentsQuery.data?.hasPreviousPage,
    nextPage, // Include memoized functions as dependencies
    previousPage,
    goToPage,
    changeLimit
  ]);

  return {
    documents: documentsQuery.data?.items || [],
    pagination, // Return the memoized object
    isLoading: documentsQuery.isLoading,
    isError: documentsQuery.isError,
    error: documentsQuery.error,
    refetch: documentsQuery.refetch,
    deleteDocument,
    isDeletingDocument: isDeleting
  };
}
