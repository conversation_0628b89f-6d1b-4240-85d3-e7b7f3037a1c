"use client"

import { useState } from 'react'
import { documentService } from '@/lib/services/document-service'
import type { DocumentAnalysis } from '@/lib/types/analysis'
import type { AnalysisEvolution } from '@/lib/types/analysis-evolution'
import { useToast } from '@/hooks/use-toast'
import { useFeatureAccess } from '@/hooks/use-feature-access'

export function useDocumentAnalysis() {
  const [isAnalysisPanelOpen, setIsAnalysisPanelOpen] = useState(false)
  const [currentAnalysis, setCurrentAnalysis] = useState<DocumentAnalysis | null>(null)
  const [analysisEvolution, setAnalysisEvolution] = useState<AnalysisEvolution | null>(null)
  const [isLoading, setIsLoading] = useState(false)
  const [isEvolutionLoading, setIsEvolutionLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const { toast } = useToast()
  const { canAccessFeature } = useFeatureAccess()
  const hasBasicAnalysisAccess = canAccessFeature('basic_analysis')

  const analyzeDocument = async (documentId: string, options?: { documentType?: string; query?: string }) => {
    if (!hasBasicAnalysisAccess) {
      const error = new Error('Subscription required: basic_analysis')
      setError(error)
      toast({
        title: "Subscription Required",
        description: "Basic analysis feature requires a subscription",
        variant: "destructive"
      })
      throw error
    }

    try {
      setIsLoading(true)
      setError(null)
      
      // First try to get the latest analysis results
      try {
        const existingAnalysis = await getAnalysisResults(documentId, { latest: true })
        setCurrentAnalysis(existingAnalysis)
        setIsAnalysisPanelOpen(true)
        return existingAnalysis
      } catch {
        // If no existing analysis, create a new one
        const analysis = await documentService.analyzeDocument(documentId, options)
        setCurrentAnalysis(analysis)
        setIsAnalysisPanelOpen(true)
        return analysis
      }
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to analyze document')
      setError(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const getAnalysisResults = async (documentId: string, options?: { latest?: boolean }) => {
    if (!hasBasicAnalysisAccess) {
      const error = new Error('Subscription required: basic_analysis')
      setError(error)
      toast({
        title: "Subscription Required",
        description: "Basic analysis feature requires a subscription",
        variant: "destructive"
      })
      throw error
    }

    try {
      setIsLoading(true)
      setError(null)
      
      const analysis = await documentService.getAnalysisResults(documentId, options)
      setCurrentAnalysis(analysis)
      return analysis
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to retrieve analysis results')
      setError(error)
      throw error
    } finally {
      setIsLoading(false)
    }
  }

  const getAnalysisEvolution = async (documentId: string) => {
    if (!hasBasicAnalysisAccess) {
      const error = new Error('Subscription required: basic_analysis')
      setError(error)
      toast({
        title: "Subscription Required",
        description: "Basic analysis feature requires a subscription",
        variant: "destructive"
      })
      throw error
    }

    try {
      setIsEvolutionLoading(true)
      setError(null)
      
      const evolution = await documentService.getAnalysisEvolution(documentId)
      setAnalysisEvolution(evolution)
      return evolution
    } catch (err) {
      const error = err instanceof Error ? err : new Error('Failed to retrieve analysis evolution data')
      setError(error)
      throw error
    } finally {
      setIsEvolutionLoading(false)
    }
  }

  const openAnalysisPanel = () => {
    setIsAnalysisPanelOpen(true)
  }

  const closeAnalysisPanel = () => {
    setIsAnalysisPanelOpen(false)
  }

  const toggleAnalysisPanel = () => {
    setIsAnalysisPanelOpen(prev => !prev)
  }

  return {
    isAnalysisPanelOpen,
    currentAnalysis,
    analysisEvolution,
    isLoading,
    isEvolutionLoading,
    error,
    analyzeDocument,
    getAnalysisResults,
    getAnalysisEvolution,
    openAnalysisPanel,
    closeAnalysisPanel,
    toggleAnalysisPanel,
    hasBasicAnalysisAccess
  }
}
