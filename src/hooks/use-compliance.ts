import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { complianceService, ComplianceError } from '../lib/services/compliance-service';
import {
  ComplianceAuditResult,
  ComplianceProfile,
  ComplianceAnalytics,
  RegulatoryFramework,
  AuditRequest,
  CreateProfileRequest,
} from '../lib/types/compliance';
import { useToast } from './use-toast';

// Hook for auditing documents
export function useAuditDocument() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: AuditRequest) => complianceService.auditDocument(request),
    onSuccess: (data) => {
      toast({
        title: "Audit Completed",
        description: `Document audit completed with score: ${data.overallScore.toFixed(1)}`,
      });
      // Invalidate audit results to refresh the list
      queryClient.invalidateQueries({ queryKey: ['compliance', 'audit-results'] });
    },
    onError: (error: ComplianceError) => {
      toast({
        title: "Audit Failed",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Hook for fetching audit results
export function useAuditResults(params?: {
  frameworks?: string[];
  status?: string;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortOrder?: 'asc' | 'desc';
}) {
  return useQuery({
    queryKey: ['compliance', 'audit-results', params],
    queryFn: () => complianceService.getAuditResults(params),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for fetching a specific audit result
export function useAuditResult(resultId: string) {
  return useQuery({
    queryKey: ['compliance', 'audit-result', resultId],
    queryFn: () => complianceService.getAuditResult(resultId),
    enabled: !!resultId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for creating compliance profiles
export function useCreateProfile() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (request: CreateProfileRequest) => complianceService.createProfile(request),
    onSuccess: (data) => {
      toast({
        title: "Profile Created",
        description: `Compliance profile "${data.name}" has been created successfully.`,
      });
      // Invalidate profiles to refresh the list
      queryClient.invalidateQueries({ queryKey: ['compliance', 'profiles'] });
    },
    onError: (error: ComplianceError) => {
      toast({
        title: "Failed to Create Profile",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Hook for fetching compliance profiles
export function useComplianceProfiles(params?: {
  industry?: string;
  page?: number;
  limit?: number;
}) {
  return useQuery({
    queryKey: ['compliance', 'profiles', params],
    queryFn: () => complianceService.getProfiles(params),
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for fetching a specific compliance profile
export function useComplianceProfile(profileId: string) {
  return useQuery({
    queryKey: ['compliance', 'profile', profileId],
    queryFn: () => complianceService.getProfile(profileId),
    enabled: !!profileId,
    staleTime: 10 * 60 * 1000, // 10 minutes
  });
}

// Hook for updating compliance profiles
export function useUpdateProfile() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ profileId, updates }: { profileId: string; updates: Partial<CreateProfileRequest> }) =>
      complianceService.updateProfile(profileId, updates),
    onSuccess: (data) => {
      toast({
        title: "Profile Updated",
        description: `Compliance profile "${data.name}" has been updated successfully.`,
      });
      // Invalidate specific profile and profiles list
      queryClient.invalidateQueries({ queryKey: ['compliance', 'profile', data.id] });
      queryClient.invalidateQueries({ queryKey: ['compliance', 'profiles'] });
    },
    onError: (error: ComplianceError) => {
      toast({
        title: "Failed to Update Profile",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Hook for deleting compliance profiles
export function useDeleteProfile() {
  const { toast } = useToast();
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (profileId: string) => complianceService.deleteProfile(profileId),
    onSuccess: () => {
      toast({
        title: "Profile Deleted",
        description: "Compliance profile has been deleted successfully.",
      });
      // Invalidate profiles to refresh the list
      queryClient.invalidateQueries({ queryKey: ['compliance', 'profiles'] });
    },
    onError: (error: ComplianceError) => {
      toast({
        title: "Failed to Delete Profile",
        description: error.message,
        variant: "destructive",
      });
    },
  });
}

// Hook for fetching regulatory frameworks
export function useRegulatoryFrameworks() {
  return useQuery({
    queryKey: ['compliance', 'frameworks'],
    queryFn: () => complianceService.getFrameworks(),
    staleTime: 60 * 60 * 1000, // 1 hour (frameworks don't change often)
  });
}

// Hook for fetching framework rules
export function useFrameworkRules(regulationId: string) {
  return useQuery({
    queryKey: ['compliance', 'framework-rules', regulationId],
    queryFn: () => complianceService.getFrameworkRules(regulationId),
    enabled: !!regulationId,
    staleTime: 60 * 60 * 1000, // 1 hour
  });
}

// Hook for fetching compliance analytics
export function useComplianceAnalytics() {
  return useQuery({
    queryKey: ['compliance', 'analytics'],
    queryFn: () => complianceService.getAnalytics(),
    staleTime: 5 * 60 * 1000, // 5 minutes
  });
}

// Hook for managing compliance audit state
export function useComplianceAudit() {
  const [selectedFrameworks, setSelectedFrameworks] = useState<string[]>([]);
  const [selectedProfile, setSelectedProfile] = useState<string | undefined>();
  const [auditOptions, setAuditOptions] = useState({
    includeRecommendations: true,
    detailedAnalysis: true,
    riskThreshold: 'medium' as 'low' | 'medium' | 'high',
  });

  const auditMutation = useAuditDocument();

  const startAudit = (documentId: string) => {
    if (selectedFrameworks.length === 0) {
      throw new Error('Please select at least one regulatory framework');
    }

    const request: AuditRequest = {
      documentId,
      frameworks: selectedFrameworks,
      profileId: selectedProfile,
      options: auditOptions,
    };

    return auditMutation.mutate(request);
  };

  return {
    selectedFrameworks,
    setSelectedFrameworks,
    selectedProfile,
    setSelectedProfile,
    auditOptions,
    setAuditOptions,
    startAudit,
    isAuditing: auditMutation.isPending,
    auditError: auditMutation.error,
    auditResult: auditMutation.data,
  };
}
