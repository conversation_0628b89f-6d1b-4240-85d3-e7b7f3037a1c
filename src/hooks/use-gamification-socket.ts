"use client";

import { useEffect, useRef, useCallback, useState } from 'react';
import { io, Socket } from 'socket.io-client';
import { Achievement, LevelUpdate, PressureEvent } from '@/lib/services/gamification-service';
import { useUser } from '@/hooks/use-user';

export interface GamificationSocketEvents {
  achievement_unlocked: (achievement: Achievement) => void;
  level_up: (levelUpdate: LevelUpdate) => void;
  xp_gained: (data: { amount: number; source: string; total: number }) => void;
  
  // Session events
  pressure_event: (event: PressureEvent) => void;
  session_score_update: (data: { score: number; breakdown: any }) => void;
  relationship_updated: (data: { characterId: string; changes: any }) => void;
  
  // Social events
  leaderboard_updated: (data: { timeframe: string; userRank: number }) => void;
  challenge_update: (data: { challengeId: string; progress: any }) => void;
  
  // Connection events
  connect: () => void;
  disconnect: () => void;
  error: (error: any) => void;
}

export interface UseGamificationSocketOptions {
  autoConnect?: boolean;
  reconnectAttempts?: number;
  reconnectDelay?: number;
}

export function useGamificationSocket(options: UseGamificationSocketOptions = {}) {
  const { autoConnect = true, reconnectAttempts = 5, reconnectDelay = 1000 } = options;
  const { user } = useUser();
  
  const socketRef = useRef<Socket | null>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [connectionState, setConnectionState] = useState<'disconnected' | 'connecting' | 'connected' | 'error'>('disconnected');
  const [error, setError] = useState<string | null>(null);

  // Event listeners storage
  const eventListenersRef = useRef<Map<keyof GamificationSocketEvents, Function[]>>(new Map());

  // Initialize socket connection
  const connect = useCallback(() => {
    setConnectionState('connected');
    setIsConnected(true);
    setError(null);
  }, []);

  const disconnect = useCallback(() => {
    setConnectionState('disconnected');
    setIsConnected(false);
  }, []);

  const joinSession = useCallback((sessionId: string) => {
    console.log('Mock: Joining session', sessionId);
  }, []);

  const leaveSession = useCallback((sessionId: string) => {
    console.log('Mock: Leaving session', sessionId);
  }, []);

  const requestLiveScore = useCallback((sessionId: string, moveData: any) => {
    console.log('Mock: Requesting live score', sessionId, moveData);
  }, []);

  const acknowledgePressureEvent = useCallback((sessionId: string, eventId: string) => {
    console.log('Mock: Acknowledging pressure event', sessionId, eventId);
  }, []);

  const on = useCallback((event: any, listener: any) => {
    console.log('Mock: Adding event listener', event);
  }, []);

  const off = useCallback((event: any, listener: any) => {
    console.log('Mock: Removing event listener', event);
  }, []);

  return {
    isConnected,
    connectionState,
    error,
    connect,
    disconnect,
    joinSession,
    leaveSession,
    requestLiveScore,
    acknowledgePressureEvent,
    on,
    off,
    clearError: () => setError(null),
    getSocket: () => null,
  };
}

// Convenience hooks for specific events
export function useAchievementNotifications() {
  const [achievements, setAchievements] = useState<Achievement[]>([]);
  const [levelUpdates, setLevelUpdates] = useState<LevelUpdate[]>([]);

  return {
    achievements,
    levelUpdates,
    clearAchievements: () => setAchievements([]),
    clearLevelUpdates: () => setLevelUpdates([]),
  };
}

export function usePressureEvents() {
  const [events, setEvents] = useState<PressureEvent[]>([]);

  return {
    events,
    clearEvents: () => setEvents([]),
    acknowledgeEvent: (sessionId: string, eventId: string) => {
      console.log('Mock: Acknowledging pressure event', sessionId, eventId);
    },
  };
}
