"use client";

import { useState, useEffect, useCallback } from 'react';
import { 
  campaignService, 
  Campaign, 
  UserCampaignProgress, 
  CampaignChapter, 
  ChapterProgress,
  CampaignSession
} from '@/lib/services/campaign-service';
import { useUser } from '@/hooks/use-user';

export interface UseCampaignOptions {
  autoFetch?: boolean;
  campaignId?: string;
}

export function useCampaign(options: UseCampaignOptions = {}) {
  const { autoFetch = true, campaignId } = options;
  const { user } = useUser();
  
  // State
  const [campaigns, setCampaigns] = useState<Campaign[]>([]);
  const [currentCampaign, setCurrentCampaign] = useState<Campaign | null>(null);
  const [userProgress, setUserProgress] = useState<UserCampaignProgress | null>(null);
  const [chapters, setChapters] = useState<CampaignChapter[]>([]);
  const [currentChapter, setCurrentChapter] = useState<CampaignChapter | null>(null);
  const [chapterProgress, setChapterProgress] = useState<Record<string, ChapterProgress>>({});
  const [activeSessions, setActiveSessions] = useState<CampaignSession[]>([]);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);

  // Fetch all campaigns
  const fetchCampaigns = useCallback(async (filters?: any) => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setError(null);
      const campaignList = await campaignService.getCampaigns({
        ...filters,
        userId: user.id
      });
      setCampaigns(campaignList);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch campaigns');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Fetch available campaigns for user
  const fetchAvailableCampaigns = useCallback(async () => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setError(null);
      const availableCampaigns = await campaignService.getAvailableCampaigns(user.id);
      setCampaigns(availableCampaigns);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch available campaigns');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Fetch specific campaign
  const fetchCampaign = useCallback(async (id: string) => {
    if (!user?.id) return;
    
    try {
      setLoading(true);
      setError(null);
      const campaignData = await campaignService.getCampaign(id, user.id);
      setCurrentCampaign(campaignData);
      
      if (campaignData.userProgress) {
        setUserProgress(campaignData.userProgress);
      }
      
      // Set chapters
      if (campaignData.chapters) {
        setChapters(campaignData.chapters);
        
        // Build chapter progress map
        const progressMap: Record<string, ChapterProgress> = {};
        if (campaignData.userProgress?.chapterProgress) {
          Object.assign(progressMap, campaignData.userProgress.chapterProgress);
        }
        setChapterProgress(progressMap);
      }
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to fetch campaign');
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Start a campaign
  const startCampaign = useCallback(async (id: string): Promise<UserCampaignProgress | null> => {
    if (!user?.id) return null;
    
    try {
      setLoading(true);
      setError(null);
      const progress = await campaignService.startCampaign(user.id, id);
      setUserProgress(progress);
      
      // Refresh campaign data
      await fetchCampaign(id);
      
      return progress;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start campaign');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user?.id, fetchCampaign]);

  // Start a chapter
  const startChapter = useCallback(async (
    campaignId: string, 
    chapterId: string
  ): Promise<ChapterProgress | null> => {
    if (!user?.id) return null;
    
    try {
      setLoading(true);
      setError(null);
      const progress = await campaignService.startChapter(user.id, campaignId, chapterId);
      
      // Update chapter progress
      setChapterProgress(prev => ({
        ...prev,
        [chapterId]: progress
      }));
      
      // Find and set current chapter
      const chapter = chapters.find(c => c.id === chapterId);
      if (chapter) {
        setCurrentChapter(chapter);
      }
      
      return progress;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start chapter');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user?.id, chapters]);

  // Complete a chapter
  const completeChapter = useCallback(async (
    campaignId: string,
    chapterId: string,
    results: {
      score: number;
      completedObjectives: string[];
      timeSpent: number;
    }
  ): Promise<ChapterProgress | null> => {
    if (!user?.id) return null;
    
    try {
      setLoading(true);
      setError(null);
      const progress = await campaignService.completeChapter(
        user.id, 
        campaignId, 
        chapterId, 
        results
      );
      
      // Update chapter progress
      setChapterProgress(prev => ({
        ...prev,
        [chapterId]: progress
      }));
      
      // Update user progress
      if (userProgress) {
        const updatedProgress = {
          ...userProgress,
          completedChapters: [...userProgress.completedChapters, chapterId],
          chapterProgress: {
            ...userProgress.chapterProgress,
            [chapterId]: progress
          }
        };
        setUserProgress(updatedProgress);
      }
      
      return progress;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to complete chapter');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user?.id, userProgress]);

  // Start a campaign session
  const startCampaignSession = useCallback(async (
    campaignId: string,
    chapterId: string,
    scenarioId: string
  ): Promise<CampaignSession | null> => {
    if (!user?.id) return null;
    
    try {
      setLoading(true);
      setError(null);
      const session = await campaignService.startCampaignSession(
        user.id,
        campaignId,
        chapterId,
        scenarioId
      );
      
      // Add to active sessions
      setActiveSessions(prev => [...prev, session]);
      
      return session;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to start campaign session');
      return null;
    } finally {
      setLoading(false);
    }
  }, [user?.id]);

  // Complete a campaign session
  const completeCampaignSession = useCallback(async (
    sessionId: string,
    results: {
      score: number;
      completedObjectives: string[];
      sessionData: any;
    }
  ): Promise<CampaignSession | null> => {
    try {
      setLoading(true);
      setError(null);
      const session = await campaignService.completeCampaignSession(sessionId, results);
      
      // Update active sessions
      setActiveSessions(prev => 
        prev.map(s => s.id === sessionId ? session : s)
      );
      
      return session;
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to complete campaign session');
      return null;
    } finally {
      setLoading(false);
    }
  }, []);

  // Get campaign leaderboard
  const getCampaignLeaderboard = useCallback(async (
    campaignId: string,
    timeframe: 'weekly' | 'monthly' | 'all_time' = 'all_time'
  ) => {
    try {
      return await campaignService.getCampaignLeaderboard(campaignId, timeframe);
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to get campaign leaderboard');
      return [];
    }
  }, []);

  // Auto-fetch data on mount
  useEffect(() => {
    if (autoFetch && user?.id) {
      if (campaignId) {
        fetchCampaign(campaignId);
      } else {
        fetchAvailableCampaigns();
      }
    }
  }, [autoFetch, user?.id, campaignId, fetchCampaign, fetchAvailableCampaigns]);

  // Computed values
  const availableCampaigns = campaigns.filter(c => {
    if (!user?.id) return false;
    // Check prerequisites
    // This would need more complex logic based on user level, achievements, etc.
    return true;
  });

  const inProgressCampaigns = campaigns.filter(c =>
    c.userProgress?.status === 'in_progress'
  );

  const completedCampaigns = campaigns.filter(c =>
    c.userProgress?.status === 'completed'
  );

  const currentChapterProgress = currentChapter ? 
    chapterProgress[currentChapter.id] : null;

  const nextChapter = currentCampaign && userProgress ? 
    chapters.find(c => c.chapterNumber === userProgress.currentChapter + 1) : null;

  const canStartNextChapter = nextChapter && currentChapterProgress?.status === 'completed';

  return {
    // State
    campaigns,
    currentCampaign,
    userProgress,
    chapters,
    currentChapter,
    chapterProgress,
    activeSessions,
    loading,
    error,
    
    // Actions
    fetchCampaigns,
    fetchAvailableCampaigns,
    fetchCampaign,
    startCampaign,
    startChapter,
    completeChapter,
    startCampaignSession,
    completeCampaignSession,
    getCampaignLeaderboard,
    
    // Computed values
    availableCampaigns,
    inProgressCampaigns,
    completedCampaigns,
    currentChapterProgress,
    nextChapter,
    canStartNextChapter,
    
    // Progress calculations
    campaignProgress: userProgress ? {
      percentage: (userProgress.completedChapters.length / chapters.length) * 100,
      chaptersCompleted: userProgress.completedChapters.length,
      totalChapters: chapters.length,
      currentScore: userProgress.totalScore
    } : null,
    
    // Utilities
    clearError: () => setError(null),
    refresh: () => {
      if (campaignId) {
        fetchCampaign(campaignId);
      } else {
        fetchAvailableCampaigns();
      }
    }
  };
}
