import { NextRequest, NextResponse } from "next/server";
import { getStoredAuth } from "@/lib/auth/auth-service";
import { 
  sampleContractPlaybooks, 
  filterSampleContractPlaybooks,
  getSampleContractPlaybookStats 
} from "@/lib/data/sample-contract-playbooks";
import type { 
  SampleContractPlaybookFilters,
  SamplePlaybooksResponse,
  SampleContractPlaybook 
} from "@/lib/types/sample-playbooks";

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await getStoredAuth();
    if (!auth?.token) {
      return NextResponse.json(
        { statusCode: 401, message: "Authentication required", error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const filters: SampleContractPlaybookFilters = {
      contractType: searchParams.get("contractType") || undefined,
      industry: searchParams.get("industry") || undefined,
      riskProfile: searchParams.get("riskProfile") || undefined,
      tags: searchParams.get("tags") || undefined,
    };

    // Filter playbooks based on parameters
    const filteredPlaybooks = filterSampleContractPlaybooks(
      sampleContractPlaybooks,
      filters
    );

    // Get statistics for the filtered results
    const stats = getSampleContractPlaybookStats(filteredPlaybooks);

    // Prepare response
    const response: SamplePlaybooksResponse<SampleContractPlaybook> = {
      samples: filteredPlaybooks,
      total: filteredPlaybooks.length,
      contractTypeDistribution: stats.contractTypeDistribution,
      riskProfileDistribution: stats.riskProfileDistribution,
      industryDistribution: stats.industryDistribution,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching sample contract playbooks:", error);
    return NextResponse.json(
      {
        statusCode: 500,
        message: "Internal server error",
        error: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}
