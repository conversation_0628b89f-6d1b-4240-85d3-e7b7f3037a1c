import { NextRequest, NextResponse } from 'next/server';
import { BASE_API_URL } from '@/lib/config';
import { getStoredAuth } from '@/lib/auth/auth-service';
import { cookies } from 'next/headers';

/**
 * Server-side API route that proxies document render requests 
 * to avoid CORS issues and handle authentication securely.
 */
export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  const documentId = params.id;
  console.log(`[Proxy /api/documents/${documentId}/render] Route invoked.`);
  
  try {
    // Retrieve authentication token from cookies sent by the browser
    const cookieStore = await cookies();
    const authCookieName = 'auth'; // <<< --- VERIFY THIS COOKIE NAME
    const authCookie = cookieStore.get(authCookieName); 
    console.log(`[Proxy ${documentId}] Attempting to read cookie: ${authCookieName}`);
    
    let accessToken: string | undefined;
    if (authCookie) {
      try {
        const authData = JSON.parse(decodeURIComponent(authCookie.value));
        accessToken = authData?.accessToken;
        console.log(`[Proxy ${documentId}] Access token found in cookie: ${accessToken ? 'Yes' : 'No'}`);
      } catch (e) {
        console.error(`[Proxy ${documentId}] Failed to parse auth cookie:`, e);
      }
    } else {
      console.log(`[Proxy ${documentId}] Cookie '${authCookieName}' not found.`);
    }

    if (!accessToken) {
        // Fallback or error if no token found in cookies
        console.warn(`[Proxy ${documentId}] No access token found in cookies. Attempting legacy.`);
        // Attempt to get from legacy storage if applicable (might not work reliably in API routes)
        const legacyAuth = getStoredAuth(); 
        accessToken = legacyAuth?.accessToken;
        if (!accessToken) {
             console.error(`[Proxy ${documentId}] Authentication token not found in cookie or legacy storage.`);
             return NextResponse.json({ error: 'Authentication token not found' }, { status: 401 });
        }
        console.log(`[Proxy ${documentId}] Using access token from legacy storage.`);
    }
    
    // Prepare headers for the backend request
    const backendHeaders = new Headers();
    backendHeaders.append('Authorization', `Bearer ${accessToken}`);
    // Forward other relevant headers if needed, e.g., Accept
    const acceptHeader = request.headers.get('Accept') || '*/*';
    backendHeaders.append('Accept', acceptHeader);
    console.log(`[Proxy ${documentId}] Sending request to backend: ${BASE_API_URL}/documents/${documentId}/render`);
    console.log(`[Proxy ${documentId}] Backend request headers:`, { Authorization: 'Bearer ***', Accept: acceptHeader });

    // Make the request to the actual backend API
    const backendResponse = await fetch(`${BASE_API_URL}/documents/${documentId}/render`, {
      method: 'GET',
      headers: backendHeaders,
      // Important: Don't send browser cookies from the Next.js server to the backend unless necessary and secure
      // credentials: 'include', // Typically not needed/used when sending Authorization header
    });
    console.log(`[Proxy ${documentId}] Backend response status: ${backendResponse.status}`);
    console.log(`[Proxy ${documentId}] Backend response headers:`, Object.fromEntries(backendResponse.headers.entries()));
    
    // Handle backend errors
    if (!backendResponse.ok) {
        const errorBody = await backendResponse.text();
        console.error(`[Proxy ${documentId}] Backend error (${backendResponse.status}): ${errorBody}`);
      return NextResponse.json(
        { error: `Backend API failed: ${backendResponse.statusText}` },
        { status: backendResponse.status }
      );
    }
    
    // Stream the response back to the client
    const responseHeaders = new Headers();
    const contentType = backendResponse.headers.get('content-type');
    const contentDisposition = backendResponse.headers.get('content-disposition');
    const contentLength = backendResponse.headers.get('content-length');

    if (contentType) responseHeaders.set('content-type', contentType);
    if (contentDisposition) responseHeaders.set('content-disposition', contentDisposition);
    if (contentLength) responseHeaders.set('content-length', contentLength);
    console.log(`[Proxy ${documentId}] Streaming response to client with headers:`, Object.fromEntries(responseHeaders.entries()));

    // Use ReadableStream for efficient streaming
    const readableStream = backendResponse.body as ReadableStream<Uint8Array> | null;

    if (!readableStream) {
        console.error(`[Proxy ${documentId}] Backend response body is empty or null.`);
        return NextResponse.json({ error: 'Backend response body is empty' }, { status: 500 });
    }

    return new NextResponse(readableStream, {
        status: backendResponse.status,
        statusText: backendResponse.statusText,
        headers: responseHeaders,
    });

  } catch (error) {
    console.error(`[Proxy ${documentId}] Error in document render proxy route:`, error);
    return NextResponse.json(
      { error: 'Internal server error while fetching document' },
      { status: 500 }
    );
  }
}
