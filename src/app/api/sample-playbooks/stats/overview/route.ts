import { NextRequest, NextResponse } from "next/server";
import { getStoredAuth } from "@/lib/auth/auth-service";
import { 
  sampleNegotiationPlaybooks, 
  getSampleNegotiationPlaybookStats 
} from "@/lib/data/sample-negotiation-playbooks";
import type { SamplePlaybookStatsResponse } from "@/lib/types/sample-playbooks";

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await getStoredAuth();
    if (!auth?.token) {
      return NextResponse.json(
        { statusCode: 401, message: "Authentication required", error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Get statistics for all sample playbooks
    const stats = getSampleNegotiationPlaybookStats(sampleNegotiationPlaybooks);

    // Prepare response
    const response: SamplePlaybookStatsResponse = {
      totalSamples: stats.totalSamples,
      totalUserPlaybooks: 0, // This would come from database in real implementation
      contractTypeDistribution: stats.contractTypeDistribution,
      difficultyDistribution: stats.difficultyDistribution,
      industryDistribution: stats.industryDistribution,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching sample negotiation playbook stats:", error);
    return NextResponse.json(
      {
        statusCode: 500,
        message: "Internal server error",
        error: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}
