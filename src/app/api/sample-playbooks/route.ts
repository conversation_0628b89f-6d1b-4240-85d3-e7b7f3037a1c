import { NextRequest, NextResponse } from "next/server";
import { getStoredAuth } from "@/lib/auth/auth-service";
import { 
  sampleNegotiationPlaybooks, 
  filterSampleNegotiationPlaybooks,
  getSampleNegotiationPlaybookStats 
} from "@/lib/data/sample-negotiation-playbooks";
import type { 
  SampleNegotiationPlaybookFilters,
  SamplePlaybooksResponse,
  SampleNegotiationPlaybook 
} from "@/lib/types/sample-playbooks";

export async function GET(request: NextRequest) {
  try {
    // Check authentication
    const auth = await getStoredAuth();
    if (!auth?.token) {
      return NextResponse.json(
        { statusCode: 401, message: "Authentication required", error: "Unauthorized" },
        { status: 401 }
      );
    }

    // Parse query parameters
    const { searchParams } = new URL(request.url);
    const filters: SampleNegotiationPlaybookFilters = {
      contractType: searchParams.get("contractType") || undefined,
      industry: searchParams.get("industry") || undefined,
      difficulty: searchParams.get("difficulty") || undefined,
      tags: searchParams.get("tags") || undefined,
    };

    // Filter playbooks based on parameters
    const filteredPlaybooks = filterSampleNegotiationPlaybooks(
      sampleNegotiationPlaybooks,
      filters
    );

    // Get statistics for the filtered results
    const stats = getSampleNegotiationPlaybookStats(filteredPlaybooks);

    // Prepare response
    const response: SamplePlaybooksResponse<SampleNegotiationPlaybook> = {
      samples: filteredPlaybooks,
      total: filteredPlaybooks.length,
      contractTypeDistribution: stats.contractTypeDistribution,
      difficultyDistribution: stats.difficultyDistribution,
      industryDistribution: stats.industryDistribution,
    };

    return NextResponse.json(response);
  } catch (error) {
    console.error("Error fetching sample negotiation playbooks:", error);
    return NextResponse.json(
      {
        statusCode: 500,
        message: "Internal server error",
        error: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}
