import { NextRequest, NextResponse } from "next/server";
import { getStoredAuth } from "@/lib/auth/auth-service";
import { sampleNegotiationPlaybooks } from "@/lib/data/sample-negotiation-playbooks";
import type { ClonedNegotiationPlaybook } from "@/lib/types/sample-playbooks";

interface RouteParams {
  params: {
    id: string;
    documentId: string;
  };
}

export async function POST(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const auth = await getStoredAuth();
    if (!auth?.token) {
      return NextResponse.json(
        { statusCode: 401, message: "Authentication required", error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id, documentId } = params;

    // Find the sample playbook
    const samplePlaybook = sampleNegotiationPlaybooks.find(p => p.id === id);

    if (!samplePlaybook) {
      return NextResponse.json(
        {
          statusCode: 404,
          message: `Sample playbook with ID '${id}' not found`,
          error: "Not Found",
        },
        { status: 404 }
      );
    }

    // Create a cloned playbook response
    // In a real implementation, this would save to the database
    const clonedPlaybook: ClonedNegotiationPlaybook = {
      _id: `cloned_${Date.now()}`, // Mock ID
      documentId,
      organizationId: auth.user?.organizationId || "default-org",
      name: `${samplePlaybook.name} - Copy`,
      isTemplate: false,
      strategies: samplePlaybook.strategies,
      simulationScenarios: samplePlaybook.simulationScenarios,
      overallAssessment: samplePlaybook.overallAssessment,
      createdAt: new Date().toISOString(),
      metadata: {
        clonedFrom: samplePlaybook.id,
        clonedAt: new Date().toISOString(),
      },
    };

    return NextResponse.json(clonedPlaybook);
  } catch (error) {
    console.error("Error cloning sample negotiation playbook:", error);
    return NextResponse.json(
      {
        statusCode: 500,
        message: "Internal server error",
        error: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}
