import { NextRequest, NextResponse } from "next/server";
import { getStoredAuth } from "@/lib/auth/auth-service";
import { sampleNegotiationPlaybooks } from "@/lib/data/sample-negotiation-playbooks";

interface RouteParams {
  params: {
    id: string;
  };
}

export async function GET(
  request: NextRequest,
  { params }: RouteParams
) {
  try {
    // Check authentication
    const auth = await getStoredAuth();
    if (!auth?.token) {
      return NextResponse.json(
        { statusCode: 401, message: "Authentication required", error: "Unauthorized" },
        { status: 401 }
      );
    }

    const { id } = params;

    // Find the sample playbook
    const playbook = sampleNegotiationPlaybooks.find(p => p.documentId === id);

    if (!playbook) {
      return NextResponse.json(
        {
          statusCode: 404,
          message: `Sample playbook with ID '${id}' not found`,
          error: "Not Found",
        },
        { status: 404 }
      );
    }

    return NextResponse.json(playbook);
  } catch (error) {
    console.error("Error fetching sample negotiation playbook:", error);
    return NextResponse.json(
      {
        statusCode: 500,
        message: "Internal server error",
        error: "Internal Server Error",
      },
      { status: 500 }
    );
  }
}
