"use client";

import React from "react";
import {
	FileText,
	Target,
	BarChart3,
	Users,
	TrendingUp,
	Plus,
	ArrowRight,
	BookOpen,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { useRouter } from "next/navigation";
import { IntegrationSummary } from "@/components/integration";
import { useFeatureAccess } from "@/hooks/use-feature-access";
import { CreditDashboardCompact } from "@/components/subscription/credit-dashboard";

export default function DashboardPage() {
	const router = useRouter();
	const { canAccessFeature } = useFeatureAccess();

	const hasSimulatorAccess = canAccessFeature("negotiation_simulator");
	const hasAdvancedAnalysis = canAccessFeature("advanced_analysis");

	const quickActions = [
		{
			title: "Upload Document",
			description: "Start analyzing a new document",
			icon: FileText,
			action: () => router.push("/documents/upload"),
			color: "bg-blue-100 text-blue-600",
			available: true,
		},
		{
			title: "Create Contract Playbook",
			description: "Set up automated contract analysis",
			icon: BookOpen,
			action: () => router.push("/contract-playbooks/create"),
			color: "bg-indigo-100 text-indigo-600",
			available: hasAdvancedAnalysis,
			badge: !hasAdvancedAnalysis ? "PRO" : undefined,
		},
		{
			title: "Create Negotiation Scenario",
			description: "Practice with AI-powered scenarios",
			icon: Target,
			action: () => router.push("/negotiation-simulator"),
			color: "bg-green-100 text-green-600",
			available: hasSimulatorAccess,
			badge: !hasSimulatorAccess ? "PRO" : undefined,
		},
		{
			title: "View Analytics",
			description: "Track your document analysis insights",
			icon: BarChart3,
			action: () => router.push("/analytics"),
			color: "bg-purple-100 text-purple-600",
			available: hasAdvancedAnalysis,
			badge: !hasAdvancedAnalysis ? "PRO" : undefined,
		},
		{
			title: "Compare Documents",
			description: "Side-by-side document comparison",
			icon: Users,
			action: () => router.push("/compare"),
			color: "bg-orange-100 text-orange-600",
			available: true,
		},
	];

	const recentFeatures = [
		{
			title: "Negotiation Playbook Integration",
			description: "Turn document analyses into practice scenarios",
			status: "New",
			url: "/negotiation-simulator?tab=integration",
		},
		{
			title: "Cross-Feature Analytics",
			description: "Track usage across all features",
			status: "Updated",
			url: "/negotiation-simulator?tab=integration",
		},
		{
			title: "AI-Powered Recommendations",
			description: "Get personalized improvement suggestions",
			status: "New",
			url: "/negotiation-simulator?tab=recommendations",
		},
	];

	return (
		<div className="container mx-auto p-6 space-y-6">
			{/* Header */}
			<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
				<div>
					<h1 className="text-3xl font-bold">Dashboard</h1>
					<p className="text-muted-foreground mt-1">
						Welcome back! Here&apos;s what&apos;s happening with your documents
						and negotiations.
					</p>
				</div>
				<Button
					onClick={() => router.push("/documents/upload")}
					className="gap-2"
				>
					<Plus className="h-4 w-4" />
					Upload Document
				</Button>
			</div>

			{/* Main Content Grid */}
			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* Left Column - Quick Actions & Recent Features */}
				<div className="lg:col-span-2 space-y-6">
					{/* Quick Actions */}
					<Card>
						<CardHeader>
							<CardTitle>Quick Actions</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								{quickActions.map((action, index) => {
									const Icon = action.icon;
									return (
										<div
											key={index}
											className={`p-4 border rounded-lg transition-colors ${
												action.available
													? "hover:bg-accent/50 cursor-pointer"
													: "opacity-60 cursor-not-allowed"
											}`}
											onClick={action.available ? action.action : undefined}
										>
											<div className="flex items-start justify-between mb-3">
												<div className={`p-2 rounded-lg ${action.color}`}>
													<Icon className="h-5 w-5" />
												</div>
												{action.badge && (
													<Badge variant="secondary" className="text-xs">
														{action.badge}
													</Badge>
												)}
											</div>
											<h3 className="font-medium mb-1">{action.title}</h3>
											<p className="text-sm text-muted-foreground">
												{action.description}
											</p>
										</div>
									);
								})}
							</div>
						</CardContent>
					</Card>

					{/* Recent Features */}
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center justify-between">
								<span>What&apos;s New</span>
								<TrendingUp className="h-5 w-5 text-muted-foreground" />
							</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="space-y-3">
								{recentFeatures.map((feature, index) => (
									<div
										key={index}
										className="flex items-center justify-between p-3 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
										onClick={() => router.push(feature.url)}
									>
										<div className="flex-1">
											<div className="flex items-center gap-2 mb-1">
												<h4 className="font-medium text-sm">{feature.title}</h4>
												<Badge
													variant={
														feature.status === "New" ? "default" : "secondary"
													}
													className="text-xs"
												>
													{feature.status}
												</Badge>
											</div>
											<p className="text-sm text-muted-foreground">
												{feature.description}
											</p>
										</div>
										<ArrowRight className="h-4 w-4 text-muted-foreground" />
									</div>
								))}
							</div>
						</CardContent>
					</Card>

					{/* Feature Overview */}
					<Card>
						<CardHeader>
							<CardTitle>Available Features</CardTitle>
						</CardHeader>
						<CardContent>
							<div className="grid grid-cols-2 md:grid-cols-4 gap-4">
								<div className="text-center p-3 border rounded-lg">
									<FileText className="h-8 w-8 mx-auto mb-2 text-blue-600" />
									<h4 className="font-medium text-sm">Document Analysis</h4>
									<p className="text-xs text-muted-foreground">
										AI-powered insights
									</p>
								</div>
								<div className="text-center p-3 border rounded-lg">
									<Target className="h-8 w-8 mx-auto mb-2 text-green-600" />
									<h4 className="font-medium text-sm">Negotiation Sim</h4>
									<p className="text-xs text-muted-foreground">
										Practice scenarios
									</p>
								</div>
								<div className="text-center p-3 border rounded-lg">
									<BarChart3 className="h-8 w-8 mx-auto mb-2 text-purple-600" />
									<h4 className="font-medium text-sm">Analytics</h4>
									<p className="text-xs text-muted-foreground">
										Usage insights
									</p>
								</div>
								<div className="text-center p-3 border rounded-lg">
									<Users className="h-8 w-8 mx-auto mb-2 text-orange-600" />
									<h4 className="font-medium text-sm">Collaboration</h4>
									<p className="text-xs text-muted-foreground">Team features</p>
								</div>
							</div>
						</CardContent>
					</Card>
				</div>

				{/* Right Column - Integration Summary & Credits */}
				<div className="lg:col-span-1 space-y-6">
					<CreditDashboardCompact />
					<IntegrationSummary userId="current-user" />
				</div>
			</div>

			{/* Bottom Section - Recent Activity Placeholder */}
			<Card>
				<CardHeader>
					<CardTitle>Recent Activity</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="text-center py-8">
						<FileText className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
						<p className="text-sm text-muted-foreground mb-4">
							Your recent document analyses and negotiation sessions will appear
							here
						</p>
						<div className="flex gap-2 justify-center">
							<Button
								variant="outline"
								onClick={() => router.push("/documents")}
							>
								View Documents
							</Button>
							<Button
								variant="outline"
								onClick={() => router.push("/negotiation-simulator")}
							>
								View Sessions
							</Button>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
