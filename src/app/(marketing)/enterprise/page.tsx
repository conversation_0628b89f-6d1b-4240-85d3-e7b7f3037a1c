import Link from "next/link";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";

const enterpriseFeatures = [
  {
    category: "Advanced Security",
    features: [
      {
        title: "Custom Deployment Options",
        description: "On-premises, private cloud, or hybrid deployment options to meet your security requirements"
      },
      {
        title: "Advanced Access Control",
        description: "Fine-grained RBAC, SSO integration, and custom security policies"
      },
      {
        title: "Data Sovereignty",
        description: "Choose your data residency with regional deployment options"
      }
    ]
  },
  {
    category: "Custom AI Solutions",
    features: [
      {
        title: "Custom AI Models",
        description: "AI models trained on your industry-specific documents and requirements"
      },
      {
        title: "Advanced Analytics",
        description: "Custom reporting, dashboards, and ML-powered insights"
      },
      {
        title: "Workflow Automation",
        description: "Custom document processing workflows tailored to your business processes"
      }
    ]
  },
  {
    category: "Enterprise Support",
    features: [
      {
        title: "Dedicated Support Team",
        description: "24/7 dedicated support with guaranteed response times"
      },
      {
        title: "Implementation Services",
        description: "Full implementation support, training, and change management"
      },
      {
        title: "SLA Guarantees",
        description: "Customizable SLAs with uptime and performance guarantees"
      }
    ]
  }
];

const complianceCertifications = [
  "SOC 2 Type II",
  "ISO 27001",
  "HIPAA",
  "GDPR",
  "PCI DSS",
  "FedRAMP"
];

export default function EnterprisePage() {
  return (
    <div className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Hero Section */}
        <div className="text-center mb-20">
          <h1 className="text-5xl font-bold mb-6">
            Enterprise-Grade Document Analysis
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
            Custom solutions for large organizations with advanced security, 
            compliance, and integration requirements.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/contact">Contact Sales</Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/demo">Request Demo</Link>
            </Button>
          </div>
        </div>

        {/* Enterprise Features */}
        {enterpriseFeatures.map((section) => (
          <div key={section.category} className="mb-20">
            <h2 className="text-3xl font-bold mb-12 text-center">
              {section.category}
            </h2>
            <div className="grid md:grid-cols-3 gap-8">
              {section.features.map((feature) => (
                <Card key={feature.title}>
                  <CardHeader>
                    <CardTitle>{feature.title}</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <p className="text-muted-foreground">
                      {feature.description}
                    </p>
                  </CardContent>
                </Card>
              ))}
            </div>
          </div>
        ))}

        {/* Compliance Section */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold mb-12 text-center">
            Compliance & Security
          </h2>
          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
            {complianceCertifications.map((cert) => (
              <Card key={cert} className="text-center p-4">
                <p className="font-semibold">{cert}</p>
              </Card>
            ))}
          </div>
        </div>

        {/* Integration Section */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold mb-12 text-center">
            Enterprise Integration
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>API & Custom Integration</CardTitle>
                <CardDescription>
                  Seamlessly integrate with your existing systems
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li>• RESTful API with comprehensive documentation</li>
                  <li>• Custom webhooks and event notifications</li>
                  <li>• Batch processing and automation</li>
                  <li>• SDK support for major programming languages</li>
                </ul>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Enterprise Deployment</CardTitle>
                <CardDescription>
                  Flexible deployment options for your needs
                </CardDescription>
              </CardHeader>
              <CardContent>
                <ul className="space-y-2">
                  <li>• On-premises deployment</li>
                  <li>• Private cloud hosting</li>
                  <li>• Hybrid solutions</li>
                  <li>• Custom network configuration</li>
                </ul>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* CTA Section */}
        <div className="text-center bg-secondary p-12 rounded-lg">
          <h2 className="text-3xl font-bold mb-6">
            Ready to transform your document processing?
          </h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Our enterprise team is ready to help you customize a solution 
            that meets your organization&apos;s specific needs.
          </p>
          <div className="flex gap-4 justify-center">
            <Button size="lg" asChild>
              <Link href="/contact">Contact Sales</Link>
            </Button>
            <Button size="lg" variant="outline" asChild>
              <Link href="/docs/enterprise">Learn More</Link>
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
} 