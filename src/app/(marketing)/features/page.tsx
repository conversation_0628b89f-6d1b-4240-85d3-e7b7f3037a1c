import { FEATURES } from "../constants";

const additionalFeatures = [
  {
    category: "Document Processing",
    features: [
      {
        title: "Multi-Format Support",
        description: "Process PDFs, Word documents, images, and more with equal accuracy"
      },
      {
        title: "Batch Processing",
        description: "Process thousands of documents simultaneously with our parallel processing engine"
      },
      {
        title: "Template Matching",
        description: "Automatically detect and apply document templates for faster processing"
      }
    ]
  },
  {
    category: "AI & Machine Learning",
    features: [
      {
        title: "Advanced OCR",
        description: "Industry-leading optical character recognition with support for 100+ languages"
      },
      {
        title: "Entity Extraction",
        description: "Automatically identify and extract key information like names, dates, and amounts"
      },
      {
        title: "Document Classification",
        description: "AI-powered document categorization and routing"
      }
    ]
  },
  {
    category: "Integration & API",
    features: [
      {
        title: "RESTful API",
        description: "Comprehensive API for seamless integration with your existing systems"
      },
      {
        title: "Webhooks",
        description: "Real-time notifications for document processing events"
      },
      {
        title: "SDK Support",
        description: "Official SDKs for popular programming languages"
      }
    ]
  }
];

export default function FeaturesPage() {
  return (
    <div className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-20">
          <h1 className="text-4xl font-bold mb-4">
            Powerful Features for Enterprise Document Processing
          </h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Our platform combines advanced AI with enterprise-grade security to transform your document processing workflow
          </p>
        </div>

        {/* Core Features */}
        <div className="mb-20">
          <h2 className="text-3xl font-bold text-center mb-12">Core Features</h2>
          <div className="grid md:grid-cols-3 gap-8">
            {FEATURES.map((feature) => (
              <div key={feature.title} className="p-6 border rounded-lg">
                <div className="w-12 h-12 bg-black dark:bg-white rounded-lg mb-4" />
                <h3 className="font-semibold text-xl mb-2">{feature.title}</h3>
                <p className="text-gray-600 dark:text-gray-300">
                  {feature.description}
                </p>
              </div>
            ))}
          </div>
        </div>

        {/* Detailed Features */}
        {additionalFeatures.map((category) => (
          <div key={category.category} className="mb-20">
            <h2 className="text-3xl font-bold mb-12">{category.category}</h2>
            <div className="grid md:grid-cols-3 gap-8">
              {category.features.map((feature) => (
                <div key={feature.title} className="p-6 border rounded-lg">
                  <h3 className="font-semibold text-xl mb-2">{feature.title}</h3>
                  <p className="text-gray-600 dark:text-gray-300">
                    {feature.description}
                  </p>
                </div>
              ))}
            </div>
          </div>
        ))}

        {/* CTA Section */}
        <div className="text-center mt-20">
          <h2 className="text-3xl font-bold mb-8">Ready to get started?</h2>
          <div className="flex gap-4 justify-center">
            <a
              href="/register"
              className="bg-black text-white dark:bg-white dark:text-black px-8 py-4 rounded-lg hover:opacity-90 font-semibold"
            >
              Start Free Trial
            </a>
            <a
              href="/contact"
              className="border border-black dark:border-white px-8 py-4 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-900 font-semibold"
            >
              Contact Sales
            </a>
          </div>
        </div>
      </div>
    </div>
  );
} 