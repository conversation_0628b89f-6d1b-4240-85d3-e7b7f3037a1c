import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardHeader, CardTitle } from "@/components/ui/card";
import { Toggle } from "@/components/ui/toggle";

interface PricingTier {
  name: string;
  price: string;
  description: string;
  features: string[];
  limits: {
    documents: string;
    users: string;
  };
  cta: {
    text: string;
    href: string;
  };
  highlighted?: boolean;
}

const pricingTiers: PricingTier[] = [
  {
    name: "🎓 Law Student",
    price: "FREE",
    description: "Perfect for law students and legal education",
    limits: {
      documents: "10 documents/month",
      users: "50 credits/month"
    },
    features: [
      "Basic document analysis",
      "Limited legal research tools",
      "Access to threaded discussions",
      "Educational features for learning",
      "Basic document templates",
      "Community support"
    ],
    cta: {
      text: "Get Started Free",
      href: "/register?plan=law_student"
    }
  },
  {
    name: "⚖️ Lawyer",
    price: "$49.99",
    description: "For individual practicing attorneys",
    limits: {
      documents: "200 documents/month",
      users: "500 credits/month"
    },
    features: [
      "All advanced analysis tools",
      "Real-time collaboration",
      "Workflow management",
      "Professional legal research",
      "Client document management",
      "Priority processing",
      "Advanced document templates",
      "Email and chat support"
    ],
    cta: {
      text: "Start Free Trial",
      href: "/register?plan=lawyer"
    },
    highlighted: true
  },
  {
    name: "🏢 Law Firm",
    price: "$199.99",
    description: "For law firms and legal organizations",
    limits: {
      documents: "Unlimited documents",
      users: "2000 credits/month"
    },
    features: [
      "All Lawyer features",
      "Team analytics and management",
      "Enterprise-grade security",
      "Advanced workflow automation",
      "Multi-user collaboration",
      "Firm-wide document management",
      "Dedicated account manager",
      "Priority support with SLA"
    ],
    cta: {
      text: "Start Free Trial",
      href: "/register?plan=law_firm"
    }
  }
];

export default function PricingPage() {
  return (
    <div className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold mb-4">Legal Profession Pricing</h1>
          <p className="text-xl text-gray-600 dark:text-gray-300 max-w-3xl mx-auto">
            Professional plans designed for the legal profession. From law students to large firms, we have the right solution for your legal practice.
          </p>
        </div>

        {/* Pricing Toggle */}
        <div className="flex justify-center mb-12">
          <div className="bg-secondary p-1 rounded-lg inline-flex">
            <Toggle
              defaultPressed
              variant="outline"
              className="data-[state=on]:bg-background"
            >
              Monthly
            </Toggle>
            <Toggle
              variant="outline"
              className="data-[state=on]:bg-background"
            >
              Annual (20% off)
            </Toggle>
          </div>
        </div>

        {/* Pricing Tiers */}
        <div className="grid md:grid-cols-2 lg:grid-cols-3 gap-8">
          {pricingTiers.map((tier) => (
            <Card
              key={tier.name}
              className={tier.highlighted ? "border-2 border-primary" : ""}
            >
              <CardHeader>
                <CardTitle>{tier.name}</CardTitle>
                <CardDescription>{tier.description}</CardDescription>
                <div className="mt-4">
                  <span className="text-4xl font-bold">{tier.price}</span>
                  {tier.price !== "Custom" && <span className="text-muted-foreground">/month</span>}
                </div>
              </CardHeader>
              <CardContent>
                {/* Limits */}
                <div className="mb-8 p-4 bg-secondary rounded-lg">
                  <div className="mb-2">
                    <span className="font-medium">Processing Limit:</span>
                    <br />
                    {tier.limits.documents}
                  </div>
                  <div>
                    <span className="font-medium">Users:</span>
                    <br />
                    {tier.limits.users}
                  </div>
                </div>

                <ul className="mb-8 space-y-4">
                  {tier.features.map((feature) => (
                    <li key={feature} className="flex items-center gap-2">
                      <svg
                        className="w-5 h-5 text-green-500"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                      >
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M5 13l4 4L19 7"
                        />
                      </svg>
                      {feature}
                    </li>
                  ))}
                </ul>
              </CardContent>
              <CardFooter>
                <Button
                  asChild
                  className="w-full"
                  variant={tier.highlighted ? "default" : "outline"}
                >
                  <Link href={tier.cta.href}>
                    {tier.cta.text}
                  </Link>
                </Button>
              </CardFooter>
            </Card>
          ))}
        </div>

        {/* Add-ons Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center mb-12">
            Credit Packages & Add-ons
          </h2>
          <div className="grid md:grid-cols-3 gap-8">
            {addons.map((addon) => (
              <Card key={addon.title}>
                <CardHeader>
                  <CardTitle>{addon.title}</CardTitle>
                  <CardDescription>{addon.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <p className="font-medium">{addon.pricing}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* FAQ Section */}
        <div className="mt-20">
          <h2 className="text-3xl font-bold text-center mb-12">
            Frequently Asked Questions
          </h2>
          <div className="grid md:grid-cols-2 gap-8">
            {faqs.map((faq) => (
              <Card key={faq.question}>
                <CardHeader>
                  <CardTitle className="text-lg">{faq.question}</CardTitle>
                </CardHeader>
                <CardContent>
                  <p className="text-muted-foreground">{faq.answer}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
}

const addons = [
  {
    title: "Student Credit Pack",
    description: "Additional credits for law students needing extra analysis",
    pricing: "50 credits for $4.99"
  },
  {
    title: "Lawyer Professional Pack",
    description: "Boost your monthly credits with 15% bonus credits included",
    pricing: "500 credits for $44.99"
  },
  {
    title: "Firm Enterprise Pack",
    description: "Large credit packages for law firms with 30% bonus credits",
    pricing: "5000 credits for $349.99"
  }
];

const faqs = [
  {
    question: "What happens if I run out of credits?",
    answer: "You can purchase additional credit packages tailored to your tier, or upgrade to a higher plan for more monthly credits and better rates."
  },
  {
    question: "Can law students upgrade to Lawyer tier after graduation?",
    answer: "Absolutely! We encourage law students to upgrade to the Lawyer tier when they begin practicing. All your documents and analysis history will be preserved."
  },
  {
    question: "Do you offer discounts for law schools or bar associations?",
    answer: "Yes, we offer special institutional pricing for law schools and bar associations. Contact our sales team for custom pricing."
  },
  {
    question: "Is my client data secure and confidential?",
    answer: "Yes, we maintain the highest security standards with attorney-client privilege protection, end-to-end encryption, and SOC 2 compliance."
  },
  {
    question: "Can multiple attorneys in a firm share documents?",
    answer: "Yes, the Law Firm tier includes multi-user collaboration, team management, and firm-wide document sharing with proper access controls."
  },
  {
    question: "Do you offer API access for legal practice management integration?",
    answer: "Yes, Lawyer and Law Firm tiers include API access to integrate with popular legal practice management systems and custom workflows."
  }
];