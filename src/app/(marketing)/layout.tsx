import { Metada<PERSON> } from "next";
import Link from "next/link";
import Image from "next/image";
import { Button } from "@/components/ui/button";
import { AuthenticatedButtons } from "@/components/auth/authenticated-buttons";

export const metadata: Metadata = {
  title: "Docgic | Intelligent Document Analysis",
  description: "Enterprise-grade document analysis and processing powered by AI",
};

export default function MarketingLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="min-h-screen flex flex-col">
      <header className="sticky top-0 z-50 w-full border-b bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
        <div className="container flex h-16 items-center justify-between">
          <div className="flex items-center gap-2">
            <Image 
              src="/logos/trans-512.png" 
              alt="Docgic Logo" 
              width={300} 
              height={300} 
              className="w-32"
            />
          </div>
          <nav className="hidden md:flex items-center gap-6">
            <Link
              href="#features"
              className="text-sm font-medium hover:text-primary"
            >
              Features
            </Link>
            <Link
              href="#how-it-works"
              className="text-sm font-medium hover:text-primary"
            >
              How It Works
            </Link>
            <Link
              href="#pricing"
              className="text-sm font-medium hover:text-primary"
            >
              Pricing
            </Link>
          </nav>
          <AuthenticatedButtons />
        </div>
      </header>

      <main className="flex-1">
        {children}
      </main>

      <footer className="border-t py-8 bg-gray-50 dark:bg-gray-900">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <div className="flex items-center gap-2">
              <Image 
                src="/logos/trans-512.png" 
                alt="Docgic Logo" 
                width={24} 
                height={24} 
                className="h-6 w-6"
              />
              <span className="font-bold">Docgic</span>
            </div>
            
            <div className="flex flex-col md:flex-row gap-8 text-sm text-gray-500 dark:text-gray-400">
              <Link href="/privacy" className="hover:text-gray-900 dark:hover:text-gray-300">Privacy Policy</Link>
              <Link href="/terms" className="hover:text-gray-900 dark:hover:text-gray-300">Terms of Service</Link>
              <Link href="/contact" className="hover:text-gray-900 dark:hover:text-gray-300">Contact Us</Link>
            </div>
          </div>
          
          <div className="mt-8 pt-8 border-t border-gray-200 dark:border-gray-800 text-center text-sm text-gray-500 dark:text-gray-400">
            &copy; {new Date().getFullYear()} Docgic. All rights reserved.
          </div>
        </div>
      </footer>
    </div>
  );
}