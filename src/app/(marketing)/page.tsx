import {
  ArrowRight,
  CheckCircle,
  FileText,
  MessageSquare,
  GitCompare,
  Search,
  Users,
  Lock,
} from "lucide-react";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import Image from "next/image";
import { HeroButtons } from "@/components/auth/hero-buttons";

export default function Home() {
  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1">
        {/* Hero Section */}
        <section className="py-20 md:py-28 bg-gradient-to-b from-background to-muted/50">
          <div className="container px-4 md:px-6">
            <div className="grid gap-6 lg:grid-cols-2 lg:gap-12 items-center">
              <div className="flex flex-col justify-center space-y-4">
                <div className="space-y-2">
                  <h1 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl lg:text-6xl">
                    Stop Wasting Hours in Document Review
                  </h1>
                  <p className="max-w-[600px] text-muted-foreground md:text-xl">
                    Get counsel-grade analysis at paralegal speed.
                  </p>
                </div>
                <HeroButtons />
                <div className="flex flex-col gap-2 mt-2">
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    <span>No credit card required</span>
                  </div>
                  <div className="flex items-center gap-2 text-sm text-muted-foreground">
                    <CheckCircle className="h-4 w-4 text-primary" />
                    <span>50 credits free for law students</span>
                  </div>
                </div>
              </div>
              <div className="flex justify-center lg:justify-end">
                <div className="relative w-full max-w-[500px] aspect-video rounded-lg overflow-hidden shadow-xl">
                  <Image
                    src="/placeholder.svg?height=500&width=800"
                    alt="Docgic platform interface showing document analysis"
                    width={800}
                    height={500}
                    className="object-cover w-full h-full"
                  />
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Features Section */}
        <section id="features" className="py-16 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Work Smarter, Not Harder
                </h2>
                <p className="max-w-[700px] text-muted-foreground md:text-xl">
                  Our customers save an average of 15 hours per week while improving accuracy by 80% compared to manual document processing.
                </p>
              </div>
            </div>

            {/* AI-Powered Legal Analysis */}
            <div className="mt-16">
              <div className="flex flex-col md:flex-row items-center gap-6 md:gap-12">
                <div className="md:w-1/2">
                  <div className="rounded-lg overflow-hidden shadow-lg">
                    <Image
                      src="/placeholder.svg?height=400&width=600"
                      alt="AI document analysis visualization"
                      width={600}
                      height={400}
                      className="w-full h-auto"
                    />
                  </div>
                </div>
                <div className="md:w-1/2 space-y-6">
                  <div className="inline-flex items-center gap-2 rounded-full bg-primary/10 px-4 py-1.5">
                    <FileText className="h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-primary">
                      AI-Powered Document Analysis
                    </span>
                  </div>
                  <h3 className="text-2xl font-bold tracking-tight md:text-3xl">
                    Counsel-Grade Document Analysis
                  </h3>
                  <p className="text-muted-foreground">
                    Automate the heavy lifting, focus on strategy.
                  </p>
                  <div className="space-y-4">
                    <div className="flex gap-3">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium">
                          Key Provision Detection
                        </h4>
                        <p className="text-muted-foreground">
                          Our AI catches critical terms, unusual provisions, and risk factors across any legal document.
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium">
                          Document Type Recognition
                        </h4>
                        <p className="text-muted-foreground">
                          Automatically identifies contracts, pleadings, briefs, and more to apply the right analysis approach.
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium">
                          Conversational Document Q&A
                        </h4>
                        <p className="text-muted-foreground">
                          Ask questions like “What stands out in this section?” and get citation-backed answers.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Interactive Chat Experience */}
            <div className="mt-24">
              <div className="flex flex-col md:flex-row-reverse items-center gap-6 md:gap-12">
                <div className="md:w-1/2">
                  <div className="rounded-lg overflow-hidden shadow-lg">
                    <Image
                      src="/placeholder.svg?height=400&width=600"
                      alt="Interactive chat interface with documents"
                      width={600}
                      height={400}
                      className="w-full h-auto"
                    />
                  </div>
                </div>
                <div className="md:w-1/2 space-y-6">
                  <div className="inline-flex items-center gap-2 rounded-full bg-primary/10 px-4 py-1.5">
                    <MessageSquare className="h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-primary">
                      Legal-Specific AI Chat
                    </span>
                  </div>
                  <h3 className="text-2xl font-bold">
                    Your Legal Documents, Now With Instant Answers
                  </h3>
                  <p className="text-muted-foreground">
                    Works on PDF, Word & native docket formats.
                  </p>
                  <div className="space-y-4">
                    <div className="flex gap-3">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium">
                          Understands your contract language
                        </h4>
                        <p className="text-muted-foreground">
                          Our AI understands legal terminology and concepts like force majeure, indemnification, and material adverse change clauses.
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium">
                          Citation-Backed Answers
                        </h4>
                        <p className="text-muted-foreground">
                          Every answer comes with direct references to specific sections, so you can verify and cite with confidence.
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium">
                          Cross-Document Analysis
                        </h4>
                        <p className="text-muted-foreground">
                          Find inconsistencies between contracts or track changes across versions of the same agreement.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Document Management & Comparison */}
            <div className="mt-24">
              <div className="flex flex-col md:flex-row items-center gap-6 md:gap-12">
                <div className="md:w-1/2">
                  <div className="rounded-lg overflow-hidden shadow-lg">
                    <Image
                      src="/placeholder.svg?height=400&width=600"
                      alt="Document comparison interface showing differences"
                      width={600}
                      height={400}
                      className="w-full h-auto"
                    />
                  </div>
                </div>
                <div className="md:w-1/2 space-y-6">
                  <div className="inline-flex items-center gap-2 rounded-full bg-primary/10 px-4 py-1.5">
                    <GitCompare className="h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-primary">
                      Document Comparison
                    </span>
                  </div>
                  <h3 className="text-2xl font-bold">
                    Redline Without the Headache
                  </h3>
                  <p className="text-muted-foreground">
                    Spot risky edits in seconds.
                  </p>
                  <div className="space-y-4">
                    <div className="flex gap-3">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium">
                          Never Miss a Client&apos;s Change
                        </h4>
                        <p className="text-muted-foreground">
                          Automatic version tracking ensures you catch every modification in contracts, eliminating costly oversights.
                        </p>
                      </div>
                    </div>
                    <div className="flex gap-3">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <div>
                        <h4 className="font-medium">
                          Spot Risky Modifications
                        </h4>
                        <p className="text-muted-foreground">
                          Our visual highlighting makes problematic changes obvious, even in complex legal documents with hundreds of revisions.
                        </p>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Feature Grid */}
            <div className="mt-24 grid gap-6 md:grid-cols-2 lg:grid-cols-3">
              {/* Document Research Integration */}
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <div className="flex flex-col space-y-4">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Search className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">
                    Document Research Integration
                  </h3>
                  <p className="text-muted-foreground">
                    Automatically detect and enrich citations with
                    metadata from trusted databases.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Automatic citation detection</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Citation enrichment</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Comprehensive case information</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Collaboration & Feedback */}
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <div className="flex flex-col space-y-4">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Users className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">
                    Team Workspaces
                  </h3>
                  <p className="text-muted-foreground">
                    Create separate workspaces for different teams, clients, or
                    practice areas with robust feedback systems.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Multi-workspace support</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>User feedback system</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Continuous improvement tracking</span>
                    </li>
                  </ul>
                </div>
              </div>

              {/* Security & Compliance */}
              <div className="rounded-lg border bg-card p-6 shadow-sm">
                <div className="flex flex-col space-y-4">
                  <div className="h-12 w-12 rounded-lg bg-primary/10 flex items-center justify-center">
                    <Lock className="h-6 w-6 text-primary" />
                  </div>
                  <h3 className="text-xl font-bold">Legal-Grade Security</h3>
                  <p className="text-muted-foreground">
                    Designed for law firms with client confidentiality requirements and strict data governance needs.
                  </p>
                  <ul className="space-y-2">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Attorney-client privilege compliant</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Per-client document isolation</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Audit trails for all document access</span>
                    </li>
                  </ul>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* Pricing Section */}
        <section id="pricing" className="py-16 md:py-24">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Legal Profession Pricing
                </h2>
                <p className="max-w-[700px] text-muted-foreground md:text-xl">
                  Professional plans designed for every stage of your legal career.
                </p>
              </div>
            </div>
            <div className="mt-16 grid gap-8 md:grid-cols-3">
              {/* Law Student Plan */}
              <div className="rounded-lg border bg-card p-8 shadow-sm">
                <div className="flex flex-col space-y-6">
                  <h3 className="text-2xl font-bold">🎓 Law Student</h3>
                  <div className="space-y-2">
                    <p className="text-4xl font-bold">FREE</p>
                    <p className="text-muted-foreground">Perfect for legal education</p>
                  </div>
                  <div className="space-y-2 py-2 border-y">
                    <div className="flex justify-between">
                      <span>Document Limit</span>
                      <span className="font-medium">10</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Credits per Month</span>
                      <span className="font-medium">50</span>
                    </div>
                  </div>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Basic document analysis</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Limited legal research tools</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Access to threaded discussions</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Educational features for learning</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Basic document templates</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Community support</span>
                    </li>
                  </ul>
                  <Button className="w-full">Get Started Free</Button>
                </div>
              </div>

              {/* Lawyer Plan */}
              <div className="rounded-lg border bg-primary p-8 shadow-sm text-primary-foreground">
                <div className="flex flex-col space-y-6">
                  <div className="flex justify-between items-center">
                    <h3 className="text-2xl font-bold">⚖️ Lawyer</h3>
                    <Badge>Most Popular</Badge>
                  </div>
                  <div className="space-y-2">
                    <p className="text-4xl font-bold">$49.99</p>
                    <p className="text-muted-foreground">For practicing attorneys</p>
                  </div>
                  <div className="space-y-2 py-2 border-y border-primary-foreground/20">
                    <div className="flex justify-between">
                      <span>Document Limit</span>
                      <span className="font-medium">200</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Credits per Month</span>
                      <span className="font-medium">500</span>
                    </div>
                  </div>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>All advanced analysis tools</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Real-time collaboration</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Workflow management</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Professional legal research</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Client document management</span>
                    </li>
                  </ul>
                  <Button variant="secondary" className="w-full">
                    Start Free Trial
                  </Button>
                </div>
              </div>

              {/* Law Firm Plan */}
              <div className="rounded-lg border bg-card p-8 shadow-sm">
                <div className="flex flex-col space-y-6">
                  <h3 className="text-2xl font-bold">🏢 Law Firm</h3>
                  <div className="space-y-2">
                    <p className="text-4xl font-bold">$199.99</p>
                    <p className="text-muted-foreground">For law firms & organizations</p>
                  </div>
                  <div className="space-y-2 py-2 border-y">
                    <div className="flex justify-between">
                      <span>Document Limit</span>
                      <span className="font-medium">Unlimited</span>
                    </div>
                    <div className="flex justify-between">
                      <span>Credits per Month</span>
                      <span className="font-medium">2000</span>
                    </div>
                  </div>
                  <ul className="space-y-3">
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>All Lawyer features</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Team analytics and management</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Enterprise-grade security</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Multi-user collaboration</span>
                    </li>
                    <li className="flex items-start gap-2">
                      <CheckCircle className="h-5 w-5 text-primary flex-shrink-0 mt-0.5" />
                      <span>Firm-wide document management</span>
                    </li>
                  </ul>
                  <Button className="w-full">Start Free Trial</Button>
                </div>
              </div>
            </div>
          </div>
        </section>

        {/* How It Works */}
        <section id="how-it-works" className="py-16 md:py-24 bg-muted/50">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  How Docgic Works
                </h2>
                <p className="max-w-[700px] text-muted-foreground md:text-xl">
                  Get started with Docgic in just a few simple steps.
                </p>
              </div>
            </div>
            <div className="mt-16 grid gap-8 md:grid-cols-3">
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary text-white text-2xl font-bold">
                  1
                </div>
                <h3 className="text-xl font-bold">Upload Documents</h3>
                <p className="text-muted-foreground">
                  Upload your documents to our secure platform. We support
                  various formats including PDF, DOCX, and more.
                </p>
              </div>
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary text-white text-2xl font-bold">
                  2
                </div>
                <h3 className="text-xl font-bold">AI Analysis</h3>
                <p className="text-muted-foreground">
                  Our AI automatically analyzes your documents, extracting key
                  information, identifying clauses, and preparing them for
                  interaction.
                </p>
              </div>
              <div className="flex flex-col items-center text-center space-y-4">
                <div className="flex h-16 w-16 items-center justify-center rounded-full bg-primary text-white text-2xl font-bold">
                  3
                </div>
                <h3 className="text-xl font-bold">Interact & Collaborate</h3>
                <p className="text-muted-foreground">
                  Chat with your documents, compare versions, share insights
                  with your team, and export your findings in various formats.
                </p>
              </div>
            </div>
          </div>
        </section>

        {/* CTA Section */}
        <section className="py-16 md:py-24 bg-primary text-primary-foreground">
          <div className="container px-4 md:px-6">
            <div className="flex flex-col items-center justify-center space-y-4 text-center">
              <div className="space-y-2">
                <h2 className="text-3xl font-bold tracking-tighter sm:text-4xl md:text-5xl">
                  Ready to Transform Your Document Workflow?
                </h2>
                <p className="max-w-[700px] md:text-xl">
                  Join the professionals who are saving time and gaining
                  insights with Docgic.
                </p>
              </div>
              <div className="flex flex-col sm:flex-row gap-4 mt-6">
                <Button variant="secondary" size="lg">
                  Start Free Trial
                </Button>
                <Button
                  variant="outline"
                  size="lg"
                  className="bg-transparent border-primary-foreground text-primary-foreground hover:bg-primary-foreground/10"
                >
                  Schedule Demo
                </Button>
              </div>
            </div>
          </div>
        </section>
      </main>
    </div>
  );
}
