import { <PERSON><PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";

const contactMethods = [
  {
    title: "Sales",
    description: "Talk to our sales team about enterprise solutions",
    email: "<EMAIL>",
    phone: "+****************"
  },
  {
    title: "Support",
    description: "Get help with technical issues and questions",
    email: "<EMAIL>",
    phone: "+****************"
  },
  {
    title: "Partnership",
    description: "Explore partnership and integration opportunities",
    email: "<EMAIL>",
    phone: "+****************"
  }
];

export default function ContactPage() {
  return (
    <div className="py-20">
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        {/* Header */}
        <div className="text-center mb-16">
          <h1 className="text-4xl font-bold mb-4">Contact Us</h1>
          <p className="text-xl text-muted-foreground max-w-2xl mx-auto">
            Get in touch with our team to learn more about our enterprise document analysis solutions.
          </p>
        </div>

        <div className="grid lg:grid-cols-3 gap-8 mb-20">
          {/* Contact Form */}
          <Card className="lg:col-span-2">
            <CardHeader>
              <CardTitle>Send us a message</CardTitle>
              <CardDescription>
                Fill out the form below and we&apos;ll get back to you as soon as possible.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <form className="space-y-6">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="firstName">First name</Label>
                    <Input id="firstName" placeholder="John" />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="lastName">Last name</Label>
                    <Input id="lastName" placeholder="Doe" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="email">Work email</Label>
                  <Input id="email" type="email" placeholder="<EMAIL>" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="company">Company</Label>
                  <Input id="company" placeholder="Company name" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="inquiry">Type of inquiry</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select an option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="sales">Sales Inquiry</SelectItem>
                      <SelectItem value="support">Technical Support</SelectItem>
                      <SelectItem value="partnership">Partnership</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="message">Message</Label>
                  <Textarea
                    id="message"
                    placeholder="Tell us about your needs..."
                    rows={5}
                  />
                </div>

                <Button type="submit" className="w-full">
                  Send Message
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Contact Methods */}
          <div className="space-y-6">
            {contactMethods.map((method) => (
              <Card key={method.title}>
                <CardHeader>
                  <CardTitle>{method.title}</CardTitle>
                  <CardDescription>{method.description}</CardDescription>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <p className="flex items-center gap-2">
                      <span className="text-muted-foreground">Email:</span>
                      <a href={`mailto:${method.email}`} className="hover:underline">
                        {method.email}
                      </a>
                    </p>
                    <p className="flex items-center gap-2">
                      <span className="text-muted-foreground">Phone:</span>
                      <a href={`tel:${method.phone}`} className="hover:underline">
                        {method.phone}
                      </a>
                    </p>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        {/* Office Locations */}
        <div>
          <h2 className="text-2xl font-bold text-center mb-8">Our Offices</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <Card>
              <CardHeader>
                <CardTitle>San Francisco</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  123 Market Street<br />
                  San Francisco, CA 94105<br />
                  United States
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>London</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  456 Oxford Street<br />
                  London, W1C 1AP<br />
                  United Kingdom
                </p>
              </CardContent>
            </Card>
            <Card>
              <CardHeader>
                <CardTitle>Singapore</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  789 Marina Bay<br />
                  Singapore 018956<br />
                  Singapore
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  );
} 