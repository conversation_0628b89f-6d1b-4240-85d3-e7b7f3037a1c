"use client";

import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { AuthProvider } from "@/lib/auth/auth-context";
import { OrganizationProvider } from "@/lib/organization/organization-context";
import { SubscriptionProvider } from "@/lib/subscription/subscription-context";
import { CollaborationProvider } from "@/lib/collaboration/collaboration-context";
import { ErrorBoundary } from "@/components/error-boundary";
import { RootError } from "@/components/error/root-error";
import { ReactNode } from "react";

const queryClient = new QueryClient({
  defaultOptions: {
    queries: {
      retry: false,
      refetchOnWindowFocus: false,
    },
  },
});

export function Providers({ children }: { children: ReactNode }) {
  return (
    <ErrorBoundary>
      <QueryClientProvider client={queryClient}>
        <AuthProvider>
          <OrganizationProvider>
            <SubscriptionProvider>
              {children}
            </SubscriptionProvider>
          </OrganizationProvider>
        </AuthProvider>
      </QueryClientProvider>
    </ErrorBoundary>
  );
}

// Root error handling
export function RootErrorBoundary({ error, reset }: { error: Error; reset: () => void }) {
  return <RootError error={error} reset={reset} />;
}