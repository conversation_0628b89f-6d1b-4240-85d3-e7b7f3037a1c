'use client';

import { useEffect, useState } from 'react';
import { ChatContainer } from '@/components/chat/chat-container';
import { ChatProvider, useChatContext } from '@/lib/chat/chat-context';
import { useSearchParams } from 'next/navigation';
import { Loader2 } from 'lucide-react';
import { chatService } from '@/lib/services/chat-service';
import { useToast } from '@/hooks/use-toast';
import { useDocumentById } from '@/hooks/use-document-by-id';

// Component to handle document parameter and create chat session
function ChatInitializer() {
  const { createSession } = useChatContext();
  const searchParams = useSearchParams();
  const documentId = searchParams.get('document');
  const [isInitializing, setIsInitializing] = useState(false);
  const [isInitialized, setIsInitialized] = useState(false);
  const { toast } = useToast();
  const { data: document, isLoading: isLoadingDocument } = useDocumentById(documentId);

  useEffect(() => {
    const initializeChat = async () => {
      if (documentId && !isInitialized && !isInitializing && document) {
        try {
          setIsInitializing(true);
          
          // Check if a session already exists for this document
          const existingSessions = await chatService.getSessions();
          const existingSession = existingSessions.find(session => session.documentId === documentId);
          
          if (existingSession) {
            // If a session exists, use it instead of creating a new one
            await createSession(existingSession.id);
            toast({
              title: "Chat Session Loaded",
              description: `Loaded existing chat session for "${existingSession.title}"`,
            });
          } else {
            // If no session exists, create a new one with the document details
            await createSession(documentId, document.name || document.filename);
            
            toast({
              title: "Chat Session Created",
              description: `Started a new chat session with "${document.name || document.filename}"`,
            });
          }
        } catch (error) {
          toast({
            title: "Failed to Create Chat Session",
            description: error instanceof Error ? error.message : "An error occurred",
            variant: "destructive"
          });
        } finally {
          setIsInitializing(false);
          setIsInitialized(true);
        }
      }
    };

    initializeChat();
  }, [documentId, createSession, isInitialized, isInitializing, toast, document]);

  if (documentId && (isLoadingDocument || isInitializing)) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="flex flex-col items-center">
          <Loader2 className="h-8 w-8 animate-spin mb-2" />
          <p className="text-sm text-muted-foreground">
            {isLoadingDocument ? "Loading document..." : "Creating chat session..."}
          </p>
        </div>
      </div>
    );
  }

  return <ChatContainer />;
}

export default function ChatPage() {
  return (
    <main className="h-[calc(100vh-4rem)]">
      <ChatProvider>
        <ChatInitializer />
      </ChatProvider>
    </main>
  );
}
