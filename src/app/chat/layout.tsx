"use client"
import { Chat<PERSON>rovider } from '@/lib/chat/chat-context'
import type React from "react"

export default function ChatLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <div className="flex min-h-screen relative">
        {/* Page content without sidebar */}
        <main className="flex-1">
          <div className="w-full h-full">
            <ChatProvider>
              {children}
            </ChatProvider>
          </div>
        </main>
      </div>
  )
}