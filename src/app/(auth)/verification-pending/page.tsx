'use client';

import { useState } from 'react';
import { useResendVerificationEmail } from '@/lib/auth/auth-service';
import { Card } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { MailCheck, RefreshCw, CheckCircle, AlertCircle } from 'lucide-react';
import Link from 'next/link';
import { useSearchParams } from 'next/navigation';
import { Alert, AlertDescription, AlertTitle } from '@/components/ui/alert';
import { UseMutationResult } from '@tanstack/react-query';

export default function VerificationPendingPage() {
  const searchParams = useSearchParams();
  const email = searchParams.get('email') || '';
  const [status, setStatus] = useState<'idle' | 'success' | 'error'>('idle');
  const [message, setMessage] = useState('');
  
  const { mutate: resendVerificationEmail, isPending: isLoading } = useResendVerificationEmail() as UseMutationResult<
    { success: boolean; message: string },
    Error,
    string,
    unknown
  >;

  const handleResendEmail = () => {
    if (!email) {
      setStatus('error');
      setMessage('Email address is missing. Please go back to the registration page.');
      return;
    }

    setStatus('idle');
    setMessage('');
    
    resendVerificationEmail(email, {
      onSuccess: (data) => {
        setStatus('success');
        setMessage(data.message || 'Verification email has been sent. Please check your inbox.');
      },
      onError: (error) => {
        setStatus('error');
        setMessage(error instanceof Error ? error.message : 'Failed to send verification email. Please try again.');
      }
    });
  };

  return (
    <div className="min-h-screen flex items-center justify-center px-4">
      <Card className="w-full max-w-md p-8 space-y-6">
        <div className="text-center space-y-4">
          <div className="flex justify-center">
            <MailCheck className="h-16 w-16 text-primary" />
          </div>
          <h1 className="text-2xl font-bold">Verify Your Email</h1>
          <p className="text-muted-foreground">
            We&apos;ve sent a verification email to <strong>{email}</strong>. 
            Please check your inbox and click the verification link to complete your registration.
          </p>
        </div>

        {status === 'success' && (
          <Alert className="bg-green-50 border-green-200 text-green-800">
            <CheckCircle className="h-4 w-4 text-green-500" />
            <AlertTitle>Email Sent</AlertTitle>
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        {status === 'error' && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertTitle>Error</AlertTitle>
            <AlertDescription>{message}</AlertDescription>
          </Alert>
        )}

        <div className="space-y-4">
          <div className="text-sm text-muted-foreground">
            <p>Didn&apos;t receive the email? Check your spam folder or click below to resend.</p>
          </div>
          
          <Button 
            onClick={handleResendEmail} 
            variant="outline" 
            className="w-full gap-2"
            disabled={isLoading}
          >
            {isLoading ? (
              <>
                <RefreshCw className="h-4 w-4 animate-spin" />
                Sending...
              </>
            ) : (
              <>
                <RefreshCw className="h-4 w-4" />
                Resend Verification Email
              </>
            )}
          </Button>
          
          <div className="text-center pt-2">
            <Button asChild variant="link">
              <Link href="/login">
                Back to Login
              </Link>
            </Button>
          </div>
        </div>
      </Card>
    </div>
  );
}
