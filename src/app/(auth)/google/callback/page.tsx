"use client";

import { useEffect } from "react";
import { useSearchParams, useRouter } from "next/navigation";
import { useAuth } from "@/lib/auth/auth-context";
import { Spinner } from "@/components/ui/spinner";

export default function GoogleCallbackPage() {
  const searchParams = useSearchParams();
  const router = useRouter();
  const token = searchParams.get("token");
  const { loginWithToken } = useAuth();

  useEffect(() => {
    if (token) {
      loginWithToken(token).catch(() => {
        router.push("/login");
      });
    } else {
      router.push("/login");
    }
  }, [token, loginWithToken, router]);

  return (
    <div className="min-h-screen flex items-center justify-center">
      <Spinner size="lg" />
      <span className="ml-2">Signing you in...</span>
    </div>
  );
}
