"use client";

import { useState } from "react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Progress } from "@/components/ui/progress";
import {
	Shield,
	FileText,
	AlertTriangle,
	CheckCircle,
	TrendingUp,
	Settings,
	Plus,
	Search,
	Filter,
} from "lucide-react";
import { ComplianceAuditForm } from "@/components/compliance/compliance-audit-form";
import { ComplianceAnalyticsDashboard } from "@/components/compliance/compliance-analytics-dashboard";
import { ComplianceProfileManager } from "@/components/compliance/compliance-profile-manager";
import { AuditResultsList } from "@/components/compliance/audit-results-list";
import { useComplianceAnalytics } from "@/hooks/use-compliance";

export default function ComplianceAuditorPage() {
	const [activeTab, setActiveTab] = useState("overview");
	const {
		data: analytics,
		isLoading: analyticsLoading,
		error: analyticsError,
	} = useComplianceAnalytics();

	const getRiskLevelColor = (level: string) => {
		switch (level) {
			case "low":
				return "bg-green-500";
			case "medium":
				return "bg-yellow-500";
			case "high":
				return "bg-orange-500";
			case "critical":
				return "bg-red-500";
			default:
				return "bg-gray-500";
		}
	};

	const getRiskLevelVariant = (level: string) => {
		switch (level) {
			case "low":
				return "default";
			case "medium":
				return "secondary";
			case "high":
				return "destructive";
			case "critical":
				return "destructive";
			default:
				return "outline";
		}
	};

	return (
		<div className="container mx-auto p-6 space-y-6">
			{/* Header */}
			<div className="flex items-center justify-between">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">
						Compliance Auditor
					</h1>
					<p className="text-muted-foreground">
						AI-powered regulatory compliance analysis for your legal documents
					</p>
				</div>
				<div className="flex items-center gap-2">
					<Button variant="outline" size="sm">
						<Filter className="h-4 w-4 mr-2" />
						Filter
					</Button>
					<Button variant="outline" size="sm">
						<Search className="h-4 w-4 mr-2" />
						Search
					</Button>
				</div>
			</div>

			{/* Quick Stats */}
			{analyticsLoading ? (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					{[1, 2, 3, 4].map((i) => (
						<Card key={i}>
							<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
								<div className="h-4 w-20 bg-muted animate-pulse rounded"></div>
								<div className="h-4 w-4 bg-muted animate-pulse rounded"></div>
							</CardHeader>
							<CardContent>
								<div className="h-8 w-16 bg-muted animate-pulse rounded mb-2"></div>
								<div className="h-3 w-24 bg-muted animate-pulse rounded"></div>
							</CardContent>
						</Card>
					))}
				</div>
			) : analyticsError ? (
				<Card>
					<CardContent className="p-6 text-center">
						<AlertTriangle className="h-8 w-8 text-yellow-500 mx-auto mb-2" />
						<p className="text-muted-foreground">
							Analytics temporarily unavailable
						</p>
						<p className="text-sm text-muted-foreground">
							You can still access other compliance features
						</p>
					</CardContent>
				</Card>
			) : analytics ? (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Total Audits
							</CardTitle>
							<FileText className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{analytics.totalAudits || 0}
							</div>
							<p className="text-xs text-muted-foreground">
								+12% from last month
							</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Average Score
							</CardTitle>
							<TrendingUp className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{(analytics.averageScore || 0).toFixed(1)}
							</div>
							<Progress
								value={(analytics.averageScore || 0) * 10}
								className="mt-2"
							/>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								Compliance Rate
							</CardTitle>
							<CheckCircle className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{((analytics.complianceRate || 0) * 100).toFixed(1)}%
							</div>
							<p className="text-xs text-muted-foreground">+5% improvement</p>
						</CardContent>
					</Card>

					<Card>
						<CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
							<CardTitle className="text-sm font-medium">
								High Risk Items
							</CardTitle>
							<AlertTriangle className="h-4 w-4 text-muted-foreground" />
						</CardHeader>
						<CardContent>
							<div className="text-2xl font-bold">
								{analytics.riskDistribution?.high || 0}
							</div>
							<p className="text-xs text-muted-foreground">
								Requires attention
							</p>
						</CardContent>
					</Card>
				</div>
			) : null}

			{/* Main Content Tabs */}
			<Tabs
				value={activeTab}
				onValueChange={setActiveTab}
				className="space-y-4"
			>
				<TabsList className="grid w-full grid-cols-5">
					<TabsTrigger value="overview">Overview</TabsTrigger>
					<TabsTrigger value="audit">New Audit</TabsTrigger>
					<TabsTrigger value="results">Audit Results</TabsTrigger>
					<TabsTrigger value="profiles">Profiles</TabsTrigger>
					<TabsTrigger value="analytics">Analytics</TabsTrigger>
				</TabsList>

				<TabsContent value="overview" className="space-y-4">
					<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
						{/* Risk Distribution */}
						<Card>
							<CardHeader>
								<CardTitle>Risk Distribution</CardTitle>
								<CardDescription>
									Current risk levels across all audited documents
								</CardDescription>
							</CardHeader>
							<CardContent>
								{analyticsError ? (
									<div className="text-center py-8">
										<p className="text-sm text-muted-foreground">
											Analytics temporarily unavailable
										</p>
									</div>
								) : !analyticsLoading &&
								  analytics &&
								  analytics.riskDistribution ? (
									<div className="space-y-3">
										{Object.entries(analytics.riskDistribution).map(
											([level, count]) => (
												<div
													key={level}
													className="flex items-center justify-between"
												>
													<div className="flex items-center gap-2">
														<div
															className={`w-3 h-3 rounded-full ${getRiskLevelColor(
																level
															)}`}
														/>
														<span className="capitalize">{level} Risk</span>
													</div>
													<Badge variant={getRiskLevelVariant(level) as any}>
														{count}
													</Badge>
												</div>
											)
										)}
									</div>
								) : (
									<div className="text-center py-8">
										<p className="text-sm text-muted-foreground">
											{analyticsLoading
												? "Loading risk data..."
												: "No risk data available"}
										</p>
									</div>
								)}
							</CardContent>
						</Card>

						{/* Framework Breakdown */}
						<Card>
							<CardHeader>
								<CardTitle>Framework Performance</CardTitle>
								<CardDescription>
									Compliance scores by regulatory framework
								</CardDescription>
							</CardHeader>
							<CardContent>
								{analyticsError ? (
									<div className="text-center py-8">
										<p className="text-sm text-muted-foreground">
											Analytics temporarily unavailable
										</p>
									</div>
								) : !analyticsLoading &&
								  analytics &&
								  analytics.frameworkBreakdown ? (
									<div className="space-y-4">
										{Object.entries(analytics.frameworkBreakdown).map(
											([framework, data]) => (
												<div key={framework} className="space-y-2">
													<div className="flex items-center justify-between">
														<span className="font-medium">{framework}</span>
														<span className="text-sm text-muted-foreground">
															{data.averageScore.toFixed(1)}/10
														</span>
													</div>
													<Progress value={data.averageScore * 10} />
													<p className="text-xs text-muted-foreground">
														{data.audits} audits completed
													</p>
												</div>
											)
										)}
									</div>
								) : (
									<div className="text-center py-8">
										<p className="text-sm text-muted-foreground">
											{analyticsLoading
												? "Loading framework data..."
												: "No framework data available"}
										</p>
									</div>
								)}
							</CardContent>
						</Card>
					</div>

					{/* Recent Activity */}
					<Card>
						<CardHeader>
							<CardTitle>Recent Audit Activity</CardTitle>
							<CardDescription>
								Latest compliance audits and their results
							</CardDescription>
						</CardHeader>
						<CardContent>
							<AuditResultsList limit={5} />
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="audit">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Shield className="h-5 w-5" />
								Start New Compliance Audit
							</CardTitle>
							<CardDescription>
								Analyze your documents against regulatory frameworks like GDPR,
								HIPAA, SOX, and PCI-DSS
							</CardDescription>
						</CardHeader>
						<CardContent>
							<ComplianceAuditForm />
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="results">
					<Card>
						<CardHeader>
							<CardTitle>Audit Results</CardTitle>
							<CardDescription>
								View and manage your compliance audit results
							</CardDescription>
						</CardHeader>
						<CardContent>
							<AuditResultsList />
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="profiles">
					<Card>
						<CardHeader>
							<CardTitle className="flex items-center gap-2">
								<Settings className="h-5 w-5" />
								Compliance Profiles
							</CardTitle>
							<CardDescription>
								Manage industry-specific compliance profiles and custom rules
							</CardDescription>
						</CardHeader>
						<CardContent>
							<ComplianceProfileManager />
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="analytics">
					<ComplianceAnalyticsDashboard />
				</TabsContent>
			</Tabs>
		</div>
	);
}
