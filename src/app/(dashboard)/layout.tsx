"use client"
import type React from "react"
import { Sidebar } from "@/components/sidebar"
import { CollaborationProvider } from "@/lib/collaboration/collaboration-context"

export default function DashboardLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <CollaborationProvider>
      <div className="flex min-h-screen relative">
          {/* Sidebar */}
          <Sidebar />

          {/* Page content */}
          <main className="flex-1 overflow-hidden md:ml-64">
            <div className="w-full h-full overflow-auto">{children}</div>
          </main>
        </div>
    </CollaborationProvider>
  )
}
