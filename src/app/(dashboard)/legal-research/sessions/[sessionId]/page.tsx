"use client";

import { useEffect } from "react";
import { use<PERSON><PERSON><PERSON>, useRouter } from "next/navigation";
import { ResearchProvider, useResearchContext } from "@/lib/legal-research/research-context";
import { ResearchChatContainer } from "@/components/legal-research/research-chat-container";
import { Button } from "@/components/ui/button";
import { ArrowLeft, Loader2 } from "lucide-react";
import { useResearchSession } from "@/hooks/use-legal-research";

function SessionPageContent() {
  const params = useParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;
  
  const { loadSession, currentSession, isLoading } = useResearchContext();
  const { data: sessionData, isLoading: isLoadingSession, error } = useResearchSession(sessionId);

  useEffect(() => {
    if (sessionData && !currentSession) {
      loadSession(sessionId);
    }
  }, [sessionData, currentSession, sessionId, loadSession]);

  if (isLoadingSession || isLoading) {
    return (
      <div className="flex items-center justify-center min-h-screen">
        <div className="flex items-center space-x-2">
          <Loader2 className="h-4 w-4 animate-spin" />
          <span>Loading research session...</span>
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-screen space-y-4">
        <h1 className="text-2xl font-bold text-destructive">Session Not Found</h1>
        <p className="text-muted-foreground">
          The research session you're looking for doesn't exist or you don't have access to it.
        </p>
        <Button onClick={() => router.push("/legal-research")} variant="outline">
          <ArrowLeft className="w-4 h-4 mr-2" />
          Back to Research
        </Button>
      </div>
    );
  }

  return <ResearchChatContainer />;
}

export default function SessionPage() {
  return (
    <ResearchProvider>
      <SessionPageContent />
    </ResearchProvider>
  );
}
