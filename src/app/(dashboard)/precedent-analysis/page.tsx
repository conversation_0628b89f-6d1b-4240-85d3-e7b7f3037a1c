"use client";

import React, { useState } from "react";
import { SimpleDocumentSelector } from "@/components/document-comparison/simple-document-selector";
import { PrecedentAnalysisForm } from "@/components/precedent-analysis/precedent-analysis-form";
import { PrecedentAnalysisResults } from "@/components/precedent-analysis/precedent-analysis-results";
import {
  precedentAnalysisService,
  PrecedentAnalysisOptions,
  PrecedentAnalysisResult,
} from "@/lib/services/precedent-analysis-service";
import { useToast } from "@/hooks/use-toast";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertCircle } from "lucide-react";

export default function PrecedentAnalysisPage() {
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(
    null
  );
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [analysisResults, setAnalysisResults] = useState<
    PrecedentAnalysisResult[] | null
  >(null);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const handleDocumentSelect = (documentId: string) => {
    setSelectedDocumentId(documentId);
    setAnalysisResults(null);
    setError(null);
  };

  const handleAnalyze = async (
    documentId: string,
    options: PrecedentAnalysisOptions
  ) => {
    setIsAnalyzing(true);
    setError(null);

    try {
      const results = await precedentAnalysisService.analyzePrecedents(
        documentId,
        options
      );
      setAnalysisResults(results);

      if (results.length === 0) {
        toast({
          title: "No precedents found",
          description: "No legal precedents were identified in this document.",
          variant: "default",
        });
      } else {
        toast({
          title: "Analysis Complete",
          description: `Found ${results.length} precedents in the document.`,
          variant: "default",
        });
      }
    } catch (err) {
      console.error("Error analyzing precedents:", err);

      // Handle specific error cases
      if (err instanceof Error) {
        if (err.message.includes("feature")) {
          setError(
            "Access to precedent analysis feature is required. Please upgrade your subscription."
          );
        } else {
          setError(
            err.message || "An error occurred during precedent analysis."
          );
        }
      } else {
        setError("An unexpected error occurred. Please try again later.");
      }

      toast({
        title: "Analysis Failed",
        description: "There was an error analyzing the document precedents.",
        variant: "destructive",
      });
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handleReset = () => {
    setSelectedDocumentId(null);
    setAnalysisResults(null);
    setError(null);
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <h1 className="text-3xl font-bold mb-2">Precedent Analysis</h1>
      <p className="text-muted-foreground mb-6">
        Analyze legal precedents cited in your documents to understand their
        relevance and impact.
      </p>

      {error && (
        <Alert variant="destructive" className="mb-6">
          <AlertCircle className="h-4 w-4" />
          <AlertTitle>Error</AlertTitle>
          <AlertDescription>{error}</AlertDescription>
        </Alert>
      )}

      <div className="container py-6 max-w-6xl">
        {!selectedDocumentId ? (
          <SimpleDocumentSelector
            title="Select a Document for Precedent Analysis"
            description="Choose a legal document to analyze its precedents."
            onSelect={handleDocumentSelect}
          />
        ) : !analysisResults ? (
          <PrecedentAnalysisForm
            documentId={selectedDocumentId}
            onAnalyze={handleAnalyze}
            isLoading={isAnalyzing}
          />
        ) : (
          <PrecedentAnalysisResults
            results={analysisResults}
            documentId={selectedDocumentId}
            onBack={handleReset}
            isLoading={false}
          />
        )}
      </div>
    </div>
  );
}
