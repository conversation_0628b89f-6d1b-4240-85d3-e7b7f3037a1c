"use client"

import { useState } from "react"
import { DocumentComparisonSelector } from "@/components/document-comparison/document-comparison-selector"
import { DocumentComparison } from "@/components/document-comparison/document-comparison"

export default function DocumentComparisonPage() {
  const [selectedDocuments, setSelectedDocuments] = useState<{
    primaryDocumentId: string
    relatedDocumentIds: string[]
  } | null>(null)

  const handleCompare = (primaryDocumentId: string, relatedDocumentIds: string[]) => {
    setSelectedDocuments({ primaryDocumentId, relatedDocumentIds })
  }

  const handleBack = () => {
    setSelectedDocuments(null)
  }

  return (
    <div className="container py-6 max-w-6xl">
      <h1 className="text-2xl font-bold mb-6 text-foreground">Document Comparison</h1>

      {!selectedDocuments ? (
        <DocumentComparisonSelector onCompare={handleCompare} onCancel={() => {}} />
      ) : (
        <DocumentComparison
          primaryDocumentId={selectedDocuments.primaryDocumentId}
          relatedDocumentIds={selectedDocuments.relatedDocumentIds}
          onBack={handleBack}
        />
      )}
    </div>
  )
}
