"use client"

import { useState, useEffect, useCallback, Suspense } from "react"
import { useSearchPara<PERSON>, useRouter } from "next/navigation"
import { useToast } from "@/hooks/use-toast"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { documentComparisonService, type EnhancedComparisonResult, type ExecutiveSummaryResult, type ExportComparisonRequest } from "@/lib/services/document-comparison-service"
import { Loader2, FileText, ArrowLeft } from "lucide-react"
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { EnhancedComparison } from "@/components/document-comparison/enhanced-comparison"
import { FeatureGuard } from "@/components/subscription/feature-guard";

function EnhancedDocumentComparisonContent() {
  const searchParams = useSearchParams()
  const router = useRouter()
  const { toast } = useToast()

  const [isLoading, setIsLoading] = useState(false)
  const [enhancedResult, setEnhancedResult] = useState<EnhancedComparisonResult | null>(null)
  const [showSummary, setShowSummary] = useState(false)
  const [executiveSummary, setExecutiveSummary] = useState<ExecutiveSummaryResult | null>(null)
  const [summaryLoading, setSummaryLoading] = useState(false)

  // Get document IDs from URL parameters
  const docA = searchParams.get("docA")
  const docB = searchParams.get("docB")

  const loadComparisonData = useCallback(async () => {
    if (!docA || !docB) return

    setIsLoading(true)
    try {
      // Fetch document content
      const [contentA, contentB] = await Promise.all([
        documentComparisonService.getDocumentContent(docA),
        documentComparisonService.getDocumentContent(docB),
      ])

      // Load enhanced comparison
      const result = await documentComparisonService.compareDocuments({
        documentA: contentA,
        documentB: contentB,
        type: "both",
        includeVisualDiff: true,
        includeSectionReferences: true,
      })

      setEnhancedResult(result)
    } catch (error) {
      console.error("Error loading comparison data:", error)
      toast({
        title: "Error",
        description: "Failed to load document comparison data",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }, [docA, docB, toast])

  useEffect(() => {
    if (docA && docB) {
      loadComparisonData()
    }
  }, [docA, docB, loadComparisonData])

  const handleExport = async (format: "pdf" | "docx" | "html") => {
    if (!enhancedResult?.id) {
      toast({
        title: "Export Error",
        description: "No comparison ID available for export",
        variant: "destructive",
      })
      return
    }

    toast({
      title: "Export Initiated",
      description: `Exporting comparison as ${format.toUpperCase()}...`,
    })

    try {
      const exportRequest: ExportComparisonRequest = {
        format: format,
        includeMetadata: true,
        highlightChanges: true,
        includeSummary: true
      }
      
      const exportResult = await documentComparisonService.exportComparison(enhancedResult.id, exportRequest)

      // Handle the export result based on format
      if (format === "html") {
        // For HTML, we might open in a new window or display in an iframe
        const htmlContent = exportResult as string
        const newWindow = window.open()
        if (newWindow) {
          newWindow.document.write(htmlContent)
          newWindow.document.close()
        }
      } else {
        // For PDF and DOCX, we download the file
        const blob = exportResult as Blob
        const url = window.URL.createObjectURL(blob)
        const a = document.createElement("a")
        a.href = url
        a.download = `comparison-${enhancedResult.id}.${format}`
        document.body.appendChild(a)
        a.click()
        window.URL.revokeObjectURL(url)
        document.body.removeChild(a)
      }

      toast({
        title: "Export Complete",
        description: `Comparison exported as ${format.toUpperCase()}`,
      })
    } catch (error) {
      console.error("Error exporting comparison:", error)
      toast({
        title: "Export Error",
        description: `Failed to export comparison as ${format.toUpperCase()}`,
        variant: "destructive",
      })
    }
  }

  const handleGetSummary = async () => {
    if (!enhancedResult?.id) {
      toast({
        title: "Summary Error",
        description: "No comparison ID available for summary",
        variant: "destructive",
      })
      return
    }

    setSummaryLoading(true)
    setShowSummary(true)

    try {
      const summary = await documentComparisonService.getExecutiveSummary(enhancedResult.id)
      setExecutiveSummary(summary)
    } catch (error) {
      console.error("Error getting executive summary:", error)
      toast({
        title: "Error",
        description: "Failed to generate executive summary",
        variant: "destructive",
      })
    } finally {
      setSummaryLoading(false)
    }
  }

  if (isLoading) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin mb-4" />
        <p className="text-lg">Loading comparison data...</p>
      </div>
    )
  }

  if (!docA || !docB) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Card className="w-full max-w-md">
          <CardContent className="pt-6">
            <div className="flex flex-col items-center text-center space-y-4">
              <FileText className="h-12 w-12 text-muted-foreground" />
              <h2 className="text-xl font-semibold">No Documents Selected</h2>
              <p className="text-muted-foreground">Please select two documents to compare.</p>
              <Button className="mt-4" onClick={() => router.push("/document-comparison")}>
                <ArrowLeft className="mr-2 h-4 w-4" />
                Back to Document Selection
              </Button>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className="container py-6 max-w-6xl">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-2xl font-bold text-foreground">Enhanced Document Comparison</h1>
        <Button variant="outline" onClick={() => router.push("/document-comparison")}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back to Document Selection
        </Button>
      </div>

      {enhancedResult ? (
        <Card>
          <CardContent className="p-6">
            <EnhancedComparison
              result={enhancedResult}
              onExport={handleExport}
              onSummary={handleGetSummary}
            />
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-6">
            <div className="text-center">
              <p>No comparison data available.</p>
            </div>
          </CardContent>
        </Card>
      )}

      <Dialog open={showSummary} onOpenChange={setShowSummary}>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Executive Summary</DialogTitle>
            <DialogDescription>AI-generated summary of document changes</DialogDescription>
          </DialogHeader>
          <div className="mt-4">
            {summaryLoading ? (
              <div className="flex items-center justify-center py-8">
                <Loader2 className="h-6 w-6 animate-spin mr-2" />
                <p>Generating summary...</p>
              </div>
            ) : executiveSummary ? (
              <div className="whitespace-pre-wrap p-4 bg-secondary/50 rounded-md">{executiveSummary.summary}</div>
            ) : (
              <p className="text-muted-foreground">No summary available</p>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </div>
  )
}

// Wrap the page component with Suspense
export default function EnhancedDocumentComparisonPage() {
  return (
    <Suspense fallback={
      <div className="flex flex-col items-center justify-center min-h-[60vh]">
        <Loader2 className="h-8 w-8 animate-spin mb-4" />
        <p className="text-lg">Loading...</p>
      </div>
    }>
      <FeatureGuard featureId="enhanced_comparison">
        <EnhancedDocumentComparisonContent />
      </FeatureGuard>
    </Suspense>
  )
}
