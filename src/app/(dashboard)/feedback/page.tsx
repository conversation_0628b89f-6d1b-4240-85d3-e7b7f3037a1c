"use client";

import { useState, useEffect, use<PERSON><PERSON>back } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Pagination } from "@/components/ui/pagination";
import { But<PERSON> } from "@/components/ui/button";
import { FeedbackFilters } from "@/components/feedback/feedback-filters";
import { FeedbackTable } from "@/components/feedback/feedback-table";
import { FeedbackService } from "@/lib/services/feedback-service";
import {
	Feedback,
	FeedbackQueryParams,
	FeedbackAnalyticsSummary,
} from "@/lib/types/user-feedback";
import { useToast } from "@/hooks/use-toast";
import {
	BarChart,
	Bar,
	XAxis,
	YAxis,
	CartesianGrid,
	Tooltip,
	Legend,
	ResponsiveContainer,
	<PERSON><PERSON><PERSON>,
	<PERSON>,
	Cell,
} from "recharts";
import {
	Thum<PERSON>Up,
	Thum<PERSON>Down,
	Message<PERSON><PERSON><PERSON>,
	Clock,
	CheckCircle,
	RefreshCw,
} from "lucide-react"; // Removed unused Lightbulb and XCircle imports

export default function FeedbackDashboardPage() {
	const [activeTab, setActiveTab] = useState("all");
	const [feedbackItems, setFeedbackItems] = useState<Feedback[]>([]);
	const [filters, setFilters] = useState<FeedbackQueryParams>({});
	const [pagination, setPagination] = useState({
		total: 0,
		page: 1,
		limit: 20,
		pages: 0,
	});
	const [loading, setLoading] = useState(true);
	const [analyticsData, setAnalyticsData] =
		useState<FeedbackAnalyticsSummary | null>(null);
	const { toast } = useToast();

	const fetchFeedback = useCallback(async () => {
		setLoading(true);
		try {
			const queryParams: FeedbackQueryParams = {
				...filters,
				page: pagination.page,
				limit: pagination.limit,
			};

			// Apply tab-specific filters
			if (activeTab === "pending") {
				queryParams.status = "pending";
			} else if (activeTab === "reviewed") {
				queryParams.status = "reviewed";
			} else if (activeTab === "implemented") {
				queryParams.status = "implemented";
			} else if (activeTab === "positive") {
				queryParams.type = "thumbs_up";
			} else if (activeTab === "negative") {
				queryParams.type = "thumbs_down";
			} else if (activeTab === "corrections") {
				queryParams.type = "correction";
			}

			const response = await FeedbackService.getFeedback(queryParams);
			setFeedbackItems(response.data);
			setPagination(response.pagination);
		} catch (error) {
			console.error("Error fetching feedback:", error);
			toast({
				title: "Error",
				description: "Failed to fetch feedback data. Please try again.",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	}, [activeTab, filters, pagination.page, pagination.limit, toast]);

	const fetchAnalytics = useCallback(async () => {
		try {
			const data = await FeedbackService.getFeedbackAnalytics();
			setAnalyticsData(data);
		} catch (error) {
			console.error("Error fetching analytics:", error);
			toast({
				title: "Error",
				description: "Failed to fetch analytics data. Please try again.",
				variant: "destructive",
			});
		}
	}, [toast]);

	useEffect(() => {
		fetchFeedback();
		fetchAnalytics();
	}, [activeTab, pagination.page, filters, fetchFeedback, fetchAnalytics]);

	const handleFilterChange = (newFilters: FeedbackQueryParams) => {
		setFilters(newFilters);
		setPagination((prev) => ({ ...prev, page: 1 })); // Reset to first page when filters change
	};

	const handlePageChange = (page: number) => {
		setPagination((prev) => ({ ...prev, page }));
	};

	const handleRefresh = () => {
		fetchFeedback();
		fetchAnalytics();
	};

	// Colors for charts
	const COLORS = ["#4f46e5", "#10b981", "#f59e0b", "#ef4444", "#6b7280"];
	const typeColors = {
		thumbs_up: "#10b981",
		thumbs_down: "#ef4444",
		correction: "#4f46e5",
		suggestion: "#f59e0b",
		general: "#6b7280",
	};

	const statusColors = {
		pending: "#f59e0b",
		reviewed: "#4f46e5",
		implemented: "#10b981",
		rejected: "#ef4444",
	};

	return (
		<div className="container mx-auto py-6 space-y-6">
			<div className="flex justify-between items-center">
				<div>
					<h1 className="text-3xl font-bold tracking-tight">User Feedback</h1>
					<p className="text-muted-foreground">
						Manage and analyze user feedback to improve your service
					</p>
				</div>
				<Button onClick={handleRefresh} variant="outline" className="gap-2">
					<RefreshCw className="h-4 w-4" />
					Refresh
				</Button>
			</div>

			<Tabs defaultValue="all" value={activeTab} onValueChange={setActiveTab}>
				<div className="flex justify-between items-end">
					<TabsList>
						<TabsTrigger value="all">All Feedback</TabsTrigger>
						<TabsTrigger value="pending" className="flex items-center gap-1">
							<Clock className="h-4 w-4" />
							Pending
						</TabsTrigger>
						<TabsTrigger value="reviewed" className="flex items-center gap-1">
							<CheckCircle className="h-4 w-4" />
							Reviewed
						</TabsTrigger>
						<TabsTrigger
							value="implemented"
							className="flex items-center gap-1"
						>
							<CheckCircle className="h-4 w-4" />
							Implemented
						</TabsTrigger>
						<TabsTrigger value="positive" className="flex items-center gap-1">
							<ThumbsUp className="h-4 w-4" />
							Positive
						</TabsTrigger>
						<TabsTrigger value="negative" className="flex items-center gap-1">
							<ThumbsDown className="h-4 w-4" />
							Negative
						</TabsTrigger>
						<TabsTrigger
							value="corrections"
							className="flex items-center gap-1"
						>
							<MessageSquare className="h-4 w-4" />
							Corrections
						</TabsTrigger>
					</TabsList>
				</div>

				<TabsContent value="analytics" className="mt-6 space-y-6">
					{analyticsData && (
						<div className="grid grid-cols-1 md:grid-cols-3 gap-6">
							<Card>
								<CardHeader className="pb-2">
									<CardTitle>Feedback Overview</CardTitle>
									<CardDescription>
										Total feedback: {analyticsData.totalFeedback}
									</CardDescription>
								</CardHeader>
								<CardContent>
									<ResponsiveContainer width="100%" height={200}>
										<PieChart>
											<Pie
												data={analyticsData.typeBreakdown}
												cx="50%"
												cy="50%"
												labelLine={false}
												outerRadius={80}
												fill="#8884d8"
												dataKey="count"
												nameKey="type"
												label={({ name, percent }) =>
													`${name} ${(percent * 100).toFixed(0)}%`
												}
											>
												{analyticsData.typeBreakdown.map((entry: FeedbackAnalyticsSummary['typeBreakdown'][0], index: number) => (
													<Cell
														key={`cell-${index}`}
														fill={
															typeColors[
																entry.type as keyof typeof typeColors
															] || COLORS[index % COLORS.length]
														}
													/>
												))}
											</Pie>
											<Tooltip />
										</PieChart>
									</ResponsiveContainer>
								</CardContent>
							</Card>

							<Card>
								<CardHeader className="pb-2">
									<CardTitle>Status Breakdown</CardTitle>
									<CardDescription>Feedback by status</CardDescription>
								</CardHeader>
								<CardContent>
									<ResponsiveContainer width="100%" height={200}>
										<PieChart>
											<Pie
												data={analyticsData.statusBreakdown}
												cx="50%"
												cy="50%"
												labelLine={false}
												outerRadius={80}
												fill="#8884d8"
												dataKey="count"
												nameKey="status"
												label={({ name, percent }) =>
													`${name} ${(percent * 100).toFixed(0)}%`
												}
											>
												{analyticsData.statusBreakdown.map((entry: FeedbackAnalyticsSummary['statusBreakdown'][0], index: number) => (
													<Cell
														key={`cell-${index}`}
														fill={
															statusColors[
																entry.status as keyof typeof statusColors
															] || COLORS[index % COLORS.length]
														}
													/>
												))}
											</Pie>
											<Tooltip />
										</PieChart>
									</ResponsiveContainer>
								</CardContent>
							</Card>

							<Card>
								<CardHeader className="pb-2">
									<CardTitle>Source Breakdown</CardTitle>
									<CardDescription>Feedback by source</CardDescription>
								</CardHeader>
								<CardContent>
									<ResponsiveContainer width="100%" height={200}>
										<BarChart
											data={analyticsData.sourceBreakdown}
											layout="vertical"
											margin={{ top: 5, right: 30, left: 80, bottom: 5 }}
										>
											<CartesianGrid strokeDasharray="3 3" />
											<XAxis type="number" />
											<YAxis
												type="category"
												dataKey="source"
												tick={{ fontSize: 12 }}
												tickFormatter={(value) => value.replace(/_/g, " ")}
											/>
											<Tooltip
												formatter={(value: number) => [value, "Count"]}
												labelFormatter={(label) => label.replace(/_/g, " ")}
											/>
											<Bar dataKey="count" fill="#4f46e5" />
										</BarChart>
									</ResponsiveContainer>
								</CardContent>
							</Card>
						</div>
					)}

					{analyticsData && (
						<Card>
							<CardHeader>
								<CardTitle>Feedback Over Time</CardTitle>
								<CardDescription>Trend of feedback submissions</CardDescription>
							</CardHeader>
							<CardContent>
								<ResponsiveContainer width="100%" height={300}>
									<BarChart
										data={analyticsData.timeSeriesData}
										margin={{ top: 20, right: 30, left: 20, bottom: 5 }}
									>
										<CartesianGrid strokeDasharray="3 3" />
										<XAxis dataKey="_id" />
										<YAxis />
										<Tooltip />
										<Legend />
										<Bar dataKey="thumbsUp" name="Positive" fill="#10b981" />
										<Bar dataKey="thumbsDown" name="Negative" fill="#ef4444" />
										<Bar
											dataKey="corrections"
											name="Corrections"
											fill="#4f46e5"
										/>
									</BarChart>
								</ResponsiveContainer>
							</CardContent>
						</Card>
					)}
				</TabsContent>

				<TabsContent value="all" className="mt-6 space-y-6">
					<Card>
						<CardHeader>
							<CardTitle>Filter Feedback</CardTitle>
							<CardDescription>
								Use the filters below to find specific feedback
							</CardDescription>
						</CardHeader>
						<CardContent>
							<FeedbackFilters onFilterChange={handleFilterChange} />
						</CardContent>
					</Card>

					<div className="space-y-4">
						<h2 className="text-xl font-semibold tracking-tight">
							{loading
								? "Loading feedback..."
								: `Feedback Results (${pagination.total})`}
						</h2>
						<FeedbackTable
							feedbackItems={feedbackItems}
							onFeedbackUpdated={fetchFeedback}
						/>

						{pagination.pages > 1 && (
							<div className="flex justify-center mt-4">
								<Pagination
									currentPage={pagination.page}
									totalPages={pagination.pages}
									onPageChange={handlePageChange}
								/>
							</div>
						)}
					</div>
				</TabsContent>

				{/* Other tabs share the same content structure */}
				{[
					"pending",
					"reviewed",
					"implemented",
					"positive",
					"negative",
					"corrections",
				].map((tab) => (
					<TabsContent key={tab} value={tab} className="mt-6 space-y-6">
						<Card>
							<CardHeader>
								<CardTitle>Filter Feedback</CardTitle>
								<CardDescription>
									Use the filters below to find specific feedback
								</CardDescription>
							</CardHeader>
							<CardContent>
								<FeedbackFilters onFilterChange={handleFilterChange} />
							</CardContent>
						</Card>

						<div className="space-y-4">
							<h2 className="text-xl font-semibold tracking-tight">
								{loading
									? "Loading feedback..."
									: `Feedback Results (${pagination.total})`}
							</h2>
							<FeedbackTable
								feedbackItems={feedbackItems}
								onFeedbackUpdated={fetchFeedback}
							/>

							{pagination.pages > 1 && (
								<div className="flex justify-center mt-4">
									<Pagination
										currentPage={pagination.page}
										totalPages={pagination.pages}
										onPageChange={handlePageChange}
									/>
								</div>
							)}
						</div>
					</TabsContent>
				))}
			</Tabs>
		</div>
	);
}
