"use client";

import { useState } from "react";
import { DevOnlyRoute } from "@/components/dev-only-wrapper";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { CollaborationDashboard } from "@/components/collaboration/collaboration-dashboard";
import { RealTimeEditor } from "@/components/collaboration/real-time-editor";
import { ThreadedComments } from "@/components/collaboration/threaded-comments";
import { WorkflowManager } from "@/components/collaboration/workflow-manager";
import { TaskManager } from "@/components/collaboration/task-manager";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { 
  Users, 
  MessageSquare, 
  GitBranch, 
  CheckSquare, 
  BarChart3,
  FileText,
  Plus
} from "lucide-react";
import { useSubscription } from "@/lib/subscription/subscription-context";

export default function CollaborationPage() {
  return (
    <DevOnlyRoute>
      <CollaborationPageContent />
    </DevOnlyRoute>
  );
}

function CollaborationPageContent() {
  const { subscription } = useSubscription();
  const [activeTab, setActiveTab] = useState("dashboard");
  const [selectedDocumentId, setSelectedDocumentId] = useState<string | null>(null);
  const [editorContent, setEditorContent] = useState("");

  // Check subscription tier access
  const hasBasicAccess = subscription?.tier && ["law_student", "lawyer", "law_firm"].includes(subscription.tier);
  const hasAdvancedAccess = subscription?.tier && ["lawyer", "law_firm"].includes(subscription.tier);
  const hasFullAccess = subscription?.tier === "law_firm";

  if (!hasBasicAccess) {
    return (
      <div className="container mx-auto py-8">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Collaboration Suite
            </CardTitle>
          </CardHeader>
          <CardContent className="text-center py-8">
            <p className="text-muted-foreground mb-4">
              Collaboration features are available starting with the Law Student tier.
            </p>
            <Button onClick={() => window.location.href = '/subscription'}>
              Upgrade Subscription
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h1 className="text-3xl font-bold">Collaboration Suite</h1>
          <p className="text-muted-foreground">
            Real-time collaboration, workflows, and team productivity tools
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {subscription?.tier === "law_student" && "Basic Access"}
            {subscription?.tier === "lawyer" && "Advanced Access"}
            {subscription?.tier === "law_firm" && "Full Access"}
          </Badge>
        </div>
      </div>

      {/* Main Content */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-6">
        <TabsList className="grid grid-cols-6 w-full max-w-4xl">
          <TabsTrigger value="dashboard" className="flex items-center gap-2">
            <BarChart3 className="h-4 w-4" />
            Dashboard
          </TabsTrigger>
          <TabsTrigger value="editor" className="flex items-center gap-2" disabled={!hasAdvancedAccess}>
            <FileText className="h-4 w-4" />
            Editor
          </TabsTrigger>
          <TabsTrigger value="comments" className="flex items-center gap-2">
            <MessageSquare className="h-4 w-4" />
            Comments
          </TabsTrigger>
          <TabsTrigger value="workflows" className="flex items-center gap-2" disabled={!hasAdvancedAccess}>
            <GitBranch className="h-4 w-4" />
            Workflows
          </TabsTrigger>
          <TabsTrigger value="tasks" className="flex items-center gap-2" disabled={!hasAdvancedAccess}>
            <CheckSquare className="h-4 w-4" />
            Tasks
          </TabsTrigger>
          <TabsTrigger value="analytics" className="flex items-center gap-2" disabled={!hasFullAccess}>
            <BarChart3 className="h-4 w-4" />
            Analytics
          </TabsTrigger>
        </TabsList>

        {/* Dashboard Tab */}
        <TabsContent value="dashboard" className="space-y-6">
          <CollaborationDashboard documentId={selectedDocumentId || undefined} />
        </TabsContent>

        {/* Real-time Editor Tab */}
        <TabsContent value="editor" className="space-y-6">
          {hasAdvancedAccess ? (
            <div className="space-y-4">
              <div className="flex items-center justify-between">
                <h2 className="text-xl font-semibold">Real-time Collaborative Editor</h2>
                <Button onClick={() => setSelectedDocumentId("demo-doc-" + Date.now())}>
                  <Plus className="h-4 w-4 mr-2" />
                  New Document
                </Button>
              </div>
              {selectedDocumentId ? (
                <RealTimeEditor
                  documentId={selectedDocumentId}
                  content={editorContent}
                  onContentChange={setEditorContent}
                />
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <FileText className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">
                      Select a document or create a new one to start collaborative editing
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">
                  Real-time editing requires Lawyer tier or higher
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Comments Tab */}
        <TabsContent value="comments" className="space-y-6">
          <div className="space-y-4">
            <h2 className="text-xl font-semibold">Threaded Discussions</h2>
            {selectedDocumentId ? (
              <ThreadedComments documentId={selectedDocumentId} />
            ) : (
              <Card>
                <CardContent className="text-center py-8">
                  <MessageSquare className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Select a document to view and create comment threads
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </TabsContent>

        {/* Workflows Tab */}
        <TabsContent value="workflows" className="space-y-6">
          {hasAdvancedAccess ? (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Workflow Management</h2>
              {selectedDocumentId ? (
                <WorkflowManager documentId={selectedDocumentId} />
              ) : (
                <Card>
                  <CardContent className="text-center py-8">
                    <GitBranch className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                    <p className="text-muted-foreground">
                      Select a document to manage workflows
                    </p>
                  </CardContent>
                </Card>
              )}
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">
                  Workflow management requires Lawyer tier or higher
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Tasks Tab */}
        <TabsContent value="tasks" className="space-y-6">
          {hasAdvancedAccess ? (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Task Management</h2>
              <TaskManager documentId={selectedDocumentId || undefined} />
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">
                  Task management requires Lawyer tier or higher
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>

        {/* Analytics Tab */}
        <TabsContent value="analytics" className="space-y-6">
          {hasFullAccess ? (
            <div className="space-y-4">
              <h2 className="text-xl font-semibold">Collaboration Analytics</h2>
              <Card>
                <CardContent className="text-center py-8">
                  <BarChart3 className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
                  <p className="text-muted-foreground">
                    Team analytics and collaboration metrics coming soon
                  </p>
                </CardContent>
              </Card>
            </div>
          ) : (
            <Card>
              <CardContent className="text-center py-8">
                <p className="text-muted-foreground">
                  Analytics features are exclusive to Law Firm tier
                </p>
              </CardContent>
            </Card>
          )}
        </TabsContent>
      </Tabs>
    </div>
  );
}
