"use client";

import { useEffect, useState } from 'react';
import { ClauseTemplateList } from '@/components/clause-library/ClauseTemplateList';
import { ClauseIdentifier } from '@/components/clause-library/ClauseIdentifier';
import { TemplateGenerationForm } from '@/components/clause-library/TemplateGenerationForm';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { documentService, type DocumentMetadata } from '@/lib/services/document-service';

export default function ClauseLibraryPage() {
  const [documents, setDocuments] = useState<DocumentMetadata[]>([]);
  const [selectedDocId, setSelectedDocId] = useState<string>('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        const response = await documentService.getDocuments(1, 50);
        setDocuments(response.items);
      } catch (err) {
        setError('Failed to fetch documents');
        console.error('Error fetching documents:', err);
      } finally {
        setLoading(false);
      }
    };

    fetchDocuments();
  }, []);
  return (
    <div className="container py-6 space-y-6">
      <div className="flex justify-between items-center">
        <h1 className="text-3xl font-bold">Clause Library</h1>
      </div>

      <Tabs defaultValue="templates" className="w-full">
        <TabsList>
          <TabsTrigger value="templates">Templates</TabsTrigger>
          <TabsTrigger value="identify">Identify Clauses</TabsTrigger>
          <TabsTrigger value="create">Create Template</TabsTrigger>
        </TabsList>

        <TabsContent value="templates" className="mt-6">
          <ClauseTemplateList />
        </TabsContent>

        <TabsContent value="identify" className="mt-6">
          <div className="space-y-6">
            {loading ? (
              <div>Loading documents...</div>
            ) : error ? (
              <div className="text-red-500">{error}</div>
            ) : (
              <div className="space-y-2">
                <h3 className="text-sm font-medium">Select Document</h3>
                <Select value={selectedDocId} onValueChange={setSelectedDocId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a document" />
                  </SelectTrigger>
                  <SelectContent>
                    {documents.map(doc => (
                      <SelectItem key={doc.id} value={doc.id}>
                        {doc.name || doc.filename}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            )}

            {selectedDocId && (
              <ClauseIdentifier
                documentId={selectedDocId}
                onMatchesFound={(matches) => {
                  console.log('Found matches:', matches);
                }}
              />
            )}
          </div>
        </TabsContent>

        <TabsContent value="create" className="mt-6">
          <TemplateGenerationForm />
        </TabsContent>
      </Tabs>
    </div>
  );
}