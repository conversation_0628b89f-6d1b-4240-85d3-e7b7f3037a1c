"use client";

import React from "react";
import { useRouter } from "next/navigation";
import { Plus, Target, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { SampleNegotiationPlaybooksBrowser } from "@/components/sample-playbooks";

export default function NegotiationPlaybooksPage() {
  const router = useRouter();

  return (
    <div className="container mx-auto p-6 space-y-8">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div>
            <h1 className="text-3xl font-bold tracking-tight">Negotiation Playbooks</h1>
            <p className="text-muted-foreground mt-2">
              Strategic negotiation guidance and expert templates for contract negotiations
            </p>
          </div>
          <Button onClick={() => router.push("/negotiation-playbooks/create")}>
            <Plus className="h-4 w-4 mr-2" />
            Create Playbook
          </Button>
        </div>

        {/* Sample Playbooks Section */}
        <div className="space-y-4">
          <div className="border-b pb-4">
            <SampleNegotiationPlaybooksBrowser 
              onPlaybookCloned={() => {
                // Could refresh user playbooks here if we had them
                console.log("Sample playbook cloned");
              }}
            />
          </div>
        </div>

        {/* User Playbooks Section */}
        <div className="space-y-4">
          <div className="flex items-center gap-2 pt-6">
            <Target className="h-5 w-5 text-primary" />
            <h2 className="text-xl font-semibold">Your Negotiation Playbooks</h2>
            <Badge variant="outline">0 playbooks</Badge>
          </div>

          {/* Empty State for User Playbooks */}
          <Card>
            <CardContent className="p-12 text-center">
              <Target className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-xl font-semibold mb-2">No Personal Negotiation Playbooks Yet</h3>
              <p className="text-muted-foreground mb-6">
                Create your first custom negotiation playbook or clone one from the sample templates above to get started.
              </p>
              <div className="flex gap-3 justify-center">
                <Button onClick={() => router.push("/negotiation-playbooks/create")}>
                  Create New Playbook
                </Button>
                <Button variant="outline" onClick={() => window.scrollTo({ top: 0, behavior: 'smooth' })}>
                  Browse Sample Templates
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>

        {/* Info Cards */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mt-8">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Sparkles className="h-5 w-5 text-primary" />
                Expert Templates
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Access professionally crafted negotiation strategies from legal experts for common contract types.
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Target className="h-5 w-5 text-primary" />
                Strategic Guidance
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Get tactical recommendations, leverage points, and deal-breaker identification for effective negotiations.
              </CardDescription>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-base">
                <Target className="h-5 w-5 text-primary" />
                Simulation Scenarios
              </CardTitle>
            </CardHeader>
            <CardContent>
              <CardDescription>
                Practice with realistic negotiation scenarios and learn optimal responses for various situations.
              </CardDescription>
            </CardContent>
          </Card>
        </div>
    </div>
  );
}
