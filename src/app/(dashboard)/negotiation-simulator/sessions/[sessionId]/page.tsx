"use client";

import { useParams, useSearchParams } from 'next/navigation';
import { useState, useEffect } from 'react';
import { NegotiationSession } from '@/components/negotiation-simulator';
import { ChatNegotiation } from '@/components/negotiation-simulator/chat-negotiation';
import { DocumentChatNegotiation } from '@/components/negotiation-simulator/document-chat-negotiation';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { MessageSquare, Settings, BarChart3, ArrowLeft } from 'lucide-react';
import { useRouter } from 'next/navigation';

export default function NegotiationSessionPage() {
  const params = useParams();
  const searchParams = useSearchParams();
  const router = useRouter();
  const sessionId = params.sessionId as string;

  // Check if chat mode is requested via URL parameter
  const initialMode = searchParams.get('mode') === 'chat' ? 'chat' : 'traditional';
  const [mode, setMode] = useState<'traditional' | 'chat'>(initialMode);

  // Check if this is a document-based session
  const isDocumentBased = searchParams.get('source') === 'document';

  const handleModeChange = (newMode: 'traditional' | 'chat') => {
    setMode(newMode);
    // Update URL without page reload
    const url = new URL(window.location.href);
    if (newMode === 'chat') {
      url.searchParams.set('mode', 'chat');
    } else {
      url.searchParams.delete('mode');
    }
    window.history.replaceState({}, '', url.toString());
  };

  const handleBackToSessions = () => {
    router.push('/negotiation-simulator');
  };

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header with Mode Selection */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={handleBackToSessions} className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Sessions
          </Button>
          <div>
            <h1 className="text-2xl font-bold">Negotiation Session</h1>
            <p className="text-muted-foreground">Session ID: {sessionId}</p>
          </div>
        </div>

        <div className="flex items-center gap-2">
          <Badge variant="outline">
            {mode === 'chat' ? 'Chat Mode' : 'Traditional Mode'}
          </Badge>
        </div>
      </div>

      {/* Mode Selection Tabs */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Settings className="h-5 w-5" />
            Negotiation Interface
          </CardTitle>
        </CardHeader>
        <CardContent>
          <Tabs value={mode} onValueChange={(value: any) => handleModeChange(value)}>
            <TabsList className="grid w-full grid-cols-2">
              <TabsTrigger value="traditional" className="gap-2">
                <BarChart3 className="h-4 w-4" />
                Traditional Simulator
              </TabsTrigger>
              <TabsTrigger value="chat" className="gap-2">
                <MessageSquare className="h-4 w-4" />
                Chat Negotiation
              </TabsTrigger>
            </TabsList>

            <div className="mt-4">
              <TabsContent value="traditional" className="space-y-4">
                <div className="p-4 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                  <h3 className="font-medium text-blue-900 dark:text-blue-100 mb-2">
                    Traditional Negotiation Simulator
                  </h3>
                  <p className="text-sm text-blue-700 dark:text-blue-300">
                    Structured rounds with formal move submission, detailed analytics, and comprehensive scoring.
                    Perfect for learning specific negotiation strategies and tactics.
                  </p>
                </div>
              </TabsContent>

              <TabsContent value="chat" className="space-y-4">
                <div className="p-4 bg-green-50 dark:bg-green-950/20 rounded-lg">
                  <h3 className="font-medium text-green-900 dark:text-green-100 mb-2">
                    {isDocumentBased ? 'Document-Aware Chat Negotiation' : 'Interactive Chat Negotiation'}
                  </h3>
                  <p className="text-sm text-green-700 dark:text-green-300">
                    {isDocumentBased
                      ? 'AI negotiator trained on your specific contract issues. Practice addressing real problems from your document analysis.'
                      : 'Natural conversation with AI negotiators, real-time relationship tracking, and automatic term extraction. Great for practicing communication skills and building rapport.'
                    }
                  </p>
                </div>
              </TabsContent>
            </div>
          </Tabs>
        </CardContent>
      </Card>

      {/* Render the appropriate interface */}
      {mode === 'traditional' ? (
        <NegotiationSession sessionId={sessionId} />
      ) : (
        <div className="space-y-4">
          {isDocumentBased ? (
            /* Document-Based Chat Negotiation */
            <DocumentChatNegotiation
              sessionId={sessionId}
              onComplete={(results) => {
                console.log('Document chat negotiation completed:', results);
                // Could integrate results back into the session
              }}
            />
          ) : (
            /* Standard Chat Negotiation */
            <>
              <Card className="bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <h3 className="font-medium">Chat Negotiation Mode</h3>
                      <p className="text-sm text-muted-foreground">
                        Practice natural conversation-based negotiation with AI
                      </p>
                    </div>
                    <Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
                      Interactive
                    </Badge>
                  </div>
                </CardContent>
              </Card>

              <ChatNegotiation
                scenarioId="software_licensing" // Default scenario, could be derived from session
                aiCharacter={{
                  name: "Alex Chen",
                  title: "Senior Sales Director",
                  personality: ["analytical", "collaborative", "results-driven"],
                  backstory: "15 years in enterprise software sales with a focus on building long-term partnerships."
                }}
                onComplete={(results) => {
                  console.log('Chat negotiation completed:', results);
                  // Could integrate results back into the session
                }}
                useBackend={true}
              />
            </>
          )}
        </div>
      )}
    </div>
  );
}
