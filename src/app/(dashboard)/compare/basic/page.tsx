"use client";

import { useState, useEffect, useMemo } from "react";
import { useRouter } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
  CardFooter,
} from "@/components/ui/card";
import { 
  Loader2, 
  FileText, 
  ArrowLeftRight, 
  AlertCircle, 
  Search, 
  Clock, 
  FlipHorizontalIcon as SwitchHorizontal, 
  Percent, 
  ChevronLeft, 
  Eye, 
  Code, 
  FileDown, 
  History, 
  Trash2, 
  Lock, 
  Sparkles 
} from "lucide-react";
import {
  documentComparisonService,
  type BasicComparisonResponse,
  type BasicComparisonRequest,
} from "@/lib/services/document-comparison-service";
import {
  documentService,
  type DocumentMetadata,
} from "@/lib/services/document-service";
import { useToast } from "@/hooks/use-toast";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { cn } from "@/lib/utils";
import { Input } from "@/components/ui/input";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { Progress } from "@/components/ui/progress";
import {
  Tooltip,
  TooltipContent,
  TooltipProvider,
  TooltipTrigger,
} from "@/components/ui/tooltip";
import { Separator } from "@/components/ui/separator";
import { SimpleDocumentSelector } from "@/components/document-comparison/simple-document-selector";
import { useUser } from "@/hooks/use-user";

// Type for recent comparisons
interface RecentComparison {
  id: string;
  documentAId: string;
  documentBId: string;
  documentAName: string;
  documentBName: string;
  timestamp: Date;
  similarityScore: number;
}

export default function BasicComparisonPage() {
  const router = useRouter();
  const { toast } = useToast();
  // Get user subscription status
  const { user } = useUser();
  const isPro = user?.subscription?.tier === "pro";
  const [documents, setDocuments] = useState<DocumentMetadata[]>([]);
  const [documentAId, setDocumentAId] = useState("");
  const [documentBId, setDocumentBId] = useState("");
  const [documentAContent, setDocumentAContent] = useState("");
  const [documentBContent, setDocumentBContent] = useState("");
  const [isLoadingContent, setIsLoadingContent] = useState(false);
  const [isLoading, setIsLoading] = useState(false);
  const [comparisonResult, setComparisonResult] =
    useState<BasicComparisonResponse | null>(null);
  const [activeTab, setActiveTab] = useState<string>("visual");
  const [searchTerm, setSearchTerm] = useState("");
  const [filterOptions, setFilterOptions] = useState({
    showAdditions: true,
    showDeletions: true,
    showModifications: true,
  });
  const [recentComparisons, setRecentComparisons] = useState<
    RecentComparison[]
  >([]);
  const [showSideBySide, setShowSideBySide] = useState(false);

  // Get document names for display in the comparison result
  const getDocumentName = (docId: string): string => {
    const doc = documents.find((d) => d.id === docId);
    return doc
      ? doc.name || doc.filename || `Document ${docId.substring(0, 6)}...`
      : docId;
  };

  // Calculate similarity score based on comparison result
  const similarityScore = useMemo(() => {
    if (!comparisonResult?.data?.metadata?.summary) return 0;

    const { data } = comparisonResult;
    const summary = data.metadata?.summary;
    
    // Get the total changes (added + removed + modified lines)
    const totalChanges = summary?.totalChanges || 0;
    
    // Get the total content size (in words) to estimate document size
    const docA = data.metadata?.documentStats?.documentA;
    const docB = data.metadata?.documentStats?.documentB;
    const totalWords = (docA?.wordCount || 0) + (docB?.wordCount || 0);
    
    // If we have no content, return 100% similarity (empty docs are identical)
    if (totalWords === 0) return 100;
    
    // Calculate similarity based on the ratio of changes to document size
    // Use a scaling factor to account for the difference between lines and words
    // Average words per line is typically around 10-15
    const averageWordsPerLine = 12;
    const estimatedTotalLines = Math.max(1, Math.round(totalWords / averageWordsPerLine));
    
    // Calculate similarity percentage
    return Math.max(0, Math.min(100, Math.round(100 - (totalChanges / estimatedTotalLines) * 100)));
  }, [comparisonResult]);

  // Fetch documents when component mounts
  useEffect(() => {
    const fetchDocuments = async () => {
      setIsLoading(true);
      try {
        const response = await documentService.getDocuments(1, 100);
        setDocuments(response.items || []);
      } catch (err) {
        console.error("Error fetching documents:", err);
        toast({
          title: "Error",
          description: "Failed to load documents. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocuments();

    // Load recent comparisons from localStorage
    const storedComparisons = localStorage.getItem("recentComparisons");
    if (storedComparisons) {
      try {
        const parsedComparisons = JSON.parse(storedComparisons);
        // Define type for parsed comparisons from localStorage (with string timestamp)
        type StoredComparison = Omit<RecentComparison, 'timestamp'> & { timestamp: string };
        
        // Convert string dates back to Date objects
        const comparisonsWithDates = parsedComparisons.map((comp: StoredComparison) => ({
          ...comp,
          timestamp: new Date(comp.timestamp),
        }));
        setRecentComparisons(comparisonsWithDates);
      } catch (e) {
        console.error("Error parsing recent comparisons:", e);
      }
    }
  }, [toast]);

  // Fetch document content when document ID changes
  useEffect(() => {
    const fetchDocumentContent = async (
      docId: string,
      setContent: (content: string) => void
    ) => {
      if (!docId) return;

      setIsLoadingContent(true);
      try {
        const content = await documentService.getDocumentContent(docId);
        setContent(content);
      } catch (err) {
        console.error(`Error fetching document content for ${docId}:`, err);
        toast({
          title: "Error",
          description: "Failed to load document content. Please try again.",
          variant: "destructive",
        });
      } finally {
        setIsLoadingContent(false);
      }
    };

    if (documentAId) {
      fetchDocumentContent(documentAId, setDocumentAContent);
    }

    if (documentBId) {
      fetchDocumentContent(documentBId, setDocumentBContent);
    }
  }, [documentAId, documentBId, toast]);

  const handleCompare = async () => {
    if (!documentAId || !documentBId) {
      toast({
        title: "Missing documents",
        description: "Please select both documents to compare",
        variant: "destructive",
      });
      return;
    }

    if (!documentAContent || !documentBContent) {
      toast({
        title: "Missing document content",
        description: "Document content is still loading or failed to load",
        variant: "destructive",
      });
      return;
    }

    setIsLoading(true);

    try {
      const request: BasicComparisonRequest = {
        documentA: documentAContent,
        documentB: documentBContent,
        documentAId,
        documentBId,
      };

      const result = await documentComparisonService.compareBasic(request);
      setComparisonResult(result);
      setActiveTab("visual");

      // Add to recent comparisons
      const newComparison: RecentComparison = {
        id: `${documentAId}-${documentBId}-${Date.now()}`,
        documentAId,
        documentBId,
        documentAName: getDocumentName(documentAId),
        documentBName: getDocumentName(documentBId),
        timestamp: new Date(),
        similarityScore: similarityScore,
      };

      const updatedComparisons = [
        newComparison,
        ...recentComparisons.slice(0, 4),
      ];
      setRecentComparisons(updatedComparisons);

      // Save to localStorage
      localStorage.setItem(
        "recentComparisons",
        JSON.stringify(updatedComparisons)
      );
    } catch (error) {
      console.error("Error comparing documents:", error);
      toast({
        title: "Comparison failed",
        description:
          error instanceof Error
            ? error.message
            : "Failed to compare documents. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const handleReset = () => {
    setComparisonResult(null);
    setDocumentAId("");
    setDocumentBId("");
    setDocumentAContent("");
    setDocumentBContent("");
    setActiveTab("visual");
    setSearchTerm("");
    setFilterOptions({
      showAdditions: true,
      showDeletions: true,
      showModifications: true,
    });
    setShowSideBySide(false);
  };
  
  // Navigate to enhanced comparison page with selected documents
  const handleEnhancedCompare = () => {
    if (isPro && documentAId && documentBId) {
      router.push(`/compare/enhanced?docA=${documentAId}&docB=${documentBId}`);
    } else if (!isPro) {
      toast({
        title: "Pro Feature",
        description: "Enhanced comparison is available for Pro subscribers only.",
        variant: "default"
      });
    }
  };

  const handleSwapDocuments = () => {
    setDocumentAId(documentBId);
    setDocumentBId(documentAId);
    setDocumentAContent(documentBContent);
    setDocumentBContent(documentAContent);
  };

  const handleLoadRecentComparison = (comparison: RecentComparison) => {
    setDocumentAId(comparison.documentAId);
    setDocumentBId(comparison.documentBId);
  };

  const handleClearRecentComparisons = () => {
    setRecentComparisons([]);
    localStorage.removeItem("recentComparisons");
    toast({
      title: "History cleared",
      description: "Your comparison history has been cleared.",
    });
  };

  const handleExportComparison = () => {
    if (!comparisonResult) return;

    const { data } = comparisonResult;

    // Create a formatted HTML document with the comparison
    const htmlContent = `
      <!DOCTYPE html>
      <html>
      <head>
        <title>Document Comparison: ${getDocumentName(
          documentAId
        )} vs ${getDocumentName(documentBId)}</title>
        <style>
          body { font-family: Arial, sans-serif; margin: 20px; }
          h1 { color: #333; }
          .summary { margin: 20px 0; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
          .stats { display: flex; gap: 20px; }
          .stat-box { padding: 10px; border: 1px solid #eee; border-radius: 5px; flex: 1; }
          .diff-content { margin-top: 20px; padding: 15px; border: 1px solid #ddd; border-radius: 5px; }
          .footer { margin-top: 30px; font-size: 12px; color: #666; }
          /* Generated diff styles */
          span[style*="background-color: ${data.visualization.colors.addition}"] {
            background-color: rgba(74, 222, 128, 0.2);
            color: #166534;
          }
          span[style*="background-color: ${data.visualization.colors.deletion}"] {
            background-color: rgba(248, 113, 113, 0.2);
            color: #b91c1c;
          }
          span[style*="background-color: ${data.visualization.colors.modification}"] {
            background-color: rgba(251, 191, 36, 0.2);
            color: #92400e;
          }
          @media (prefers-color-scheme: dark) {
            body { background-color: #1a1a1a; color: #e0e0e0; }
            h1 { color: #e0e0e0; }
            .summary, .diff-content { border-color: #333; }
            .stat-box { border-color: #333; background-color: #222; }
            span[style*="background-color: ${data.visualization.colors.addition}"] {
              background-color: rgba(22, 163, 74, 0.85);
              color: white;
            }
            span[style*="background-color: ${data.visualization.colors.deletion}"] {
              background-color: rgba(220, 38, 38, 0.85);
              color: white;
            }
            span[style*="background-color: ${data.visualization.colors.modification}"] {
              background-color: rgba(217, 119, 6, 0.85);
              color: white;
            }
          }
        </style>
      </head>
      <body>
        <h1>Document Comparison Report</h1>
        <p><strong>Date:</strong> ${new Date().toLocaleString()}</p>
        <p><strong>Document A:</strong> ${getDocumentName(documentAId)}</p>
        <p><strong>Document B:</strong> ${getDocumentName(documentBId)}</p>
        <p><strong>Similarity Score:</strong> ${similarityScore}%</p>
        
        <div class="summary">
          <h2>Summary</h2>
          <div class="stats">
            <div class="stat-box">
              <h3>Added Lines</h3>
              <p>${data.metadata.summary.addedLines}</p>
            </div>
            <div class="stat-box">
              <h3>Removed Lines</h3>
              <p>${data.metadata.summary.removedLines}</p>
            </div>
            <div class="stat-box">
              <h3>Modified Lines</h3>
              <p>${data.metadata.summary.modifiedLines}</p>
            </div>
            <div class="stat-box">
              <h3>Total Changes</h3>
              <p>${data.metadata.summary.totalChanges}</p>
            </div>
          </div>
        </div>
        
        <div class="diff-content">
          <h2>Visual Comparison</h2>
          ${data.visualization.htmlDiff}
        </div>
        
        <div class="footer">
          <p>Generated by Document Comparison Tool</p>
        </div>
      </body>
      </html>
    `;

    // Create a Blob with the HTML content
    const blob = new Blob([htmlContent], { type: "text/html" });
    const url = URL.createObjectURL(blob);

    // Create a link and trigger download
    const a = document.createElement("a");
    a.href = url;
    a.download = `comparison-${documentAId.substring(
      0,
      6
    )}-${documentBId.substring(0, 6)}.html`;
    document.body.appendChild(a);
    a.click();

    // Clean up
    document.body.removeChild(a);
    URL.revokeObjectURL(url);

    toast({
      title: "Export successful",
      description: "Comparison report has been downloaded.",
    });
  };

  const renderComparisonForm = () => {
    return (
      <div className="space-y-8">
        <Card className="shadow-sm border-t-4 border-t-gray-500">
          <CardHeader className="pb-3 border-b bg-gray-50/30 dark:bg-gray-900/20">
            <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
              <FileText className="h-5 w-5 mr-2" />
              Document Comparison
            </CardTitle>
            <CardDescription>
              Compare the content of two documents to identify differences and
              similarities
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6 pt-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6 relative">
              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-3">First Document</h3>
                  <SimpleDocumentSelector
                    onSelect={(docId) => setDocumentAId(docId)}
                  />
                  {documentAId && documentAContent && (
                    <div className="mt-3 border rounded-md p-4 ">
                      <div className="flex justify-between items-center mb-2">
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Document Preview
                        </p>
                        <Badge variant="outline" className="text-xs">
                          {documentAContent.length.toLocaleString()} characters
                        </Badge>
                      </div>
                      <div className="max-h-[200px] overflow-y-auto text-sm p-3 bg-white dark:bg-gray-800 rounded border">
                        {documentAContent.substring(0, 500)}
                        {documentAContent.length > 500 && "..."}
                      </div>
                    </div>
                  )}
                  {documentAId && isLoadingContent && (
                    <div className="flex items-center justify-center p-6 mt-3 border rounded-md ">
                      <Loader2 className="h-5 w-5 animate-spin mr-2 text-gray-500" />
                      <span className="text-sm text-gray-500">
                        Loading document content...
                      </span>
                    </div>
                  )}
                </div>
              </div>

              {/* Swap button for mobile */}
              <div className="md:hidden flex justify-center my-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={handleSwapDocuments}
                  disabled={!documentAId || !documentBId}
                  className="flex items-center"
                >
                  <SwitchHorizontal className="h-4 w-4 mr-2" />
                  Swap Documents
                </Button>
              </div>

              {/* Swap button for desktop */}
              <div className="hidden md:flex absolute left-1/2 top-1/2 -translate-x-1/2 -translate-y-1/2 z-10">
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={handleSwapDocuments}
                        disabled={!documentAId || !documentBId}
                        className="rounded-full h-10 w-10 bg-white dark:bg-gray-800 shadow-md"
                      >
                        <SwitchHorizontal className="h-4 w-4" />
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>Swap documents</p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>

              <div className="space-y-4">
                <div>
                  <h3 className="text-lg font-medium mb-3">Second Document</h3>
                  <SimpleDocumentSelector
                    onSelect={(docId) => setDocumentBId(docId)}
                  />
                  {documentBId && documentBContent && (
                    <div className="mt-3 border rounded-md p-4 ">
                      <div className="flex justify-between items-center mb-2">
                        <p className="text-sm font-medium text-gray-700 dark:text-gray-300">
                          Document Preview
                        </p>
                        <Badge variant="outline" className="text-xs">
                          {documentBContent.length.toLocaleString()} characters
                        </Badge>
                      </div>
                      <div className="max-h-[200px] overflow-y-auto text-sm p-3 bg-white dark:bg-gray-800 rounded border">
                        {documentBContent.substring(0, 500)}
                        {documentBContent.length > 500 && "..."}
                      </div>
                    </div>
                  )}
                  {documentBId && isLoadingContent && (
                    <div className="flex items-center justify-center p-6 mt-3 border rounded-md ">
                      <Loader2 className="h-5 w-5 animate-spin mr-2 text-gray-500" />
                      <span className="text-sm text-gray-500">
                        Loading document content...
                      </span>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="flex flex-col sm:flex-row gap-3 items-stretch sm:items-center pt-2 pb-4">
            <Button
              type="submit"
              className={cn(
                "w-full text-sm py-2 h-10 transition-all",
                documentAId && documentBId
                  ? "bg-gray-800 hover:bg-gray-900 dark:bg-gray-700 dark:hover:bg-gray-600"
                  : "bg-gray-400 dark:bg-gray-600"
              )}
              disabled={
                isLoading ||
                !documentAId ||
                !documentBId ||
                !documentAContent ||
                !documentBContent ||
                isLoadingContent
              }
              onClick={handleCompare}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-5 w-5 animate-spin" />
                  Comparing Documents...
                </>
              ) : (
                <>
                  <ArrowLeftRight className="mr-2 h-5 w-5" />
                  Compare Documents
                </>
              )}
            </Button>
          </CardFooter>
        </Card>

        {/* Recent Comparisons Section */}
        {recentComparisons.length > 0 && (
          <Card className="shadow-sm">
            <CardHeader className="pb-3 border-b">
              <div className="flex justify-between items-center">
                <CardTitle className="text-lg flex items-center text-gray-700 dark:text-gray-300">
                  <History className="h-5 w-5 mr-2" />
                  Recent Comparisons
                </CardTitle>
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={handleClearRecentComparisons}
                  className="h-8 text-xs"
                >
                  <Trash2 className="h-3.5 w-3.5 mr-1" />
                  Clear History
                </Button>
              </div>
            </CardHeader>
            <CardContent className="pt-4">
              <div className="space-y-3">
                {recentComparisons.map((comparison) => (
                  <div
                    key={comparison.id}
                    className="border rounded-md p-3 hover:bg-gray-50 dark:hover:bg-gray-900 transition-colors cursor-pointer"
                    onClick={() => handleLoadRecentComparison(comparison)}
                  >
                    <div className="flex justify-between items-center mb-2">
                      <div className="flex items-center">
                        <Clock className="h-4 w-4 mr-2 text-gray-500" />
                        <span className="text-sm text-gray-500">
                          {comparison.timestamp.toLocaleString()}
                        </span>
                      </div>
                      <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
                        <Percent className="h-3 w-3 mr-1" />
                        {comparison.similarityScore}% Similar
                      </Badge>
                    </div>
                    <div className="flex items-center gap-2">
                      <div className="flex-1 truncate">
                        <p className="text-sm font-medium truncate">
                          {comparison.documentAName}
                        </p>
                      </div>
                      <ArrowLeftRight className="h-4 w-4 text-gray-400 flex-shrink-0" />
                      <div className="flex-1 truncate">
                        <p className="text-sm font-medium truncate">
                          {comparison.documentBName}
                        </p>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        )}
      </div>
    );
  };

  const renderComparisonResults = () => {
    if (!comparisonResult || !comparisonResult.data) {
      return (
        <div className="flex flex-col items-center justify-center p-8 border border-dashed rounded-lg">
          <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
          <h3 className="text-lg font-medium mb-2">
            Invalid Comparison Result
          </h3>
          <p className="text-muted-foreground mb-4 text-center max-w-md">
            The comparison result is invalid or incomplete. Please try comparing
            the documents again.
          </p>
          <Button onClick={handleReset} variant="outline">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back to Document Selection
          </Button>
        </div>
      );
    }

    const { data } = comparisonResult;

    // Filter diffs based on search term and filter options
    const filteredDiffs = data.diffs.filter((segment) => {
      // Apply search filter
      if (
        searchTerm &&
        !segment.text.toLowerCase().includes(searchTerm.toLowerCase())
      ) {
        return false;
      }

      // Apply type filters
      if (segment.type === "insert" && !filterOptions.showAdditions)
        return false;
      if (segment.type === "delete" && !filterOptions.showDeletions)
        return false;
      if (segment.type === "equal" && !filterOptions.showModifications)
        return false;

      return true;
    });

    return (
      <div className="space-y-6">
        <div className="sticky top-0 bg-background z-10 py-3 border-b mb-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
          <Button
            variant="outline"
            onClick={handleReset}
            className="self-start flex items-center"
          >
            <ChevronLeft className="h-4 w-4 mr-2" />
            Compare Different Documents
          </Button>

          <div className="flex items-center gap-2">
            <Badge
              variant="outline"
              className="capitalize flex items-center gap-1"
            >
              <FileText className="h-3 w-3 mr-1" />
              Document Comparison
            </Badge>
            <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200">
              {data.metadata?.summary?.totalChanges || 0} Changes
            </Badge>
            <Badge className="bg-gray-100 text-gray-800 dark:bg-gray-800 dark:text-gray-200 flex items-center">
              <Percent className="h-3 w-3 mr-1" />
              {similarityScore}% Similar
            </Badge>
          </div>
          
          {/* Enhanced Comparison Button */}
          <div className="flex justify-end mt-2">
            <TooltipProvider>
              <Tooltip>
                <TooltipTrigger asChild>
                  <Button
                    variant="outline"
                    className="gap-2"
                    onClick={handleEnhancedCompare}
                  >
                    <Sparkles className="h-4 w-4" />
                    Enhance Comparison
                    {!isPro && <Lock className="h-3 w-3 ml-1" />}
                  </Button>
                </TooltipTrigger>
                <TooltipContent>
                  {isPro ? "Get detailed analysis with enhanced comparison" : "Pro subscription required"}
                </TooltipContent>
              </Tooltip>
            </TooltipProvider>
          </div>
        </div>

        <Card className="shadow-sm border-t-4 border-t-gray-500">
          <CardHeader className="pb-3 border-b bg-gray-50/30 dark:bg-gray-900/20">
            <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
              <FileText className="h-5 w-5 mr-2" />
              Comparison Summary
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-6 pt-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="border-l-4 border-gray-400 dark:border-gray-600 pl-4 py-3">
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {getDocumentName(documentAId)}
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Word Count:</p>
                    <p className="text-sm font-medium">
                      {data.metadata?.documentStats?.documentA?.wordCount?.toLocaleString() ||
                        "N/A"}
                    </p>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Character Count:</p>
                    <p className="text-sm font-medium">
                      {data.metadata?.documentStats?.documentA?.charCount?.toLocaleString() ||
                        "N/A"}
                    </p>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Word Count:</p>
                    <p className="text-sm font-medium">
                      {data?.metadata?.documentStats?.documentA?.wordCount ? data.metadata.documentStats.documentA.wordCount.toLocaleString() : "N/A"}
                    </p>
                  </div>
                </div>
              </div>
              <div className="border-l-4 border-gray-400 dark:border-gray-600 pl-4 py-3">
                <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-2">
                  {getDocumentName(documentBId)}
                </h4>
                <div className="space-y-2">
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Word Count:</p>
                    <p className="text-sm font-medium">
                      {data.metadata?.documentStats?.documentB?.wordCount?.toLocaleString() ||
                        "N/A"}
                    </p>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Character Count:</p>
                    <p className="text-sm font-medium">
                      {data.metadata?.documentStats?.documentB?.charCount?.toLocaleString() ||
                        "N/A"}
                    </p>
                  </div>
                  <div className="flex justify-between items-center">
                    <p className="text-sm text-gray-500">Word Count:</p>
                    <p className="text-sm font-medium">
                      {data?.metadata?.documentStats?.documentB?.wordCount ? data.metadata.documentStats.documentB.wordCount.toLocaleString() : "N/A"}
                    </p>
                  </div>
                </div>
              </div>
            </div>

            <Separator />

            <div>
              <h4 className="font-medium text-gray-700 dark:text-gray-300 mb-4">
                Similarity Analysis
              </h4>
              <div className="space-y-4">
                <div>
                  <div className="flex justify-between items-center mb-2">
                    <span className="text-sm text-gray-500">
                      Overall Similarity
                    </span>
                    <span className="text-sm font-medium">
                      {similarityScore}%
                    </span>
                  </div>
                  <Progress value={similarityScore} className="h-2" />
                </div>

                <div className="grid grid-cols-2 sm:grid-cols-4 gap-4">
                  <div className="border rounded-md p-3 ">
                    <p className="text-xs text-gray-500 mb-1">Added Lines</p>
                    <p className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                      {data.metadata?.summary?.addedLines || 0}
                    </p>
                  </div>
                  <div className="border rounded-md p-3 ">
                    <p className="text-xs text-gray-500 mb-1">Removed Lines</p>
                    <p className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                      {data.metadata?.summary?.removedLines || 0}
                    </p>
                  </div>
                  <div className="border rounded-md p-3 ">
                    <p className="text-xs text-gray-500 mb-1">Modified Lines</p>
                    <p className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                      {data.metadata?.summary?.modifiedLines || 0}
                    </p>
                  </div>
                  <div className="border rounded-md p-3 ">
                    <p className="text-xs text-gray-500 mb-1">Total Changes</p>
                    <p className="text-lg font-semibold text-gray-700 dark:text-gray-300">
                      {data.metadata?.summary?.totalChanges || 0}
                    </p>
                  </div>
                </div>
              </div>
            </div>
          </CardContent>
          <CardFooter className="pt-2 pb-4">
            <Button
              variant="outline"
              onClick={handleExportComparison}
              className="flex items-center"
            >
              <FileDown className="h-4 w-4 mr-2" />
              Export Comparison Report
            </Button>
          </CardFooter>
        </Card>

        <Tabs
          defaultValue="visual"
          className="w-full"
          value={activeTab}
          onValueChange={setActiveTab}
        >
          <div className="mb-6 sticky top-16 z-10 bg-background pt-2 pb-3 border-b">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4">
              <TabsList className="w-full sm:w-auto justify-start overflow-x-auto bg-muted/30">
                <TabsTrigger
                  value="visual"
                  className="flex items-center gap-1 data-[state=active]:bg-gray-100 data-[state=active]:text-gray-800 dark:data-[state=active]:bg-gray-800 dark:data-[state=active]:text-gray-200"
                >
                  <Eye className="h-3.5 w-3.5" />
                  <span>Visual Diff</span>
                </TabsTrigger>
                <TabsTrigger
                  value="text"
                  className="flex items-center gap-1 data-[state=active]:bg-gray-100 data-[state=active]:text-gray-800 dark:data-[state=active]:bg-gray-800 dark:data-[state=active]:text-gray-200"
                >
                  <Code className="h-3.5 w-3.5" />
                  <span>Text Segments</span>
                </TabsTrigger>
              </TabsList>

              <div className="flex items-center gap-2 w-full sm:w-auto">
                <div className="relative flex-1 sm:flex-initial">
                  <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
                  <Input
                    placeholder="Search differences..."
                    value={searchTerm}
                    onChange={(e) => setSearchTerm(e.target.value)}
                    className="pl-8 h-9 text-sm w-full"
                  />
                </div>

                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="h-9"
                        onClick={() => setShowSideBySide(!showSideBySide)}
                      >
                        {showSideBySide ? (
                          <ArrowLeftRight className="h-4 w-4" />
                        ) : (
                          <SwitchHorizontal className="h-4 w-4" />
                        )}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      <p>
                        {showSideBySide ? "Unified view" : "Side-by-side view"}
                      </p>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              </div>
            </div>

            <div className="flex items-center gap-3 flex-wrap">
              <span className="text-sm text-gray-500">Filter:</span>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-additions"
                  checked={filterOptions.showAdditions}
                  onCheckedChange={(checked) =>
                    setFilterOptions({
                      ...filterOptions,
                      showAdditions: !!checked,
                    })
                  }
                />
                <Label
                  htmlFor="show-additions"
                  className="text-sm font-normal flex items-center"
                >
                  <div
                    className="w-3 h-3 rounded-full mr-1"
                    style={{
                      backgroundColor: data.visualization.colors.addition,
                    }}
                  ></div>
                  Additions
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-deletions"
                  checked={filterOptions.showDeletions}
                  onCheckedChange={(checked) =>
                    setFilterOptions({
                      ...filterOptions,
                      showDeletions: !!checked,
                    })
                  }
                />
                <Label
                  htmlFor="show-deletions"
                  className="text-sm font-normal flex items-center"
                >
                  <div
                    className="w-3 h-3 rounded-full mr-1"
                    style={{
                      backgroundColor: data.visualization.colors.deletion,
                    }}
                  ></div>
                  Deletions
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="show-modifications"
                  checked={filterOptions.showModifications}
                  onCheckedChange={(checked) =>
                    setFilterOptions({
                      ...filterOptions,
                      showModifications: !!checked,
                    })
                  }
                />
                <Label
                  htmlFor="show-modifications"
                  className="text-sm font-normal flex items-center"
                >
                  <div
                    className="w-3 h-3 rounded-full mr-1"
                    style={{
                      backgroundColor: data.visualization.colors.modification,
                    }}
                  ></div>
                  Unchanged
                </Label>
              </div>
            </div>
          </div>

          <TabsContent value="visual" className="space-y-6 mt-0">
            <Card className="shadow-sm">
              <CardHeader className="pb-3 border-b">
                <CardTitle className="text-lg flex items-center text-gray-700 dark:text-gray-300">
                  Visual Comparison
                </CardTitle>
                <CardDescription>
                  Highlighted differences between documents
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4">
                {showSideBySide ? (
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="border rounded-md p-4 bg-white dark:bg-gray-900">
                      <h3 className="text-sm font-medium mb-3 pb-2 border-b">
                        {getDocumentName(documentAId)}
                      </h3>
                      <div
                        className="prose dark:prose-invert max-w-none text-sm"
                        dangerouslySetInnerHTML={{
                          __html: documentAContent
                        }}
                      />
                    </div>
                    <div className="border rounded-md p-4 bg-white dark:bg-gray-900">
                      <h3 className="text-sm font-medium mb-3 pb-2 border-b">
                        {getDocumentName(documentBId)}
                      </h3>
                      <div
                        className="prose dark:prose-invert max-w-none text-sm"
                        dangerouslySetInnerHTML={{
                          __html: documentBContent
                        }}
                      />
                    </div>
                  </div>
                ) : (
                  <div className="border rounded-md p-4 bg-white dark:bg-gray-900">
                    <style jsx global>{`
                      /* Enhanced diff styling for light and dark mode */
                      .diff-wrapper span[style*="background-color: #e6ffe6"] {
                        background-color: rgba(74, 222, 128, 0.2) !important;
                        color: #166534 !important;
                      }
                      .diff-wrapper span[style*="background-color: #ffe6e6"] {
                        background-color: rgba(248, 113, 113, 0.2) !important;
                        color: #b91c1c !important;
                      }
                      .diff-wrapper span[style*="background-color: #e6f0ff"] {
                        background-color: rgba(96, 165, 250, 0.2) !important;
                        color: #1e40af !important;
                      }

                      /* Dark mode overrides */
                      .dark
                        .diff-wrapper
                        span[style*="background-color: #e6ffe6"] {
                        background-color: rgba(22, 163, 74, 0.2) !important;
                        color: #86efac !important;
                      }
                      .dark
                        .diff-wrapper
                        span[style*="background-color: #ffe6e6"] {
                        background-color: rgba(220, 38, 38, 0.2) !important;
                        color: #fca5a5 !important;
                      }
                      .dark
                        .diff-wrapper
                        span[style*="background-color: #e6f0ff"] {
                        background-color: rgba(37, 99, 235, 0.2) !important;
                        color: #93c5fd !important;
                      }
                    `}</style>
                    <div
                      className="prose dark:prose-invert max-w-none diff-wrapper"
                      dangerouslySetInnerHTML={{
                        __html: data.visualization.htmlDiff,
                      }}
                    />
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="text" className="space-y-6 mt-0">
            <Card className="shadow-sm">
              <CardHeader className="pb-3 border-b">
                <CardTitle className="text-lg flex items-center text-gray-700 dark:text-gray-300">
                  Text Segments
                </CardTitle>
                <CardDescription>
                  Detailed view of text changes by segment
                </CardDescription>
              </CardHeader>
              <CardContent className="pt-4">
                {filteredDiffs.length === 0 ? (
                  <div className="text-center py-8 border border-dashed rounded-md">
                    <AlertCircle className="h-8 w-8 mx-auto text-gray-400 mb-2" />
                    <h3 className="text-lg font-medium text-gray-700 dark:text-gray-300 mb-1">
                      No matching segments
                    </h3>
                    <p className="text-sm text-gray-500">
                      Try adjusting your search or filter settings
                    </p>
                  </div>
                ) : (
                  <div className="space-y-3">
                    {filteredDiffs.map((segment, index) => (
                      <div
                        key={`segment-${index}`}
                        className={cn(
                          "p-3 border rounded-md",
                          segment.type === "equal"
                            ? ""
                            : segment.type === "insert"
                            ? "bg-gray-100 dark:bg-gray-800 border-l-4 border-l-green-500"
                            : "bg-gray-100 dark:bg-gray-800 border-l-4 border-l-red-500"
                        )}
                      >
                        <div className="flex items-center justify-between gap-2 mb-2">
                          <Badge
                            className={cn(
                              "capitalize",
                              segment.type === "equal"
                                ? "bg-gray-200 text-gray-700 dark:bg-gray-700 dark:text-gray-300"
                                : segment.type === "insert"
                                ? "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300"
                                : "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300"
                            )}
                          >
                            {segment.type === "equal"
                              ? "Unchanged"
                              : segment.type === "insert"
                              ? "Added"
                              : "Removed"}
                          </Badge>

                          {searchTerm &&
                            segment.text
                              .toLowerCase()
                              .includes(searchTerm.toLowerCase()) && (
                              <Badge variant="outline" className="text-xs">
                                Contains search term
                              </Badge>
                            )}
                        </div>
                        <p className="text-sm whitespace-pre-wrap">
                          {segment.text}
                        </p>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </div>
    );
  };

  return (
    <div className="container mx-auto py-6 max-w-7xl">
      <div className="mb-6">
        <h1 className="text-2xl font-bold text-gray-800 dark:text-gray-200 mb-2">
          Document Comparison
        </h1>
        <p className="text-gray-500 dark:text-gray-400">
          Compare two documents to identify differences, similarities, and
          changes between them.
        </p>
      </div>
      {comparisonResult ? renderComparisonResults() : renderComparisonForm()}
    </div>
  );
}
