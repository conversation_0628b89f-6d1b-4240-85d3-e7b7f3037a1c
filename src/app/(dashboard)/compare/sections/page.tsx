"use client"

import { useState, useEffect, useMemo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import {
  AlertCircle,
  ChevronLeft,
  FileText,
  Loader2,
  Plus,
  Trash2,
  CheckCircle,
  AlertTriangle,
  Info,
  ArrowRight,
  FileSymlink,
  Lightbulb,
  Diff,
  GitCompare,
  Layers,
  Sparkles,
} from "lucide-react"
import {
  documentService,
  type CompareSectionsPayload,
  type DocumentMetadata,
  type DocumentSectionInput,
} from "@/lib/services/document-service"

// Custom interfaces to match the actual API response structure
interface SectionInfo {
  documentName: string
  content: string
}

interface Provision {
  document: string
  content: string
  sectionReference?: string
  conflicts?: string
}

interface KeyProvision {
  topic: string
  provisions: Provision[]
  significance: string
  recommendation: string
}

interface Gap {
  area: string
  description: string
  recommendation: string
  priority: string
  presentIn?: string[]
  missingFrom?: string[]
}

interface Finding {
  documentType: string
  observation: string
  implication: string
}

interface DocumentRelationship {
  primaryDocument: string
  relatedDocument: string
  relationship: string
  description: string
}

interface DocumentSet {
  count: number
  documentTypes: string[]
  relationships: DocumentRelationship[]
}

interface TypeSpecificAnalysis {
  dominantDocumentType: string
  findings: Finding[]
}

interface SummaryContent {
  overallCoherence: string
  keyInsights: string[]
  recommendations: string[]
}

// Interface for similarity and difference sections
interface ExtendedProvision {
  title: string
  description: string
  sections: SectionInfo[]
}

interface CompareSectionsResponse {
  documentSet: DocumentSet
  typeSpecificAnalysis: TypeSpecificAnalysis
  keyProvisions: KeyProvision[]
  gaps: Gap[]
  summary: SummaryContent
  similarities?: ExtendedProvision[]
  differences?: ExtendedProvision[]
  findings?: Finding[]
}
import { useToast } from "@/hooks/use-toast"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Badge } from "@/components/ui/badge"
import { DocumentSectionSelector } from "@/components/document-section-selector"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/lib/utils"

// Helper type for section input with a unique ID for React keys
type SectionInputWithId = {
  id: string
  title: string
  content: string
}

export default function CompareSectionsPage() {
  const { toast } = useToast()
  const [documents, setDocuments] = useState<DocumentMetadata[]>([])
  const [isLoadingDocs, setIsLoadingDocs] = useState(false)
  const [selectedDocIds, setSelectedDocIds] = useState<string[]>([])
  const [sectionsMap, setSectionsMap] = useState<Record<string, SectionInputWithId[]>>({})
  const [comparisonType, setComparisonType] = useState<"both" | "differences" | "similarities">("both")
  const [activeTab, setActiveTab] = useState<string>("all")

  // Calculate if the form is valid based on selected documents and sections
  const isFormValid = useMemo(() => {
    const validDocuments = selectedDocIds.filter((id) => id).length > 0
    const hasSections = Object.values(sectionsMap).some((sections) => sections && sections.length > 0)
    return validDocuments && hasSections
  }, [selectedDocIds, sectionsMap])

  const [comparisonResult, setComparisonResult] = useState<CompareSectionsResponse | null>(null)
  const [isLoading, setIsLoading] = useState(false)

  // Memoize document names to avoid recalculation
  const documentNameMap = useMemo(() => {
    const nameMap: Record<string, string> = {}
    documents.forEach((doc) => {
      nameMap[doc.id] = doc.name || doc.filename || `Document ${doc.id.substring(0, 6)}...`
    })
    return nameMap
  }, [documents])

  useEffect(() => {
    const fetchDocuments = async () => {
      setIsLoadingDocs(true)
      try {
        const response = await documentService.getDocuments(1, 100)
        setDocuments(response.items || [])
      } catch (err) {
        console.error("Error fetching documents:", err)
        setDocuments([])
        toast({
          title: "Error",
          description: "Failed to load documents. Please try again.",
          variant: "destructive",
        })
      } finally {
        setIsLoadingDocs(false)
      }
    }

    fetchDocuments()
  }, [toast])

  const handleAddDocument = () => {
    if (selectedDocIds.length < 5) {
      setSelectedDocIds([...selectedDocIds, ""])
    } else {
      toast({
        title: "Maximum Documents",
        description: "You can compare up to 5 documents at a time.",
      })
    }
  }

  const addSection = (docId: string) => {
    const newSection = {
      id: `section-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
      title: "",
      content: "",
    }

    setSectionsMap((prevMap) => ({
      ...prevMap,
      [docId]: [...(prevMap[docId] || []), newSection],
    }))
  }

  const removeSection = (docId: string, sectionId: string) => {
    setSectionsMap((prevMap) => ({
      ...prevMap,
      [docId]: (prevMap[docId] || []).filter((section) => section.id !== sectionId),
    }))
  }

  const updateSectionField = (docId: string, sectionId: string, field: "title" | "content", value: string) => {
    setSectionsMap((prevMap) => ({
      ...prevMap,
      [docId]: (prevMap[docId] || []).map((section) =>
        section.id === sectionId ? { ...section, [field]: value } : section,
      ),
    }))
  }

  const handleDocumentChange = (index: number, docId: string) => {
    const newSelectedIds = [...selectedDocIds]
    const oldDocId = newSelectedIds[index]
    newSelectedIds[index] = docId
    setSelectedDocIds(newSelectedIds)

    if (oldDocId && oldDocId !== docId) {
      setSectionsMap((prev) => {
        const newMap = { ...prev }
        if (!newSelectedIds.includes(oldDocId)) {
          delete newMap[oldDocId]
        }
        return newMap
      })
    }
  }

  const handleRemoveDocument = (index: number) => {
    const docId = selectedDocIds[index]
    const newSelectedIds = selectedDocIds.filter((_, i) => i !== index)
    setSelectedDocIds(newSelectedIds)

    if (docId && !newSelectedIds.includes(docId)) {
      setSectionsMap((prev) => {
        const newMap = { ...prev }
        delete newMap[docId]
        return newMap
      })
    }
  }

  const validateComparisonData = () => {
    const selectedDocs = [...selectedDocIds].filter((id) => id)

    if (selectedDocs.length < 1) {
      toast({
        title: "Error",
        description: "Please select at least one document.",
        variant: "destructive",
      })
      return false
    }

    for (const docId of selectedDocs) {
      const sections = sectionsMap[docId] || []
      if (sections.length === 0) {
        toast({
          title: "Error",
          description: `Please add at least one section for ${documentNameMap[docId]}.`,
          variant: "destructive",
        })
        return false
      }

      for (const section of sections) {
        if (!section.title.trim()) {
          toast({
            title: "Error",
            description: `Please provide a title for all sections in ${documentNameMap[docId]}.`,
            variant: "destructive",
          })
          return false
        }
        if (!section.content.trim()) {
          toast({
            title: "Error",
            description: `Please provide content for all sections in ${documentNameMap[docId]}.`,
            variant: "destructive",
          })
          return false
        }
      }
    }

    return true
  }

  const handleCompare = async () => {
    if (!validateComparisonData()) return

    setIsLoading(true)

    try {
      // Convert our UI data structure to the expected API payload format
      const documentSections: { [documentId: string]: DocumentSectionInput[] } = {}

      // Process each selected document
      selectedDocIds
        .filter((id) => id)
        .forEach((docId) => {
          // Map each section to the DocumentSectionInput format
          documentSections[docId] = (sectionsMap[docId] || []).map((section) => ({
            title: section.title,
            content: section.content,
            sectionId: section.id, // Using the existing ID
          }))
        })

      const payload: CompareSectionsPayload = {
        documentSections,
        comparisonType,
      }

      const result = await documentService.compareDocumentSections(payload)
      // Cast the result to our custom interface to handle the actual response structure
      setComparisonResult(result as unknown as CompareSectionsResponse)
      setActiveTab("all")
    } catch (err) {
      console.error("Error comparing sections:", err)
      toast({
        title: "Error",
        description: "Failed to compare sections. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  const handleReset = () => {
    setComparisonResult(null)
  }

  // Function to get appropriate badge for coherence level
  const getCoherenceBadge = (coherence: string) => {
    const lowerCoherence = coherence.toLowerCase()
    if (lowerCoherence.includes("high")) {
      return (
        <Badge className="bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200 flex items-center gap-1 border-slate-200 dark:border-slate-700">
          <CheckCircle className="h-3 w-3" />
          High Coherence
        </Badge>
      )
    } else if (lowerCoherence.includes("medium")) {
      return (
        <Badge className="bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200 flex items-center gap-1 border-slate-200 dark:border-slate-700">
          <Info className="h-3 w-3" />
          Medium Coherence
        </Badge>
      )
    } else {
      return (
        <Badge className="bg-slate-200 text-slate-800 dark:bg-slate-700 dark:text-slate-200 flex items-center gap-1 border-slate-300 dark:border-slate-600">
          <AlertTriangle className="h-3 w-3" />
          Low Coherence
        </Badge>
      )
    }
  }

  // Function to get appropriate badge for significance level
  const getSignificanceBadge = (significance: string) => {
    const lowerSignificance = significance.toLowerCase()
    if (lowerSignificance.includes("high")) {
      return (
        <Badge className="bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200 border-slate-200 dark:border-slate-700">
          High
        </Badge>
      )
    } else if (lowerSignificance.includes("medium")) {
      return (
        <Badge className="bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200 border-slate-200 dark:border-slate-700">
          Medium
        </Badge>
      )
    } else {
      return (
        <Badge className="bg-slate-50 text-slate-500 dark:bg-slate-900 dark:text-slate-400 border-slate-200 dark:border-slate-800">
          Low
        </Badge>
      )
    }
  }

  // Function to get appropriate badge for priority level
  const getPriorityBadge = (priority: string) => {
    const lowerPriority = priority.toLowerCase()
    if (lowerPriority.includes("high")) {
      return (
        <Badge className="bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200 border-slate-200 dark:border-slate-700">
          High Priority
        </Badge>
      )
    } else if (lowerPriority.includes("medium")) {
      return (
        <Badge className="bg-slate-100 text-slate-800 dark:bg-slate-800 dark:text-slate-200 border-slate-200 dark:border-slate-700">
          Medium Priority
        </Badge>
      )
    } else {
      return (
        <Badge className="bg-slate-50 text-slate-500 dark:bg-slate-900 dark:text-slate-400 border-slate-200 dark:border-slate-800">
          Low Priority
        </Badge>
      )
    }
  }

  const renderDocumentSelector = () => {
    return (
      <div className="space-y-6">
        <Card className="shadow-sm border-t-4 border-t-gray-500">
          <CardHeader className="pb-2 sm:pb-4">
            <CardTitle className="text-lg sm:text-xl flex items-center">
              <FileText className="h-5 w-5 mr-2 text-primary" />
              Document Sections
            </CardTitle>
            <CardDescription className="text-xs sm:text-sm">
              Select documents and add sections to compare
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-6">
            <div className="space-y-4">
              {selectedDocIds.length === 0 ? (
                <div className="text-center p-8 border border-dashed rounded-lg">
                  <FileSymlink className="h-12 w-12 mx-auto text-muted-foreground mb-3" />
                  <h3 className="text-lg font-medium mb-2">No Documents Selected</h3>
                  <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                    Select documents and add specific sections to compare their content, identify similarities, and
                    highlight differences.
                  </p>
                  <Button onClick={handleAddDocument} className="mx-auto">
                    <Plus className="h-4 w-4 mr-2" />
                    Add Document
                  </Button>
                </div>
              ) : (
                selectedDocIds.map((docId, index) => (
                  <div
                    key={`doc-${index}`}
                    className={cn(
                      "border rounded-lg p-3 sm:p-4 space-y-3 sm:space-y-4 transition-all",
                      docId ? "border-primary/20 shadow-sm" : "border-dashed",
                    )}
                  >
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-3 items-stretch sm:items-center">
                      <div className="flex-1">
                        <label className="text-sm font-medium mb-1.5 block text-primary">Document {index + 1}</label>
                        <Select
                          value={docId}
                          onValueChange={(value) => handleDocumentChange(index, value)}
                          disabled={isLoadingDocs}
                        >
                          <SelectTrigger className="w-full">
                            <SelectValue placeholder={isLoadingDocs ? "Loading documents..." : "Select a document"} />
                          </SelectTrigger>
                          <SelectContent>
                            {documents.map((doc) => (
                              <SelectItem key={doc.id} value={doc.id}>
                                {doc.name || doc.filename || doc.id}
                              </SelectItem>
                            ))}
                          </SelectContent>
                        </Select>
                      </div>
                      <Button
                        variant="outline"
                        size="icon"
                        onClick={() => handleRemoveDocument(index)}
                        className="self-end sm:self-center mt-6 sm:mt-0"
                      >
                        <Trash2 className="h-4 w-4 text-destructive" />
                      </Button>
                    </div>

                    {docId ? (
                      <div className="space-y-4">
                        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-0 mb-2">
                          <h4 className="text-sm font-medium flex items-center">
                            <Layers className="h-4 w-4 mr-1.5 text-primary" />
                            Sections for {documentNameMap[docId]}
                          </h4>
                          <div className="flex flex-col md:flex-row gap-2 w-full sm:w-auto">
                            <Button
                              variant="outline"
                              size="sm"
                              className="text-xs py-2 flex-1 sm:flex-none"
                              onClick={() => addSection(docId)}
                            >
                              <Plus className="h-3 w-3 mr-1" />
                              Add Empty Section
                            </Button>
                            <div className="flex-1 sm:flex-none">
                              <DocumentSectionSelector
                                documentId={docId}
                                document={{ id: docId, name: documentNameMap[docId] }}
                                onSectionSelect={(section) => {
                                  const newSection = {
                                    id: `section-${Date.now()}-${Math.random().toString(36).substring(2, 9)}`,
                                    title: section.title,
                                    content: section.content,
                                  }
                                  setSectionsMap((prevMap) => ({
                                    ...prevMap,
                                    [docId]: [...(prevMap[docId] || []), newSection],
                                  }))
                                }}
                              />
                            </div>
                          </div>
                        </div>

                        {(sectionsMap[docId] || []).length === 0 ? (
                          <div className="text-center py-6 px-4 border border-dashed rounded-md bg-muted/10">
                            <p className="text-muted-foreground mb-2">No sections added yet</p>
                            <Button
                              variant="link"
                              className="text-xs sm:text-sm h-auto py-0"
                              onClick={() => addSection(docId)}
                            >
                              Add your first section
                            </Button>
                          </div>
                        ) : (
                          <div className="space-y-4">
                            {(sectionsMap[docId] || []).map((section, sectionIndex) => (
                              <div
                                key={section.id}
                                className={cn(
                                  "border rounded-md p-3 sm:p-4 space-y-3 transition-all",
                                  section.title && section.content ? "border-primary/20 bg-primary/5" : "border-dashed",
                                )}
                              >
                                <div className="flex flex-row justify-between items-center gap-2">
                                  <h5 className="text-sm font-medium flex items-center">
                                    <span className="flex items-center justify-center w-5 h-5 rounded-full bg-primary/10 text-primary text-xs mr-2">
                                      {sectionIndex + 1}
                                    </span>
                                    Section Details
                                  </h5>
                                  <Button
                                    variant="ghost"
                                    size="icon"
                                    onClick={() => removeSection(docId, section.id)}
                                    className="h-8 w-8"
                                  >
                                    <Trash2 className="h-4 w-4 text-destructive" />
                                  </Button>
                                </div>
                                <div className="space-y-3">
                                  <div>
                                    <label className="text-xs font-medium mb-1 block text-muted-foreground">
                                      Section Title
                                    </label>
                                    <Input
                                      placeholder="e.g., Confidentiality Clause"
                                      value={section.title}
                                      onChange={(e) => updateSectionField(docId, section.id, "title", e.target.value)}
                                    />
                                  </div>
                                  <div>
                                    <label className="text-xs font-medium mb-1 block text-muted-foreground">
                                      Section Content
                                    </label>
                                    <Textarea
                                      placeholder="Paste the section content here..."
                                      value={section.content}
                                      onChange={(e) => updateSectionField(docId, section.id, "content", e.target.value)}
                                      className="min-h-[120px]"
                                    />
                                  </div>
                                </div>
                              </div>
                            ))}
                          </div>
                        )}
                      </div>
                    ) : (
                      <div className="text-center py-4 px-4 border border-dashed rounded-md bg-muted/10">
                        <p className="text-muted-foreground">Please select a document first</p>
                      </div>
                    )}
                  </div>
                ))
              )}

              {selectedDocIds.length > 0 && selectedDocIds.length < 5 && (
                <Button variant="outline" className="w-full text-xs sm:text-sm py-1 h-9" onClick={handleAddDocument}>
                  <Plus className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
                  Add Another Document
                </Button>
              )}
            </div>
          </CardContent>
          <CardFooter className="flex flex-col sm:flex-row gap-3 items-stretch sm:items-center">
            <Button
              type="submit"
              className={cn(
                "w-full text-xs sm:text-sm py-1 h-9 sm:h-10 transition-all",
                isFormValid ? "bg-primary hover:bg-primary/90" : "bg-primary/70",
              )}
              disabled={isLoading || !isFormValid}
              onClick={handleCompare}
            >
              {isLoading ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Comparing...
                </>
              ) : (
                <>
                  <GitCompare className="mr-2 h-4 w-4" />
                  Compare Sections
                </>
              )}
            </Button>

            {selectedDocIds.length >= 5 && (
              <Alert variant="destructive" className="mt-4 w-full">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Maximum limit reached</AlertTitle>
                <AlertDescription>You can compare up to 5 documents at a time.</AlertDescription>
              </Alert>
            )}
          </CardFooter>
        </Card>

        {/* Comparison Type */}
        <Card className="shadow-sm border-t-4 border-t-gray-500">
          <CardHeader className="pb-2 sm:pb-4">
            <CardTitle className="text-lg sm:text-xl flex items-center">
              <Diff className="h-5 w-5 mr-2 text-gray-500" />
              Comparison Type
            </CardTitle>
            <CardDescription className="text-xs sm:text-sm">
              Choose what to include in the comparison results
            </CardDescription>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
              <div
                className={cn(
                  "border rounded-lg p-3 cursor-pointer transition-all",
                  comparisonType === "both"
                    ? "border-primary bg-primary/5"
                    : "hover:border-primary/20 hover:bg-primary/5",
                )}
                onClick={() => setComparisonType("both")}
              >
                <div className="flex items-center mb-2">
                  <div
                    className={cn(
                      "w-4 h-4 rounded-full mr-2 border-2",
                      comparisonType === "both" ? "border-primary bg-primary/20" : "border-muted-foreground",
                    )}
                  />
                  <h3 className="font-medium">Both</h3>
                </div>
                <p className="text-xs text-muted-foreground">Show similarities and differences between documents</p>
              </div>

              <div
                className={cn(
                  "border rounded-lg p-3 cursor-pointer transition-all",
                  comparisonType === "differences"
                    ? "border-primary bg-primary/5"
                    : "hover:border-primary/20 hover:bg-primary/5",
                )}
                onClick={() => setComparisonType("differences")}
              >
                <div className="flex items-center mb-2">
                  <div
                    className={cn(
                      "w-4 h-4 rounded-full mr-2 border-2",
                      comparisonType === "differences" ? "border-primary bg-primary/20" : "border-muted-foreground",
                    )}
                  />
                  <h3 className="font-medium">Differences Only</h3>
                </div>
                <p className="text-xs text-muted-foreground">Focus on variations between documents</p>
              </div>

              <div
                className={cn(
                  "border rounded-lg p-3 cursor-pointer transition-all",
                  comparisonType === "similarities"
                    ? "border-primary bg-primary/5"
                    : "hover:border-primary/20 hover:bg-primary/5",
                )}
                onClick={() => setComparisonType("similarities")}
              >
                <div className="flex items-center mb-2">
                  <div
                    className={cn(
                      "w-4 h-4 rounded-full mr-2 border-2",
                      comparisonType === "similarities" ? "border-primary bg-primary/20" : "border-muted-foreground",
                    )}
                  />
                  <h3 className="font-medium">Similarities Only</h3>
                </div>
                <p className="text-xs text-muted-foreground">Focus on common elements between documents</p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  const renderComparisonResults = () => {
    if (!comparisonResult) return null

    return (
      <div className="space-y-6">
        <div className="sticky top-0 bg-background z-10 py-3 border-b mb-4 flex flex-col sm:flex-row justify-between items-start sm:items-center gap-3">
          <Button variant="outline" onClick={handleReset} className="self-start flex items-center">
            <ChevronLeft className="h-4 w-4 mr-2" />
            Back to Selection
          </Button>

          <div className="flex items-center gap-2">
            <Badge variant="outline" className="capitalize flex items-center gap-1">
              <GitCompare className="h-3 w-3 mr-1" />
              {comparisonType === "both"
                ? "Similarities & Differences"
                : comparisonType === "differences"
                  ? "Differences Only"
                  : "Similarities Only"}
            </Badge>
            {comparisonResult.documentSet && (
              <Badge className="bg-blue-100 text-blue-800 dark:bg-blue-900/30 dark:text-blue-200">
                {comparisonResult.documentSet.count} Documents
              </Badge>
            )}
          </div>
        </div>

        <Tabs defaultValue="all" className="w-full" value={activeTab} onValueChange={setActiveTab}>
          <div className="mb-6 sticky top-16 z-10 bg-background pt-2 pb-3 border-b">
            <TabsList className="w-full justify-start overflow-x-auto bg-muted/30">
              <TabsTrigger
                value="all"
                className="flex items-center gap-1 data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
              >
                <Sparkles className="h-3.5 w-3.5" />
                <span>All Results</span>
              </TabsTrigger>
              <TabsTrigger
                value="summary"
                className="flex items-center gap-1 data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
              >
                <FileText className="h-3.5 w-3.5" />
                <span>Summary</span>
              </TabsTrigger>
              {comparisonResult.keyProvisions && comparisonResult.keyProvisions.length > 0 && (
                <TabsTrigger
                  value="provisions"
                  className="flex items-center gap-1 data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
                >
                  <Layers className="h-3.5 w-3.5" />
                  <span>Key Provisions</span>
                </TabsTrigger>
              )}
              {comparisonResult.similarities && comparisonResult.similarities.length > 0 && (
                <TabsTrigger
                  value="similarities"
                  className="flex items-center gap-1 data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
                >
                  <CheckCircle className="h-3.5 w-3.5" />
                  <span>Similarities</span>
                </TabsTrigger>
              )}
              {comparisonResult.differences && comparisonResult.differences.length > 0 && (
                <TabsTrigger
                  value="differences"
                  className="flex items-center gap-1 data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
                >
                  <Diff className="h-3.5 w-3.5" />
                  <span>Differences</span>
                </TabsTrigger>
              )}
              {comparisonResult.gaps && comparisonResult.gaps.length > 0 && (
                <TabsTrigger
                  value="gaps"
                  className="flex items-center gap-1 data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
                >
                  <AlertTriangle className="h-3.5 w-3.5" />
                  <span>Gaps</span>
                </TabsTrigger>
              )}
              {comparisonResult.typeSpecificAnalysis && (
                <TabsTrigger
                  value="analysis"
                  className="flex items-center gap-1 data-[state=active]:bg-primary/10 data-[state=active]:text-primary"
                >
                  <Lightbulb className="h-3.5 w-3.5" />
                  <span>Analysis</span>
                </TabsTrigger>
              )}
            </TabsList>
          </div>

          <TabsContent value="all" className="space-y-6 mt-0">
            {/* Summary Section */}
            {comparisonResult?.summary && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-gray-50/30 dark:bg-gray-900/20">
                  <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                    <FileText className="h-5 w-5 mr-2" />
                    Comparison Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="border-l-4 border-primary/40 pl-4 py-3 flex-1">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-primary">Overall Coherence</h4>
                        {comparisonResult.summary.overallCoherence &&
                          getCoherenceBadge(comparisonResult.summary.overallCoherence)}
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div
                          className={cn(
                            "h-full rounded-full",
                            comparisonResult.summary.overallCoherence.toLowerCase().includes("high")
                              ? "bg-gray-800"
                              : comparisonResult.summary.overallCoherence.toLowerCase().includes("medium")
                                ? "bg-gray-600"
                                : "bg-gray-400",
                          )}
                          style={{
                            width: comparisonResult.summary.overallCoherence.toLowerCase().includes("high")
                              ? "90%"
                              : comparisonResult.summary.overallCoherence.toLowerCase().includes("medium")
                                ? "60%"
                                : "30%",
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  {comparisonResult.summary.keyInsights && comparisonResult.summary.keyInsights.length > 0 && (
                    <div className="border-l-4 border-primary/40 pl-4 py-3">
                      <h4 className="font-semibold mb-3 text-primary flex items-center">
                        <Info className="h-4 w-4 mr-1" />
                        Key Insights
                      </h4>
                      <div className="space-y-3">
                        {comparisonResult.summary.keyInsights.map((insight: string, idx: number) => (
                          <div
                            key={`insight-${idx}`}
                            className={cn(
                              "p-2",
                              idx < 2
                                ? "border-l-4 border-blue-300 dark:border-blue-700 bg-blue-50/30 dark:bg-blue-900/10 pl-3"
                                : "border-l-4 border-blue-200 dark:border-blue-800/50 pl-3",
                            )}
                          >
                            <p className={cn("text-sm", idx < 2 ? "font-medium text-blue-700 dark:text-blue-400" : "")}>
                              {insight}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {comparisonResult.summary.recommendations && comparisonResult.summary.recommendations.length > 0 && (
                    <div className="border-l-4 border-green-400 dark:border-green-600 pl-4 py-3">
                      <h4 className="font-semibold mb-3 text-green-700 dark:text-green-400 flex items-center">
                        <Lightbulb className="h-4 w-4 mr-1" />
                        Recommendations
                      </h4>
                      <div className="space-y-3">
                        {comparisonResult.summary.recommendations.map((rec: string, idx: number) => (
                          <div
                            key={`rec-${idx}`}
                            className="p-2 border-l-4 border-green-300 dark:border-green-700 bg-green-50/30 dark:bg-green-900/10 pl-3"
                          >
                            <p className="text-sm">{rec}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}

            {/* Document Set Info Section */}
            {comparisonResult?.documentSet && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-900/20">
                  <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                    <FileSymlink className="h-5 w-5 mr-2" />
                    Document Set Information
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  <div className="flex flex-wrap gap-3">
                    <div className="border-l-4 border-gray-400 dark:border-gray-600 pl-3 py-2 flex items-center flex-1 min-w-[120px]">
                      <div className="rounded-full border border-indigo-200 dark:border-indigo-800 p-2 mr-3">
                        <FileText className="h-4 w-4 text-indigo-600 dark:text-indigo-400" />
                      </div>
                      <div>
                        <p className="text-xs text-muted-foreground">Document Count</p>
                        <p className="text-lg font-semibold text-indigo-700 dark:text-indigo-400">
                          {comparisonResult.documentSet.count}
                        </p>
                      </div>
                    </div>

                    {comparisonResult.documentSet.documentTypes && (
                      <div className="border-l-4 border-gray-400 dark:border-gray-600 pl-3 py-2 flex-1 min-w-[200px]">
                        <p className="text-xs font-medium text-purple-700 dark:text-purple-400 mb-2">Document Types</p>
                        <div className="flex flex-wrap gap-2">
                          {comparisonResult.documentSet.documentTypes.map((type, idx) => {
                            // Assign different colors based on document type
                            const colorClass = "bg-primary/10 text-primary border-primary/20"

                            return (
                              <span
                                key={`type-${idx}`}
                                className={`text-xs font-medium px-2 py-1 rounded-full border ${colorClass}`}
                              >
                                {type}
                              </span>
                            )
                          })}
                        </div>
                      </div>
                    )}
                  </div>

                  {comparisonResult.documentSet.relationships &&
                    comparisonResult.documentSet.relationships.length > 0 && (
                      <div className="border-l-4 border-blue-400 dark:border-blue-600 pl-3 py-2">
                        <h4 className="font-medium mb-3 text-blue-700 dark:text-blue-400 flex items-center">
                          <ArrowRight className="h-4 w-4 mr-1" />
                          Document Relationships
                        </h4>
                        <div className="space-y-3">
                          {comparisonResult.documentSet.relationships.map((rel: DocumentRelationship, idx: number) => (
                            <div
                              key={`rel-${idx}`}
                              className="border-l-4 border-blue-200 dark:border-blue-800 pl-3 py-2 bg-muted/5"
                            >
                              <p className="text-sm font-medium mb-1">{rel.description}</p>
                              <div className="flex items-center gap-2 mt-1 text-xs">
                                <span className="font-medium border px-1.5 py-0.5 rounded bg-indigo-50 text-indigo-700 dark:bg-indigo-900/20 dark:text-indigo-300 border-indigo-200 dark:border-indigo-800">
                                  {rel.primaryDocument}
                                </span>
                                <span className="text-muted-foreground">{rel.relationship}</span>
                                <span className="font-medium border px-1.5 py-0.5 rounded bg-purple-50 text-purple-700 dark:bg-purple-900/20 dark:text-purple-300 border-purple-200 dark:border-purple-800">
                                  {rel.relatedDocument}
                                </span>
                              </div>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                </CardContent>
              </Card>
            )}

            {/* Key Provisions Section */}
            {comparisonResult?.keyProvisions && comparisonResult.keyProvisions.length > 0 && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                  <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                    <Layers className="h-5 w-5 mr-2" />
                    Key Provisions
                  </CardTitle>
                  <CardDescription>Important provisions identified across documents</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  {Array.isArray(comparisonResult.keyProvisions) &&
                    comparisonResult.keyProvisions.map((provision: KeyProvision, idx: number) => (
                      <div key={`provision-${idx}`} className="border rounded-md p-3 sm:p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-gray-700 dark:text-gray-300">{provision.topic}</h4>
                          {getSignificanceBadge(provision.significance)}
                        </div>

                        <div className="space-y-4 mt-4">
                          {provision.provisions && provision.provisions.length > 0 && (
                            <div>
                              <h5 className="text-sm font-medium mb-2 text-purple-600 dark:text-purple-400">
                                Provisions:
                              </h5>
                              <div className="space-y-3">
                                {provision.provisions.map((p, pIdx) => {
                                  // Determine border color based on document or conflicts
                                  const borderColorClass = p.conflicts
                                    ? "border-amber-400 dark:border-amber-600"
                                    : pIdx % 2 === 0
                                      ? "border-purple-400 dark:border-purple-600"
                                      : "border-blue-400 dark:border-blue-600"

                                  return (
                                    <div
                                      key={`prov-${idx}-${pIdx}`}
                                      className={`border-l-4 ${borderColorClass} pl-3 py-2 bg-muted/5 rounded-sm`}
                                    >
                                      <div className="flex items-start gap-2 mb-2">
                                        <span className="text-xs font-medium border px-1.5 py-0.5 rounded bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border-blue-200 dark:border-blue-800">
                                          {p.document}
                                        </span>
                                        {p.sectionReference && (
                                          <span className="text-xs border px-1 py-0.5 rounded text-muted-foreground">
                                            {p.sectionReference}
                                          </span>
                                        )}
                                      </div>
                                      <p className="text-sm bg-muted/30 p-2 rounded border-l-2 border-l-primary/20 italic">
                                        {p.content}
                                      </p>
                                      {p.conflicts && (
                                        <div className="mt-2 border-l-2 border-amber-400 dark:border-amber-600 pl-2 py-1">
                                          <p className="text-xs text-amber-600 dark:text-amber-400 flex items-center">
                                            <AlertTriangle className="h-3 w-3 mr-1" />
                                            <span className="font-medium">Conflicts:</span> {p.conflicts}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                  )
                                })}
                              </div>
                            </div>
                          )}
                          {provision.recommendation && (
                            <div className="mt-4 border-l-4 border-green-400 dark:border-green-600 pl-3 py-2 bg-green-50/30 dark:bg-green-900/10 rounded-sm">
                              <h5 className="text-sm font-medium mb-1 text-green-700 dark:text-green-400 flex items-center">
                                <Lightbulb className="h-3.5 w-3.5 mr-1" />
                                Recommendation:
                              </h5>
                              <p className="text-sm">{provision.recommendation}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                </CardContent>
              </Card>
            )}

            {/* Similarities Section */}
            {comparisonResult?.similarities && comparisonResult.similarities.length > 0 && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                  <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Similarities
                  </CardTitle>
                  <CardDescription>Common provisions across documents</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  {Array.isArray(comparisonResult.similarities) &&
                    comparisonResult.similarities.map((similarity: ExtendedProvision, idx: number) => (
                      <div key={`similarity-${idx}`} className="border rounded-md p-3 sm:p-4 shadow-sm">
                        <div className="border-l-4 border-green-400 dark:border-green-600 pl-3 mb-3">
                          <h4 className="font-semibold text-green-700 dark:text-green-400 py-1">{similarity.title}</h4>
                          <p className="text-sm pb-1">{similarity.description}</p>
                        </div>
                        <div className="space-y-3 mt-4">
                          {similarity.sections && similarity.sections.length > 0 ? (
                            similarity.sections.map((section: SectionInfo, sectionIdx: number) => (
                              <div key={`sim-section-${idx}-${sectionIdx}`} className="text-sm">
                                <div className="flex items-center gap-2 mb-1">
                                  <span className="text-xs font-medium border px-1.5 py-0.5 rounded bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300 border-green-200 dark:border-green-800">
                                    {section.documentName}
                                  </span>
                                </div>
                                <div className="pl-3 border-l-4 border-green-200 dark:border-green-800 py-2 bg-muted/5 rounded-sm">
                                  <p className="text-sm bg-muted/30 p-2 rounded border-l-2 border-l-primary/20 italic">
                                    {section.content}
                                  </p>
                                </div>
                              </div>
                            ))
                          ) : (
                            <p className="text-sm text-muted-foreground">No section details available</p>
                          )}
                        </div>
                      </div>
                    ))}
                </CardContent>
              </Card>
            )}

            {/* Similarities Tab Content */}
            <TabsContent value="similarities" className="space-y-6 mt-0">
              {comparisonResult?.similarities && comparisonResult.similarities.length > 0 && (
                <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                  <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                    <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                      <CheckCircle className="h-5 w-5 mr-2" />
                      Similarities
                    </CardTitle>
                    <CardDescription>Common provisions across documents</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 pt-4">
                    {Array.isArray(comparisonResult.similarities) &&
                      comparisonResult.similarities.map((similarity: ExtendedProvision, idx: number) => (
                        <div key={`similarity-${idx}`} className="border rounded-md p-3 sm:p-4 shadow-sm">
                          <div className="border-l-4 border-green-400 dark:border-green-600 pl-3 mb-3">
                            <h4 className="font-semibold text-green-700 dark:text-green-400 py-1">
                              {similarity.title}
                            </h4>
                            <p className="text-sm pb-1">{similarity.description}</p>
                          </div>
                          <div className="space-y-3 mt-4">
                            {similarity.sections && similarity.sections.length > 0 ? (
                              similarity.sections.map((section: SectionInfo, sectionIdx: number) => (
                                <div key={`sim-section-${idx}-${sectionIdx}`} className="text-sm">
                                  <div className="flex items-center gap-2 mb-1">
                                    <span className="text-xs font-medium border px-1.5 py-0.5 rounded bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300 border-green-200 dark:border-green-800">
                                      {section.documentName}
                                    </span>
                                  </div>
                                  <div className="pl-3 border-l-4 border-green-200 dark:border-green-800 py-2 bg-muted/5 rounded-sm">
                                    <p className="text-sm bg-muted/30 p-2 rounded border-l-2 border-l-primary/20 italic">
                                      {section.content}
                                    </p>
                                  </div>
                                </div>
                              ))
                            ) : (
                              <p className="text-sm text-muted-foreground">No section details available</p>
                            )}
                          </div>
                        </div>
                      ))}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Differences Tab Content */}
            <TabsContent value="differences" className="space-y-6 mt-0">
              {comparisonResult?.differences && comparisonResult.differences.length > 0 && (
                <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                  <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                    <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                      <Diff className="h-5 w-5 mr-2" />
                      Differences
                    </CardTitle>
                    <CardDescription>Variations in provisions across documents</CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 pt-4">
                    {Array.isArray(comparisonResult.differences) &&
                      comparisonResult.differences.map((difference: ExtendedProvision, idx: number) => (
                        <div key={`difference-${idx}`} className="border rounded-md p-3 sm:p-4 shadow-sm">
                          <div className="border-l-4 border-amber-400 dark:border-amber-600 pl-3 mb-3">
                            <h4 className="font-semibold text-amber-700 dark:text-amber-400 py-1">
                              {difference.title}
                            </h4>
                            <p className="text-sm pb-1">{difference.description}</p>
                          </div>
                          <div className="space-y-3 mt-4">
                            {difference.sections && difference.sections.length > 0 ? (
                              difference.sections.map((section: SectionInfo, sectionIdx: number) => (
                                <div key={`diff-section-${idx}-${sectionIdx}`} className="text-sm">
                                  <div className="flex items-center gap-2 mb-1">
                                    <span className="text-xs font-medium border px-1.5 py-0.5 rounded bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300 border-amber-200 dark:border-amber-800">
                                      {section.documentName}
                                    </span>
                                  </div>
                                  <div className="pl-3 border-l-4 border-amber-200 dark:border-amber-800 py-2 bg-muted/5 rounded-sm">
                                    <p className="text-sm bg-muted/30 p-2 rounded border-l-2 border-l-primary/20 italic">
                                      {section.content}
                                    </p>
                                  </div>
                                </div>
                              ))
                            ) : (
                              <p className="text-sm text-muted-foreground">No section details available</p>
                            )}
                          </div>
                        </div>
                      ))}
                  </CardContent>
                </Card>
              )}
            </TabsContent>

            {/* Differences Section */}
            {comparisonResult?.differences && comparisonResult.differences.length > 0 && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                  <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                    <Diff className="h-5 w-5 mr-2" />
                    Differences
                  </CardTitle>
                  <CardDescription>Variations in provisions across documents</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  {Array.isArray(comparisonResult.differences) &&
                    comparisonResult.differences.map((difference: ExtendedProvision, idx: number) => (
                      <div key={`difference-${idx}`} className="border rounded-md p-3 sm:p-4 shadow-sm">
                        <div className="border-l-4 border-amber-400 dark:border-amber-600 pl-3 mb-3">
                          <h4 className="font-semibold text-amber-700 dark:text-amber-400 py-1">{difference.title}</h4>
                          <p className="text-sm pb-1">{difference.description}</p>
                        </div>
                        <div className="space-y-3 mt-4">
                          {difference.sections && difference.sections.length > 0 ? (
                            difference.sections.map((section: SectionInfo, sectionIdx: number) => (
                              <div key={`diff-section-${idx}-${sectionIdx}`} className="text-sm">
                                <div className="flex items-center gap-2 mb-1">
                                  <span className="text-xs font-medium border px-1.5 py-0.5 rounded bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300 border-amber-200 dark:border-amber-800">
                                    {section.documentName}
                                  </span>
                                </div>
                                <div className="pl-3 border-l-4 border-amber-200 dark:border-amber-800 py-2 bg-muted/5 rounded-sm">
                                  <p className="text-sm bg-muted/30 p-2 rounded border-l-2 border-l-primary/20 italic">
                                    {section.content}
                                  </p>
                                </div>
                              </div>
                            ))
                          ) : (
                            <p className="text-sm text-muted-foreground">No section details available</p>
                          )}
                        </div>
                      </div>
                    ))}
                </CardContent>
              </Card>
            )}

            {/* Gaps Section */}
            {comparisonResult?.gaps && comparisonResult.gaps.length > 0 && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                  <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                    <AlertTriangle className="h-5 w-5 mr-2" />
                    Gaps
                  </CardTitle>
                  <CardDescription>Missing provisions in some documents</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  {Array.isArray(comparisonResult.gaps) &&
                    comparisonResult.gaps.map((gap: Gap, idx: number) => (
                      <div key={`gap-${idx}`} className="mb-4">
                        <div className="border-l-4 border-red-400 dark:border-red-600 pl-4 py-2 mb-3">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-semibold text-red-700 dark:text-red-400">{gap.area}</h4>
                            {getPriorityBadge(gap.priority)}
                          </div>
                          <p className="text-sm">{gap.description}</p>
                        </div>
                        <div className="flex flex-col sm:flex-row gap-4">
                          {gap.presentIn && gap.presentIn.length > 0 && (
                            <div className="border-l-4 border-green-400 dark:border-green-600 pl-4 py-2 flex-1">
                              <h5 className="text-sm font-medium text-green-700 dark:text-green-400 mb-2 flex items-center">
                                <CheckCircle className="h-3.5 w-3.5 mr-1" />
                                Present in:
                              </h5>
                              <ul className="space-y-1 text-sm">
                                {Array.isArray(gap.presentIn) ? (
                                  gap.presentIn.map((doc: string, docIdx: number) => (
                                    <li key={`present-${idx}-${docIdx}`} className="flex items-center">
                                      <CheckCircle className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                                      {doc}
                                    </li>
                                  ))
                                ) : (
                                  <li>No documents</li>
                                )}
                              </ul>
                            </div>
                          )}
                          {gap.missingFrom && gap.missingFrom.length > 0 && (
                            <div className="border-l-4 border-red-300 dark:border-red-700 pl-4 py-2 flex-1">
                              <h5 className="text-sm font-medium text-red-700 dark:text-red-400 mb-2 flex items-center">
                                <AlertTriangle className="h-3.5 w-3.5 mr-1" />
                                Missing from:
                              </h5>
                              <ul className="space-y-1 text-sm">
                                {Array.isArray(gap.missingFrom) ? (
                                  gap.missingFrom.map((doc: string, docIdx: number) => (
                                    <li key={`missing-${idx}-${docIdx}`} className="flex items-center">
                                      <AlertCircle className="h-3.5 w-3.5 mr-1 text-red-600 dark:text-red-400" />
                                      {doc}
                                    </li>
                                  ))
                                ) : (
                                  <li>No documents</li>
                                )}
                              </ul>
                            </div>
                          )}
                        </div>
                        {gap.recommendation && (
                          <div className="mt-4 border-l-4 border-blue-400 dark:border-blue-600 pl-4 py-2 bg-blue-50/30 dark:bg-blue-900/10 rounded-sm">
                            <h5 className="text-sm font-medium mb-1 text-blue-700 dark:text-blue-400 flex items-center">
                              <Lightbulb className="h-3.5 w-3.5 mr-1" />
                              Recommendation:
                            </h5>
                            <p className="text-sm">{gap.recommendation}</p>
                          </div>
                        )}
                      </div>
                    ))}
                </CardContent>
              </Card>
            )}

            {/* Type Specific Analysis Section */}
            {comparisonResult?.typeSpecificAnalysis?.findings &&
              comparisonResult.typeSpecificAnalysis.findings.length > 0 && (
                <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                  <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                    <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                      <Lightbulb className="h-5 w-5 mr-2" />
                      Document Analysis
                    </CardTitle>
                    <CardDescription>
                      Insights for {comparisonResult.typeSpecificAnalysis.dominantDocumentType} documents
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 pt-4">
                    {Array.isArray(comparisonResult.typeSpecificAnalysis.findings) &&
                      comparisonResult.typeSpecificAnalysis.findings.map((finding: Finding, idx: number) => (
                        <div key={`finding-${idx}`} className="mb-4">
                          <div className="border-l-4 border-gray-400 dark:border-gray-600 pl-4 py-2 mb-3">
                            <div className="flex items-start gap-2 mb-2">
                              <span className="text-xs font-medium border px-2 py-0.5 rounded bg-teal-50 text-teal-700 dark:bg-teal-900/20 dark:text-teal-300 border-teal-200 dark:border-teal-800">
                                {finding.documentType}
                              </span>
                            </div>
                          </div>
                          <div className="space-y-3">
                            <div className="border-l-4 border-teal-300 dark:border-teal-700 pl-4 py-2 bg-teal-50/30 dark:bg-teal-900/10 rounded-sm">
                              <h5 className="text-sm font-medium text-teal-700 dark:text-teal-400 mb-1 flex items-center">
                                <Info className="h-3.5 w-3.5 mr-1" />
                                Observation:
                              </h5>
                              <p className="text-sm">{finding.observation}</p>
                            </div>
                            <div className="border-l-4 border-blue-300 dark:border-blue-700 pl-4 py-2 bg-blue-50/30 dark:bg-blue-900/10 rounded-sm">
                              <h5 className="text-sm font-medium text-blue-700 dark:text-blue-400 mb-1 flex items-center">
                                <Lightbulb className="h-3.5 w-3.5 mr-1" />
                                Implication:
                              </h5>
                              <p className="text-sm">{finding.implication}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                  </CardContent>
                </Card>
              )}
          </TabsContent>

          {/* Summary Tab Content */}
          <TabsContent value="summary" className="space-y-6 mt-0">
            {comparisonResult?.summary && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-muted/30">
                  <CardTitle className="text-xl flex items-center text-primary">
                    <FileText className="h-5 w-5 mr-2" />
                    Comparison Summary
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  <div className="flex flex-col sm:flex-row gap-4">
                    <div className="border-l-4 border-primary/40 pl-4 py-3 flex-1">
                      <div className="flex items-center justify-between mb-3">
                        <h4 className="font-semibold text-primary">Overall Coherence</h4>
                        {comparisonResult.summary.overallCoherence &&
                          getCoherenceBadge(comparisonResult.summary.overallCoherence)}
                      </div>
                      <div className="h-2 bg-muted rounded-full overflow-hidden">
                        <div
                          className={cn(
                            "h-full rounded-full",
                            comparisonResult.summary.overallCoherence.toLowerCase().includes("high")
                              ? "bg-gray-800"
                              : comparisonResult.summary.overallCoherence.toLowerCase().includes("medium")
                                ? "bg-gray-600"
                                : "bg-gray-400",
                          )}
                          style={{
                            width: comparisonResult.summary.overallCoherence.toLowerCase().includes("high")
                              ? "90%"
                              : comparisonResult.summary.overallCoherence.toLowerCase().includes("medium")
                                ? "60%"
                                : "30%",
                          }}
                        ></div>
                      </div>
                    </div>
                  </div>

                  {comparisonResult.summary.keyInsights && comparisonResult.summary.keyInsights.length > 0 && (
                    <div className="border-l-4 border-primary/40 pl-4 py-3">
                      <h4 className="font-semibold mb-3 text-primary flex items-center">
                        <Info className="h-4 w-4 mr-1" />
                        Key Insights
                      </h4>
                      <div className="space-y-3">
                        {comparisonResult.summary.keyInsights.map((insight: string, idx: number) => (
                          <div
                            key={`insight-${idx}`}
                            className={cn(
                              "p-2",
                              idx < 2
                                ? "border-l-4 border-blue-300 dark:border-blue-700 bg-blue-50/30 dark:bg-blue-900/10 pl-3"
                                : "border-l-4 border-blue-200 dark:border-blue-800/50 pl-3",
                            )}
                          >
                            <p className={cn("text-sm", idx < 2 ? "font-medium text-blue-700 dark:text-blue-400" : "")}>
                              {insight}
                            </p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}

                  {comparisonResult.summary.recommendations && comparisonResult.summary.recommendations.length > 0 && (
                    <div className="border-l-4 border-green-400 dark:border-green-600 pl-4 py-3">
                      <h4 className="font-semibold mb-3 text-green-700 dark:text-green-400 flex items-center">
                        <Lightbulb className="h-4 w-4 mr-1" />
                        Recommendations
                      </h4>
                      <div className="space-y-3">
                        {comparisonResult.summary.recommendations.map((rec: string, idx: number) => (
                          <div
                            key={`rec-${idx}`}
                            className="p-2 border-l-4 border-green-300 dark:border-green-700 bg-green-50/30 dark:bg-green-900/10 pl-3"
                          >
                            <p className="text-sm">{rec}</p>
                          </div>
                        ))}
                      </div>
                    </div>
                  )}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Key Provisions Tab Content */}
          <TabsContent value="provisions" className="space-y-6 mt-0">
            {comparisonResult?.keyProvisions && comparisonResult.keyProvisions.length > 0 && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                  <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                    <Layers className="h-5 w-5 mr-2" />
                    Key Provisions
                  </CardTitle>
                  <CardDescription>Important provisions identified across documents</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  {Array.isArray(comparisonResult.keyProvisions) &&
                    comparisonResult.keyProvisions.map((provision: KeyProvision, idx: number) => (
                      <div key={`provision-${idx}`} className="border rounded-md p-3 sm:p-4 shadow-sm">
                        <div className="flex items-center justify-between mb-3">
                          <h4 className="font-semibold text-gray-700 dark:text-gray-300">{provision.topic}</h4>
                          {getSignificanceBadge(provision.significance)}
                        </div>

                        <div className="space-y-4 mt-4">
                          {provision.provisions && provision.provisions.length > 0 && (
                            <div>
                              <h5 className="text-sm font-medium mb-2 text-purple-600 dark:text-purple-400">
                                Provisions:
                              </h5>
                              <div className="space-y-3">
                                {provision.provisions.map((p, pIdx) => {
                                  // Determine border color based on document or conflicts
                                  const borderColorClass = p.conflicts
                                    ? "border-amber-400 dark:border-amber-600"
                                    : pIdx % 2 === 0
                                      ? "border-purple-400 dark:border-purple-600"
                                      : "border-blue-400 dark:border-blue-600"

                                  return (
                                    <div
                                      key={`prov-${idx}-${pIdx}`}
                                      className={`border-l-4 ${borderColorClass} pl-3 py-2 bg-muted/5 rounded-sm`}
                                    >
                                      <div className="flex items-start gap-2 mb-2">
                                        <span className="text-xs font-medium border px-1.5 py-0.5 rounded bg-blue-50 text-blue-700 dark:bg-blue-900/20 dark:text-blue-300 border-blue-200 dark:border-blue-800">
                                          {p.document}
                                        </span>
                                        {p.sectionReference && (
                                          <span className="text-xs border px-1 py-0.5 rounded text-muted-foreground">
                                            {p.sectionReference}
                                          </span>
                                        )}
                                      </div>
                                      <p className="text-sm bg-muted/30 p-2 rounded border-l-2 border-l-primary/20 italic">
                                        {p.content}
                                      </p>
                                      {p.conflicts && (
                                        <div className="mt-2 border-l-2 border-amber-400 dark:border-amber-600 pl-2 py-1">
                                          <p className="text-xs text-amber-600 dark:text-amber-400 flex items-center">
                                            <AlertTriangle className="h-3 w-3 mr-1" />
                                            <span className="font-medium">Conflicts:</span> {p.conflicts}
                                          </p>
                                        </div>
                                      )}
                                    </div>
                                  )
                                })}
                              </div>
                            </div>
                          )}
                          {provision.recommendation && (
                            <div className="mt-4 border-l-4 border-green-400 dark:border-green-600 pl-3 py-2 bg-green-50/30 dark:bg-green-900/10 rounded-sm">
                              <h5 className="text-sm font-medium mb-1 text-green-700 dark:text-green-400 flex items-center">
                                <Lightbulb className="h-3.5 w-3.5 mr-1" />
                                Recommendation:
                              </h5>
                              <p className="text-sm">{provision.recommendation}</p>
                            </div>
                          )}
                        </div>
                      </div>
                    ))}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Similarities Tab Content */}
          <TabsContent value="similarities" className="space-y-6 mt-0">
            {comparisonResult?.similarities && comparisonResult.similarities.length > 0 && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                  <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                    <CheckCircle className="h-5 w-5 mr-2" />
                    Similarities
                  </CardTitle>
                  <CardDescription>Common provisions across documents</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  {Array.isArray(comparisonResult.similarities) &&
                    comparisonResult.similarities.map((similarity: ExtendedProvision, idx: number) => (
                      <div key={`similarity-${idx}`} className="border rounded-md p-3 sm:p-4 shadow-sm">
                        <div className="border-l-4 border-green-400 dark:border-green-600 pl-3 mb-3">
                          <h4 className="font-semibold text-green-700 dark:text-green-400 py-1">{similarity.title}</h4>
                          <p className="text-sm pb-1">{similarity.description}</p>
                        </div>
                        <div className="space-y-3 mt-4">
                          {similarity.sections && similarity.sections.length > 0 ? (
                            similarity.sections.map((section: SectionInfo, sectionIdx: number) => (
                              <div key={`sim-section-${idx}-${sectionIdx}`} className="text-sm">
                                <div className="flex items-center gap-2 mb-1">
                                  <span className="text-xs font-medium border px-1.5 py-0.5 rounded bg-green-50 text-green-700 dark:bg-green-900/20 dark:text-green-300 border-green-200 dark:border-green-800">
                                    {section.documentName}
                                  </span>
                                </div>
                                <div className="pl-3 border-l-4 border-green-200 dark:border-green-800 py-2 bg-muted/5 rounded-sm">
                                  <p className="text-sm bg-muted/30 p-2 rounded border-l-2 border-l-primary/20 italic">
                                    {section.content}
                                  </p>
                                </div>
                              </div>
                            ))
                          ) : (
                            <p className="text-sm text-muted-foreground">No section details available</p>
                          )}
                        </div>
                      </div>
                    ))}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Differences Tab Content */}
          <TabsContent value="differences" className="space-y-6 mt-0">
            {comparisonResult?.differences && comparisonResult.differences.length > 0 && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                  <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                    <Diff className="h-5 w-5 mr-2" />
                    Differences
                  </CardTitle>
                  <CardDescription>Variations in provisions across documents</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  {Array.isArray(comparisonResult.differences) &&
                    comparisonResult.differences.map((difference: ExtendedProvision, idx: number) => (
                      <div key={`difference-${idx}`} className="border rounded-md p-3 sm:p-4 shadow-sm">
                        <div className="border-l-4 border-amber-400 dark:border-amber-600 pl-3 mb-3">
                          <h4 className="font-semibold text-amber-700 dark:text-amber-400 py-1">{difference.title}</h4>
                          <p className="text-sm pb-1">{difference.description}</p>
                        </div>
                        <div className="space-y-3 mt-4">
                          {difference.sections && difference.sections.length > 0 ? (
                            difference.sections.map((section: SectionInfo, sectionIdx: number) => (
                              <div key={`diff-section-${idx}-${sectionIdx}`} className="text-sm">
                                <div className="flex items-center gap-2 mb-1">
                                  <span className="text-xs font-medium border px-1.5 py-0.5 rounded bg-amber-50 text-amber-700 dark:bg-amber-900/20 dark:text-amber-300 border-amber-200 dark:border-amber-800">
                                    {section.documentName}
                                  </span>
                                </div>
                                <div className="pl-3 border-l-4 border-amber-200 dark:border-amber-800 py-2 bg-muted/5 rounded-sm">
                                  <p className="text-sm bg-muted/30 p-2 rounded border-l-2 border-l-primary/20 italic">
                                    {section.content}
                                  </p>
                                </div>
                              </div>
                            ))
                          ) : (
                            <p className="text-sm text-muted-foreground">No section details available</p>
                          )}
                        </div>
                      </div>
                    ))}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Gaps Tab Content */}
          <TabsContent value="gaps" className="space-y-6 mt-0">
            {comparisonResult?.gaps && comparisonResult.gaps.length > 0 && (
              <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                  <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                    <AlertTriangle className="h-5 w-5 mr-2" />
                    Gaps
                  </CardTitle>
                  <CardDescription>Missing provisions in some documents</CardDescription>
                </CardHeader>
                <CardContent className="space-y-4 pt-4">
                  {Array.isArray(comparisonResult.gaps) &&
                    comparisonResult.gaps.map((gap: Gap, idx: number) => (
                      <div key={`gap-${idx}`} className="mb-4">
                        <div className="border-l-4 border-red-400 dark:border-red-600 pl-4 py-2 mb-3">
                          <div className="flex items-center justify-between mb-2">
                            <h4 className="font-semibold text-red-700 dark:text-red-400">{gap.area}</h4>
                            {getPriorityBadge(gap.priority)}
                          </div>
                          <p className="text-sm">{gap.description}</p>
                        </div>
                        <div className="flex flex-col sm:flex-row gap-4">
                          {gap.presentIn && gap.presentIn.length > 0 && (
                            <div className="border-l-4 border-green-400 dark:border-green-600 pl-4 py-2 flex-1">
                              <h5 className="text-sm font-medium text-green-700 dark:text-green-400 mb-2 flex items-center">
                                <CheckCircle className="h-3.5 w-3.5 mr-1" />
                                Present in:
                              </h5>
                              <ul className="space-y-1 text-sm">
                                {Array.isArray(gap.presentIn) ? (
                                  gap.presentIn.map((doc: string, docIdx: number) => (
                                    <li key={`present-${idx}-${docIdx}`} className="flex items-center">
                                      <CheckCircle className="h-3.5 w-3.5 mr-1 text-green-600 dark:text-green-400" />
                                      {doc}
                                    </li>
                                  ))
                                ) : (
                                  <li>No documents</li>
                                )}
                              </ul>
                            </div>
                          )}
                          {gap.missingFrom && gap.missingFrom.length > 0 && (
                            <div className="border-l-4 border-red-300 dark:border-red-700 pl-4 py-2 flex-1">
                              <h5 className="text-sm font-medium text-red-700 dark:text-red-400 mb-2 flex items-center">
                                <AlertTriangle className="h-3.5 w-3.5 mr-1" />
                                Missing from:
                              </h5>
                              <ul className="space-y-1 text-sm">
                                {Array.isArray(gap.missingFrom) ? (
                                  gap.missingFrom.map((doc: string, docIdx: number) => (
                                    <li key={`missing-${idx}-${docIdx}`} className="flex items-center">
                                      <AlertCircle className="h-3.5 w-3.5 mr-1 text-red-600 dark:text-red-400" />
                                      {doc}
                                    </li>
                                  ))
                                ) : (
                                  <li>No documents</li>
                                )}
                              </ul>
                            </div>
                          )}
                        </div>
                        {gap.recommendation && (
                          <div className="mt-4 border-l-4 border-blue-400 dark:border-blue-600 pl-4 py-2 bg-blue-50/30 dark:bg-blue-900/10 rounded-sm">
                            <h5 className="text-sm font-medium mb-1 text-blue-700 dark:text-blue-400 flex items-center">
                              <Lightbulb className="h-3.5 w-3.5 mr-1" />
                              Recommendation:
                            </h5>
                            <p className="text-sm">{gap.recommendation}</p>
                          </div>
                        )}
                      </div>
                    ))}
                </CardContent>
              </Card>
            )}
          </TabsContent>

          {/* Analysis Tab Content */}
          <TabsContent value="analysis" className="space-y-6 mt-0">
            {comparisonResult?.typeSpecificAnalysis?.findings &&
              comparisonResult.typeSpecificAnalysis.findings.length > 0 && (
                <Card className="mb-6 shadow-sm border-t-4 border-t-gray-500">
                  <CardHeader className="pb-3 border-b bg-gray-50/50 dark:bg-gray-950/10">
                    <CardTitle className="text-xl flex items-center text-gray-700 dark:text-gray-300">
                      <Lightbulb className="h-5 w-5 mr-2" />
                      Document Analysis
                    </CardTitle>
                    <CardDescription>
                      Insights for {comparisonResult.typeSpecificAnalysis.dominantDocumentType} documents
                    </CardDescription>
                  </CardHeader>
                  <CardContent className="space-y-4 pt-4">
                    {Array.isArray(comparisonResult.typeSpecificAnalysis.findings) &&
                      comparisonResult.typeSpecificAnalysis.findings.map((finding: Finding, idx: number) => (
                        <div key={`finding-${idx}`} className="mb-4">
                          <div className="border-l-4 border-gray-400 dark:border-gray-600 pl-4 py-2 mb-3">
                            <div className="flex items-start gap-2 mb-2">
                              <span className="text-xs font-medium border px-2 py-0.5 rounded bg-teal-50 text-teal-700 dark:bg-teal-900/20 dark:text-teal-300 border-teal-200 dark:border-teal-800">
                                {finding.documentType}
                              </span>
                            </div>
                          </div>
                          <div className="space-y-3">
                            <div className="border-l-4 border-teal-300 dark:border-teal-700 pl-4 py-2 bg-teal-50/30 dark:bg-teal-900/10 rounded-sm">
                              <h5 className="text-sm font-medium text-teal-700 dark:text-teal-400 mb-1 flex items-center">
                                <Info className="h-3.5 w-3.5 mr-1" />
                                Observation:
                              </h5>
                              <p className="text-sm">{finding.observation}</p>
                            </div>
                            <div className="border-l-4 border-blue-300 dark:border-blue-700 pl-4 py-2 bg-blue-50/30 dark:bg-blue-900/10 rounded-sm">
                              <h5 className="text-sm font-medium text-blue-700 dark:text-blue-400 mb-1 flex items-center">
                                <Lightbulb className="h-3.5 w-3.5 mr-1" />
                                Implication:
                              </h5>
                              <p className="text-sm">{finding.implication}</p>
                            </div>
                          </div>
                        </div>
                      ))}
                  </CardContent>
                </Card>
              )}
          </TabsContent>
        </Tabs>
      </div>
    )
  }

  return (
    <div className="container mx-auto py-3 sm:py-8 px-4 sm:px-6 md:px-8 max-w-full sm:max-w-[95%] lg:max-w-[90%]">
      <h1 className="text-xl sm:text-2xl md:text-3xl font-bold mb-1 sm:mb-2 pl-8 sm:pl-0 flex items-center">
        <GitCompare className="h-6 w-6 mr-2 text-primary hidden sm:inline" />
        Compare Document Sections
      </h1>
      <p className="text-xs sm:text-sm md:text-base text-muted-foreground mb-3 sm:mb-4 md:mb-6 pl-8 sm:pl-0">
        Compare specific sections across multiple documents to identify similarities and differences.
      </p>

      {isLoading && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm flex items-center justify-center z-50">
          <div className="bg-card p-6 rounded-lg shadow-lg border flex flex-col items-center max-w-md w-full">
            <Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
            <h3 className="text-lg font-medium mb-2">Analyzing Documents</h3>
            <p className="text-sm text-muted-foreground text-center mb-4">
              We&apos;re comparing your document sections and generating insights. This may take a moment...
            </p>
            <div className="w-full space-y-2">
              <div className="h-1.5 w-full bg-muted rounded-full overflow-hidden">
                <div className="h-full bg-primary rounded-full animate-pulse" style={{ width: "70%" }}></div>
              </div>
              <div className="flex justify-between text-xs text-muted-foreground">
                <span>Analyzing sections</span>
                <span>Please wait</span>
              </div>
            </div>
          </div>
        </div>
      )}

      {comparisonResult ? renderComparisonResults() : renderDocumentSelector()}
    </div>
  )
}
