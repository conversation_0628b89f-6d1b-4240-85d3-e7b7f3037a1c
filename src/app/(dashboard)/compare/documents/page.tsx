"use client";

import { useState } from "react";
import { DocumentComparisonSelector } from "@/components/document-comparison/document-comparison-selector";
import { DocumentComparison } from "@/components/document-comparison/document-comparison";

export default function CompareDocumentsPage() {
  const [selectedDocuments, setSelectedDocuments] = useState<{
    primaryDocumentId: string;
    relatedDocumentIds: string[];
  } | null>(null);

  const handleFullDocumentCompare = (primaryDocId: string, relatedDocIds: string[]) => {
    setSelectedDocuments({
      primaryDocumentId: primaryDocId,
      relatedDocumentIds: relatedDocIds,
    });
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <h1 className="text-3xl font-bold mb-2">Compare Full Documents</h1>
      <p className="text-muted-foreground mb-6">
        Compare entire documents to identify similarities, differences, and relationships.
      </p>

      <div className="container py-6 max-w-6xl">
        {!selectedDocuments ? (
          <DocumentComparisonSelector
            onCompare={handleFullDocumentCompare}
            onCancel={() => {}}
          />
        ) : (
          <DocumentComparison
            primaryDocumentId={selectedDocuments.primaryDocumentId}
            relatedDocumentIds={selectedDocuments.relatedDocumentIds}
            onBack={() => setSelectedDocuments(null)}
          />
        )}
      </div>
    </div>
  );
}
