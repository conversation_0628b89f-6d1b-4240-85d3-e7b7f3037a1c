"use client";

import { useState, useEffect } from "react";
import { useSearchParams } from "next/navigation";
import { But<PERSON> } from "@/components/ui/button";
import {
  Card,
  CardContent,
  CardHeader,
  CardTitle,
  CardDescription,
} from "@/components/ui/card";
import { ArrowLeftRight, Loader2 } from "lucide-react";
import { SimpleDocumentSelector } from "@/components/document-comparison/simple-document-selector";
import { EnhancedComparison } from "@/components/document-comparison/enhanced-comparison";
import { documentComparisonService, type EnhancedComparisonResult, type EnhancedComparisonRequest } from "@/lib/services/document-comparison-service";
import { documentService } from "@/lib/services/document-service";
import { useToast } from "@/hooks/use-toast";

export default function EnhancedComparisonPage() {
  // Define a type for our selected documents to use consistently
  type SelectedDocuments = {
    documentA: string;
    documentB: string;
  };

  const searchParams = useSearchParams();
  const docA = searchParams.get('docA') || "";
  const docB = searchParams.get('docB') || "";

  const [selectedDocuments, setSelectedDocuments] = useState<SelectedDocuments>({ 
    documentA: docA, 
    documentB: docB 
  });
  const [comparisonResult, setComparisonResult] = useState<EnhancedComparisonResult | null>(null);
  const [loading, setLoading] = useState(false);
  // Document content is only used for the API request
  const [, setDocumentAContent] = useState("");
  const [, setDocumentBContent] = useState("");
  const { toast } = useToast();

  const handleEnhancedCompare = (documentA: string, documentB: string) => {
    setSelectedDocuments({
      documentA,
      documentB,
    });
  };

  // Fetch document content and perform comparison when documents are selected
  useEffect(() => {
    const fetchDocumentsAndCompare = async () => {
      // Early return if selectedDocuments is null or either document ID is missing
      if (!selectedDocuments.documentA || !selectedDocuments.documentB) return;
      
      setLoading(true);
      try {
        // Fetch document content
        const [docAContent, docBContent] = await Promise.all([
          documentService.getDocumentContent(selectedDocuments.documentA),
          documentService.getDocumentContent(selectedDocuments.documentB)
        ]);
        
        setDocumentAContent(docAContent);
        setDocumentBContent(docBContent);
        
        // Perform enhanced comparison
        const request: EnhancedComparisonRequest = {
          documentA: docAContent,
          documentB: docBContent,
          type: "both",
          includeVisualDiff: true,
          includeSectionReferences: true,
          // Use partial metadata with just the ID
          documentAMetadata: { 
            id: selectedDocuments.documentA,
            title: `Document ${selectedDocuments.documentA}`,
            createdAt: new Date().toISOString(),
            organizationId: "",
            userId: ""
          },
          documentBMetadata: {
            id: selectedDocuments.documentB,
            title: `Document ${selectedDocuments.documentB}`,
            createdAt: new Date().toISOString(),
            organizationId: "",
            userId: ""
          }
        };
        
        const result = await documentComparisonService.compareDocuments(request);
        setComparisonResult(result);
      } catch (error) {
        console.error("Error in enhanced comparison:", error);
        toast({
          title: "Error",
          description: "Failed to compare documents. Please try again.",
          variant: "destructive"
        });
      } finally {
        setLoading(false);
      }
    };
    
    fetchDocumentsAndCompare();
  }, [selectedDocuments, toast]);

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <h1 className="text-3xl font-bold mb-2">Enhanced Document Comparison</h1>
      <p className="text-muted-foreground mb-6">
        Compare documents with enhanced visualization and detailed section analysis.
      </p>

      <div className="container py-6 max-w-6xl">
        {!selectedDocuments.documentA || !selectedDocuments.documentB ? (
          <Card>
            <CardHeader>
              <CardTitle>Select Documents to Compare</CardTitle>
              <CardDescription>
                Choose two documents to perform an enhanced comparison with detailed analysis.
              </CardDescription>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                <div>
                  <h3 className="text-lg font-medium mb-3">First Document</h3>
                  <SimpleDocumentSelector
                    onSelect={(docId) => {
                      if (selectedDocuments.documentB.length > 0) {
                        handleEnhancedCompare(docId, selectedDocuments.documentB);
                      } else {
                        setSelectedDocuments(prev => ({ documentA: docId, documentB: prev.documentB }));
                      }
                    }}
                  />
                </div>
                <div>
                  <h3 className="text-lg font-medium mb-3">Second Document</h3>
                  <SimpleDocumentSelector
                    onSelect={(docId) => {
                      if (selectedDocuments.documentA.length > 0) {
                        handleEnhancedCompare(selectedDocuments.documentA, docId);
                      } else {
                        setSelectedDocuments(prev => ({ documentA: prev.documentA, documentB: docId }));
                      }
                    }}
                  />
                </div>
              </div>
              <div className="flex justify-center mt-6">
                <Button
                  size="lg"
                  disabled={!selectedDocuments.documentA || !selectedDocuments.documentB}
                  onClick={() => {
                    handleEnhancedCompare(selectedDocuments.documentA, selectedDocuments.documentB);
                  }}
                >
                  <ArrowLeftRight className="mr-2 h-4 w-4" />
                  Compare Documents
                </Button>
              </div>
            </CardContent>
          </Card>
        ) : (
          <div>
            <Button
              variant="outline"
              className="mb-4"
              onClick={() => {
                setSelectedDocuments({ documentA: "", documentB: "" });
                setComparisonResult(null);
              }}
            >
              ← Back to Selection
            </Button>
            
            {loading ? (
              <div className="flex flex-col items-center justify-center py-12">
                <Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
                <p className="text-lg font-medium">Comparing documents...</p>
                <p className="text-sm text-muted-foreground">This may take a moment for large documents.</p>
              </div>
            ) : comparisonResult ? (
              <EnhancedComparison result={comparisonResult} />
            ) : (
              <div className="flex flex-col items-center justify-center py-12">
                <p className="text-lg font-medium">Failed to generate comparison</p>
                <p className="text-sm text-muted-foreground">Please try again or select different documents.</p>
              </div>
            )}
          </div>
        )}
      </div>
    </div>
  );
}
