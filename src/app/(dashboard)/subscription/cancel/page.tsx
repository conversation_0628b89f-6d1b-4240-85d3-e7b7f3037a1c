"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { XCircle } from "lucide-react";

export default function SubscriptionCancelPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isVerified, setIsVerified] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshSubscription } = useSubscription();
  
  const sessionId = searchParams.get("session_id");

  useEffect(() => {
    const verifyCancellation = async () => {
      // Skip if already verified to prevent loop calls
      if (isVerified) return;
      
      if (!sessionId) {
        router.push("/subscription");
        return;
      }

      try {
        // Refresh subscription data to get the latest subscription details
        await refreshSubscription();
        setIsLoading(false);
        setIsVerified(true); // Mark as verified to prevent additional calls
      } catch (error) {
        console.error("Error verifying cancellation:", error);
        router.push("/subscription");
      }
    };

    verifyCancellation();
  }, [sessionId, refreshSubscription, router, isVerified]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="container py-12 max-w-md">
      <Card className="text-center">
        <CardHeader>
          <div className="mx-auto mb-4 bg-amber-100 dark:bg-amber-900/30 w-16 h-16 rounded-full flex items-center justify-center">
            <XCircle className="h-8 w-8 text-amber-600 dark:text-amber-400" />
          </div>
          <CardTitle className="text-2xl">Subscription Cancelled</CardTitle>
          <CardDescription>Your subscription has been cancelled successfully</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-2">
          <p>Your subscription has been cancelled.</p>
          <p className="text-sm text-muted-foreground">
            You will continue to have access to all features until the end of your current billing period.
            After that, your account will revert to the free tier.
          </p>
        </CardContent>
        
        <CardFooter className="flex justify-center">
          <Button onClick={() => router.push("/subscription")}>
            View Subscription Details
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
