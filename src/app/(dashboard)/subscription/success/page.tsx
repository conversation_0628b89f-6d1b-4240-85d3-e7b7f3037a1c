"use client";

import { useEffect, useState } from "react";
import { useRouter, useSearchParams } from "next/navigation";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { CheckCircle } from "lucide-react";

export default function SubscriptionSuccessPage() {
  const [isLoading, setIsLoading] = useState(true);
  const [isVerified, setIsVerified] = useState(false);
  const router = useRouter();
  const searchParams = useSearchParams();
  const { refreshSubscription } = useSubscription();
  
  const sessionId = searchParams.get("session_id");

  useEffect(() => {
    const verifyPayment = async () => {
      // Skip if already verified to prevent loop calls
      if (isVerified) return;
      
      if (!sessionId) {
        router.push("/subscription");
        return;
      }

      try {
        // Refresh subscription data to get the latest subscription details
        await refreshSubscription();
        setIsLoading(false);
        setIsVerified(true); // Mark as verified to prevent additional calls
      } catch (error) {
        console.error("Error verifying payment:", error);
        router.push("/subscription");
      }
    };

    verifyPayment();
  }, [sessionId, refreshSubscription, router, isVerified]);

  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Spinner size="lg" />
      </div>
    );
  }

  return (
    <div className="container py-12 max-w-md">
      <Card className="text-center">
        <CardHeader>
          <div className="mx-auto mb-4 bg-green-100 dark:bg-green-900/30 w-16 h-16 rounded-full flex items-center justify-center">
            <CheckCircle className="h-8 w-8 text-green-600 dark:text-green-400" />
          </div>
          <CardTitle className="text-2xl">Payment Successful</CardTitle>
          <CardDescription>Your subscription has been updated successfully</CardDescription>
        </CardHeader>
        
        <CardContent className="space-y-2">
          <p>Thank you for your subscription. Your account has been updated with the new plan benefits.</p>
          <p className="text-sm text-muted-foreground">
            You will receive a confirmation email with your receipt shortly.
          </p>
        </CardContent>
        
        <CardFooter className="flex justify-center">
          <Button onClick={() => router.push("/subscription")}>
            View Subscription Details
          </Button>
        </CardFooter>
      </Card>
    </div>
  );
}
