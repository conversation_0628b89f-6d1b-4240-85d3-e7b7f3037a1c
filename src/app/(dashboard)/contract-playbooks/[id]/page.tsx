"use client";

import React, { useState, use } from 'react';
import { useRouter } from 'next/navigation';
import { ArrowLeft, Edit, Trash2, Copy, Download, AlertTriangle, CheckCircle, Clock, XCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { useUser } from '@/hooks/use-user';
import { usePlaybook, useDeletePlaybook, useDuplicatePlaybook, useExportPlaybook, usePlaybookAnalytics } from '@/lib/services/contract-playbooks-service';
import { FeatureGuard } from '@/components/subscription/feature-guard';
import { CONTRACT_TYPES, RULE_TYPES, SEVERITY_LEVELS } from '@/lib/types/contract-playbooks';
import type { PlaybookRule } from '@/lib/types/contract-playbooks';

interface PageParams {
  id: string;
}

interface PlaybookDetailPageProps {
  params: Promise<PageParams>;
}

export default function PlaybookDetailPage({ params }: PlaybookDetailPageProps) {
  const unwrappedParams = use<PageParams>(params);
  const router = useRouter();
  const { toast } = useToast();
  const [activeTab, setActiveTab] = useState('overview');

  // API hooks
  const { data: playbook, isLoading, error } = usePlaybook(unwrappedParams.id);
  const { data: analytics } = usePlaybookAnalytics(unwrappedParams.id);
  const { user } = useUser();
  const deletePlaybook = useDeletePlaybook();
  const duplicatePlaybook = useDuplicatePlaybook();
  const exportPlaybook = useExportPlaybook();

  const handleDelete = async () => {
    if (!playbook || !confirm(`Are you sure you want to delete "${playbook.name}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deletePlaybook.mutateAsync(unwrappedParams.id);
      toast({
        title: "Playbook deleted",
        description: `"${playbook.name}" has been successfully deleted.`,
      });
      router.push('/contract-playbooks');
    } catch {
      toast({
        title: "Error",
        description: "Failed to delete playbook. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleDuplicate = async () => {
    if (!playbook) return;

    try {
      const duplicated = await duplicatePlaybook.mutateAsync({
        id: unwrappedParams.id,
        data: { name: `${playbook.name} (Copy)` }
      });
      toast({
        title: "Playbook duplicated",
        description: `"${playbook.name}" has been successfully duplicated.`,
      });
      router.push(`/contract-playbooks/${duplicated.id}`);
    } catch {
      toast({
        title: "Error",
        description: "Failed to duplicate playbook. Please try again.",
        variant: "destructive",
      });
    }
  };

  const handleExport = async () => {
    if (!playbook) return;

    try {
      const exportData = await exportPlaybook.mutateAsync(unwrappedParams.id);
      
      // Create and download file
      const blob = new Blob([JSON.stringify(exportData, null, 2)], { type: 'application/json' });
      const url = URL.createObjectURL(blob);
      const a = document.createElement('a');
      a.href = url;
      a.download = `${playbook.name.replace(/[^a-z0-9]/gi, '_').toLowerCase()}_playbook.json`;
      document.body.appendChild(a);
      a.click();
      document.body.removeChild(a);
      URL.revokeObjectURL(url);

      toast({
        title: "Playbook exported",
        description: `"${playbook.name}" has been successfully exported.`,
      });
    } catch {
      toast({
        title: "Error",
        description: "Failed to export playbook. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getContractTypeLabel = (type: string) => {
    return CONTRACT_TYPES.find(ct => ct.value === type)?.label || type;
  };

  const getRuleTypeLabel = (type: string) => {
    return RULE_TYPES.find(rt => rt.value === type)?.label || type;
  };

  const getSeverityColor = (severity: string) => {
    return SEVERITY_LEVELS.find(sl => sl.value === severity)?.color || 'text-gray-600';
  };

  const getSeverityIcon = (severity: string) => {
    switch (severity) {
      case 'LOW': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'MEDIUM': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'HIGH': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'CRITICAL': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <CheckCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  if (isLoading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="h-32 bg-muted rounded"></div>
          <div className="h-64 bg-muted rounded"></div>
        </div>
      </div>
    );
  }

  if (error || !playbook) {
    return (
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <h2 className="text-2xl font-semibold mb-2">Playbook not found</h2>
          <p className="text-muted-foreground mb-6">
            The playbook you&apos;re looking for doesn&apos;t exist or you don&apos;t have access to it.
          </p>
          <Button onClick={() => router.push('/contract-playbooks')}>
            Back to Playbooks
          </Button>
        </div>
      </div>
    );
  }

  return (
    <FeatureGuard featureId="contract_playbooks">
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center gap-4">
          <Button variant="ghost" size="sm" onClick={() => router.back()}>
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back
          </Button>
          <div className="flex-1">
            <h1 className="text-3xl font-bold">{playbook.name}</h1>
            <p className="text-muted-foreground">
              {playbook.description || 'No description provided'}
            </p>
          </div>
          <div className="flex items-center gap-2">
            <Button variant="outline" onClick={() => router.push(`/contract-playbooks/${unwrappedParams.id}/edit`)}>
              <Edit className="h-4 w-4 mr-2" />
              Edit
            </Button>
            <Button variant="outline" onClick={handleDuplicate}>
              <Copy className="h-4 w-4 mr-2" />
              Duplicate
            </Button>
            <Button variant="outline" onClick={handleExport}>
              <Download className="h-4 w-4 mr-2" />
              Export
            </Button>
            <Button variant="destructive" onClick={handleDelete}>
              <Trash2 className="h-4 w-4 mr-2" />
              Delete
            </Button>
          </div>
        </div>

        {/* Quick Info */}
        <Card>
          <CardContent className="p-6">
            <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Contract Type</h3>
                <Badge variant="secondary">{getContractTypeLabel(playbook.contractType)}</Badge>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Version</h3>
                <p className="font-medium">v{playbook.version}</p>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Status</h3>
                <Badge variant={playbook.isActive ? "default" : "secondary"}>
                  {playbook.isActive ? 'Active' : 'Inactive'}
                </Badge>
              </div>
              <div>
                <h3 className="font-medium text-sm text-muted-foreground mb-2">Rules</h3>
                <p className="font-medium">{playbook.rules.length} rules</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Tabs */}
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList>
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="rules">Rules ({playbook.rules.length})</TabsTrigger>
            <TabsTrigger value="metadata">Metadata</TabsTrigger>
            {analytics && <TabsTrigger value="analytics">Analytics</TabsTrigger>}
          </TabsList>

          <TabsContent value="overview" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Playbook Information</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div>
                    <h4 className="font-medium mb-2">Basic Information</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Name:</span>
                        <span>{playbook.name}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Contract Type:</span>
                        <span>{getContractTypeLabel(playbook.contractType)}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Version:</span>
                        <span>v{playbook.version}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Template:</span>
                        <span>{playbook.isTemplate ? 'Yes' : 'No'}</span>
                      </div>
                    </div>
                  </div>
                  <div>
                    <h4 className="font-medium mb-2">Timestamps</h4>
                    <div className="space-y-2 text-sm">
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Created:</span>
                        <span>{new Date(playbook.createdAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Updated:</span>
                        <span>{new Date(playbook.updatedAt).toLocaleDateString()}</span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Created by:</span>
                        <span>
                          {user?.id === playbook.createdBy ? "You" : "Another user"}
                        </span>
                      </div>
                      <div className="flex justify-between">
                        <span className="text-muted-foreground">Updated by:</span>
                        <span>
                          {user?.id === playbook.updatedBy ? "You" : "Another user"}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
                
                {playbook.description && (
                  <>
                    <Separator />
                    <div>
                      <h4 className="font-medium mb-2">Description</h4>
                      <p className="text-sm text-muted-foreground">{playbook.description}</p>
                    </div>
                  </>
                )}
              </CardContent>
            </Card>

            {/* Rules Summary */}
            <Card>
              <CardHeader>
                <CardTitle>Rules Summary</CardTitle>
                <CardDescription>
                  Overview of rules by type and severity
                </CardDescription>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                  {RULE_TYPES.map((ruleType) => {
                    const count = playbook.rules.filter(rule => rule.ruleType === ruleType.value).length;
                    return (
                      <div key={ruleType.value} className="text-center p-4 border rounded-lg">
                        <div className="text-2xl font-bold">{count}</div>
                        <div className="text-sm text-muted-foreground">{ruleType.label}</div>
                      </div>
                    );
                  })}
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="rules" className="space-y-6">
            <div className="grid gap-4">
              {playbook.rules.map((rule: PlaybookRule) => (
                <RuleCard key={rule.id} rule={rule} getRuleTypeLabel={getRuleTypeLabel} getSeverityColor={getSeverityColor} getSeverityIcon={getSeverityIcon} />
              ))}
            </div>
          </TabsContent>

          <TabsContent value="metadata" className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Metadata</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Industry</h4>
                      <p className="text-sm text-muted-foreground">
                        {playbook.metadata.industry || 'Not specified'}
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Jurisdiction</h4>
                      <p className="text-sm text-muted-foreground">
                        {playbook.metadata.jurisdiction || 'Not specified'}
                      </p>
                    </div>
                    <div>
                      <h4 className="font-medium mb-2">Risk Profile</h4>
                      <p className="text-sm text-muted-foreground">
                        {playbook.metadata.riskProfile || 'Not specified'}
                      </p>
                    </div>
                  </div>
                  <div className="space-y-4">
                    <div>
                      <h4 className="font-medium mb-2">Tags</h4>
                      <div className="flex flex-wrap gap-2">
                        {playbook.metadata.tags?.length ? (
                          playbook.metadata.tags.map((tag, index) => (
                            <Badge key={index} variant="outline">{tag}</Badge>
                          ))
                        ) : (
                          <p className="text-sm text-muted-foreground">No tags</p>
                        )}
                      </div>
                    </div>
                    {playbook.metadata.customFields && Object.keys(playbook.metadata.customFields).length > 0 && (
                      <div>
                        <h4 className="font-medium mb-2">Custom Fields</h4>
                        <div className="space-y-2">
                          {Object.entries(playbook.metadata.customFields).map(([key, value]) => (
                            <div key={key} className="flex justify-between text-sm">
                              <span className="text-muted-foreground">{key}:</span>
                              <span>{String(value)}</span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          {analytics && (
            <TabsContent value="analytics" className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Total Analyses</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{analytics.totalAnalyses}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Average Score</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">{analytics?.averageScore?.toFixed(1) || '0.0'}</div>
                  </CardContent>
                </Card>
                <Card>
                  <CardHeader>
                    <CardTitle className="text-lg">Avg Processing Time</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="text-3xl font-bold">
                      {analytics?.performanceMetrics?.averageProcessingTime ?? '0'}s
                    </div>
                  </CardContent>
                </Card>
              </div>

              <Card>
                <CardHeader>
                  <CardTitle>Risk Distribution</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                    {analytics?.riskDistribution ? Object.entries(analytics.riskDistribution).map(([level, count]) => (
                      <div key={level} className="text-center p-4 border rounded-lg">
                        <div className="text-2xl font-bold">{count}</div>
                        <div className="text-sm text-muted-foreground">{level} Risk</div>
                      </div>
                    )) : null}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          )}
        </Tabs>
      </div>
    </FeatureGuard>
  );
}

interface RuleCardProps {
  rule: PlaybookRule;
  getRuleTypeLabel: (type: string) => string;
  getSeverityColor: (severity: string) => string;
  getSeverityIcon: (severity: string) => React.ReactNode;
}

function RuleCard({ rule, getRuleTypeLabel, getSeverityColor, getSeverityIcon }: RuleCardProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div>
            <CardTitle className="text-lg">{rule.name}</CardTitle>
            <CardDescription>{rule.description}</CardDescription>
          </div>
          <div className="flex items-center gap-2">
            {getSeverityIcon(rule.severity)}
            <Badge variant="outline" className={getSeverityColor(rule.severity)}>
              {rule.severity}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-3">
          <div className="flex items-center gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Type:</span>
              <span className="ml-2">{getRuleTypeLabel(rule.ruleType)}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Category:</span>
              <span className="ml-2">{rule.category}</span>
            </div>
            <div>
              <span className="text-muted-foreground">Status:</span>
              <Badge variant={rule.isActive ? "default" : "secondary"} className="ml-2">
                {rule.isActive ? 'Active' : 'Inactive'}
              </Badge>
            </div>
          </div>
          
          {rule.criteria.keywords && rule.criteria.keywords.length > 0 && (
            <div>
              <h5 className="font-medium text-sm mb-2">Keywords</h5>
              <div className="flex flex-wrap gap-1">
                {rule.criteria.keywords.map((keyword, index) => (
                  <Badge key={index} variant="outline" className="text-xs">
                    {keyword}
                  </Badge>
                ))}
              </div>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
