"use client";

import React, { useState } from 'react';
import { useRouter } from 'next/navigation';
import { Search, Filter, FileText, BarChart3, Clock, AlertTriangle, CheckCircle, XCircle, Eye, Trash2 } from 'lucide-react';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { useToast } from '@/hooks/use-toast';
import { useAnalyses, useDeleteAnalysis } from '@/lib/services/contract-playbooks-service';
import { FeatureGuard } from '@/components/subscription/feature-guard';
import { RIS<PERSON>_LEVELS, ANALYSIS_STATUS } from '@/lib/types/contract-playbooks';
import type { ContractAnalysis, AnalysisSearchParams } from '@/lib/types/contract-playbooks';

export default function AnalysesPage() {
  const router = useRouter();
  const { toast } = useToast();
  const [searchParams, setSearchParams] = useState<AnalysisSearchParams>({
    page: 1,
    limit: 20,
  });

  // API hooks
  const { data: analysesData, isLoading, error } = useAnalyses(searchParams);
  const deleteAnalysis = useDeleteAnalysis();

  const handleSearch = (query: string) => {
    setSearchParams(prev => ({ ...prev, contractId: query || undefined, page: 1 }));
  };

  const handleFilterChange = (key: keyof AnalysisSearchParams, value: string | undefined) => {
    setSearchParams(prev => ({ ...prev, [key]: value, page: 1 }));
  };

  const handleDelete = async (id: string, playbookName: string) => {
    if (!confirm(`Are you sure you want to delete this analysis for "${playbookName}"? This action cannot be undone.`)) {
      return;
    }

    try {
      await deleteAnalysis.mutateAsync(id);
      toast({
        title: "Analysis deleted",
        description: "The analysis has been successfully deleted.",
      });
    } catch (error) {
      toast({
        title: "Error",
        description: "Failed to delete analysis. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getRiskLevelConfig = (riskLevel: string) => {
    return RISK_LEVELS.find(rl => rl.value === riskLevel) || RISK_LEVELS[0];
  };

  const getStatusConfig = (status: string) => {
    return ANALYSIS_STATUS.find(as => as.value === status) || ANALYSIS_STATUS[0];
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'LOW': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'MEDIUM': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'HIGH': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'CRITICAL': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <CheckCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  return (
    <FeatureGuard feature="contract_playbooks" fallback={
      <div className="container mx-auto p-6">
        <div className="text-center py-12">
          <BarChart3 className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
          <h2 className="text-2xl font-semibold mb-2">Contract Analysis Results</h2>
          <p className="text-muted-foreground mb-6">
            Upgrade to PRO to access contract analysis results and insights.
          </p>
          <Button onClick={() => router.push('/subscription')}>
            Upgrade to PRO
          </Button>
        </div>
      </div>
    }>
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-3xl font-bold flex items-center gap-2">
              <BarChart3 className="h-8 w-8" />
              Analysis Results
            </h1>
            <p className="text-muted-foreground mt-1">
              View and manage contract analysis results from your playbooks.
            </p>
          </div>
          <Button onClick={() => router.push('/contract-playbooks')} variant="outline">
            View Playbooks
          </Button>
        </div>

        {/* Filters */}
        <Card>
          <CardContent className="p-4">
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <div className="relative">
                  <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                  <Input
                    placeholder="Search by contract ID..."
                    className="pl-10"
                    value={searchParams.contractId || ''}
                    onChange={(e) => handleSearch(e.target.value)}
                  />
                </div>
              </div>
              <Select
                value={searchParams.riskLevel || 'all'}
                onValueChange={(value) => handleFilterChange('riskLevel', value === 'all' ? undefined : value)}
              >
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Risk Level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Risk Levels</SelectItem>
                  {RISK_LEVELS.map((level) => (
                    <SelectItem key={level.value} value={level.value}>
                      {level.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Select
                value={searchParams.status || 'all'}
                onValueChange={(value) => handleFilterChange('status', value === 'all' ? undefined : value)}
              >
                <SelectTrigger className="w-48">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Statuses</SelectItem>
                  {ANALYSIS_STATUS.map((status) => (
                    <SelectItem key={status.value} value={status.value}>
                      {status.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </CardContent>
        </Card>

        {/* Content */}
        {isLoading ? (
          <div className="space-y-4">
            {[1, 2, 3, 4, 5].map((i) => (
              <Card key={i} className="animate-pulse">
                <CardHeader>
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-3 bg-muted rounded w-1/2"></div>
                </CardHeader>
                <CardContent>
                  <div className="space-y-2">
                    <div className="h-3 bg-muted rounded"></div>
                    <div className="h-3 bg-muted rounded w-2/3"></div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : error ? (
          <Card>
            <CardContent className="p-8 text-center">
              <p className="text-muted-foreground">Failed to load analyses. Please try again.</p>
              <Button 
                variant="outline" 
                className="mt-4"
                onClick={() => window.location.reload()}
              >
                Retry
              </Button>
            </CardContent>
          </Card>
        ) : !analysesData?.analyses.length ? (
          <Card>
            <CardContent className="p-8 text-center">
              <BarChart3 className="h-16 w-16 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-semibold mb-2">No analyses found</h3>
              <p className="text-muted-foreground mb-6">
                {searchParams.contractId || searchParams.riskLevel || searchParams.status
                  ? "No analyses match your current filters."
                  : "No contract analyses have been performed yet."
                }
              </p>
              <Button onClick={() => router.push('/contract-playbooks')}>
                View Playbooks
              </Button>
            </CardContent>
          </Card>
        ) : (
          <>
            {/* Analyses List */}
            <div className="space-y-4">
              {analysesData.analyses.map((analysis: ContractAnalysis) => (
                <AnalysisCard
                  key={analysis.id}
                  analysis={analysis}
                  onView={() => router.push(`/contract-playbooks/analyses/${analysis.id}`)}
                  onDelete={() => handleDelete(analysis.id, analysis.playbookName)}
                  getRiskLevelConfig={getRiskLevelConfig}
                  getStatusConfig={getStatusConfig}
                  getRiskIcon={getRiskIcon}
                />
              ))}
            </div>

            {/* Pagination */}
            {analysesData.totalPages > 1 && (
              <div className="flex justify-center gap-2">
                <Button
                  variant="outline"
                  disabled={searchParams.page === 1}
                  onClick={() => setSearchParams(prev => ({ ...prev, page: (prev.page || 1) - 1 }))}
                >
                  Previous
                </Button>
                <span className="flex items-center px-4">
                  Page {searchParams.page} of {analysesData.totalPages}
                </span>
                <Button
                  variant="outline"
                  disabled={searchParams.page === analysesData.totalPages}
                  onClick={() => setSearchParams(prev => ({ ...prev, page: (prev.page || 1) + 1 }))}
                >
                  Next
                </Button>
              </div>
            )}
          </>
        )}
      </div>
    </FeatureGuard>
  );
}

interface AnalysisCardProps {
  analysis: ContractAnalysis;
  onView: () => void;
  onDelete: () => void;
  getRiskLevelConfig: (riskLevel: string) => { value: string; label: string; color: string };
  getStatusConfig: (status: string) => { value: string; label: string; color: string };
  getRiskIcon: (riskLevel: string) => React.ReactNode;
}

function AnalysisCard({ 
  analysis, 
  onView, 
  onDelete,
  getRiskLevelConfig,
  getStatusConfig,
  getRiskIcon 
}: AnalysisCardProps) {
  const riskConfig = getRiskLevelConfig(analysis.riskLevel);
  const statusConfig = getStatusConfig(analysis.status);

  return (
    <Card className="hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex-1">
            <CardTitle className="text-lg flex items-center gap-2">
              <FileText className="h-5 w-5" />
              Contract Analysis
            </CardTitle>
            <CardDescription>
              Playbook: {analysis.playbookName} • Contract: {analysis.contractId}
            </CardDescription>
          </div>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="ghost" size="sm">
                <Eye className="h-4 w-4" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align="end">
              <DropdownMenuItem onClick={onView}>
                <Eye className="h-4 w-4 mr-2" />
                View Details
              </DropdownMenuItem>
              <DropdownMenuItem onClick={onDelete} className="text-destructive">
                <Trash2 className="h-4 w-4 mr-2" />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="space-y-4">
          {/* Status and Risk */}
          <div className="flex items-center gap-4 flex-wrap">
            <div className="flex items-center gap-2">
              <Badge className={statusConfig.color}>
                {statusConfig.label}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              {getRiskIcon(analysis.riskLevel)}
              <Badge className={riskConfig.color}>
                {riskConfig.label}
              </Badge>
            </div>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Score:</span>
              <span className="font-medium">{analysis.overallScore}/100</span>
            </div>
          </div>

          {/* Summary Stats */}
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="text-muted-foreground">Total Rules:</span>
              <div className="font-medium">{analysis.summary.totalRules}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Passed:</span>
              <div className="font-medium text-green-600">{analysis.summary.passedRules}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Failed:</span>
              <div className="font-medium text-red-600">{analysis.summary.failedRules}</div>
            </div>
            <div>
              <span className="text-muted-foreground">Critical Issues:</span>
              <div className="font-medium text-red-600">{analysis.summary.criticalIssues}</div>
            </div>
          </div>

          {/* Deviations Preview */}
          {analysis.deviations.length > 0 && (
            <div>
              <h4 className="font-medium text-sm mb-2">Key Issues ({analysis.deviations.length})</h4>
              <div className="space-y-1">
                {analysis.deviations.slice(0, 3).map((deviation, index) => (
                  <div key={index} className="flex items-center gap-2 text-sm">
                    {getRiskIcon(deviation.severity)}
                    <span className="text-muted-foreground">{deviation.ruleName}</span>
                  </div>
                ))}
                {analysis.deviations.length > 3 && (
                  <div className="text-sm text-muted-foreground">
                    +{analysis.deviations.length - 3} more issues
                  </div>
                )}
              </div>
            </div>
          )}

          {/* Footer */}
          <div className="flex justify-between items-center text-sm text-muted-foreground pt-2 border-t">
            <span>Analyzed {new Date(analysis.analyzedAt).toLocaleDateString()}</span>
            <Button variant="ghost" size="sm" onClick={onView}>
              View Details →
            </Button>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
