"use client";

import { useState } from "react";
import {
	courtListenerService,
	type SearchParams,
} from "@/lib/services/court-listener-service";
import { CaseDetail, type CaseData } from "../../../components/case-search/case-detail";
import { CaseSearchInterface } from "../../../components/case-search/case-search-interface";
import { CaseSearchResults } from "../../../components/case-search/case-search-result";

interface ApiOpinion {
	author_id: number | null;
	cites: number[];
	download_url: string | null;
	id: number;
	joined_by_ids: number[];
	local_path: string | null;
	meta: {
		timestamp: string;
		date_created: string;
	};
	ordering_key: number | null;
	per_curiam: boolean;
	sha1: string;
	snippet: string;
	type: string;
}

interface ApiMeta {
	timestamp: string;
	date_created: string;
	score: { bm25: number };
	query?: string;
}

interface ApiResponse {
	absolute_url: string;
	attorney: string;
	caseName: string;
	caseNameFull: string;
	citation: string[];
	citeCount: number;
	cluster_id: number;
	court: string;
	court_citation_string: string;
	court_id: string;
	dateFiled: string;
	dateArgued?: string | null;
	dateReargued?: string | null;
	dateTerminated?: string | null;
	docket_id: number;
	id?: number;
	judge?: string;
	jurisdiction?: string;
	opinions?: ApiOpinion[];
	status?: string;
	meta: ApiMeta;
	[key: string]: unknown;
}

// Transform API response into our CaseData format
// Updated transformation function
function transformToCaseData(result: ApiResponse): CaseData {
	return {
		docket_id: result.docket_id.toString(),
		caseName: result.caseName,
		caseNameFull: result.caseNameFull,
		dateFiled: result.dateFiled,
		court: result.court,
		absolute_url: result.absolute_url,
		cluster_id: result.cluster_id?.toString(),
		status: result.status,
		citation: result.citation,
		citeCount: result.citeCount,
		attorney: result.attorney,
		// Handle null values for dates
		dateArgued: result.dateArgued || undefined,
		dateReargued: result.dateReargued || undefined,
		dateTerminated: result.dateTerminated || undefined,
		judge: result.judge,
		// Map opinions to expected format
		opinions: result.opinions?.map(opinion => ({
			type: opinion.type as "lead-opinion" | "concurrence-opinion" | "dissent",
			author_id: opinion.author_id?.toString(),
			per_curiam: opinion.per_curiam,
			snippet: opinion.snippet
		}))
	};
}


export default function CaseSearchPage() {
	const [searchResults, setSearchResults] = useState<CaseData[]>([]);
	const [isSearching, setIsSearching] = useState(false);
	const [error, setError] = useState<string | null>(null);
	const [selectedCase, setSelectedCase] = useState<CaseData | null>(null);
	const [totalResults, setTotalResults] = useState<number>(0);
	const [lastQuery, setLastQuery] = useState<string>("");

	// Add pagination state
	const [currentPage, setCurrentPage] = useState<number>(1);
	const [totalPages, setTotalPages] = useState<number>(1);
	const [hasNextPage, setHasNextPage] = useState<boolean>(false);
	const [hasPreviousPage, setHasPreviousPage] = useState<boolean>(false);
	const [pageSize, setPageSize] = useState<number>(10);

	const handleSearch = async (params: SearchParams) => {
		try {
			setIsSearching(true);
			setError(null);
			setSelectedCase(null);

			// Reset pagination when performing a new search
			setCurrentPage(1);

			// Include pagination parameters
			const searchParams = {
				...params,
				page: 1,
				page_size: pageSize,
			};

			setLastQuery(params.query || ""); // Store the query
			const response = await courtListenerService.searchCases(searchParams);

			if (response.status === "success") {
				const results = response.data.results as ApiResponse[];
				const transformedResults = results.map(transformToCaseData);
				setSearchResults(transformedResults);
				setTotalResults(response.data.count);

				// Calculate total pages
				const calculatedTotalPages = Math.ceil(response.data.count / pageSize);
				setTotalPages(calculatedTotalPages);

				// Set pagination flags
				setHasNextPage(response.data.next !== null);
				setHasPreviousPage(response.data.previous !== null);

				if (response.data.results.length === 0) {
					setError("No results found for your search criteria");
				}
			} else {
				setError("Failed to search cases");
			}
		} catch (error) {
			console.error("Error searching cases:", error);
			setError("An error occurred while searching. Please try again.");
		} finally {
			setIsSearching(false);
		}
	};

	const handlePageChange = async (newPage: number) => {
		if (
			newPage < 1 ||
			newPage > totalPages ||
			newPage === currentPage ||
			isSearching
		) {
			return;
		}

		try {
			setIsSearching(true);

			// Get the current search parameters and update the page
			const searchParams: SearchParams = {
				query: lastQuery,
				page: newPage,
				page_size: pageSize,
			};

			const response = await courtListenerService.searchCases(searchParams);

			if (response.status === "success") {
				const results = response.data.results as ApiResponse[];
				const transformedResults = results.map(transformToCaseData);
				setSearchResults(transformedResults);
				setCurrentPage(newPage);
				setHasNextPage(response.data.next !== null);
				setHasPreviousPage(response.data.previous !== null);
			} else {
				setError("Failed to load page");
			}
		} catch (error) {
			console.error("Error changing page:", error);
			setError("An error occurred while loading the page. Please try again.");
		} finally {
			setIsSearching(false);
		}
	};

	const handlePageSizeChange = (newSize: number) => {
		setPageSize(newSize);
		// Re-run the search with the new page size if we have results
		if (searchResults.length > 0) {
			handleSearch({
				query: lastQuery,
				page_size: newSize,
			});
		}
	};

	const handleCaseSelect = (caseData: CaseData) => {
		setSelectedCase(caseData);
	};

	const handleBackToResults = () => {
		setSelectedCase(null);
	};

	return (
		<div className="min-h-screen bg-background dark:bg-[#1a1a1a]">
			<div className="container mx-auto py-8 px-4">
				<h1 className="text-3xl font-bold mb-8 text-foreground">
					Legal Case Search
				</h1>

				{selectedCase ? (
					<CaseDetail caseData={selectedCase} onBack={handleBackToResults} />
				) : (
					<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
						<div className="lg:col-span-1">
							<CaseSearchInterface
								onSearch={handleSearch}
								isSearching={isSearching}
							/>
						</div>
						<div className="lg:col-span-2">
							<CaseSearchResults
								results={searchResults}
								isLoading={isSearching}
								error={error}
								onSelectCase={handleCaseSelect}
								totalResults={totalResults}
								currentPage={currentPage}
								totalPages={totalPages}
								hasNextPage={hasNextPage}
								hasPreviousPage={hasPreviousPage}
								pageSize={pageSize}
								onPageChange={handlePageChange}
								onPageSizeChange={handlePageSizeChange}
							/>
						</div>
					</div>
				)}
			</div>
		</div>
	);
}
