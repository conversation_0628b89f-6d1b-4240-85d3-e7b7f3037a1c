"use client"

import { useState, useEffect, useCallback } from "react"
import { useToast } from "@/hooks/use-toast"
import { AnalyticsDashboard } from "@/components/analytics/analytics-dashboard"
import { DocumentAnalyticsComponent } from "@/components/analytics/document-analytics"
import { UserAnalyticsComponent } from "@/components/analytics/user-analytics"
import { CitationAnalyticsComponent } from "@/components/analytics/citation-analytics"
import {
  analyticsService,
  type AnalyticsParams,
  type OrganizationAnalytics,
  type DocumentAnalytics,
  type UserAnalytics,
  type CitationAnalytics,
} from "@/lib/services/analytics-service"
import { DateRangePicker } from "@/components/analytics/date-range-picker"
import type { DateRange } from "react-day-picker"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Loader2 } from "lucide-react"
import { format, subDays, differenceInDays } from "date-fns"

export default function AnalyticsPage() {
  const { toast } = useToast()
  const [dashboardAnalytics, setDashboardAnalytics] = useState<OrganizationAnalytics | null>(null)
  const [previousPeriodAnalytics, setPreviousPeriodAnalytics] = useState<OrganizationAnalytics | null>(null)
  const [documentAnalytics, setDocumentAnalytics] = useState<DocumentAnalytics | null>(null)
  const [userAnalytics, setUserAnalytics] = useState<UserAnalytics | null>(null)
  const [citationAnalytics, setCitationAnalytics] = useState<CitationAnalytics | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [activeTab, setActiveTab] = useState("overview")
  const [granularity, setGranularity] = useState<"day" | "week" | "month">("day")
  const [dateRange, setDateRange] = useState<DateRange | undefined>(() => ({
    from: subDays(new Date(), 30),
    to: new Date(),
  }))

  const fetchAnalytics = useCallback(async () => {
    if (!dateRange?.from || !dateRange?.to) return;
    
    try {
      setIsLoading(true)

      const params: AnalyticsParams = {
        startDate: format(dateRange.from, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        endDate: format(dateRange.to, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        granularity,
        includeTimeSeriesData: true,
        includeCitationMetrics: true,
        includeTopicAnalysis: true,
      }

      const durationInDays = differenceInDays(dateRange.to, dateRange.from)
      const previousPeriodEnd = subDays(dateRange.from, 1)
      const previousPeriodStart = subDays(previousPeriodEnd, durationInDays)

      const previousPeriodParams: AnalyticsParams = {
        startDate: format(previousPeriodStart, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        endDate: format(previousPeriodEnd, "yyyy-MM-dd'T'HH:mm:ss.SSS'Z'"),
        granularity,
        includeTimeSeriesData: false,
        includeCitationMetrics: true,
        includeTopicAnalysis: false,
      }

      const [dashboardData, previousPeriodData, documentData, userData, citationData] = await Promise.all([
        analyticsService.getDashboardAnalytics(params),
        analyticsService.getDashboardAnalytics(previousPeriodParams),
        analyticsService.getDocumentAnalytics(params),
        analyticsService.getUserAnalytics(params),
        analyticsService.getCitationAnalytics(params),
      ])

      setDashboardAnalytics(dashboardData)
      setPreviousPeriodAnalytics(previousPeriodData)
      setDocumentAnalytics(documentData)
      setUserAnalytics(userData)
      setCitationAnalytics(citationData)
    } catch (error) {
      console.error("Failed to fetch analytics:", error)
      toast({
        title: "Error",
        description: "Failed to load analytics data. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }, [dateRange, granularity, toast])

  useEffect(() => {
    void fetchAnalytics()
  }, [fetchAnalytics])

  const handleDateRangeChange = (range: DateRange | undefined) => {
    setDateRange(range)
  }

  const handleGranularityChange = (value: string) => {
    setGranularity(value as "day" | "week" | "month")
  }

  return (
    <div className="min-h-screen bg-background dark:bg-[#1a1a1a]">
      <div className="container mx-auto py-8 px-4">
        <div className="flex flex-col md:flex-row justify-between items-start md:items-center mb-8 gap-4">
          <div>
            <h1 className="text-3xl font-bold text-foreground">Analytics Dashboard</h1>
            <p className="text-muted-foreground mt-1">Insights and metrics for your legal document analysis</p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4">
            <DateRangePicker value={dateRange} onChange={handleDateRangeChange} />

            <Select value={granularity} onValueChange={handleGranularityChange}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Select granularity" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="day">Daily</SelectItem>
                <SelectItem value="week">Weekly</SelectItem>
                <SelectItem value="month">Monthly</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
          <TabsList className="grid grid-cols-4 mb-8">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="documents">Documents</TabsTrigger>
            <TabsTrigger value="users">Users</TabsTrigger>
            <TabsTrigger value="citations">Citations</TabsTrigger>
          </TabsList>

          {isLoading ? (
            <div className="flex justify-center items-center h-64">
              <Loader2 className="h-8 w-8 animate-spin text-primary" />
              <span className="ml-2 text-lg">Loading analytics data...</span>
            </div>
          ) : (
            <>
              <TabsContent value="overview" className="mt-0">
                {dashboardAnalytics ? (
                  <AnalyticsDashboard
                    analytics={dashboardAnalytics}
                    previousPeriodAnalytics={previousPeriodAnalytics}
                  />
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">No overview data available</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="documents" className="mt-0">
                {documentAnalytics ? (
                  <DocumentAnalyticsComponent analytics={documentAnalytics} />
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">No document analytics data available</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="users" className="mt-0">
                {userAnalytics ? (
                  <UserAnalyticsComponent analytics={userAnalytics} />
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">No user analytics data available</p>
                  </div>
                )}
              </TabsContent>

              <TabsContent value="citations" className="mt-0">
                {citationAnalytics ? (
                  <CitationAnalyticsComponent analytics={citationAnalytics} />
                ) : (
                  <div className="text-center py-12">
                    <p className="text-muted-foreground">No citation analytics data available</p>
                  </div>
                )}
              </TabsContent>
            </>
          )}
        </Tabs>

        {dashboardAnalytics && (
          <div className="text-xs text-muted-foreground mt-4 text-right">
            Last updated: {new Date(dashboardAnalytics.lastUpdated).toLocaleString()}
          </div>
        )}
      </div>
    </div>
  )
}
