"use client";

import { useRouter } from "next/navigation";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { XCircle, ArrowLeft, ShoppingCart } from "lucide-react";

export default function CreditPurchaseCancelPage() {
  const router = useRouter();

  return (
    <div className="container mx-auto py-8 max-w-2xl">
      <Card className="text-center">
        <CardHeader className="pb-4">
          <div className="mx-auto mb-4 w-16 h-16 bg-orange-100 rounded-full flex items-center justify-center">
            <XCircle className="w-8 h-8 text-orange-600" />
          </div>
          <CardTitle className="text-2xl text-orange-600">
            Credit Purchase Cancelled
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <p className="text-muted-foreground">
              Your credit purchase was cancelled. No charges have been made to your payment method.
            </p>
            
            <p className="text-sm text-muted-foreground">
              You can try purchasing credits again at any time, or continue using your existing credit balance.
            </p>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button 
              onClick={() => router.push("/subscription")}
              className="flex items-center gap-2"
            >
              <ShoppingCart className="h-4 w-4" />
              Try Again
            </Button>
            
            <Button 
              variant="outline"
              onClick={() => router.push("/chat")}
              className="flex items-center gap-2"
            >
              <ArrowLeft className="h-4 w-4" />
              Continue to Dashboard
            </Button>
          </div>

          <div className="pt-4 border-t">
            <p className="text-sm text-muted-foreground">
              Need help? Contact our support team for assistance with credit purchases.
            </p>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
