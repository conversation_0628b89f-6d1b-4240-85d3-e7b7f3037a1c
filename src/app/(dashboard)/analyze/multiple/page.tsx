"use client";

import { useState } from "react";
import { DocumentComparisonSelector } from "@/components/document-comparison/document-comparison-selector";
import { DocumentComparison } from "@/components/document-comparison/document-comparison";

export default function AnalyzeMultipleDocumentsPage() {
  const [selectedDocuments, setSelectedDocuments] = useState<{
    primaryDocumentId: string;
    relatedDocumentIds: string[];
  } | null>(null);

  const handleMultiDocumentAnalysis = (primaryDocId: string, relatedDocIds: string[]) => {
    setSelectedDocuments({
      primaryDocumentId: primaryDocId,
      relatedDocumentIds: relatedDocIds,
    });
  };

  return (
    <div className="container mx-auto py-8 px-4 md:px-6">
      <h1 className="text-3xl font-bold mb-2">Analyze Multiple Documents</h1>
      <p className="text-muted-foreground mb-6">
        Analyze relationships, similarities, and differences across multiple documents.
      </p>

      <div className="container py-6 max-w-6xl">
        {!selectedDocuments ? (
          <DocumentComparisonSelector
            onCompare={handleMultiDocumentAnalysis}
            onCancel={() => {}}
          />
        ) : (
          <DocumentComparison
            primaryDocumentId={selectedDocuments.primaryDocumentId}
            relatedDocumentIds={selectedDocuments.relatedDocumentIds}
            onBack={() => setSelectedDocuments(null)}
          />
        )}
      </div>
    </div>
  );
}
