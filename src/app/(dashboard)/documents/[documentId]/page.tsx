"use client";

import React, { useState, useEffect } from "react";
import { useParams, useSearchParams } from "next/navigation";
import { useDocumentById } from "@/hooks/use-document-by-id";
import { documentService } from "@/lib/services/document-service";
import { isWebViewable, formatBytes, formatDate } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
	ArrowLeft,
	Download,
	FileText,
	Brain,
	Shield,
	Sparkles,
	BarChart3,
} from "lucide-react";
import Link from "next/link";
import { useToast } from "@/hooks/use-toast";
import { NegotiationPlaybook } from "@/components/negotiation-playbook";
import { PrivilegeLogAutomation } from "@/components/privilege-log";
import {
	ClauseIntelligencePanel,
	RelatedDocumentsGenerator,
} from "@/components/document-automation";
import { ContractAnalysisModal } from "@/components/contract-playbooks";

export default function DocumentDetailPage() {
	const params = useParams();
	const searchParams = useSearchParams();
	const documentId = params.documentId as string;
	const { data: document, isLoading, error } = useDocumentById(documentId);
	const { toast } = useToast();
	const [isDownloading, setIsDownloading] = useState(false);
	const [activeTab, setActiveTab] = useState("document");

	// Handle tab parameter from URL
	useEffect(() => {
		const tab = searchParams.get("tab");
		if (tab === "playbook") {
			setActiveTab("playbook");
		} else if (tab === "privilege") {
			setActiveTab("privilege");
		} else if (tab === "automation") {
			setActiveTab("automation");
		}
	}, [searchParams]);

	// Construct the URL for the server-side proxy route
	const proxyRenderUrl = documentId
		? `/api/documents/${documentId}/render`
		: "";

	// Handle document download with proper authentication (remains client-side)
	const handleDownload = async () => {
		if (!document) return;

		setIsDownloading(true);
		try {
			// Use the existing client-side download method which handles auth
			await documentService.downloadDocument(documentId, document.filename);
		} catch (error) {
			console.error("Error downloading document:", error);
			toast({
				title: "Download Failed",
				description:
					"There was an error downloading the document. Please try again.",
				variant: "destructive",
			});
		} finally {
			setIsDownloading(false);
		}
	};

	// Loading state
	if (isLoading) {
		return (
			<div className="container mx-auto py-8 px-4 md:px-6">
				{/* Skeleton UI */}
				<Skeleton className="h-8 w-1/4 mb-6" />
				<Skeleton className="h-6 w-full mb-2" />
				<Skeleton className="h-6 w-3/4 mb-6" />
				<div className="flex gap-2 mb-6">
					<Skeleton className="h-10 w-32" />
				</div>
				<Skeleton className="h-[70vh] w-full" />
			</div>
		);
	}

	// Error state
	if (error) {
		return (
			<div className="container mx-auto py-8 px-4 md:px-6 flex items-center justify-center min-h-[calc(100vh-theme(spacing.16))]">
				<div className="text-center">
					<h2 className="text-xl font-semibold mb-2">Error Loading Document</h2>
					<p className="text-muted-foreground mb-4">
						Could not load document details. Please try again later.
					</p>
					<Button
						variant="outline"
						className="mt-4"
						onClick={() => window.location.reload()} // Simple reload for now
					>
						Try Again
					</Button>
				</div>
			</div>
		);
	}

	// Document not found state
	if (!document) {
		return (
			<div className="container mx-auto py-8 px-4 md:px-6">
				<div className="flex items-center mb-6">
					<Link href="/documents" className="mr-2">
						<Button variant="outline" size="icon">
							<ArrowLeft className="h-4 w-4" />
						</Button>
					</Link>
					<h1 className="text-2xl font-bold">Document Not Found</h1>
				</div>
				<p>The requested document could not be found.</p>
				<Link href="/documents" className="mt-4 inline-block">
					<Button>Return to Documents</Button>
				</Link>
			</div>
		);
	}

	// Main content
	return (
		<div className="container mx-auto py-8 px-4 md:px-6">
			{/* Header with back button and document title */}
			<div className="flex items-center mb-6">
				<Link href="/documents" className="mr-2">
					<Button variant="outline" size="icon">
						<ArrowLeft className="h-4 w-4" />
					</Button>
				</Link>
				<h1 className="text-2xl font-bold truncate">
					{document.name || document.filename}
				</h1>
			</div>

			{/* Document metadata */}
			<div className="flex flex-wrap gap-x-6 gap-y-2 mb-6 text-sm text-muted-foreground">
				{document.fileType && <p>Type: {document.fileType}</p>}
				{document.size !== undefined && (
					<p>Size: {formatBytes(document.size)}</p>
				)}
				{document.uploadDate && (
					<p>Uploaded: {formatDate(document.uploadDate)}</p>
				)}
				{document.metadata?.pageCount && (
					<p>Pages: {document.metadata.pageCount}</p>
				)}
				{document.metadata?.author && <p>Author: {document.metadata.author}</p>}
			</div>

			{/* Action buttons */}
			<div className="mb-6 flex gap-3">
				<Button
					variant="outline"
					className="gap-2"
					onClick={handleDownload}
					disabled={isDownloading}
				>
					<Download className="h-4 w-4" />
					{isDownloading ? "Downloading..." : "Download Original"}
				</Button>
				<ContractAnalysisModal
					documentId={documentId}
					documentName={document.name || document.filename}
					trigger={
						<Button variant="default" className="gap-2">
							<BarChart3 className="h-4 w-4" />
							Analyze with Playbook
						</Button>
					}
					onAnalysisComplete={() => {
						toast({
							title: "Analysis Started",
							description:
								"Your contract analysis has been initiated. You'll be notified when it's complete.",
						});
					}}
				/>
			</div>

			{/* Tabbed interface */}
			<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
				<TabsList className="grid w-full grid-cols-4">
					<TabsTrigger value="document" className="gap-2">
						<FileText className="h-4 w-4" />
						Document
					</TabsTrigger>
					<TabsTrigger value="automation" className="gap-2">
						<Sparkles className="h-4 w-4" />
						Automation
					</TabsTrigger>
					<TabsTrigger value="playbook" className="gap-2">
						<Brain className="h-4 w-4" />
						Negotiation Playbook
					</TabsTrigger>
					<TabsTrigger value="privilege" className="gap-2">
						<Shield className="h-4 w-4" />
						Privilege Log
					</TabsTrigger>
				</TabsList>

				<TabsContent value="document" className="mt-6">
					{/* Document viewer */}
					{document.fileType && isWebViewable(document.fileType) ? (
						// Use iframe pointed to our server-side proxy route
						<div className="w-full border rounded-md overflow-hidden bg-background">
							<div className="p-2 bg-muted flex items-center justify-between border-b">
								<div className="flex items-center">
									<FileText className="h-4 w-4 mr-2 text-muted-foreground" />
									<span className="text-sm font-medium">
										{document.filename}
									</span>
								</div>
								{/* Optional: Link to open proxy URL in new tab */}
								<a
									href={proxyRenderUrl}
									target="_blank"
									rel="noopener noreferrer"
								>
									<Button variant="ghost" size="sm" className="gap-1">
										<FileText className="h-3 w-3" />
										<span className="text-xs">Open Full</span>
									</Button>
								</a>
							</div>
							{proxyRenderUrl ? (
								<iframe
									src={proxyRenderUrl}
									className="w-full h-[70vh] border-0"
									title={document.name || document.filename}
								/>
							) : (
								<div className="flex items-center justify-center h-[70vh] p-6 text-center">
									<p>Cannot generate preview URL.</p>
								</div>
							)}
						</div>
					) : (
						// Fallback for non-web-viewable types (text preview)
						<div className="prose dark:prose-invert max-w-none border rounded-md p-6 bg-card">
							<div className="mb-6 p-4 bg-muted rounded-md">
								<p className="text-sm">
									This document type{" "}
									{document.fileType ? `(${document.fileType})` : ""} cannot be
									viewed directly in the browser. Please use the download button
									above to view the original file.
								</p>
							</div>
							<h3 className="text-lg font-semibold mb-4">
								Text Content Preview:
							</h3>
							{document.content ? (
								<pre className="whitespace-pre-wrap break-words text-sm bg-muted/50 p-4 rounded-md max-h-[60vh] overflow-y-auto">
									{document.content}
								</pre>
							) : (
								<p className="text-sm text-muted-foreground italic">
									No text content available for preview.
								</p>
							)}
						</div>
					)}
				</TabsContent>

				<TabsContent value="automation" className="mt-6">
					<div className="space-y-6">
						<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
							{/* Clause Intelligence Panel */}
							<ClauseIntelligencePanel
								documentType={document.fileType || "contract"}
								currentContent={document.content || ""}
								onClauseInsert={(clause) => {
									// Could integrate with a document editor here
									console.log("Insert clause:", clause);
								}}
								onSuggestionApply={(suggestion) => {
									// Could integrate with a document editor here
									console.log("Apply suggestion:", suggestion);
								}}
							/>

							{/* Related Documents Generator */}
							<RelatedDocumentsGenerator
								primaryDocumentId={documentId}
								onSuccess={(result) => {
									toast({
										title: "Documents Generated",
										description: `Generated ${result.documents.length} related documents successfully.`,
									});
								}}
								onError={(error) => {
									toast({
										title: "Generation Failed",
										description: error.message,
										variant: "destructive",
									});
								}}
							/>
						</div>
					</div>
				</TabsContent>

				<TabsContent value="playbook" className="mt-6">
					<NegotiationPlaybook documentId={documentId} />
				</TabsContent>

				<TabsContent value="privilege" className="mt-6">
					<PrivilegeLogAutomation documentId={documentId} />
				</TabsContent>
			</Tabs>
		</div>
	);
}
