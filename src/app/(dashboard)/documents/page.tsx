"use client"

import { DocumentsView } from "@/components/documents"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Plus } from "lucide-react"
import Link from "next/link"

export default function AllDocumentsPage() {
  return (
    <div className="px-3 sm:container py-4 sm:py-6 max-w-7xl overflow-hidden">
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center gap-2">
          <h1 className="text-lg sm:text-2xl font-bold text-foreground">Documents</h1>
        </div>
        <Link href="/documents/upload">
          <Button className="flex items-center gap-1 px-3 py-1.5 h-auto">
            <Plus className="h-4 w-4" />
            <span>Upload Document</span>
          </Button>
        </Link>
      </div>
      <DocumentsView />
    </div>
  )
}
