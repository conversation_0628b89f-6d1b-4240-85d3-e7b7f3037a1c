import React from "react";
import { DepositionForm } from "@/components/deposition/deposition-form";
import { depositionService } from "@/lib/services/deposition-service";

interface EditDepositionPageProps {
  params: {
    id: string;
  };
}

export default async function EditDepositionPage({
  params,
}: EditDepositionPageProps) {
  const deposition = await depositionService.getDepositionPreparation(
    params.id
  );

  return (
    <div className="container py-6">
      <DepositionForm initialData={deposition} isEditing={true} />
    </div>
  );
}

export async function generateMetadata({ params }: EditDepositionPageProps) {
  return {
    title: `Edit Deposition | Legal Document Analysis`,
    description: "Edit deposition preparation details",
  };
}
