"use client";

import { ResearchProvider } from "@/lib/legal-research/research-context";
import type React from "react";

export default function LegalResearchLayout({
  children,
}: {
  children: React.ReactNode;
}) {
  return (
    <div className="flex min-h-screen relative">
      {/* Page content without sidebar */}
      <main className="flex-1">
        <div className="w-full h-full">
          <ResearchProvider>
            {children}
          </ResearchProvider>
        </div>
      </main>
    </div>
  );
}
