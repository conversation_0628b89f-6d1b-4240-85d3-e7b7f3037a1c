"use client";

import { useState } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  Bar<PERSON><PERSON>,
  TrendingUp,
  Search,
  CreditCard,
  Users,
  Clock,
  ArrowLeft,
} from "lucide-react";
import { useRouter } from "next/navigation";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { useResearchAnalytics } from "@/hooks/use-legal-research";
import type { AnalyticsParams } from "@/lib/types/legal-research";

export default function LegalResearchAnalyticsPage() {
  const router = useRouter();
  const { subscription } = useSubscription();
  const [period, setPeriod] = useState<AnalyticsParams["period"]>("30d");
  
  const { data: analyticsData, isLoading, error } = useResearchAnalytics({ period });

  // Check if user has access to analytics
  const hasAnalyticsAccess = subscription?.tier === 'lawyer' || subscription?.tier === 'law_firm';

  if (!hasAnalyticsAccess) {
    return (
      <div className="container mx-auto py-8 px-4">
        <div className="max-w-2xl mx-auto text-center space-y-6">
          <div className="space-y-2">
            <h1 className="text-3xl font-bold">Analytics Not Available</h1>
            <p className="text-muted-foreground">
              Research analytics are available for Lawyer and Law Firm subscription tiers.
            </p>
          </div>
          
          <div className="bg-muted/50 rounded-lg p-6 space-y-4">
            <h3 className="font-semibold">Upgrade to access:</h3>
            <ul className="text-sm text-muted-foreground space-y-2">
              <li>• Research usage patterns and trends</li>
              <li>• Practice area and jurisdiction insights</li>
              <li>• Credit consumption analytics</li>
              <li>• Query performance metrics</li>
            </ul>
          </div>

          <div className="flex space-x-4 justify-center">
            <Button onClick={() => router.push("/legal-research")} variant="outline">
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back to Research
            </Button>
            <Button onClick={() => router.push("/subscription")}>
              Upgrade Subscription
            </Button>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto py-8 px-4 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="space-y-1">
          <div className="flex items-center space-x-2">
            <Button
              variant="ghost"
              size="sm"
              onClick={() => router.push("/legal-research")}
            >
              <ArrowLeft className="w-4 h-4 mr-2" />
              Back
            </Button>
          </div>
          <h1 className="text-3xl font-bold flex items-center">
            <BarChart className="w-8 h-8 mr-3" />
            Research Analytics
          </h1>
          <p className="text-muted-foreground">
            Insights into your legal research patterns and usage
          </p>
        </div>

        <div className="flex items-center space-x-2">
          <Select value={period} onValueChange={(value: any) => setPeriod(value)}>
            <SelectTrigger className="w-32">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="7d">Last 7 days</SelectItem>
              <SelectItem value="30d">Last 30 days</SelectItem>
              <SelectItem value="90d">Last 90 days</SelectItem>
              <SelectItem value="1y">Last year</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {error && (
        <Card className="border-destructive">
          <CardContent className="p-6">
            <p className="text-destructive">
              Failed to load analytics data. Please try again later.
            </p>
          </CardContent>
        </Card>
      )}

      {isLoading ? (
        <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardContent className="p-6">
                <div className="animate-pulse space-y-2">
                  <div className="h-4 bg-muted rounded w-3/4"></div>
                  <div className="h-8 bg-muted rounded w-1/2"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : analyticsData?.data ? (
        <div className="space-y-6">
          {/* Summary Cards */}
          <div className="grid gap-6 md:grid-cols-2 lg:grid-cols-4">
            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Total Queries</CardTitle>
                <Search className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analyticsData.data.summary.totalQueries}
                </div>
                <p className="text-xs text-muted-foreground">
                  Across {analyticsData.data.summary.totalSessions} sessions
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Credits Used</CardTitle>
                <CreditCard className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analyticsData.data.summary.totalCreditsUsed}
                </div>
                <p className="text-xs text-muted-foreground">
                  Avg {analyticsData.data.summary.averageCreditsPerQuery.toFixed(1)} per query
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Research Sessions</CardTitle>
                <Users className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {analyticsData.data.summary.totalSessions}
                </div>
                <p className="text-xs text-muted-foreground">
                  Avg {analyticsData.data.summary.averageQueriesPerSession.toFixed(1)} queries each
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
                <CardTitle className="text-sm font-medium">Period</CardTitle>
                <Clock className="h-4 w-4 text-muted-foreground" />
              </CardHeader>
              <CardContent>
                <div className="text-2xl font-bold">
                  {period === "7d" ? "7 Days" : 
                   period === "30d" ? "30 Days" : 
                   period === "90d" ? "90 Days" : "1 Year"}
                </div>
                <p className="text-xs text-muted-foreground">
                  Analysis period
                </p>
              </CardContent>
            </Card>
          </div>

          {/* Detailed Analytics */}
          <Tabs defaultValue="practice-areas" className="space-y-4">
            <TabsList>
              <TabsTrigger value="practice-areas">Practice Areas</TabsTrigger>
              <TabsTrigger value="jurisdictions">Jurisdictions</TabsTrigger>
              <TabsTrigger value="trends">Trends</TabsTrigger>
            </TabsList>

            <TabsContent value="practice-areas" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Top Practice Areas</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.data.topPracticeAreas.map((area, index) => (
                      <div key={area.area} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">#{index + 1}</Badge>
                          <span className="font-medium capitalize">
                            {area.area.replace('_', ' ')}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-muted-foreground">
                            {area.queries} queries
                          </span>
                          <Badge variant="secondary">
                            {area.percentage.toFixed(1)}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="jurisdictions" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Top Jurisdictions</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.data.topJurisdictions.map((jurisdiction, index) => (
                      <div key={jurisdiction.jurisdiction} className="flex items-center justify-between">
                        <div className="flex items-center space-x-2">
                          <Badge variant="outline">#{index + 1}</Badge>
                          <span className="font-medium capitalize">
                            {jurisdiction.jurisdiction.replace('_', ' ')}
                          </span>
                        </div>
                        <div className="flex items-center space-x-2">
                          <span className="text-sm text-muted-foreground">
                            {jurisdiction.queries} queries
                          </span>
                          <Badge variant="secondary">
                            {jurisdiction.percentage.toFixed(1)}%
                          </Badge>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            <TabsContent value="trends" className="space-y-4">
              <Card>
                <CardHeader>
                  <CardTitle>Query Volume Trends</CardTitle>
                </CardHeader>
                <CardContent>
                  <div className="space-y-3">
                    {analyticsData.data.trends.queryVolume.slice(-7).map((trend) => (
                      <div key={trend.date} className="flex items-center justify-between">
                        <span className="text-sm font-medium">
                          {new Date(trend.date).toLocaleDateString()}
                        </span>
                        <div className="flex items-center space-x-4">
                          <span className="text-sm text-muted-foreground">
                            {trend.queries} queries
                          </span>
                          <span className="text-sm text-muted-foreground">
                            {trend.credits} credits
                          </span>
                        </div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </div>
      ) : (
        <Card>
          <CardContent className="p-6 text-center">
            <p className="text-muted-foreground">
              No analytics data available for the selected period.
            </p>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
