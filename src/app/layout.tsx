import { Inter } from "next/font/google"
import "./globals.css";
import type { Metadata } from "next";
import { Providers } from "./providers";
import { Toaster } from "@/components/ui/toaster";
import { ThemeProvider } from "./theme-provider";

export const metadata: Metadata = {
	title: "Docgic | Intelligent Document Analysis",
	description: "Enterprise-grade document analysis and processing powered by AI",
	icons: {
		icon: [
			{ url: "/logos/favicon-16x16.png", sizes: "16x16", type: "image/png" },
			{ url: "/logos/favicon-32x32.png", sizes: "32x32", type: "image/png" },
			{ url: "/logos/favicon.ico", sizes: "any" }
		],
		apple: [
			{ url: "/logos/apple-touch-icon.png" }
		],
		other: [
			{ rel: "manifest", url: "/logos/site.webmanifest" }
		]
	}
};
const inter = Inter({ subsets: ["latin"] })
export default function RootLayout({
	children,
}: {
	children: React.ReactNode;
}) {
	return (
		<html lang="en" suppressHydrationWarning>
			<body className={inter.className}>
				<ThemeProvider attribute="class" defaultTheme="dark" enableSystem>
					<Providers>{children}</Providers>
					<Toaster />
				</ThemeProvider>
			</body>
		</html>
	);
}
