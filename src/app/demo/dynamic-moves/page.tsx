"use client";

import React, { useState } from 'react';
import { <PERSON>, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { ArrowLeft, Lightbulb, Target, TrendingUp } from 'lucide-react';
import { DynamicMoveForm } from '@/components/negotiation-simulator/dynamic-move-form';
import { ScenarioSelection } from '@/components/negotiation-simulator/scenario-selection';
import MoveContextEngine, { SCENARIO_CONFIGS } from '@/lib/negotiation/move-context';

export default function DynamicMovesDemo() {
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [currentRound, setCurrentRound] = useState(1);
  const [previousMoves, setPreviousMoves] = useState<any[]>([]);
  const [negotiationContext, setNegotiationContext] = useState<any>(null);
  const [moveHistory, setMoveHistory] = useState<any[]>([]);

  const handleScenarioSelect = (scenarioId: string) => {
    setSelectedScenario(scenarioId);
    setCurrentRound(1);
    setPreviousMoves([]);
    setMoveHistory([]);
    
    // Create initial context
    const context = MoveContextEngine.determineContext(scenarioId, 1, []);
    setNegotiationContext(context);
  };

  const handleMoveSubmit = (moveData: any) => {
    console.log('Move submitted:', moveData);
    
    // Add to move history
    const newMove = {
      ...moveData,
      round: currentRound,
      timestamp: new Date().toISOString()
    };
    
    setMoveHistory(prev => [...prev, newMove]);
    setPreviousMoves(prev => [...prev, newMove]);
    
    // Update context for next round
    const nextRound = currentRound + 1;
    setCurrentRound(nextRound);
    
    if (selectedScenario) {
      const updatedContext = MoveContextEngine.determineContext(
        selectedScenario, 
        nextRound, 
        [...previousMoves, newMove]
      );
      setNegotiationContext(updatedContext);
    }
  };

  const handleBackToScenarios = () => {
    setSelectedScenario(null);
    setNegotiationContext(null);
    setCurrentRound(1);
    setPreviousMoves([]);
    setMoveHistory([]);
  };

  const getSuggestedStrategies = () => {
    if (!negotiationContext) return [];
    return MoveContextEngine.getSuggestedStrategies(negotiationContext);
  };

  const getContextualTips = () => {
    if (!negotiationContext) return [];
    return MoveContextEngine.getContextualTips(negotiationContext);
  };

  if (!selectedScenario) {
    return (
      <div className="container mx-auto p-6 space-y-6">
        <div className="text-center space-y-2">
          <h1 className="text-3xl font-bold">Dynamic Move Forms Demo</h1>
          <p className="text-muted-foreground">
            Experience how negotiation forms adapt based on scenario type, context, and AI personality
          </p>
        </div>
        
        <ScenarioSelection 
          onSelectScenario={handleScenarioSelect}
          userLevel={5} // Show all scenarios
        />
      </div>
    );
  }

  const scenario = SCENARIO_CONFIGS[selectedScenario];
  const suggestedStrategies = getSuggestedStrategies();
  const contextualTips = getContextualTips();

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-4">
          <Button variant="outline" onClick={handleBackToScenarios} className="gap-2">
            <ArrowLeft className="h-4 w-4" />
            Back to Scenarios
          </Button>
          <div>
            <h1 className="text-2xl font-bold">{scenario?.title}</h1>
            <p className="text-muted-foreground">{scenario?.description}</p>
          </div>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant="outline">Round {currentRound}</Badge>
          <Badge className="capitalize">
            {negotiationContext?.phase?.replace('_', ' ')}
          </Badge>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Dynamic Move Form */}
        <div className="lg:col-span-2">
          {negotiationContext && (
            <DynamicMoveForm
              context={negotiationContext}
              onSubmit={handleMoveSubmit}
              disabled={false}
              loading={false}
            />
          )}
        </div>

        {/* Context & Tips Sidebar */}
        <div className="space-y-6">
          {/* Context Info */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Context
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Type:</span>
                  <Badge variant="outline" className="capitalize">
                    {negotiationContext?.type?.replace('_', ' ')}
                  </Badge>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Phase:</span>
                  <Badge variant="outline" className="capitalize">
                    {negotiationContext?.phase?.replace('_', ' ')}
                  </Badge>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Complexity:</span>
                  <Badge variant="outline" className="capitalize">
                    {negotiationContext?.complexity}
                  </Badge>
                </div>
                <div className="flex justify-between text-sm">
                  <span>Previous Moves:</span>
                  <span className="font-medium">{negotiationContext?.previousMoves}</span>
                </div>
              </div>

              <Separator />

              <div className="space-y-2">
                <h4 className="font-medium text-sm">AI Personality</h4>
                <div className="space-y-1">
                  {negotiationContext?.aiPersonality?.detailOriented && (
                    <Badge variant="secondary" className="mr-1 mb-1">Detail-Oriented</Badge>
                  )}
                  {negotiationContext?.aiPersonality?.riskAverse && (
                    <Badge variant="secondary" className="mr-1 mb-1">Risk-Averse</Badge>
                  )}
                  {negotiationContext?.aiPersonality?.timeConstrained && (
                    <Badge variant="secondary" className="mr-1 mb-1">Time-Constrained</Badge>
                  )}
                  {negotiationContext?.aiPersonality?.relationshipFocused && (
                    <Badge variant="secondary" className="mr-1 mb-1">Relationship-Focused</Badge>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Strategy Suggestions */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <TrendingUp className="h-5 w-5" />
                Strategy Suggestions
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              {suggestedStrategies.slice(0, 3).map((strategy, index) => (
                <div key={strategy.strategy} className="space-y-2">
                  <div className="flex items-center justify-between">
                    <span className="font-medium text-sm">{strategy.label}</span>
                    <div className="flex items-center gap-1">
                      <div className="w-12 bg-gray-200 rounded-full h-2">
                        <div 
                          className="bg-green-500 h-2 rounded-full" 
                          style={{ width: `${strategy.effectiveness * 100}%` }}
                        />
                      </div>
                      <span className="text-xs text-muted-foreground">
                        {Math.round(strategy.effectiveness * 100)}%
                      </span>
                    </div>
                  </div>
                  <p className="text-xs text-muted-foreground">{strategy.description}</p>
                  {index < 2 && <Separator />}
                </div>
              ))}
            </CardContent>
          </Card>

          {/* Contextual Tips */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Lightbulb className="h-5 w-5" />
                Tips for This Move
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ul className="space-y-2">
                {contextualTips.map((tip, index) => (
                  <li key={index} className="text-sm flex items-start gap-2">
                    <span className="text-blue-500 mt-1">•</span>
                    <span>{tip}</span>
                  </li>
                ))}
              </ul>
            </CardContent>
          </Card>

          {/* Move History */}
          {moveHistory.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle>Move History</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                {moveHistory.slice(-3).map((move, index) => (
                  <div key={index} className="space-y-2">
                    <div className="flex items-center justify-between">
                      <span className="font-medium text-sm">Round {move.round}</span>
                      <Badge variant="outline" className="capitalize">
                        {move.strategy?.replace('_', ' ')?.toLowerCase()}
                      </Badge>
                    </div>
                    <p className="text-xs text-muted-foreground line-clamp-2">
                      {move.message}
                    </p>
                    {move.terms && Object.keys(move.terms).length > 0 && (
                      <div className="text-xs text-muted-foreground">
                        Terms: {Object.keys(move.terms).length} items
                      </div>
                    )}
                    {index < Math.min(moveHistory.length, 3) - 1 && <Separator />}
                  </div>
                ))}
              </CardContent>
            </Card>
          )}
        </div>
      </div>
    </div>
  );
}
