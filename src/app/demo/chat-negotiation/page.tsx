"use client";

import React, { useState } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { NewUserGuide, useNewUserGuide } from '@/components/navigation/new-user-guide';
import { 
  ArrowLeft, 
  MessageSquare, 
  Bot, 
  Zap, 
  Target,
  Users,
  TrendingUp,
  Clock,
  Star
} from 'lucide-react';
import { ChatNegotiation } from '@/components/negotiation-simulator/chat-negotiation';

const DEMO_SCENARIOS = [
  {
    id: 'software_licensing',
    title: 'Software Licensing Deal',
    description: 'Negotiate enterprise software licensing terms',
    aiCharacter: {
      name: '<PERSON>',
      title: 'VP of Sales at TechCorp',
      personality: ['analytical', 'results-driven', 'collaborative'],
      avatar: '/avatars/sarah.jpg'
    },
    context: 'Your company needs enterprise software for 500 users. Budget is flexible but you need good support.',
    difficulty: 'Intermediate',
    estimatedTime: '10-15 minutes'
  },
  {
    id: 'salary',
    title: 'Salary Negotiation',
    description: 'Negotiate your compensation package',
    aiCharacter: {
      name: '<PERSON>',
      title: 'Head of Engineering',
      personality: ['direct', 'fair', 'growth-focused'],
      avatar: '/avatars/marcus.jpg'
    },
    context: 'Senior Software Engineer role at a growing startup. They love your background.',
    difficulty: 'Beginner',
    estimatedTime: '8-12 minutes'
  },
  {
    id: 'real_estate',
    title: 'Property Purchase',
    description: 'Buy your dream home',
    aiCharacter: {
      name: 'Jennifer Walsh',
      title: 'Real Estate Agent',
      personality: ['experienced', 'market-savvy', 'relationship-focused'],
      avatar: '/avatars/jennifer.jpg'
    },
    context: 'Perfect family home in a competitive market. Multiple offers expected.',
    difficulty: 'Advanced',
    estimatedTime: '15-20 minutes'
  },
  {
    id: 'service_contract',
    title: 'Consulting Agreement',
    description: 'Negotiate terms for professional services',
    aiCharacter: {
      name: 'David Kim',
      title: 'Business Development Director',
      personality: ['professional', 'detail-oriented', 'partnership-minded'],
      avatar: '/avatars/david.jpg'
    },
    context: 'They need help with digital transformation. 6-month project, good relationship potential.',
    difficulty: 'Intermediate',
    estimatedTime: '12-18 minutes'
  }
];

export default function ChatNegotiationDemo() {
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [completedNegotiations, setCompletedNegotiations] = useState<string[]>([]);
  const [useBackend, setUseBackend] = useState(false);
  const { NewUserGuideComponent } = useNewUserGuide();

  const handleScenarioSelect = (scenarioId: string) => {
    setSelectedScenario(scenarioId);
  };

  const handleNegotiationComplete = (result: any) => {
    console.log('Negotiation completed:', result);
    setCompletedNegotiations(prev => [...prev, selectedScenario!]);
    setSelectedScenario(null);
  };

  const handleBackToScenarios = () => {
    setSelectedScenario(null);
  };

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty) {
      case 'Beginner': return 'bg-green-100 text-green-800';
      case 'Intermediate': return 'bg-yellow-100 text-yellow-800';
      case 'Advanced': return 'bg-red-100 text-red-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (selectedScenario) {
    const scenario = DEMO_SCENARIOS.find(s => s.id === selectedScenario);
    if (!scenario) return null;

    return (
      <div className="container mx-auto p-6 space-y-6">
        {/* Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-4">
            <Button variant="outline" onClick={handleBackToScenarios} className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back to Scenarios
            </Button>
            <div>
              <h1 className="text-2xl font-bold">{scenario.title}</h1>
              <p className="text-muted-foreground">{scenario.context}</p>
            </div>
          </div>
          <div className="flex items-center gap-2">
            <Badge className={getDifficultyColor(scenario.difficulty)}>
              {scenario.difficulty}
            </Badge>
            <Button
              variant="outline"
              size="sm"
              onClick={(e) => {
                e.stopPropagation();
                setUseBackend(!useBackend);
              }}
              className={useBackend ? 'bg-green-50 border-green-300' : ''}
            >
              {useBackend ? 'Backend: ON' : 'Backend: OFF'}
            </Button>
          </div>
        </div>

        {/* Chat Interface */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6">
          <div className="lg:col-span-3">
            <ChatNegotiation
              scenarioId={scenario.id}
              aiCharacter={scenario.aiCharacter}
              onComplete={handleNegotiationComplete}
              useBackend={useBackend}
            />
          </div>

          {/* Sidebar with tips and context */}
          <div className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Your Goal
                </CardTitle>
              </CardHeader>
              <CardContent className="text-sm">
                <p>{scenario.context}</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Bot className="h-5 w-5" />
                  AI Character
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex items-center gap-3">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                    {scenario.aiCharacter.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <div className="font-medium">{scenario.aiCharacter.name}</div>
                    <div className="text-sm text-muted-foreground">{scenario.aiCharacter.title}</div>
                  </div>
                </div>
                <div className="space-y-1">
                  <p className="text-xs text-muted-foreground">Personality traits:</p>
                  <div className="flex flex-wrap gap-1">
                    {scenario.aiCharacter.personality.map(trait => (
                      <Badge key={trait} variant="secondary" className="text-xs">
                        {trait}
                      </Badge>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Zap className="h-5 w-5" />
                  Chat Tips
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="space-y-1">
                  <p className="font-medium">💬 Natural Conversation</p>
                  <p className="text-muted-foreground">Type naturally - the AI extracts key terms automatically</p>
                </div>
                <Separator />
                <div className="space-y-1">
                  <p className="font-medium">💡 Use Suggestions</p>
                  <p className="text-muted-foreground">Click suggested responses or type your own</p>
                </div>
                <Separator />
                <div className="space-y-1">
                  <p className="font-medium">📊 Watch Metrics</p>
                  <p className="text-muted-foreground">Trust, respect, and pressure levels change based on your approach</p>
                </div>
                <Separator />
                <div className="space-y-1">
                  <p className="font-medium">🎯 Be Specific</p>
                  <p className="text-muted-foreground">Mention numbers, dates, and specific terms when relevant</p>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Session Info
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span>Estimated time:</span>
                  <span className="font-medium">{scenario.estimatedTime}</span>
                </div>
                <div className="flex justify-between">
                  <span>Difficulty:</span>
                  <Badge className={getDifficultyColor(scenario.difficulty)} variant="outline">
                    {scenario.difficulty}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span>XP Reward:</span>
                  <span className="font-medium flex items-center gap-1">
                    <Zap className="h-3 w-3" />
                    {scenario.difficulty === 'Beginner' ? '50' : scenario.difficulty === 'Intermediate' ? '100' : '200'}
                  </span>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <div className="flex items-center justify-center gap-2">
          <MessageSquare className="h-8 w-8 text-blue-600" />
          <h1 className="text-3xl font-bold">Interactive Chat Negotiations</h1>
        </div>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Experience natural, conversational negotiations with AI characters.
          Type naturally and watch as the system intelligently extracts key terms and tracks relationship dynamics.
        </p>
      </div>

      {/* Backend Status */}
      <Card className={`${useBackend ? 'bg-gradient-to-r from-green-50 to-blue-50 dark:from-green-950/20 dark:to-blue-950/20' : 'bg-gradient-to-r from-yellow-50 to-orange-50 dark:from-yellow-950/20 dark:to-orange-950/20'}`}>
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className={`w-3 h-3 rounded-full ${useBackend ? 'bg-green-500' : 'bg-yellow-500'}`} />
              <div>
                <h3 className="font-semibold">
                  {useBackend ? 'Backend Integration: ENABLED' : 'Demo Mode: MOCK DATA'}
                </h3>
                <p className="text-sm text-muted-foreground">
                  {useBackend
                    ? 'Using real API endpoints for negotiation processing and AI responses'
                    : 'Using client-side simulation for demonstration purposes'
                  }
                </p>
              </div>
            </div>
            <Button
              variant="outline"
              onClick={() => setUseBackend(!useBackend)}
              className={useBackend ? 'bg-green-50 border-green-300 dark:bg-green-950/20 dark:border-green-700' : 'bg-yellow-50 border-yellow-300 dark:bg-yellow-950/20 dark:border-yellow-700'}
            >
              Switch to {useBackend ? 'Demo' : 'Backend'} Mode
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Features */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20">
        <CardContent className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6">
            <div className="text-center">
              <div className="w-12 h-12 bg-blue-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <MessageSquare className="h-6 w-6 text-blue-600" />
              </div>
              <h3 className="font-semibold mb-1">Natural Chat</h3>
              <p className="text-sm text-muted-foreground">Type like you're texting a colleague</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Zap className="h-6 w-6 text-green-600" />
              </div>
              <h3 className="font-semibold mb-1">Smart Extraction</h3>
              <p className="text-sm text-muted-foreground">AI automatically captures key terms</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-purple-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <TrendingUp className="h-6 w-6 text-purple-600" />
              </div>
              <h3 className="font-semibold mb-1">Live Metrics</h3>
              <p className="text-sm text-muted-foreground">Real-time relationship tracking</p>
            </div>
            <div className="text-center">
              <div className="w-12 h-12 bg-yellow-100 rounded-full flex items-center justify-center mx-auto mb-3">
                <Users className="h-6 w-6 text-yellow-600" />
              </div>
              <h3 className="font-semibold mb-1">AI Personalities</h3>
              <p className="text-sm text-muted-foreground">Each character has unique traits</p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Scenarios */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {DEMO_SCENARIOS.map((scenario) => (
          <Card
            key={scenario.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              completedNegotiations.includes(scenario.id) ? 'ring-2 ring-green-500' : ''
            }`}
            onClick={() => handleScenarioSelect(scenario.id)}
          >
            <CardHeader>
              <div className="flex items-start justify-between">
                <div className="space-y-2">
                  <CardTitle className="flex items-center gap-2">
                    {scenario.title}
                    {completedNegotiations.includes(scenario.id) && (
                      <Star className="h-4 w-4 text-yellow-500 fill-current" />
                    )}
                  </CardTitle>
                  <p className="text-sm text-muted-foreground">{scenario.description}</p>
                </div>
                <Badge className={getDifficultyColor(scenario.difficulty)}>
                  {scenario.difficulty}
                </Badge>
              </div>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold text-sm">
                  {scenario.aiCharacter.name.split(' ').map(n => n[0]).join('')}
                </div>
                <div>
                  <div className="font-medium text-sm">{scenario.aiCharacter.name}</div>
                  <div className="text-xs text-muted-foreground">{scenario.aiCharacter.title}</div>
                </div>
              </div>

              <p className="text-sm text-muted-foreground">{scenario.context}</p>

              <div className="flex items-center justify-between text-xs text-muted-foreground">
                <span className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {scenario.estimatedTime}
                </span>
                <span className="flex items-center gap-1">
                  <Zap className="h-3 w-3" />
                  +{scenario.difficulty === 'Beginner' ? '50' : scenario.difficulty === 'Intermediate' ? '100' : '200'} XP
                </span>
              </div>

              <Button className="w-full" variant="outline">
                {completedNegotiations.includes(scenario.id) ? 'Play Again' : 'Start Negotiation'}
              </Button>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* Tips */}
      <Card className="bg-yellow-50 border-yellow-200 dark:bg-yellow-950/20 dark:border-yellow-800">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <MessageSquare className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">Chat Negotiation Tips</h4>
              <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                <li>• <strong>Be conversational:</strong> "I'm thinking around $50k for this" works better than formal language</li>
                <li>• <strong>Ask questions:</strong> "What's most important to you?" builds rapport</li>
                <li>• <strong>Use suggestions:</strong> Click the suggested responses or type your own</li>
                <li>• <strong>Watch the metrics:</strong> Trust and respect levels change based on your approach</li>
                <li>• <strong>Be specific:</strong> Mention actual numbers, dates, and terms when relevant</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* New User Guide */}
      <NewUserGuideComponent />
    </div>
  );
}
