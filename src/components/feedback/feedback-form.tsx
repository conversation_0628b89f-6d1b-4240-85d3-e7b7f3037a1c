"use client"

import { useState } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Switch } from "@/components/ui/switch"
import { FeedbackService } from "@/lib/services/feedback-service"
import {
  CreateFeedbackRequest,
  FeedbackSource,
  FeedbackType,
  FeedbackContextData
} from "@/lib/types/user-feedback"
import { useToast } from "@/hooks/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { ThumbsUp, Thum<PERSON>Down, MessageSquare, Lightbulb } from "lucide-react"

const feedbackFormSchema = z.object({
  content: z.string().min(1, "Feedback content is required"),
  type: z.enum(["thumbs_up", "thumbs_down", "correction", "suggestion", "general"]),
  isAnonymous: z.boolean().default(false),
  rating: z.number().min(1).max(5).optional(),
})

type FeedbackFormValues = z.infer<typeof feedbackFormSchema>

interface FeedbackFormProps {
  sourceId: string
  source: FeedbackSource
  contextData?: FeedbackContextData
  open: boolean
  onOpenChange: (open: boolean) => void
  onFeedbackSubmitted?: () => void
  initialType?: FeedbackType
}

export function FeedbackForm({
  sourceId,
  source,
  contextData = {},
  open,
  onOpenChange,
  onFeedbackSubmitted,
  initialType = "general"
}: FeedbackFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const form = useForm<FeedbackFormValues>({
    resolver: zodResolver(feedbackFormSchema),
    defaultValues: {
      content: "",
      type: initialType,
      isAnonymous: false,
    },
  })

  const onSubmit = async (values: FeedbackFormValues) => {
    setIsSubmitting(true)
    
    try {
      const feedbackData: CreateFeedbackRequest = {
        content: values.content,
        type: values.type,
        source,
        contextData,
        sourceId,
        rating: values.rating,
        isAnonymous: values.isAnonymous
      }
      
      await FeedbackService.createFeedback(feedbackData)
      
      toast({
        title: "Feedback submitted",
        description: "Thank you for your feedback!",
        variant: "default",
      })
      
      form.reset()
      onOpenChange(false)
      
      if (onFeedbackSubmitted) {
        onFeedbackSubmitted()
      }
    } catch (error) {
      console.error("Error submitting feedback:", error)
      toast({
        title: "Error",
        description: "Failed to submit feedback. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  const feedbackTypeIcons = {
    thumbs_up: <ThumbsUp className="h-4 w-4" />,
    thumbs_down: <ThumbsDown className="h-4 w-4" />,
    correction: <MessageSquare className="h-4 w-4" />,
    suggestion: <Lightbulb className="h-4 w-4" />,
    general: <MessageSquare className="h-4 w-4" />,
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Submit Feedback</DialogTitle>
          <DialogDescription>
            Your feedback helps us improve our service. Please share your thoughts.
          </DialogDescription>
        </DialogHeader>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="type"
              render={({ field }) => (
                <FormItem className="space-y-3">
                  <FormLabel>Feedback Type</FormLabel>
                  <FormControl>
                    <RadioGroup
                      onValueChange={field.onChange}
                      defaultValue={field.value}
                      className="flex flex-col space-y-1"
                    >
                      <div className="grid grid-cols-2 gap-2">
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="thumbs_up" />
                          </FormControl>
                          <FormLabel className="font-normal flex items-center">
                            {feedbackTypeIcons.thumbs_up}
                            <span className="ml-2">Positive</span>
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="thumbs_down" />
                          </FormControl>
                          <FormLabel className="font-normal flex items-center">
                            {feedbackTypeIcons.thumbs_down}
                            <span className="ml-2">Negative</span>
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="correction" />
                          </FormControl>
                          <FormLabel className="font-normal flex items-center">
                            {feedbackTypeIcons.correction}
                            <span className="ml-2">Correction</span>
                          </FormLabel>
                        </FormItem>
                        <FormItem className="flex items-center space-x-3 space-y-0">
                          <FormControl>
                            <RadioGroupItem value="suggestion" />
                          </FormControl>
                          <FormLabel className="font-normal flex items-center">
                            {feedbackTypeIcons.suggestion}
                            <span className="ml-2">Suggestion</span>
                          </FormLabel>
                        </FormItem>
                      </div>
                    </RadioGroup>
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="content"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Your Feedback</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Please share your thoughts, suggestions, or report any issues..."
                      className="min-h-[100px]"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="isAnonymous"
              render={({ field }) => (
                <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3">
                  <div className="space-y-0.5">
                    <FormLabel>Submit Anonymously</FormLabel>
                    <FormDescription>
                      Your name will not be associated with this feedback
                    </FormDescription>
                  </div>
                  <FormControl>
                    <Switch
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Submitting..." : "Submit Feedback"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
