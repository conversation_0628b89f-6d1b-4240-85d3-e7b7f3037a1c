"use client"

import { useState } from "react"
import { Button } from "@/components/ui/button"
import { MessageSquare } from "lucide-react"
import { cn } from "@/lib/utils"
import { FeedbackSource } from "@/lib/types/user-feedback"
import { FeedbackButton } from "./feedback-button"
import { FeedbackForm } from "./feedback-form"

interface FeedbackTriggerProps {
  sourceId: string;
  source: FeedbackSource;
  contextData?: Record<string, unknown>;
  className?: string;
  variant?: "minimal" | "standard" | "full";
}

export function FeedbackTrigger({ 
  sourceId, 
  source, 
  contextData = {}, 
  className,
  variant = "standard"
}: FeedbackTriggerProps) {
  const [feedbackFormOpen, setFeedbackFormOpen] = useState(false)
  const [selectedFeedback, setSelectedFeedback] = useState<"thumbs_up" | "thumbs_down" | null>(null)

  const handleFeedbackSubmitted = (type: "thumbs_up" | "thumbs_down") => {
    setSelectedFeedback(type)
  }

  return (
    <>
      <div className={cn("flex items-center gap-2", className)}>
        {variant === "minimal" ? (
          <Button
            variant="ghost"
            size="sm"
            className="h-8 px-2 text-muted-foreground hover:text-foreground"
            onClick={() => setFeedbackFormOpen(true)}
          >
            <MessageSquare className="h-4 w-4 mr-1" />
            <span className="text-xs">Feedback</span>
          </Button>
        ) : (
          <>
            <FeedbackButton 
              sourceId={sourceId}
              source={source}
              contextData={contextData}
              onFeedbackSubmitted={handleFeedbackSubmitted}
            />
            
            {variant === "full" && (
              <Button
                variant="ghost"
                size="sm"
                className="h-8 px-2 text-muted-foreground hover:text-foreground"
                onClick={() => setFeedbackFormOpen(true)}
              >
                <MessageSquare className="h-4 w-4 mr-1" />
                <span className="text-xs">Detailed Feedback</span>
              </Button>
            )}
          </>
        )}
      </div>
      
      <FeedbackForm
        sourceId={sourceId}
        source={source}
        contextData={contextData}
        open={feedbackFormOpen}
        onOpenChange={setFeedbackFormOpen}
        initialType={selectedFeedback || "general"}
      />
    </>
  )
}
