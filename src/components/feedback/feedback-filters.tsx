"use client" // Corrected typo

import { useState, useEffect } from "react"
import { zodResolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Form, FormControl, FormField, FormItem, FormLabel } from "@/components/ui/form"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Input } from "@/components/ui/input"; // Fixed unterminated string
import { DateRangePicker } from "@/components/analytics/date-range-picker" // Corrected import name
import {
  FeedbackQueryParams,
  FeedbackCategory,
  FeedbackType, // Added import
  FeedbackStatus, // Added import
  FeedbackSource // Added import
} from "@/lib/types/user-feedback"
import { FeedbackService } from "@/lib/services/feedback-service"
import { Search, X } from "lucide-react"

const filterSchema = z.object({
  type: z.string().optional(),
  status: z.string().optional(),
  source: z.string().optional(),
  categoryId: z.string().optional(),
  startDate: z.date().optional(),
  endDate: z.date().optional(),
  searchText: z.string().optional(),
})

type FilterValues = z.infer<typeof filterSchema>

interface FeedbackFiltersProps {
  onFilterChange: (filters: FeedbackQueryParams) => void
}

export function FeedbackFilters({ onFilterChange }: FeedbackFiltersProps) {
  const [categories, setCategories] = useState<FeedbackCategory[]>([])
  
  const form = useForm<FilterValues>({
    resolver: zodResolver(filterSchema),
    defaultValues: {
      type: "",
      status: "",
      source: "",
      categoryId: "",
      searchText: "",
    },
  })

  useEffect(() => {
    const loadCategories = async () => {
      try {
        const categoriesData = await FeedbackService.getCategories()
        setCategories(categoriesData)
      } catch (error) {
        console.error("Error loading categories:", error)
      }
    }
    
    loadCategories()
  }, [])

  const onSubmit = (values: FilterValues) => {
    const filters: FeedbackQueryParams = {}
    
    if (values.type && values.type !== "") {
      filters.type = values.type as FeedbackType // Use specific type
    }
    
    if (values.status && values.status !== "") {
      filters.status = values.status as FeedbackStatus // Use specific type
    }
    
    if (values.source && values.source !== "") {
      filters.source = values.source as FeedbackSource // Use specific type
    }
    
    if (values.categoryId && values.categoryId !== "") {
      filters.categoryId = values.categoryId
    }
    
    if (values.startDate) {
      filters.startDate = values.startDate.toISOString()
    }
    
    if (values.endDate) {
      filters.endDate = values.endDate.toISOString()
    }
    
    if (values.searchText && values.searchText.trim() !== "") {
      filters.searchText = values.searchText.trim()
    }
    
    onFilterChange(filters)
  }

  const resetFilters = () => {
    form.reset({
      type: "",
      status: "",
      source: "",
      categoryId: "",
      startDate: undefined,
      endDate: undefined,
      searchText: "",
    })
    
    onFilterChange({})
  }

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          <FormField
            control={form.control}
            name="type"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Feedback Type</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="All types" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="all">All types</SelectItem>
                    <SelectItem value="thumbs_up">Positive Feedback</SelectItem>
                    <SelectItem value="thumbs_down">Negative</SelectItem>
                    <SelectItem value="correction">Correction</SelectItem>
                    <SelectItem value="suggestion">Suggestion</SelectItem>
                    <SelectItem value="general">General</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="status"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Status</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="All statuses" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="all">All statuses</SelectItem>
                    <SelectItem value="pending">Pending</SelectItem>
                    <SelectItem value="reviewed">Reviewed</SelectItem>
                    <SelectItem value="implemented">Implemented</SelectItem>
                    <SelectItem value="rejected">Rejected</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="source"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Source</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="All sources" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="all">All sources</SelectItem>
                    <SelectItem value="document_analysis">Document Analysis</SelectItem>
                    <SelectItem value="chat">Chat</SelectItem>
                    <SelectItem value="document_comparison">Document Comparison</SelectItem>
                    <SelectItem value="citation_analysis">Citation Analysis</SelectItem>
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="categoryId"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Category</FormLabel>
                <Select 
                  onValueChange={field.onChange} 
                  defaultValue={field.value}
                >
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="All categories" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="all">All categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.id}>
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </FormItem>
            )}
          />
        </div>
        
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <FormField
            control={form.control}
            name="startDate"
            render={({ field }) => ( // Restored field
              <FormItem className="flex flex-col">
                <FormLabel>Start Date</FormLabel>
                <DateRangePicker // Corrected component usage
                  value={field.value ? { from: field.value, to: undefined } : undefined} // Adapt props if necessary
                  onChange={(range) => field.onChange(range?.from)} // Adapt props if necessary
                  // placeholder="Select start date" // Prop might not exist
                />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="endDate"
            render={({ field }) => ( // Restored field
              <FormItem className="flex flex-col">
                <FormLabel>End Date</FormLabel>
                <DateRangePicker // Corrected component usage
                  value={field.value ? { from: field.value, to: undefined } : undefined} // Adapt props if necessary
                  onChange={(range) => field.onChange(range?.from)} // Adapt props if necessary
                  // placeholder="Select end date" // Prop might not exist
                />
              </FormItem>
            )}
          />
          
          <FormField
            control={form.control}
            name="searchText"
            render={({ field }) => (
              <FormItem className="flex flex-col">
                <FormLabel>Search</FormLabel>
                <div className="relative">
                  <Search className="absolute left-2 top-2.5 h-4 w-4 text-muted-foreground" />
                  <FormControl>
                    <Input
                      placeholder="Search in feedback content..."
                      className="pl-8"
                      {...field}
                    />
                  </FormControl>
                </div>
              </FormItem>
            )}
          />
        </div>
        
        <div className="flex justify-between">
          <Button
            type="button"
            variant="outline"
            onClick={resetFilters}
            className="gap-1"
          >
            <X className="h-4 w-4" />
            Reset Filters
          </Button>
          <Button type="submit">Apply Filters</Button>
        </div>
      </form>
    </Form>
  )
}

