"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ThumbsUp, ThumbsDown } from "lucide-react"
import { cn } from "@/lib/utils"
import { FeedbackService } from "@/lib/services/feedback-service"
import { CreateFeedbackRequest, FeedbackSource } from "@/lib/types/user-feedback"
import { useToast } from "@/hooks/use-toast"

interface FeedbackButtonProps {
  sourceId: string;
  source: FeedbackSource;
  contextData?: Record<string, unknown>;
  onFeedbackSubmitted?: (type: "thumbs_up" | "thumbs_down") => void;
  className?: string;
}

export function FeedbackButton({ 
  sourceId, 
  source, 
  contextData = {}, 
  onFeedbackSubmitted,
  className 
}: FeedbackButtonProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [selectedFeedback, setSelectedFeedback] = useState<"thumbs_up" | "thumbs_down" | null>(null)
  const { toast } = useToast()

  const handleFeedback = async (type: "thumbs_up" | "thumbs_down") => {
    if (isSubmitting) return
    
    setIsSubmitting(true)
    setSelectedFeedback(type)
    
    try {
      const feedbackData: CreateFeedbackRequest = {
        content: type === "thumbs_up" ? "This was helpful" : "This needs improvement",
        type,
        source,
        contextData,
        sourceId,
        isAnonymous: false
      }
      
      await FeedbackService.createFeedback(feedbackData)
      
      toast({
        title: "Feedback submitted",
        description: "Thank you for your feedback!",
        variant: "default",
      })
      
      if (onFeedbackSubmitted) {
        onFeedbackSubmitted(type)
      }
    } catch (error) {
      console.error("Error submitting feedback:", error)
      toast({
        title: "Error",
        description: "Failed to submit feedback. Please try again.",
        variant: "destructive",
      })
      setSelectedFeedback(null)
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className={cn("flex items-center gap-2", className)}>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "h-8 px-2 text-muted-foreground hover:text-foreground",
          selectedFeedback === "thumbs_up" && "bg-primary/10 text-primary"
        )}
        disabled={isSubmitting}
        onClick={() => handleFeedback("thumbs_up")}
      >
        <ThumbsUp className="h-4 w-4 mr-1" />
        <span className="text-xs">Helpful</span>
      </Button>
      <Button
        variant="ghost"
        size="sm"
        className={cn(
          "h-8 px-2 text-muted-foreground hover:text-foreground",
          selectedFeedback === "thumbs_down" && "bg-destructive/10 text-destructive"
        )}
        disabled={isSubmitting}
        onClick={() => handleFeedback("thumbs_down")}
      >
        <ThumbsDown className="h-4 w-4 mr-1" />
        <span className="text-xs">Not helpful</span>
      </Button>
    </div>
  )
}
