"use client"

import { useState } from "react"
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from "@/components/ui/table"
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { 
  ThumbsUp, 
  ThumbsDown, 
  MessageSquare, 
  Lightbulb, 
  MoreHorizontal, 
  CheckCircle, 
  XCircle, 
  Clock, 
  AlertTriangle 
} from "lucide-react"
import { Feedback, FeedbackStatus, FeedbackType } from "@/lib/types/user-feedback"
import { formatDistanceToNow } from "date-fns"
import { FeedbackService } from "@/lib/services/feedback-service"
import { useToast } from "@/hooks/use-toast" // Corrected import path

interface FeedbackTableProps {
  feedbackItems: Feedback[]
  onFeedbackUpdated: () => void
}

export function FeedbackTable({ feedbackItems, onFeedbackUpdated }: FeedbackTableProps) {
  const [loading, setLoading] = useState<Record<string, boolean>>({})
  const { toast } = useToast() // Restored useToast hook

  const feedbackTypeIcons: Record<FeedbackType, React.ReactNode> = {
    thumbs_up: <ThumbsUp className="h-4 w-4 text-green-500" />,
    thumbs_down: <ThumbsDown className="h-4 w-4 text-red-500" />,
    correction: <MessageSquare className="h-4 w-4 text-blue-500" />,
    suggestion: <Lightbulb className="h-4 w-4 text-amber-500" />,
    general: <MessageSquare className="h-4 w-4 text-gray-500" />,
  }

  const statusBadgeVariants: Record<FeedbackStatus, { variant: "default" | "outline" | "secondary" | "destructive", icon: React.ReactNode }> = {
    pending: { 
      variant: "outline", 
      icon: <Clock className="h-3 w-3 mr-1 text-amber-500" /> 
    },
    reviewed: { 
      variant: "secondary", 
      icon: <CheckCircle className="h-3 w-3 mr-1 text-blue-500" /> 
    },
    implemented: { 
      variant: "default", 
      icon: <CheckCircle className="h-3 w-3 mr-1" /> 
    },
    rejected: { 
      variant: "destructive", 
      icon: <XCircle className="h-3 w-3 mr-1" /> 
    },
  }

  const updateFeedbackStatus = async (id: string, status: FeedbackStatus) => {
    setLoading(prev => ({ ...prev, [id]: true }))
    
    try {
      await FeedbackService.updateFeedback(id, { status })
      toast({ // Restored toast usage
        title: "Feedback updated",
        description: `Feedback status changed to ${status}`,
        variant: "default",
      })
      onFeedbackUpdated()
    } catch (error) {
      console.error("Error updating feedback:", error)
      toast({ // Restored toast usage
        title: "Error",
        description: "Failed to update feedback status. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(prev => ({ ...prev, [id]: false }))
    }
  }

  const deleteFeedback = async (id: string) => {
    setLoading(prev => ({ ...prev, [id]: true }))
    
    try {
      await FeedbackService.deleteFeedback(id)
      toast({ // Restored toast usage
        title: "Feedback deleted",
        description: "Feedback has been deleted successfully",
        variant: "default",
      })
      onFeedbackUpdated()
    } catch (error) {
      console.error("Error deleting feedback:", error)
      toast({ // Restored toast usage
        title: "Error",
        description: "Failed to delete feedback. Please try again.",
        variant: "destructive",
      })
    } finally {
      setLoading(prev => ({ ...prev, [id]: false }))
    }
  }

  return (
    <div className="rounded-md border">
      <Table>
        <TableHeader>
          <TableRow>
            <TableHead className="w-[80px]">Type</TableHead>
            <TableHead>Content</TableHead>
            <TableHead className="w-[100px]">Source</TableHead>
            <TableHead className="w-[100px]">Status</TableHead>
            <TableHead className="w-[120px]">Date</TableHead>
            <TableHead className="w-[80px] text-right">Actions</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          {feedbackItems.length === 0 ? (
            <TableRow>
              <TableCell colSpan={6} className="h-24 text-center">
                No feedback found.
              </TableCell>
            </TableRow>
          ) : (
            feedbackItems.map((feedback) => (
              <TableRow key={feedback.id}>
                <TableCell>
                  <div className="flex items-center justify-center">
                    {feedbackTypeIcons[feedback.type]}
                  </div>
                </TableCell>
                <TableCell>
                  <div className="max-w-[400px] truncate">
                    {feedback.content}
                  </div>
                </TableCell>
                <TableCell>
                  <Badge variant="outline" className="capitalize">
                    {feedback.source.replace(/_/g, ' ')}
                  </Badge>
                </TableCell>
                <TableCell>
                  <Badge 
                    variant={statusBadgeVariants[feedback.status].variant}
                    className="flex items-center w-fit"
                  >
                    {statusBadgeVariants[feedback.status].icon}
                    <span className="capitalize">{feedback.status}</span>
                  </Badge>
                </TableCell>
                <TableCell className="text-muted-foreground text-sm">
                  {formatDistanceToNow(new Date(feedback.createdAt), { addSuffix: true })}
                </TableCell>
                <TableCell className="text-right">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="ghost" className="h-8 w-8 p-0">
                        <span className="sr-only">Open menu</span>
                        <MoreHorizontal className="h-4 w-4" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent align="end">
                      <DropdownMenuLabel>Actions</DropdownMenuLabel>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => updateFeedbackStatus(feedback.id, "reviewed")}
                        disabled={loading[feedback.id] || feedback.status === "reviewed"}
                      >
                        <CheckCircle className="h-4 w-4 mr-2 text-blue-500" />
                        Mark as Reviewed
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => updateFeedbackStatus(feedback.id, "implemented")}
                        disabled={loading[feedback.id] || feedback.status === "implemented"}
                      >
                        <CheckCircle className="h-4 w-4 mr-2 text-green-500" />
                        Mark as Implemented
                      </DropdownMenuItem>
                      <DropdownMenuItem 
                        onClick={() => updateFeedbackStatus(feedback.id, "rejected")}
                        disabled={loading[feedback.id] || feedback.status === "rejected"}
                      >
                        <XCircle className="h-4 w-4 mr-2 text-red-500" />
                        Mark as Rejected
                      </DropdownMenuItem>
                      <DropdownMenuSeparator />
                      <DropdownMenuItem 
                        onClick={() => deleteFeedback(feedback.id)}
                        disabled={loading[feedback.id]}
                        className="text-red-500 focus:text-red-500"
                      >
                        <AlertTriangle className="h-4 w-4 mr-2" />
                        Delete Feedback
                      </DropdownMenuItem>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </TableCell>
              </TableRow>
            ))
          )}
        </TableBody>
      </Table>
    </div>
  )
}
