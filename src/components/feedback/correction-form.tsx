"use client"

import { useState } from "react"
import { zod<PERSON>esolver } from "@hookform/resolvers/zod"
import { useForm } from "react-hook-form"
import * as z from "zod"
import { But<PERSON> } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Form, FormControl, FormField, FormItem, FormLabel, FormMessage } from "@/components/ui/form"
import { FeedbackService } from "@/lib/services/feedback-service"
import { CreateCorrectionRequest, FeedbackSource, SourceType, FeedbackContextData } from "@/lib/types/user-feedback"
import { useToast } from "@/hooks/use-toast"
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle } from "@/components/ui/dialog"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"

const correctionFormSchema = z.object({
  originalContent: z.string().min(1, "Original content is required"),
  correctedContent: z.string().min(1, "Corrected content is required"),
  feedbackId: z.string().optional(),
})

type CorrectionFormValues = z.infer<typeof correctionFormSchema>

interface CorrectionFormProps {
  sourceId: string
  sourceType: SourceType
  contextData?: FeedbackContextData
  originalContent?: string
  open: boolean
  onOpenChange: (open: boolean) => void
  onCorrectionSubmitted?: () => void
  feedbackId?: string
}

export function CorrectionForm({
  sourceId,
  sourceType,
  contextData = {},
  originalContent = "",
  open,
  onOpenChange,
  onCorrectionSubmitted,
  feedbackId
}: CorrectionFormProps) {
  const [isSubmitting, setIsSubmitting] = useState(false)
  const { toast } = useToast()

  const form = useForm<CorrectionFormValues>({
    resolver: zodResolver(correctionFormSchema),
    defaultValues: {
      originalContent: originalContent,
      correctedContent: originalContent,
      feedbackId: feedbackId,
    },
  })

  const onSubmit = async (values: CorrectionFormValues) => {
    setIsSubmitting(true)
    
    try {
      // If no feedbackId is provided, create a feedback entry first
      let feedbackIdToUse = values.feedbackId;
      
      if (!feedbackIdToUse) {
        const feedbackResponse = await FeedbackService.createFeedback({
          content: "Correction submitted",
          type: "correction",
          source: sourceType as unknown as FeedbackSource, // Type conversion for compatibility
          contextData,
          sourceId,
        });
        
        feedbackIdToUse = feedbackResponse.id;
      }
      
      const correctionData: CreateCorrectionRequest = {
        feedbackId: feedbackIdToUse,
        originalContent: values.originalContent,
        correctedContent: values.correctedContent,
        contextData,
        sourceId,
        sourceType,
      }
      
      await FeedbackService.createCorrection(correctionData)
      
      toast({
        title: "Correction submitted",
        description: "Thank you for your correction! It will be reviewed by our team.",
        variant: "default",
      })
      
      form.reset()
      onOpenChange(false)
      
      if (onCorrectionSubmitted) {
        onCorrectionSubmitted()
      }
    } catch (error) {
      console.error("Error submitting correction:", error)
      toast({
        title: "Error",
        description: "Failed to submit correction. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>Submit a Correction</DialogTitle>
          <DialogDescription>
            Help us improve our content by submitting a correction.
          </DialogDescription>
        </DialogHeader>
        
        <Alert variant="default" className="bg-blue-50 dark:bg-blue-950/30 border-blue-200 dark:border-blue-900">
          <AlertCircle className="h-4 w-4 text-blue-600 dark:text-blue-400" />
          <AlertTitle>Correction Guidelines</AlertTitle>
          <AlertDescription className="text-xs text-muted-foreground">
            Please provide the exact text that needs correction and your suggested correction.
            Our team will review your submission and implement it if appropriate.
          </AlertDescription>
        </Alert>
        
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            <FormField
              control={form.control}
              name="originalContent"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Original Text</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter the original text that needs correction..."
                      className="min-h-[100px] font-mono text-sm"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="correctedContent"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Corrected Text</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Enter your corrected version..."
                      className="min-h-[100px] font-mono text-sm"
                      {...field}
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <DialogFooter>
              <Button
                type="button"
                variant="outline"
                onClick={() => onOpenChange(false)}
                disabled={isSubmitting}
              >
                Cancel
              </Button>
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? "Submitting..." : "Submit Correction"}
              </Button>
            </DialogFooter>
          </form>
        </Form>
      </DialogContent>
    </Dialog>
  )
}
