"use client";

import React, { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import { Card, CardContent, CardHeader } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { PlusCircle, FileText, Trash2, Edit, Eye } from "lucide-react";
import {
  DepositionPreparation,
  DepositionStatus,
} from "@/lib/types/deposition";
import { depositionService } from "@/lib/services/deposition-service";
import { useToast } from "@/components/ui/use-toast";

const statusColors = {
  DRAFT: "bg-gray-200 text-gray-800",
  IN_PROGRESS: "bg-blue-200 text-blue-800",
  COMPLETED: "bg-green-200 text-green-800",
};

const statusLabels: Record<DepositionStatus, string> = {
  DRAFT: "Draft",
  IN_PROGRESS: "In Progress",
  COMPLETED: "Completed",
};

export function DepositionList() {
  const [depositions, setDepositions] = useState<DepositionPreparation[]>([]);
  const [loading, setLoading] = useState(true);
  const router = useRouter();
  const { toast } = useToast();

  useEffect(() => {
    loadDepositions();
  }, []);

  const loadDepositions = async () => {
    try {
      setLoading(true);
      const data = await depositionService.getAllDepositionPreparations();
      setDepositions(data);
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error ? error.message : "Failed to load depositions",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const handleDelete = async (id: string) => {
    try {
      await depositionService.deleteDepositionPreparation(id);
      toast({
        title: "Success",
        description: "Deposition preparation deleted successfully",
      });
      loadDepositions();
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to delete deposition",
        variant: "destructive",
      });
    }
  };

  const getStatusBadge = (status: string) => {
    // Normalize status to uppercase to match our type definition
    const normalizedStatus = status.toUpperCase() as DepositionStatus;
    return (
      <Badge className={statusColors[normalizedStatus]}>
        {statusLabels[normalizedStatus] || status}
      </Badge>
    );
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-1/3" />
          <Skeleton className="h-10 w-32" />
        </div>
        <Card>
          <CardHeader>
            <Skeleton className="h-6 w-full" />
          </CardHeader>
          <CardContent>
            {[...Array(5)].map((_, i) => (
              <div key={i} className="flex justify-between items-center py-2">
                <Skeleton className="h-5 w-1/3" />
                <Skeleton className="h-5 w-1/4" />
                <Skeleton className="h-5 w-1/5" />
                <Skeleton className="h-8 w-24" />
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-4">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Deposition Preparations</h2>
        <Button
          onClick={() => router.push("/depositions/create")}
          className="flex items-center gap-2"
        >
          <PlusCircle className="h-4 w-4" />
          New Preparation
        </Button>
      </div>

      {depositions.length === 0 ? (
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-10">
            <FileText className="h-16 w-16 text-gray-400 mb-4" />
            <h3 className="text-xl font-medium mb-2">No depositions yet</h3>
            <p className="text-gray-500 mb-4 text-center max-w-md">
              Create your first deposition preparation to start organizing
              questions and analyzing transcripts.
            </p>
            <Button
              onClick={() => router.push("/depositions/create")}
              className="flex items-center gap-2"
            >
              <PlusCircle className="h-4 w-4" />
              Create Deposition
            </Button>
          </CardContent>
        </Card>
      ) : (
        <Card>
          <CardContent className="p-0">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Title</TableHead>
                  <TableHead>Witnesses</TableHead>
                  <TableHead>Created</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {depositions.map((deposition) => (
                  <TableRow key={deposition.id}>
                    <TableCell className="font-medium">
                      {deposition.title}
                    </TableCell>
                    <TableCell>
                      {deposition.targetWitnesses.length > 0
                        ? deposition.targetWitnesses.join(", ")
                        : "None specified"}
                    </TableCell>
                    <TableCell>
                      {format(new Date(deposition.createdAt), "MMM d, yyyy")}
                    </TableCell>
                    <TableCell>{getStatusBadge(deposition.status)}</TableCell>
                    <TableCell className="text-right">
                      <div className="flex justify-end gap-2">
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() =>
                            router.push(`/depositions/${deposition.id}`)
                          }
                        >
                          <Eye className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() =>
                            router.push(`/depositions/${deposition.id}/edit`)
                          }
                        >
                          <Edit className="h-4 w-4" />
                        </Button>
                        <Button
                          variant="outline"
                          size="icon"
                          onClick={() => handleDelete(deposition.id)}
                        >
                          <Trash2 className="h-4 w-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      )}
    </div>
  );
}

export default DepositionList;
