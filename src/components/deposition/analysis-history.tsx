"use client";

import React, { useState, useEffect, useCallback } from "react";
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>er, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import {
	FileText,
	Eye,
	Download,
	Calendar,
	Clock,
	TrendingUp,
	AlertCircle,
	CheckCircle,
} from "lucide-react";
import { depositionService } from "@/lib/services/deposition-service";
import { DepositionAnalysis } from "./deposition-analysis";
import type { DepositionAnalysisResponse } from "@/lib/types/deposition";

interface AnalysisHistoryProps {
	depositionId: string;
}

interface AnalysisListItem {
	id: string;
	analyzedAt: string;
	focusAreas: string[];
	overallCredibilityScore: number;
	keyFindingsCount: number;
	analysisDurationMs: number;
	confidence: string;
}

export function AnalysisHistory({ depositionId }: AnalysisHistoryProps) {
	const [analyses, setAnalyses] = useState<AnalysisListItem[]>([]);
	const [fullAnalyses, setFullAnalyses] = useState<
		DepositionAnalysisResponse[]
	>([]);
	const [selectedAnalysis, setSelectedAnalysis] =
		useState<DepositionAnalysisResponse | null>(null);
	const [loading, setLoading] = useState(true);
	const [loadingAnalysis, setLoadingAnalysis] = useState(false);
	const { toast } = useToast();

	const loadAnalysisHistory = useCallback(async () => {
		try {
			setLoading(true);
			const history = await depositionService.getAnalysisHistory(depositionId);

			// Ensure history is an array
			if (!Array.isArray(history)) {
				console.error("Expected array but got:", typeof history, history);
				setAnalyses([]);
				return;
			}

			const mappedHistory: AnalysisListItem[] = history.map((analysis) => {
				// Ensure analysis is an object
				if (!analysis || typeof analysis !== "object") {
					console.error("Invalid analysis object:", analysis);
					return {
						id: "unknown",
						analyzedAt: new Date().toISOString(),
						focusAreas: [],
						overallCredibilityScore: 0,
						keyFindingsCount: 0,
						analysisDurationMs: 0,
						confidence: "unknown",
					};
				}

				return {
					id: analysis.id || "",
					analyzedAt: analysis.metadata?.analyzedAt || new Date().toISOString(),
					focusAreas: analysis.focusAreas || [],
					overallCredibilityScore: analysis.overallCredibilityScore || 0,
					keyFindingsCount: analysis.keyFindings?.length || 0,
					analysisDurationMs: analysis.metadata?.analysisDurationMs || 0,
					confidence: analysis.metadata?.confidence || "unknown",
				};
			});
			setAnalyses(mappedHistory);
			setFullAnalyses(history); // Store full analyses for direct access
		} catch (err) {
			console.error("Failed to load analysis history:", err);
			toast({
				title: "Error",
				description: "Failed to load analysis history",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	}, [depositionId, toast]);

	const loadAnalysis = async (analysisId: string) => {
		try {
			console.log("Loading analysis with ID:", analysisId);
			setLoadingAnalysis(true);

			// First try to find the analysis in our cached data
			const cachedAnalysis = fullAnalyses.find(
				(analysis) => analysis.id === analysisId
			);
			if (cachedAnalysis) {
				console.log("Using cached analysis:", cachedAnalysis);
				setSelectedAnalysis(cachedAnalysis);
				return;
			}

			// Fall back to API call if not found in cache
			console.log("Analysis not found in cache, making API call...");
			const analysis = await depositionService.getDepositionAnalysis(
				analysisId
			);
			console.log("Loaded analysis from API:", analysis);
			setSelectedAnalysis(analysis);
		} catch (err) {
			console.error("Failed to load analysis details:", err);
			toast({
				title: "Error",
				description: `Failed to load analysis details: ${
					err instanceof Error ? err.message : "Unknown error"
				}`,
				variant: "destructive",
			});
		} finally {
			setLoadingAnalysis(false);
		}
	};

	const downloadAnalysisAsCsv = (analysis: AnalysisListItem) => {
		// Create CSV content for the analysis summary
		let csvContent = "data:text/csv;charset=utf-8,";
		csvContent +=
			"Analysis ID,Date,Focus Areas,Credibility Score,Key Findings,Duration (ms),Confidence\n";
		csvContent += `${analysis.id},"${new Date(
			analysis.analyzedAt
		).toLocaleString()}","${(analysis.focusAreas || []).join("; ")}",${
			analysis.overallCredibilityScore
		},${analysis.keyFindingsCount},${analysis.analysisDurationMs},${
			analysis.confidence
		}\n`;

		const encodedUri = encodeURI(csvContent);
		const link = document.createElement("a");
		link.setAttribute("href", encodedUri);
		link.setAttribute("download", `analysis-summary-${analysis.id}.csv`);
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	useEffect(() => {
		loadAnalysisHistory();
	}, [loadAnalysisHistory]);

	const getConfidenceBadgeVariant = (confidence: string) => {
		switch (confidence.toLowerCase()) {
			case "high":
				return "default";
			case "medium":
				return "secondary";
			case "low":
				return "outline";
			default:
				return "outline";
		}
	};

	const getCredibilityColor = (score: number) => {
		if (score >= 0.8) return "text-green-600";
		if (score >= 0.6) return "text-yellow-600";
		return "text-red-600";
	};

	if (selectedAnalysis) {
		return (
			<div className="space-y-6">
				<div className="flex justify-between items-center">
					<h2 className="text-2xl font-bold">Analysis Details</h2>
					<Button
						variant="outline"
						onClick={() => setSelectedAnalysis(null)}
						className="flex items-center gap-2"
					>
						<FileText className="h-4 w-4" />
						Back to History
					</Button>
				</div>

				<DepositionAnalysis analysis={selectedAnalysis} />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<FileText className="h-5 w-5" />
						Analysis History
					</CardTitle>
				</CardHeader>
				<CardContent>
					{loading ? (
						<div className="space-y-4">
							{[...Array(3)].map((_, i) => (
								<div key={i} className="border rounded-lg p-4">
									<div className="flex justify-between items-start mb-3">
										<Skeleton className="h-4 w-32" />
										<Skeleton className="h-6 w-16" />
									</div>
									<div className="space-y-2">
										<Skeleton className="h-3 w-full" />
										<Skeleton className="h-3 w-3/4" />
									</div>
								</div>
							))}
						</div>
					) : !analyses || analyses.length === 0 ? (
						<div className="text-center py-8">
							<AlertCircle className="h-12 w-12 text-gray-400 mx-auto mb-4" />
							<h3 className="text-lg font-medium mb-2">No Previous Analyses</h3>
							<p className="text-gray-500">
								No transcript analyses have been performed for this deposition
								yet.
							</p>
						</div>
					) : (
						<div className="space-y-4">
							{analyses.map((analysis) => (
								<div
									key={analysis.id}
									className="border rounded-lg p-4 hover:bg-gray-50 transition-colors"
								>
									<div className="flex justify-between items-start mb-3">
										<div className="flex items-center gap-2">
											<Calendar className="h-4 w-4 text-gray-500" />
											<span className="text-sm font-medium">
												{new Date(analysis.analyzedAt).toLocaleString()}
											</span>
										</div>
										<Badge
											variant={getConfidenceBadgeVariant(analysis.confidence)}
										>
											{analysis.confidence} confidence
										</Badge>
									</div>

									<div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
										<div className="flex items-center gap-2">
											<TrendingUp className="h-4 w-4 text-gray-500" />
											<span className="text-sm">
												Credibility:
												<span
													className={`font-medium ml-1 ${getCredibilityColor(
														analysis.overallCredibilityScore
													)}`}
												>
													{Math.round(analysis.overallCredibilityScore * 100)}%
												</span>
											</span>
										</div>

										<div className="flex items-center gap-2">
											<CheckCircle className="h-4 w-4 text-gray-500" />
											<span className="text-sm">
												{analysis.keyFindingsCount} key findings
											</span>
										</div>

										<div className="flex items-center gap-2">
											<Clock className="h-4 w-4 text-gray-500" />
											<span className="text-sm">
												{(analysis.analysisDurationMs / 1000).toFixed(1)}s
											</span>
										</div>
									</div>

									<div className="mb-4">
										<span className="text-sm text-gray-500">Focus Areas: </span>
										<div className="flex flex-wrap gap-1 mt-1">
											{analysis.focusAreas && analysis.focusAreas.length > 0 ? (
												analysis.focusAreas.map((area, index) => (
													<Badge
														key={index}
														variant="outline"
														className="text-xs"
													>
														{area}
													</Badge>
												))
											) : (
												<Badge variant="outline" className="text-xs">
													No focus areas specified
												</Badge>
											)}
										</div>
									</div>

									<div className="flex gap-2">
										<Button
											size="sm"
											onClick={() => {
												console.log(
													"View Details clicked for analysis:",
													analysis.id
												);
												loadAnalysis(analysis.id);
											}}
											disabled={loadingAnalysis}
											className="flex items-center gap-2"
										>
											<Eye className="h-4 w-4" />
											View Details
										</Button>
										<Button
											size="sm"
											variant="outline"
											onClick={() => downloadAnalysisAsCsv(analysis)}
											className="flex items-center gap-2"
										>
											<Download className="h-4 w-4" />
											Export
										</Button>
									</div>
								</div>
							))}
						</div>
					)}
				</CardContent>
			</Card>
		</div>
	);
}
