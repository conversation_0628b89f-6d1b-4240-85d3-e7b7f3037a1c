"use client";

import React, { useState } from "react";
import {
  Card,
  CardContent,
  CardDescription,
  <PERSON><PERSON><PERSON><PERSON>,
  Card<PERSON>eader,
  Card<PERSON>itle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Slider } from "@/components/ui/slider";
import { Lightbulb, Loader2 } from "lucide-react";
import {
  DepositionPreparation,
  GenerateQuestionsDto,
  CategoryInputFormat,
  CATEGORY_MAPPINGS
} from "@/lib/types/deposition";
import { depositionService } from "@/lib/services/deposition-service";
import { useToast } from "@/components/ui/use-toast";

interface GenerateQuestionsFormProps {
  depositionId: string;
  deposition: DepositionPreparation;
  onQuestionsGenerated: () => void;
}

export function GenerateQuestionsForm({
  depositionId,
  deposition,
  onQuestionsGenerated,
}: GenerateQuestionsFormProps) {
  const { toast } = useToast();
  const [loading, setLoading] = useState(false);
  const [focusAreas, setFocusAreas] = useState<string[]>([]);
  const [newFocusArea, setNewFocusArea] = useState("");
  const [questionCount, setQuestionCount] = useState(10);
  const [includeFollowUps, setIncludeFollowUps] = useState(true);
  const [priorityLevel, setPriorityLevel] = useState<
    "all" | "high" | "medium" | "low"
  >("all");
  const [questionCategories, setQuestionCategories] = useState<CategoryInputFormat[]>(
    ["background", "credibility", "timeline"]
  );
  const [overrideContext, setOverrideContext] = useState(false);
  const [customContext, setCustomContext] = useState("");
  const [overrideWitnesses, setOverrideWitnesses] = useState(false);
  const [customWitnesses, setCustomWitnesses] = useState<string[]>([]);
  const [newWitness, setNewWitness] = useState("");
  const [overrideIssues, setOverrideIssues] = useState(false);
  const [customIssues, setCustomIssues] = useState<string[]>([]);
  const [newIssue, setNewIssue] = useState("");

  const addFocusArea = () => {
    if (newFocusArea.trim()) {
      setFocusAreas([...focusAreas, newFocusArea.trim()]);
      setNewFocusArea("");
    }
  };

  const removeFocusArea = (index: number) => {
    setFocusAreas(focusAreas.filter((_, i) => i !== index));
  };

  const addWitness = () => {
    if (newWitness.trim()) {
      setCustomWitnesses([...customWitnesses, newWitness.trim()]);
      setNewWitness("");
    }
  };

  const removeWitness = (index: number) => {
    setCustomWitnesses(customWitnesses.filter((_, i) => i !== index));
  };

  const addIssue = () => {
    if (newIssue.trim()) {
      setCustomIssues([...customIssues, newIssue.trim()]);
      setNewIssue("");
    }
  };

  const removeIssue = (index: number) => {
    setCustomIssues(customIssues.filter((_, i) => i !== index));
  };

  const handleGenerateQuestions = async () => {
    try {
      setLoading(true);

      const data: GenerateQuestionsDto = {
        caseContext: overrideContext ? customContext : deposition.caseContext,
        targetWitnesses: overrideWitnesses
          ? customWitnesses
          : deposition.targetWitnesses,
        keyIssues: overrideIssues ? customIssues : deposition.keyIssues,
        focusAreas: focusAreas.length > 0 ? focusAreas : undefined,
        questionCount,
        includeFollowUps,
        priorityLevel: priorityLevel === "all" ? undefined : priorityLevel,
        questionCategories:
          questionCategories.length > 0 ? questionCategories : undefined,
      };

      await depositionService.generateQuestionsForDeposition(
        depositionId,
        data
      );

      toast({
        title: "Success",
        description: "Questions generated successfully",
      });

      onQuestionsGenerated();
    } catch (error) {
      toast({
        title: "Error",
        description:
          error instanceof Error
            ? error.message
            : "Failed to generate questions",
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-5 w-5" />
          Generate Questions with AI
        </CardTitle>
        <CardDescription>
          Use AI to generate relevant deposition questions based on case context
          and key issues
        </CardDescription>
      </CardHeader>
      <CardContent className="space-y-6">
        <div className="grid gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="override-context"
              checked={overrideContext}
              onCheckedChange={(checked) =>
                setOverrideContext(checked === true)
              }
            />
            <Label htmlFor="override-context">Override case context</Label>
          </div>
          {overrideContext ? (
            <Textarea
              value={customContext}
              onChange={(e) => setCustomContext(e.target.value)}
              placeholder="Enter custom case context"
              className="min-h-[100px]"
            />
          ) : (
            <div className="bg-muted p-3 rounded-md">
              <p className="text-sm text-muted-foreground whitespace-pre-wrap">
                {deposition.caseContext}
              </p>
            </div>
          )}
        </div>

        <div className="grid gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="override-witnesses"
              checked={overrideWitnesses}
              onCheckedChange={(checked) =>
                setOverrideWitnesses(checked === true)
              }
            />
            <Label htmlFor="override-witnesses">
              Override target witnesses
            </Label>
          </div>
          {overrideWitnesses ? (
            <div>
              <div className="flex flex-wrap gap-2 mb-2">
                {customWitnesses.map((witness, index) => (
                  <div
                    key={index}
                    className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md flex items-center gap-2"
                  >
                    <span>{witness}</span>
                    <button
                      type="button"
                      onClick={() => removeWitness(index)}
                      className="text-secondary-foreground/70 hover:text-secondary-foreground"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  value={newWitness}
                  onChange={(e) => setNewWitness(e.target.value)}
                  placeholder="Add a witness"
                  className="flex-1"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      addWitness();
                    }
                  }}
                />
                <Button type="button" onClick={addWitness} variant="secondary">
                  Add
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-wrap gap-2">
              {deposition.targetWitnesses.map((witness, index) => (
                <div
                  key={index}
                  className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md"
                >
                  {witness}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="grid gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="override-issues"
              checked={overrideIssues}
              onCheckedChange={(checked) => setOverrideIssues(checked === true)}
            />
            <Label htmlFor="override-issues">Override key issues</Label>
          </div>
          {overrideIssues ? (
            <div>
              <div className="flex flex-wrap gap-2 mb-2">
                {customIssues.map((issue, index) => (
                  <div
                    key={index}
                    className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md flex items-center gap-2"
                  >
                    <span>{issue}</span>
                    <button
                      type="button"
                      onClick={() => removeIssue(index)}
                      className="text-secondary-foreground/70 hover:text-secondary-foreground"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  value={newIssue}
                  onChange={(e) => setNewIssue(e.target.value)}
                  placeholder="Add a key issue"
                  className="flex-1"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      addIssue();
                    }
                  }}
                />
                <Button type="button" onClick={addIssue} variant="secondary">
                  Add
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-wrap gap-2">
              {deposition.keyIssues.map((issue, index) => (
                <div
                  key={index}
                  className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md"
                >
                  {issue}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="grid gap-2">
          <Label>Focus Areas (Optional)</Label>
          <div className="flex flex-wrap gap-2 mb-2">
            {focusAreas.map((area, index) => (
              <div
                key={index}
                className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md flex items-center gap-2"
              >
                <span>{area}</span>
                <button
                  type="button"
                  onClick={() => removeFocusArea(index)}
                  className="text-secondary-foreground/70 hover:text-secondary-foreground"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newFocusArea}
              onChange={(e) => setNewFocusArea(e.target.value)}
              placeholder="Add a focus area"
              className="flex-1"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  addFocusArea();
                }
              }}
            />
            <Button type="button" onClick={addFocusArea} variant="secondary">
              Add
            </Button>
          </div>
        </div>

        <div className="grid gap-2">
          <div className="flex items-center space-x-2">
            <Checkbox
              id="override-issues"
              checked={overrideIssues}
              onCheckedChange={(checked) => setOverrideIssues(checked === true)}
            />
            <Label htmlFor="override-issues">Override key issues</Label>
          </div>
          {overrideIssues ? (
            <div>
              <div className="flex flex-wrap gap-2 mb-2">
                {customIssues.map((issue, index) => (
                  <div
                    key={index}
                    className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md flex items-center gap-2"
                  >
                    <span>{issue}</span>
                    <button
                      type="button"
                      onClick={() => removeIssue(index)}
                      className="text-secondary-foreground/70 hover:text-secondary-foreground"
                    >
                      ×
                    </button>
                  </div>
                ))}
              </div>
              <div className="flex gap-2">
                <Input
                  value={newIssue}
                  onChange={(e) => setNewIssue(e.target.value)}
                  placeholder="Add a key issue"
                  className="flex-1"
                  onKeyDown={(e) => {
                    if (e.key === "Enter") {
                      e.preventDefault();
                      addIssue();
                    }
                  }}
                />
                <Button type="button" onClick={addIssue} variant="secondary">
                  Add
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-wrap gap-2">
              {deposition.keyIssues.map((issue, index) => (
                <div
                  key={index}
                  className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md"
                >
                  {issue}
                </div>
              ))}
            </div>
          )}
        </div>

        <div className="grid gap-2">
          <Label>Focus Areas (Optional)</Label>
          <div className="flex flex-wrap gap-2 mb-2">
            {focusAreas.map((area, index) => (
              <div
                key={index}
                className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md flex items-center gap-2"
              >
                <span>{area}</span>
                <button
                  type="button"
                  onClick={() => removeFocusArea(index)}
                  className="text-secondary-foreground/70 hover:text-secondary-foreground"
                >
                  ×
                </button>
              </div>
            ))}
          </div>
          <div className="flex gap-2">
            <Input
              value={newFocusArea}
              onChange={(e) => setNewFocusArea(e.target.value)}
              placeholder="Add a focus area"
              className="flex-1"
              onKeyDown={(e) => {
                if (e.key === "Enter") {
                  e.preventDefault();
                  addFocusArea();
                }
              }}
            />
            <Button type="button" onClick={addFocusArea} variant="secondary">
              Add
            </Button>
          </div>
        </div>

        <div className="grid gap-4">
          <div className="grid gap-2">
            <Label htmlFor="question-count">
              Number of questions: {questionCount}
            </Label>
            <Slider
              id="question-count"
              min={1}
              max={20}
              step={1}
              value={[questionCount]}
              onValueChange={([value]) => setQuestionCount(value)}
              className="w-full"
            />
          </div>

          <div className="grid gap-2">
            <Label>Question Categories</Label>
            <div className="flex flex-wrap gap-2">
              {Object.entries(CATEGORY_MAPPINGS).map(([category, config]) => (
                <div key={category} className="flex items-center space-x-1">
                  <Checkbox
                    id={`category-${category}`}
                    checked={config.inputVariations.some(v => questionCategories.includes(v as CategoryInputFormat))}
                    onCheckedChange={(checked) => {
                      if (checked) {
                        // Add the first variation as the default
                        setQuestionCategories([
                          ...questionCategories,
                          config.inputVariations[0] as CategoryInputFormat
                        ]);
                      } else {
                        // Remove all variations of this category
                        setQuestionCategories(
                          questionCategories.filter(c =>
                            !config.inputVariations.includes(c)
                          )
                        );
                      }
                    }}
                  />
                  <Label
                    htmlFor={`category-${category}`}
                    className="capitalize"
                  >
                    {category.toLowerCase().replace(/_/g, ' ')}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="grid gap-2">
            <Label>Priority Level</Label>
            <div className="flex gap-4">
              {["all", "high", "medium", "low"].map((level) => (
                <div key={level} className="flex items-center space-x-1">
                  <input
                    type="radio"
                    id={`priority-${level}`}
                    name="priority"
                    checked={priorityLevel === level}
                    onChange={() => setPriorityLevel(level as "all" | "high" | "medium" | "low")}
                    className="h-4 w-4 text-primary focus:ring-primary"
                  />
                  <Label htmlFor={`priority-${level}`} className="capitalize">
                    {level}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-follow-ups"
              checked={includeFollowUps}
              onCheckedChange={(checked) =>
                setIncludeFollowUps(checked === true)
              }
            />
            <Label htmlFor="include-follow-ups">
              Include suggested follow-up questions
            </Label>
          </div>
        </div>
      </CardContent>
      <CardFooter>
        <Button
          onClick={handleGenerateQuestions}
          disabled={loading}
          className="w-full"
        >
          {loading ? (
            <>
              <Loader2 className="mr-2 h-4 w-4 animate-spin" />
              Generating Questions...
            </>
          ) : (
            <>
              <Lightbulb className="mr-2 h-4 w-4" />
              Generate Questions
            </>
          )}
        </Button>
      </CardFooter>
    </Card>
  );
}

export default GenerateQuestionsForm;
