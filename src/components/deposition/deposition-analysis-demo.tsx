"use client";

import React, { useState } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON>le } from "@/components/ui/card";
import { depositionService } from "@/lib/services/deposition-service";
import { DepositionAnalysis } from "./deposition-analysis";
import type { DepositionAnalysisResponse } from "@/lib/types/deposition";

/**
 * Demo component to test the new deposition analysis API integration
 */
export function DepositionAnalysisDemo() {
	const [analysis, setAnalysis] = useState<DepositionAnalysisResponse | null>(
		null
	);
	const [loading, setLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	const handleTestAnalysis = async () => {
		setLoading(true);
		setError(null);

		try {
			// Note: This requires actual transcript data to be provided
			const testData = {
				transcript: "", // User needs to provide actual transcript
				caseContext: "", // User needs to provide actual case context
				focusAreas: ["credibility", "inconsistencies", "timeline"],
			};

			if (!testData.transcript || !testData.caseContext) {
				throw new Error("Please provide transcript and case context data");
			}

			const result = await depositionService.analyzeDepositionTranscript(
				testData
			);
			setAnalysis(result);
		} catch (err) {
			setError(
				err instanceof Error ? err.message : "Failed to analyze transcript"
			);
		} finally {
			setLoading(false);
		}
	};

	const handleTestGetAnalysis = async () => {
		setLoading(true);
		setError(null);

		try {
			// Note: This requires an actual analysis ID
			const testId = ""; // User needs to provide actual analysis ID

			if (!testId) {
				throw new Error("Please provide a valid analysis ID");
			}

			const result = await depositionService.getDepositionAnalysis(testId);
			setAnalysis(result);
		} catch (err) {
			setError(err instanceof Error ? err.message : "Failed to get analysis");
		} finally {
			setLoading(false);
		}
	};

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle>Deposition Analysis API Demo</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="flex gap-4">
						<Button
							onClick={handleTestAnalysis}
							disabled={loading}
							variant="default"
						>
							{loading ? "Analyzing..." : "Test Analyze Transcript"}
						</Button>
						<Button
							onClick={handleTestGetAnalysis}
							disabled={loading}
							variant="outline"
						>
							{loading ? "Loading..." : "Test Get Analysis"}
						</Button>
					</div>

					{error && (
						<div className="p-4 bg-red-50 border border-red-200 rounded-md">
							<p className="text-red-800 text-sm">Error: {error}</p>
						</div>
					)}
				</CardContent>
			</Card>

			{analysis && <DepositionAnalysis analysis={analysis} />}
		</div>
	);
}
