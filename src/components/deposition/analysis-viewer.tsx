"use client";

import React, { useState, useEffect } from "react";
import { <PERSON>, <PERSON><PERSON>ontent, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Skeleton } from "@/components/ui/skeleton";
import { useToast } from "@/components/ui/use-toast";
import { ArrowLeft, Download, AlertCircle } from "lucide-react";
import { depositionService } from "@/lib/services/deposition-service";
import { DepositionAnalysis } from "./deposition-analysis";
import type { DepositionAnalysisResponse } from "@/lib/types/deposition";

interface AnalysisViewerProps {
  analysisId: string;
  onBack?: () => void;
  showBackButton?: boolean;
}

/**
 * Component to view a specific deposition analysis by ID
 * Can be used standalone or embedded in other components
 */
export function AnalysisViewer({ 
  analysisId, 
  onBack, 
  showBackButton = true 
}: AnalysisViewerProps) {
  const [analysis, setAnalysis] = useState<DepositionAnalysisResponse | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  const loadAnalysis = async () => {
    try {
      setLoading(true);
      setError(null);
      const result = await depositionService.getDepositionAnalysis(analysisId);
      setAnalysis(result);
    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : "Failed to load analysis";
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
    } finally {
      setLoading(false);
    }
  };

  const downloadAnalysisAsCsv = () => {
    if (!analysis) return;

    // Create comprehensive CSV content
    let csvContent = "data:text/csv;charset=utf-8,";

    // Analysis metadata
    csvContent += "Analysis Metadata\n";
    csvContent += "Field,Value\n";
    csvContent += `Analysis ID,${analysis.id}\n`;
    csvContent += `Deposition ID,${analysis.depositionId}\n`;
    csvContent += `Analyzed At,"${new Date(analysis.metadata.analyzedAt).toLocaleString()}"\n`;
    csvContent += `Overall Credibility Score,${analysis.overallCredibilityScore}\n`;
    csvContent += `Confidence,${analysis.metadata.confidence}\n`;
    csvContent += `Duration (ms),${analysis.metadata.analysisDurationMs}\n`;
    csvContent += `Focus Areas,"${analysis.focusAreas.join('; ')}"\n`;
    csvContent += "\n";

    // Key findings
    csvContent += "Key Findings\n";
    analysis.keyFindings.forEach((finding, index) => {
      csvContent += `${index + 1},"${finding.replace(/"/g, '""')}"\n`;
    });
    csvContent += "\n";

    // Key testimony analysis
    csvContent += "Key Testimony Analysis\n";
    csvContent += "Speaker,Statement,Credibility Score,Confidence,Reasoning\n";
    analysis.keyTestimonyAnalysis.forEach((testimony) => {
      csvContent += `"${testimony.speaker}","${testimony.statement.replace(/"/g, '""')}",${testimony.credibilityScore},"${testimony.confidence}","${testimony.reasoning.replace(/"/g, '""')}"\n`;
    });
    csvContent += "\n";

    // Cross-examination suggestions
    csvContent += "Cross-Examination Suggestions\n";
    csvContent += "Topic,Question,Purpose,Legal Basis\n";
    analysis.crossExaminationSuggestions.forEach((suggestion) => {
      csvContent += `"${suggestion.topic}","${suggestion.question.replace(/"/g, '""')}","${suggestion.purpose.replace(/"/g, '""')}","${suggestion.legalBasis.replace(/"/g, '""')}"\n`;
    });
    csvContent += "\n";

    // Inconsistencies
    csvContent += "Inconsistencies\n";
    csvContent += "Witness,Statement 1,Statement 2,Context,Severity\n";
    analysis.inconsistencies.forEach((inconsistency) => {
      csvContent += `"${inconsistency.witness}","${inconsistency.statement1.replace(/"/g, '""')}","${inconsistency.statement2.replace(/"/g, '""')}","${inconsistency.context.replace(/"/g, '""')}",${inconsistency.severity}\n`;
    });

    // Create download link
    const encodedUri = encodeURI(csvContent);
    const link = document.createElement("a");
    link.setAttribute("href", encodedUri);
    link.setAttribute("download", `deposition-analysis-${analysisId}-${new Date().toISOString().slice(0, 10)}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);

    toast({
      title: "Success",
      description: "Analysis exported successfully",
    });
  };

  useEffect(() => {
    if (analysisId) {
      loadAnalysis();
    }
  }, [analysisId]);

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          {showBackButton && (
            <Skeleton className="h-10 w-32" />
          )}
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        
        <div className="space-y-4">
          {[...Array(4)].map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-48" />
              </CardHeader>
              <CardContent>
                <div className="space-y-2">
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-4 w-1/2" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error || !analysis) {
    return (
      <div className="space-y-6">
        {showBackButton && onBack && (
          <Button
            variant="outline"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        )}
        
        <Card>
          <CardContent className="flex flex-col items-center justify-center py-12">
            <AlertCircle className="h-12 w-12 text-gray-400 mb-4" />
            <h3 className="text-lg font-medium mb-2">Analysis Not Found</h3>
            <p className="text-gray-500 text-center mb-4">
              {error || "The analysis you're looking for doesn't exist or couldn't be loaded."}
            </p>
            <Button onClick={loadAnalysis} variant="outline">
              Try Again
            </Button>
          </CardContent>
        </Card>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        {showBackButton && onBack && (
          <Button
            variant="outline"
            onClick={onBack}
            className="flex items-center gap-2"
          >
            <ArrowLeft className="h-4 w-4" />
            Back
          </Button>
        )}
        
        <h2 className="text-2xl font-bold">Analysis Details</h2>
        
        <Button
          variant="outline"
          onClick={downloadAnalysisAsCsv}
          className="flex items-center gap-2"
        >
          <Download className="h-4 w-4" />
          Export CSV
        </Button>
      </div>
      
      <DepositionAnalysis analysis={analysis} />
    </div>
  );
}

/**
 * Hook to use the analysis viewer in other components
 */
export function useAnalysisViewer() {
  const [currentAnalysisId, setCurrentAnalysisId] = useState<string | null>(null);
  
  const viewAnalysis = (analysisId: string) => {
    setCurrentAnalysisId(analysisId);
  };
  
  const closeViewer = () => {
    setCurrentAnalysisId(null);
  };
  
  return {
    currentAnalysisId,
    viewAnalysis,
    closeViewer,
    AnalysisViewer: currentAnalysisId ? (
      <AnalysisViewer 
        analysisId={currentAnalysisId} 
        onBack={closeViewer}
      />
    ) : null
  };
}
