"use client";

import { useState, useEffect, useCallback } from "react";
import { useRouter } from "next/navigation";
import { format } from "date-fns";
import type { DocumentMetadata } from "@/lib/services/document-service";
import { documentService } from "@/lib/services/document-service";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { Skeleton } from "@/components/ui/skeleton";
import {
	ArrowLeft,
	Edit,
	Trash2,
	FileText,
	FileQuestion,
	MessageSquare,
	Lightbulb,
	MoreVertical,
	History,
} from "lucide-react";
import type {
	DepositionPreparation,
	DepositionStatus,
} from "@/lib/types/deposition";
import { depositionService } from "@/lib/services/deposition-service";
import { useToast } from "@/components/ui/use-toast";
import QuestionList from "./question-list";
import GenerateQuestionsForm from "./generate-questions-form";
import AnalyzeTranscriptForm from "./analyze-transcript-form";
import { AnalysisHistory } from "./analysis-history";

const statusColors = {
	DRAFT: "bg-gray-200 text-gray-800",
	IN_PROGRESS: "bg-blue-200 text-blue-800",
	COMPLETED: "bg-green-200 text-green-800",
};

const statusLabels = {
	DRAFT: "Draft",
	IN_PROGRESS: "In Progress",
	COMPLETED: "Completed",
};

interface DepositionDetailsProps {
	id: string;
}

export function DepositionDetails({ id }: DepositionDetailsProps) {
	const [deposition, setDeposition] = useState<DepositionPreparation | null>(
		null
	);
	const [loading, setLoading] = useState(true);
	const [relatedDocuments, setRelatedDocuments] = useState<DocumentMetadata[]>(
		[]
	);
	const [loadingRelatedDocuments, setLoadingRelatedDocuments] = useState(false);
	const router = useRouter();
	const { toast } = useToast();
	const [activeTab, setActiveTab] = useState("details");

	const loadDeposition = useCallback(async () => {
		try {
			setLoading(true);
			const data = await depositionService.getDepositionPreparation(id);
			setDeposition(data);
			if (data?.relatedDocumentIds && data.relatedDocumentIds.length > 0) {
				setLoadingRelatedDocuments(true);
				const docs = await Promise.all(
					data.relatedDocumentIds.map((docId) =>
						documentService.getDocument(docId)
					)
				);
				setRelatedDocuments(
					docs.filter((doc): doc is DocumentMetadata => doc !== null)
				);
				setLoadingRelatedDocuments(false);
			} else {
				setRelatedDocuments([]);
			}
		} catch (error) {
			toast({
				title: "Error",
				description:
					error instanceof Error ? error.message : "Failed to load deposition",
				variant: "destructive",
			});
		} finally {
			setLoading(false);
		}
	}, [id, toast]);

	useEffect(() => {
		loadDeposition();
	}, [loadDeposition]);

	const handleDelete = async () => {
		try {
			await depositionService.deleteDepositionPreparation(id);
			toast({
				title: "Success",
				description: "Deposition preparation deleted successfully",
			});
			router.push("/depositions");
		} catch (error) {
			toast({
				title: "Error",
				description:
					error instanceof Error
						? error.message
						: "Failed to delete deposition",
				variant: "destructive",
			});
		}
	};

	const handleStatusChange = async (status: DepositionStatus) => {
		try {
			await depositionService.updateDepositionPreparation(id, { status });
			toast({
				title: "Success",
				description: "Status updated successfully",
			});
			loadDeposition();
		} catch (error) {
			toast({
				title: "Error",
				description:
					error instanceof Error ? error.message : "Failed to update status",
				variant: "destructive",
			});
		}
	};

	if (loading) {
		return (
			<div className="space-y-4 px-3 sm:px-0">
				<div className="flex items-center gap-2">
					<Button
						variant="outline"
						size="icon"
						disabled
						className="h-8 w-8 sm:h-10 sm:w-10"
					>
						<ArrowLeft className="h-4 w-4" />
					</Button>
					<Skeleton className="h-6 sm:h-8 w-1/3" />
				</div>
				<Card>
					<CardHeader className="p-3 sm:p-6">
						<Skeleton className="h-6 sm:h-8 w-1/2" />
						<Skeleton className="h-3 sm:h-4 w-1/3" />
					</CardHeader>
					<CardContent className="p-3 sm:p-6 space-y-3 sm:space-y-4">
						{[...Array(5)].map((_, i) => (
							<Skeleton key={i} className="h-3 sm:h-4 w-full" />
						))}
					</CardContent>
				</Card>
			</div>
		);
	}

	if (!deposition) {
		return (
			<div className="flex flex-col items-center justify-center py-10">
				<FileText className="h-16 w-16 text-gray-400 mb-4" />
				<h3 className="text-xl font-medium mb-2">Deposition not found</h3>
				<p className="text-gray-500 mb-4">
					The deposition preparation you&apos;re looking for doesn&apos;t exist
					or has been deleted.
				</p>
				<Button
					onClick={() => router.push("/depositions")}
					className="flex items-center gap-2"
				>
					<ArrowLeft className="h-4 w-4" />
					Back to Depositions
				</Button>
			</div>
		);
	}

	return (
		<div className="space-y-4 sm:space-y-6 sm:pt-6">
			<div className="flex items-start justify-between z-20 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-3 sm:p-6 border-b">
				<div className="space-y-1 sm:space-y-2">
					<div className="flex items-center gap-2 sm:gap-3">
						<Button
							variant="outline"
							size="icon"
							onClick={() => router.push("/depositions")}
							className="shrink-0 h-8 w-8 sm:h-10 sm:w-10"
						>
							<ArrowLeft className="h-4 w-4" />
						</Button>
						<div>
							<h1 className="text-lg sm:text-2xl font-bold tracking-tight truncate max-w-[200px] sm:max-w-none">
								{deposition.title}
							</h1>
							<div className="flex flex-wrap items-center gap-2 mt-1">
								<Badge className={`${statusColors[deposition.status]} text-xs`}>
									{statusLabels[deposition.status]}
								</Badge>
								<span className="text-xs sm:text-sm text-muted-foreground">
									Last updated:{" "}
									{new Date(deposition.updatedAt).toLocaleDateString()}
								</span>
							</div>
						</div>
					</div>

					<div className="ml-10 sm:ml-12 space-y-1 sm:space-y-2">
						<p className="text-xs sm:text-sm text-muted-foreground line-clamp-2 sm:line-clamp-none">
							{deposition.description}
						</p>
					</div>
				</div>

				<DropdownMenu>
					<DropdownMenuTrigger asChild>
						<Button variant="outline" size="icon" className="h-10 w-10">
							<MoreVertical className="h-4 w-4" />
						</Button>
					</DropdownMenuTrigger>
					<DropdownMenuContent align="end">
						<DropdownMenuItem
							onClick={() => router.push(`/depositions/${id}/edit`)}
						>
							<Edit className="h-4 w-4 mr-2" />
							Edit
						</DropdownMenuItem>
						<DropdownMenuItem
							onClick={handleDelete}
							className="text-destructive"
						>
							<Trash2 className="h-4 w-4 mr-2" />
							Delete
						</DropdownMenuItem>
					</DropdownMenuContent>
				</DropdownMenu>
			</div>

			<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
				<TabsList className="grid grid-cols-5 gap-1 sm:gap-2 mb-4 sticky top-[52px] sm:top-[76px] z-10 bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 border-b p-1 sm:p-0">
					<TabsTrigger
						value="details"
						className="flex items-center justify-center gap-1 px-1 sm:px-3 py-1.5 text-xs sm:text-sm"
					>
						<FileText className="h-3 w-3 sm:h-4 sm:w-4" />
						<span className="hidden sm:inline">Details</span>
						<span className="sm:hidden">Info</span>
					</TabsTrigger>
					<TabsTrigger
						value="questions"
						className="flex items-center justify-center gap-1 px-1 sm:px-3 py-1.5 text-xs sm:text-sm"
					>
						<MessageSquare className="h-3 w-3 sm:h-4 sm:w-4" />
						<span className="hidden sm:inline">Questions</span>
						<span className="sm:hidden">Q&A</span>
					</TabsTrigger>
					<TabsTrigger
						value="generate"
						className="flex items-center justify-center gap-1 px-1 sm:px-3 py-1.5 text-xs sm:text-sm"
					>
						<Lightbulb className="h-3 w-3 sm:h-4 sm:w-4" />
						<span className="hidden sm:inline">Generate</span>
						<span className="sm:hidden">Gen</span>
					</TabsTrigger>
					<TabsTrigger
						value="analyze"
						className="flex items-center justify-center gap-1 px-1 sm:px-3 py-1.5 text-xs sm:text-sm"
					>
						<FileQuestion className="h-3 w-3 sm:h-4 sm:w-4" />
						<span className="hidden sm:inline">Analyze</span>
						<span className="sm:hidden">Anlyz</span>
					</TabsTrigger>
					<TabsTrigger
						value="history"
						className="flex items-center justify-center gap-1 px-1 sm:px-3 py-1.5 text-xs sm:text-sm"
					>
						<History className="h-3 w-3 sm:h-4 sm:w-4" />
						<span className="hidden sm:inline">History</span>
						<span className="sm:hidden">Hist</span>
					</TabsTrigger>
				</TabsList>

				<TabsContent value="details" className="mt-0">
					<Card>
						<CardHeader>
							<CardTitle>Deposition Details</CardTitle>
							<CardDescription>
								Created on{" "}
								{format(new Date(deposition.createdAt), "MMMM d, yyyy")}
							</CardDescription>
						</CardHeader>
						<CardContent className="space-y-4 sm:space-y-6 p-3 sm:p-6">
							{deposition.description && (
								<div>
									<h3 className="text-sm font-medium text-gray-500 mb-1">
										Description
									</h3>
									<p>{deposition.description}</p>
								</div>
							)}

							<div>
								<h3 className="text-sm font-medium text-gray-500 mb-1">
									Status
								</h3>
								<div className="flex flex-wrap gap-2">
									<Button
										variant={
											deposition.status === "DRAFT" ? "default" : "outline"
										}
										size="sm"
										className="h-8 text-xs sm:text-sm"
										onClick={() => handleStatusChange("DRAFT")}
									>
										Draft
									</Button>
									<Button
										variant={
											deposition.status === "IN_PROGRESS"
												? "default"
												: "outline"
										}
										size="sm"
										className="h-8 text-xs sm:text-sm"
										onClick={() => handleStatusChange("IN_PROGRESS")}
									>
										In Progress
									</Button>
									<Button
										variant={
											deposition.status === "COMPLETED" ? "default" : "outline"
										}
										size="sm"
										className="h-8 text-xs sm:text-sm"
										onClick={() => handleStatusChange("COMPLETED")}
									>
										Completed
									</Button>
								</div>
							</div>

							<div>
								<h3 className="text-sm font-medium text-gray-500 mb-1">
									Target Witnesses
								</h3>
								<div className="flex flex-wrap gap-2">
									{deposition.targetWitnesses.length > 0 ? (
										deposition.targetWitnesses.map((witness, index) => (
											<Badge key={index} variant="secondary">
												{witness}
											</Badge>
										))
									) : (
										<p className="text-gray-500">No witnesses specified</p>
									)}
								</div>
							</div>

							<div>
								<h3 className="text-sm font-medium text-gray-500 mb-1">
									Case Context
								</h3>
								<p className="whitespace-pre-wrap">{deposition.caseContext}</p>
							</div>

							<div>
								<h3 className="text-sm font-medium text-gray-500 mb-1">
									Key Issues
								</h3>
								<div className="flex flex-wrap gap-2">
									{deposition.keyIssues.length > 0 ? (
										deposition.keyIssues.map((issue, index) => (
											<Badge key={index} variant="outline">
												{issue}
											</Badge>
										))
									) : (
										<p className="text-gray-500">No key issues specified</p>
									)}
								</div>
							</div>

							{deposition.relatedDocumentIds &&
								deposition.relatedDocumentIds.length > 0 && (
									<div>
										<h3 className="text-sm font-medium text-gray-500 mb-1">
											Related Documents
										</h3>
										<div className="flex flex-col gap-2">
											{loadingRelatedDocuments ? (
												[...Array(deposition.relatedDocumentIds.length)].map(
													(_, i) => <Skeleton key={i} className="h-10 w-full" />
												)
											) : relatedDocuments.length > 0 ? (
												relatedDocuments.map((doc) => (
													<Button
														key={doc.id}
														variant="outline"
														className="justify-start w-full"
														onClick={() =>
															router.push(`/dashboard/documents/${doc.id}`)
														}
													>
														<FileText className="h-4 w-4 mr-2 shrink-0" />
														<span className="truncate">{doc.filename}</span>
													</Button>
												))
											) : (
												<p className="text-gray-500">
													No related documents found
												</p>
											)}
										</div>
									</div>
								)}
						</CardContent>
					</Card>
				</TabsContent>

				<TabsContent value="questions" className="mt-0">
					<QuestionList
						depositionId={id}
						questions={deposition.questions}
						onQuestionsChanged={loadDeposition}
					/>
				</TabsContent>
				<TabsContent value="generate" className="mt-0">
					<GenerateQuestionsForm
						depositionId={id}
						deposition={deposition}
						onQuestionsGenerated={loadDeposition}
					/>
				</TabsContent>
				<TabsContent value="analyze" className="mt-0">
					<AnalyzeTranscriptForm
						depositionId={id}
						caseContext={deposition.caseContext}
					/>
				</TabsContent>
				<TabsContent value="history" className="mt-0">
					<AnalysisHistory depositionId={id} />
				</TabsContent>
			</Tabs>
		</div>
	);
}

export default DepositionDetails;
