"use client";

import React, { useState } from "react";

import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import {
	PlusCircle,
	Edit,
	Trash2,
	AlertCircle,
	Search,
	ChevronDown,
	ChevronUp,
	Filter,
	X,
	Clock,
	ShieldCheck,
	GraduationCap,
	AlertTriangle,
	HelpCircle,
	Lightbulb,
	MoreVertical,
} from "lucide-react";
import {
	DepositionQuestion,
	QuestionCategory,
	QuestionPriority,
	CreateDepositionQuestionDto,
	UpdateDepositionQuestionDto,
} from "@/lib/types/deposition";
import { depositionService } from "@/lib/services/deposition-service";
import { useToast } from "@/components/ui/use-toast";

const priorityColors = {
	high: "bg-red-100 text-red-800 border-red-200",
	medium: "bg-yellow-100 text-yellow-800 border-yellow-200",
	low: "bg-green-100 text-green-800 border-green-200",
};

type NormalizedCategory = 'general' | 'documentation' | 'credibility' | 'consistency' | 'expert-qualification' | 'impeachment';

const categoryMappings: Record<string, NormalizedCategory> = {
  // General category variations
  background: "general",
  general: "general",
  basic: "general",
  "basic-info": "general",
  "background-info": "general",
  "witness-background": "general",
  "witness-background-info": "general",

  // Credibility category variations
  credibility: "credibility",
  "witness-reliability": "credibility",
  reliability: "credibility",
  "fact-finding": "credibility",
  "facts-and-evidence": "credibility",

  // Consistency category variations
  consistency: "consistency",
  timeline: "consistency",
  chronology: "consistency",
  "sequence-of-events": "consistency",
  "timeline-sequence": "consistency",

  // Documentation category variations
  documentation: "documentation",
  "document-review": "documentation",
  "document-analysis": "documentation",
  "records-review": "documentation",
  "evidence-review": "documentation",
  "evidence-gathering": "documentation",
  contractterms: "documentation",
  damages: "documentation",

  // Expert qualification category variations
  expert: "expert-qualification",
  "expert-qualification": "expert-qualification",
  expertise: "expert-qualification",
  qualifications: "expert-qualification",
  "expert-background": "expert-qualification",

  // Impeachment category variations
  impeachment: "impeachment",
  "credibility-challenge": "impeachment",
  "prior-inconsistency": "impeachment",
  contradiction: "impeachment",
  bias: "impeachment",
};

const categoryColors: Record<NormalizedCategory, string> = {
  general: "bg-gray-100 text-gray-800 border-gray-200",
  documentation: "bg-blue-100 text-blue-800 border-blue-200",
  credibility: "bg-purple-100 text-purple-800 border-purple-200",
  consistency: "bg-amber-100 text-amber-800 border-amber-200",
  "expert-qualification": "bg-indigo-100 text-indigo-800 border-indigo-200",
  impeachment: "bg-red-100 text-red-800 border-red-200",
};

const categoryIcons: Record<NormalizedCategory, React.ReactElement> = {
  general: <HelpCircle className="h-4 w-4" />,
  documentation: <Clock className="h-4 w-4" />,
  credibility: <ShieldCheck className="h-4 w-4" />,
  consistency: <AlertTriangle className="h-4 w-4" />,
  "expert-qualification": <GraduationCap className="h-4 w-4" />,
  impeachment: <AlertTriangle className="h-4 w-4" />,
};

const categoryLabels: Record<NormalizedCategory, string> = {
  general: "Basic Information & Background",
  documentation: "Documents & Evidence",
  credibility: "Witness Credibility",
  consistency: "Timeline & Consistency",
  "expert-qualification": "Expert Qualifications",
  impeachment: "Impeachment",
};

const categoryDescriptions: Record<NormalizedCategory, string> = {
  general: "Basic facts, witness background, and general information",
  documentation: "Document review, records analysis, and evidence gathering",
  credibility: "Witness reliability, fact finding, and evidence verification",
  consistency: "Timeline verification, chronological sequence of events",
  "expert-qualification": "Expert background, qualifications, and expertise",
  impeachment: "Credibility challenges, prior inconsistencies, and bias",
};

// Helper function to get normalized category
const getNormalizedCategory = (category: string): NormalizedCategory => {
  const normalized = category.toLowerCase();
  return categoryMappings[normalized] || "general";
};

interface QuestionListProps {
	depositionId: string;
	questions: DepositionQuestion[];
	onQuestionsChanged: (questions: DepositionQuestion[]) => void;
}

export function QuestionList({
	depositionId,
	questions,
	onQuestionsChanged,
}: QuestionListProps) {
	const [searchTerm, setSearchTerm] = useState("");
	const [filters, setFilters] = useState({
		category: "all",
		priority: "all",
	});
	const [expandedQuestions, setExpandedQuestions] = useState<
		Record<string, boolean>
	>({});

	const toggleQuestion = (questionId: string) => {
		setExpandedQuestions((prev) => ({
			...prev,
			[questionId]: !prev[questionId],
		}));
	};

	const filteredQuestions = questions.filter((question) => {
		const matchesSearch =
			question.text.toLowerCase().includes(searchTerm.toLowerCase()) ||
			question.purpose.toLowerCase().includes(searchTerm.toLowerCase());

		const matchesCategory =
			filters.category === "all" || question.category === filters.category;
		const matchesPriority =
			filters.priority === "all" || question.priority === filters.priority;

		return matchesSearch && matchesCategory && matchesPriority;
	});

	const clearFilters = () => {
		setFilters({ category: "", priority: "" });
		setSearchTerm("");
	};

	const hasActiveFilters = filters.category || filters.priority || searchTerm;
	const { toast } = useToast();
	const [isAddDialogOpen, setIsAddDialogOpen] = useState(false);
	const [isEditDialogOpen, setIsEditDialogOpen] = useState(false);
	const [isDeleteDialogOpen, setIsDeleteDialogOpen] = useState(false);
	const [currentQuestion, setCurrentQuestion] =
		useState<DepositionQuestion | null>(null);

	// New question form state
	const [newQuestion, setNewQuestion] = useState<CreateDepositionQuestionDto>({
		text: "",
		category: "GENERAL",
		purpose: "",
		priority: "medium",
		notes: "",
		suggestedFollowUps: [],
		relatedDocuments: [],
	});

	// Edit question form state
	const [editQuestion, setEditQuestion] = useState<UpdateDepositionQuestionDto>(
		{
			text: "",
			purpose: "",
			priority: "medium",
			notes: "",
		}
	);

	// New follow-up state
	const [newFollowUp, setNewFollowUp] = useState("");

	const handleAddQuestion = async () => {
		try {
			await depositionService.addQuestion(depositionId, newQuestion);
			// Refresh the parent component to get the updated questions
			const updatedDeposition =
				await depositionService.getDepositionPreparation(depositionId);
			toast({
				title: "Success",
				description: "Question added successfully",
			});
			setIsAddDialogOpen(false);
			resetNewQuestionForm();
			onQuestionsChanged(updatedDeposition.questions || []);
		} catch (error) {
			toast({
				title: "Error",
				description:
					error instanceof Error ? error.message : "Failed to add question",
				variant: "destructive",
			});
		}
	};

	const handleEditQuestion = async () => {
		if (!currentQuestion) return;

		try {
			await depositionService.updateQuestion(
				depositionId,
				currentQuestion.id,
				editQuestion
			);
			// Refresh the parent component to get the updated questions
			const updatedDeposition =
				await depositionService.getDepositionPreparation(depositionId);
			toast({
				title: "Success",
				description: "Question updated successfully",
			});
			setIsEditDialogOpen(false);
			onQuestionsChanged(updatedDeposition.questions || []);
		} catch (error) {
			toast({
				title: "Error",
				description:
					error instanceof Error ? error.message : "Failed to update question",
				variant: "destructive",
			});
		}
	};

	const handleDeleteQuestion = async () => {
		if (!currentQuestion) return;

		try {
			await depositionService.deleteQuestion(depositionId, currentQuestion.id);
			// Refresh the parent component to get the updated questions
			const updatedDeposition =
				await depositionService.getDepositionPreparation(depositionId);
			toast({
				title: "Success",
				description: "Question deleted successfully",
			});
			setIsDeleteDialogOpen(false);
			onQuestionsChanged(updatedDeposition.questions || []);
		} catch (error) {
			toast({
				title: "Error",
				description:
					error instanceof Error ? error.message : "Failed to delete question",
				variant: "destructive",
			});
		}
	};

	const resetNewQuestionForm = () => {
		setNewQuestion({
			text: "",
			category: "GENERAL",
			purpose: "",
			priority: "medium",
			notes: "",
			suggestedFollowUps: [],
			relatedDocuments: [],
		});
	};

	const openEditDialog = (question: DepositionQuestion) => {
		setCurrentQuestion(question);
		setEditQuestion({
			text: question.text,
			purpose: question.purpose,
			priority: question.priority,
			notes: question.notes || "",
		});
		setIsEditDialogOpen(true);
	};

	const openDeleteDialog = (question: DepositionQuestion) => {
		setCurrentQuestion(question);
		setIsDeleteDialogOpen(true);
	};

	const addFollowUp = () => {
		if (newFollowUp.trim()) {
			setNewQuestion({
				...newQuestion,
				suggestedFollowUps: [
					...(newQuestion.suggestedFollowUps || []),
					newFollowUp.trim(),
				],
			});
			setNewFollowUp("");
		}
	};

	const removeFollowUp = (index: number) => {
		if (!newQuestion.suggestedFollowUps) return;

		setNewQuestion({
			...newQuestion,
			suggestedFollowUps: newQuestion.suggestedFollowUps.filter(
				(_, i) => i !== index
			),
		});
	};

	return (
		<div className="space-y-6">
			{/* Header with search and filters */}
			<div className="bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60 p-4 rounded-lg border">
				<div className="flex flex-col space-y-4">
					<div className="flex flex-col sm:flex-row gap-4 items-start sm:items-end">
						<div className="relative flex-1 w-full">
							<div className="relative">
								<Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-muted-foreground" />
								<Input
									placeholder="Search questions by text or purpose..."
									className="pl-10 h-11 text-base"
									value={searchTerm}
									onChange={(e) => setSearchTerm(e.target.value)}
								/>
							</div>
						</div>

						<div className="flex flex-wrap gap-2 w-full sm:w-auto">
							<Select
								value={filters.category}
								onValueChange={(value) =>
									setFilters((prev) => ({ ...prev, category: value }))
								}
							>
								<SelectTrigger className="w-full sm:w-[180px] h-11">
									<Filter className="h-4 w-4 mr-2 opacity-50" />
									<span className="truncate">
										{filters.category === "all"
											? "All Categories"
											: categoryLabels[
													filters.category as keyof typeof categoryLabels
											  ]}
									</span>
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Categories</SelectItem>
									{Object.entries(categoryLabels).map(([value, label]) => (
										<SelectItem
											key={value}
											value={value}
											className="flex items-center gap-2"
										>
											{categoryIcons[value as keyof typeof categoryIcons]}
											{label}
										</SelectItem>
									))}
								</SelectContent>
							</Select>

							<Select
								value={filters.priority}
								onValueChange={(value) =>
									setFilters((prev) => ({ ...prev, priority: value }))
								}
							>
								<SelectTrigger className="w-full sm:w-[150px] h-11">
									<Filter className="h-4 w-4 mr-2 opacity-50" />
									<span className="capitalize">
										{filters.priority === "all"
											? "All Priorities"
											: filters.priority}
									</span>
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="all">All Priorities</SelectItem>
									<SelectItem value="high" className="text-red-600">
										High
									</SelectItem>
									<SelectItem value="medium" className="text-amber-600">
										Medium
									</SelectItem>
									<SelectItem value="low" className="text-green-600">
										Low
									</SelectItem>
								</SelectContent>
							</Select>

							{hasActiveFilters && (
								<Button
									variant="ghost"
									onClick={clearFilters}
									className="h-11 text-muted-foreground hover:text-foreground"
								>
									<X className="h-4 w-4 mr-1.5" />
									Clear filters
								</Button>
							)}
						</div>
					</div>

					<div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 pt-2">
						<div className="flex items-center">
							<h2 className="text-xl font-semibold tracking-tight">
								Deposition Questions
							</h2>
							<span className="ml-3 px-2.5 py-0.5 text-xs font-medium rounded-full bg-muted text-muted-foreground">
								{filteredQuestions.length}{" "}
								{filteredQuestions.length === 1 ? "question" : "questions"}
							</span>
						</div>

						<Button
							onClick={() => setIsAddDialogOpen(true)}
							className="w-full sm:w-auto h-11 px-6"
						>
							<PlusCircle className="h-4 w-4 mr-2" />
							Add Question
						</Button>
					</div>
				</div>
			</div>

			{/* Questions List */}
			<div className="space-y-4">
				{filteredQuestions.length === 0 ? (
					<div className="flex flex-col items-center justify-center py-16 px-4 text-center rounded-lg border-2 border-dashed border-muted-foreground/20 bg-muted/30">
						<AlertCircle className="h-14 w-14 text-muted-foreground/60 mb-4" />
						<h3 className="text-xl font-semibold tracking-tight mb-2">
							{hasActiveFilters
								? "No matching questions found"
								: "No questions yet"}
						</h3>
						<p className="text-muted-foreground mb-6 max-w-md">
							{hasActiveFilters
								? "Try adjusting your search or filters to find what you're looking for."
								: "Get started by adding your first question or use the AI question generator."}
						</p>
						<div className="flex flex-col sm:flex-row gap-3">
							{hasActiveFilters ? (
								<Button
									variant="outline"
									onClick={clearFilters}
									className="h-11 px-6"
								>
									Clear all filters
								</Button>
							) : (
								<Button
									onClick={() => setIsAddDialogOpen(true)}
									className="h-11 px-6"
								>
									<PlusCircle className="h-4 w-4 mr-2" />
									Add Question
								</Button>
							)}
							{!hasActiveFilters && (
								<Button variant="outline" className="h-11 px-6">
									<Lightbulb className="h-4 w-4 mr-2" />
									Generate with AI
								</Button>
							)}
						</div>
					</div>
				) : (
					<div className="grid gap-3 sm:gap-4 md:grid-cols-2 lg:grid-cols-1">
						{filteredQuestions.map((question) => (
							<div
								key={question.id}
								className="group relative bg-card border rounded-lg overflow-hidden hover:shadow-md transition-all duration-200"
							>
								<div
									className="p-3 sm:p-5 cursor-pointer"
									onClick={() => toggleQuestion(question.id)}
								>
									<div className="flex items-start gap-3">
										<div className="flex-1 min-w-0">
											<h3 className="font-medium text-sm sm:text-base leading-snug mb-1.5 sm:mb-2">
												<span className={!expandedQuestions[question.id] ? "line-clamp-2" : ""}>
													{question.text}
												</span>
											</h3>
											<div className="flex flex-col sm:flex-row sm:items-center text-xs sm:text-sm text-muted-foreground mb-2">
												<span className="font-medium shrink-0">Witness:&nbsp;</span>
												<span className={!expandedQuestions[question.id] ? "line-clamp-1" : ""}>
													{question.targetWitness || "Not specified"}
												</span>
											</div>
											<div className="flex flex-wrap items-center gap-1.5 sm:gap-2">
												<Badge
													variant="outline"
													className={`${categoryColors[getNormalizedCategory(question.category.toLowerCase())]} px-1.5 sm:px-2 py-0.5 sm:py-1 text-xs font-medium flex items-center gap-1`}
													title={categoryDescriptions[getNormalizedCategory(question.category.toLowerCase())]}
												>
													<span className="shrink-0">{categoryIcons[getNormalizedCategory(question.category.toLowerCase())]}</span>
													<span className={!expandedQuestions[question.id] ? "line-clamp-1" : ""}>
														{categoryLabels[getNormalizedCategory(question.category.toLowerCase())]}
													</span>
												</Badge>
												<Badge
													variant="outline"
													className={`${priorityColors[question.priority]} px-1.5 sm:px-2 py-0.5 sm:py-1 text-xs font-medium capitalize`}
												>
													{question.priority}
												</Badge>
											</div>
										</div>

										<div className="flex items-center gap-1 sm:opacity-0 group-hover:sm:opacity-100 transition-opacity">
											<DropdownMenu>
												<DropdownMenuTrigger asChild>
													<Button
														variant="ghost"
														size="sm"
														className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
														onClick={(e) => e.stopPropagation()}
													>
														<MoreVertical className="h-3.5 w-3.5" />
													</Button>
												</DropdownMenuTrigger>
												<DropdownMenuContent align="end">
													<DropdownMenuItem
														onClick={(e) => {
															e.stopPropagation();
															openEditDialog(question);
														}}
													>
														<Edit className="h-3.5 w-3.5 mr-2" />
														Edit Question
													</DropdownMenuItem>
													<DropdownMenuItem
														className="text-destructive"
														onClick={(e) => {
															e.stopPropagation();
															openDeleteDialog(question);
														}}
													>
														<Trash2 className="h-3.5 w-3.5 mr-2" />
														Delete Question
													</DropdownMenuItem>
												</DropdownMenuContent>
											</DropdownMenu>
											<Button
												variant="ghost"
												size="sm"
												className="h-8 w-8 p-0 text-muted-foreground hover:text-foreground"
												onClick={(e) => {
													e.stopPropagation();
													toggleQuestion(question.id);
												}}
											>
												{expandedQuestions[question.id] ? (
													<ChevronUp className="h-3.5 w-3.5" />
												) : (
													<span className="flex items-center">
														<ChevronDown className="h-3.5 w-3.5" />
														<span className="sr-only">Show full content</span>
													</span>
												)}
											</Button>
										</div>
									</div>

									{expandedQuestions[question.id] && (
										<div className="mt-4 pl-3 space-y-4 border-l-2 border-border/50">
											<div className="bg-muted/30 p-3 rounded-lg">
												<h5 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-1.5">
													Purpose
												</h5>
												<p className="text-sm text-foreground/90">
													{question.purpose}
												</p>
											</div>

											{question.notes && (
												<div className="bg-muted/10 p-3 rounded-lg">
													<h5 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-1.5">
														Notes
													</h5>
													<p className="text-sm text-foreground/80 whitespace-pre-line">
														{question.notes}
													</p>
												</div>
											)}

											{question.suggestedFollowUps &&
												question.suggestedFollowUps.length > 0 && (
													<div className="bg-muted/5 p-3 rounded-lg border border-muted/30">
														<h5 className="text-xs font-semibold text-muted-foreground uppercase tracking-wider mb-2">
															Suggested Follow-ups
														</h5>
														<ul className="space-y-2">
															{question.suggestedFollowUps.map(
																(followUp, index) => (
																	<li
																		key={index}
																		className="flex items-start gap-2"
																	>
																		<span className="text-muted-foreground/70 mt-1">
																			•
																		</span>
																		<span className="text-sm text-foreground/90">
																			{followUp}
																		</span>
																	</li>
																)
															)}
														</ul>
													</div>
												)}
										</div>
									)}
								</div>
							</div>
						))}
					</div>
				)}
			</div>

			{/* Add Question Dialog */}
			<Dialog open={isAddDialogOpen} onOpenChange={setIsAddDialogOpen}>
				<DialogContent className="max-w-2xl">
					<DialogHeader>
						<DialogTitle>Add New Question</DialogTitle>
						<DialogDescription>
							Create a new question for this deposition preparation.
						</DialogDescription>
					</DialogHeader>
					<div className="grid gap-4 py-4">
						<div className="grid gap-2">
							<Label htmlFor="question-text">Question Text</Label>
							<Textarea
								id="question-text"
								value={newQuestion.text}
								onChange={(e) =>
									setNewQuestion({ ...newQuestion, text: e.target.value })
								}
								placeholder="Enter the question text"
								className="min-h-[100px]"
							/>
						</div>
						<div className="grid grid-cols-2 gap-4">
							<div className="grid gap-2">
								<Label htmlFor="category">Category</Label>
								<Select
									value={newQuestion.category}
									onValueChange={(value: QuestionCategory) =>
										setNewQuestion({ ...newQuestion, category: value })
									}
								>
									<SelectTrigger id="category">
										<SelectValue placeholder="Select category" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="timeline">Timeline</SelectItem>
										<SelectItem value="credibility">Credibility</SelectItem>
										<SelectItem value="expert_qualification">
											Expert Qualification
										</SelectItem>
										<SelectItem value="impeachment">Impeachment</SelectItem>
										<SelectItem value="other">Other</SelectItem>
									</SelectContent>
								</Select>
							</div>
							<div className="grid gap-2">
								<Label htmlFor="priority">Priority</Label>
								<Select
									value={newQuestion.priority}
									onValueChange={(value: QuestionPriority) =>
										setNewQuestion({ ...newQuestion, priority: value })
									}
								>
									<SelectTrigger id="priority">
										<SelectValue placeholder="Select priority" />
									</SelectTrigger>
									<SelectContent>
										<SelectItem value="high">High</SelectItem>
										<SelectItem value="medium">Medium</SelectItem>
										<SelectItem value="low">Low</SelectItem>
									</SelectContent>
								</Select>
							</div>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="purpose">Purpose</Label>
							<Input
								id="purpose"
								value={newQuestion.purpose}
								onChange={(e) =>
									setNewQuestion({ ...newQuestion, purpose: e.target.value })
								}
								placeholder="Why are you asking this question?"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="notes">Notes (Optional)</Label>
							<Textarea
								id="notes"
								value={newQuestion.notes}
								onChange={(e) =>
									setNewQuestion({ ...newQuestion, notes: e.target.value })
								}
								placeholder="Additional notes or context"
							/>
						</div>
						<div className="grid gap-2">
							<Label>Suggested Follow-up Questions (Optional)</Label>
							<div className="flex flex-wrap gap-2 mb-2">
								{newQuestion.suggestedFollowUps?.map((followUp, index) => (
									<div
										key={index}
										className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md flex items-center gap-2"
									>
										<span>{followUp}</span>
										<button
											type="button"
											onClick={() => removeFollowUp(index)}
											className="text-secondary-foreground/70 hover:text-secondary-foreground"
										>
											×
										</button>
									</div>
								))}
							</div>
							<div className="flex gap-2">
								<Input
									value={newFollowUp}
									onChange={(e) => setNewFollowUp(e.target.value)}
									placeholder="Add a follow-up question"
									className="flex-1"
									onKeyDown={(e) => {
										if (e.key === "Enter") {
											e.preventDefault();
											addFollowUp();
										}
									}}
								/>
								<Button type="button" onClick={addFollowUp} variant="secondary">
									Add
								</Button>
							</div>
						</div>
					</div>
					<DialogFooter>
						<Button variant="outline" onClick={() => setIsAddDialogOpen(false)}>
							Cancel
						</Button>
						<Button onClick={handleAddQuestion}>Add Question</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Edit Question Dialog */}
			<Dialog open={isEditDialogOpen} onOpenChange={setIsEditDialogOpen}>
				<DialogContent className="max-w-2xl">
					<DialogHeader>
						<DialogTitle>Edit Question</DialogTitle>
						<DialogDescription>
							Update this deposition question.
						</DialogDescription>
					</DialogHeader>
					<div className="grid gap-4 py-4">
						<div className="grid gap-2">
							<Label htmlFor="edit-question-text">Question Text</Label>
							<Textarea
								id="edit-question-text"
								value={editQuestion.text}
								onChange={(e) =>
									setEditQuestion({ ...editQuestion, text: e.target.value })
								}
								placeholder="Enter the question text"
								className="min-h-[100px]"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="edit-purpose">Purpose</Label>
							<Input
								id="edit-purpose"
								value={editQuestion.purpose}
								onChange={(e) =>
									setEditQuestion({ ...editQuestion, purpose: e.target.value })
								}
								placeholder="Why are you asking this question?"
							/>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="edit-priority">Priority</Label>
							<Select
								value={editQuestion.priority}
								onValueChange={(value: QuestionPriority) =>
									setEditQuestion({ ...editQuestion, priority: value })
								}
							>
								<SelectTrigger id="edit-priority">
									<SelectValue placeholder="Select priority" />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="high">High</SelectItem>
									<SelectItem value="medium">Medium</SelectItem>
									<SelectItem value="low">Low</SelectItem>
								</SelectContent>
							</Select>
						</div>
						<div className="grid gap-2">
							<Label htmlFor="edit-notes">Notes (Optional)</Label>
							<Textarea
								id="edit-notes"
								value={editQuestion.notes}
								onChange={(e) =>
									setEditQuestion({ ...editQuestion, notes: e.target.value })
								}
								placeholder="Additional notes or context"
							/>
						</div>
					</div>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsEditDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button onClick={handleEditQuestion}>Update Question</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>

			{/* Delete Question Dialog */}
			<Dialog open={isDeleteDialogOpen} onOpenChange={setIsDeleteDialogOpen}>
				<DialogContent>
					<DialogHeader>
						<DialogTitle>Delete Question</DialogTitle>
						<DialogDescription>
							Are you sure you want to delete this question? This action cannot
							be undone.
						</DialogDescription>
					</DialogHeader>
					<DialogFooter>
						<Button
							variant="outline"
							onClick={() => setIsDeleteDialogOpen(false)}
						>
							Cancel
						</Button>
						<Button variant="destructive" onClick={handleDeleteQuestion}>
							Delete
						</Button>
					</DialogFooter>
				</DialogContent>
			</Dialog>
		</div>
	);
}

export default QuestionList;
