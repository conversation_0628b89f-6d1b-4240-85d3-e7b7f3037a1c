"use client";

import React, { useState } from "react";
import { Loader2, FileText, Download, FileQuestion } from "lucide-react";

interface ApiValidationError {
	field: string;
	constraints: string[];
}

interface ApiErrorResponse {
	statusCode: number;
	message: string;
	error: string;
	details?: ApiValidationError[];
	path: string;
	timestamp: string;
}
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
	CardFooter,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { useToast } from "@/components/ui/use-toast";
import {
	AnalyzeDepositionDto,
	DepositionAnalysis,
} from "@/lib/types/deposition";
import { depositionService } from "@/lib/services/deposition-service";
import { DepositionAnalysis as DepositionAnalysisComponent } from "./deposition-analysis";

interface AnalyzeTranscriptFormProps {
	depositionId?: string;
	caseContext: string;
	onAnalysisComplete?: (analysis: DepositionAnalysis) => void;
}

export function AnalyzeTranscriptForm({
	depositionId,
	caseContext,
	onAnalysisComplete,
}: AnalyzeTranscriptFormProps) {
	const { toast } = useToast();
	const [loading, setLoading] = useState(false);
	const [transcript, setTranscript] = useState("");
	const [focusAreas, setFocusAreas] = useState<string[]>([]);
	const [newFocusArea, setNewFocusArea] = useState("");
	const [overrideContext, setOverrideContext] = useState(false);
	const [customContext, setCustomContext] = useState("");
	const [analysisResult, setAnalysisResult] =
		useState<DepositionAnalysis | null>(null);

	const addFocusArea = () => {
		if (newFocusArea.trim()) {
			setFocusAreas([...focusAreas, newFocusArea.trim()]);
			setNewFocusArea("");
		}
	};

	const removeFocusArea = (index: number) => {
		setFocusAreas(focusAreas.filter((_, i) => i !== index));
	};

	const handleAnalyzeTranscript = async () => {
		if (!transcript.trim()) {
			toast({
				title: "Error",
				description: "Please provide a transcript to analyze",
				variant: "destructive",
			});
			return;
		}

		try {
			setLoading(true);

			const data: AnalyzeDepositionDto = {
				transcript,
				caseContext: overrideContext ? customContext : caseContext,
				focusAreas:
					focusAreas.length > 0
						? focusAreas
						: ["credibility", "inconsistencies", "cross-examination"],
				depositionId: depositionId,
			};

			const result = await depositionService.analyzeDepositionTranscript(data);
			setAnalysisResult(result);

			if (onAnalysisComplete) {
				onAnalysisComplete(result);
			}

			toast({
				title: "Success",
				description: "Transcript analyzed successfully",
			});
		} catch (error) {
			// Handle API validation errors
			if (error instanceof Error) {
				try {
					const apiError = JSON.parse(error.message) as ApiErrorResponse;
					if (apiError.details) {
						// Show validation details in the error message
						const errors = apiError.details
							.map(
								(detail: ApiValidationError) =>
									`${detail.field}: ${detail.constraints.join(", ")}`
							)
							.join("\n");

						toast({
							title: "Validation Error",
							description: errors,
							variant: "destructive",
						});
					} else {
						toast({
							title: "Error",
							description: apiError.message || "Failed to analyze transcript",
							variant: "destructive",
						});
					}
				} catch {
					// If not JSON, show the original error message
					toast({
						title: "Error",
						description: error.message,
						variant: "destructive",
					});
				}
			} else {
				toast({
					title: "Error",
					description: "Failed to analyze transcript",
					variant: "destructive",
				});
			}
		} finally {
			setLoading(false);
		}
	};

	const downloadAnalysisAsCsv = () => {
		if (!analysisResult) return;

		// Create CSV content
		let csvContent = "data:text/csv;charset=utf-8,";

		// Add header
		csvContent +=
			"Analysis Type,Speaker,Statement,Credibility Score,Confidence,Reasoning\n";

		// Add key testimony analysis
		analysisResult.keyTestimonyAnalysis.forEach((item) => {
			csvContent += `Key Testimony,${item.speaker},"${item.statement.replace(
				/"/g,
				'""'
			)}",${item.credibilityScore},${item.confidence},"${item.reasoning.replace(
				/"/g,
				'""'
			)}"\n`;
		});

		// Add inconsistencies
		analysisResult.inconsistencies.forEach((inconsistency) => {
			csvContent += `Inconsistency,${
				inconsistency.witness
			},"${inconsistency.statement1.replace(/"/g, '""')}",,,${
				inconsistency.severity
			}\n`;
			csvContent += `Inconsistency,${
				inconsistency.witness
			},"${inconsistency.statement2.replace(/"/g, '""')}",,,${
				inconsistency.severity
			}\n`;
		});

		// Add cross-examination suggestions
		analysisResult.crossExaminationSuggestions.forEach((item) => {
			csvContent += `Cross-Examination,,"${item.question.replace(
				/"/g,
				'""'
			)}",,,"${item.purpose.replace(/"/g, '""')}"\n`;
		});

		// Create download link
		const encodedUri = encodeURI(csvContent);
		const link = document.createElement("a");
		link.setAttribute("href", encodedUri);
		link.setAttribute(
			"download",
			`deposition-analysis-${new Date().toISOString().slice(0, 10)}.csv`
		);
		document.body.appendChild(link);
		link.click();
		document.body.removeChild(link);
	};

	if (analysisResult) {
		return (
			<div className="space-y-6">
				<div className="flex justify-between items-center">
					<h2 className="text-2xl font-bold">Deposition Analysis</h2>
					<div className="flex gap-2">
						<Button
							variant="outline"
							onClick={() => setAnalysisResult(null)}
							className="flex items-center gap-2"
						>
							<FileText className="h-4 w-4" />
							New Analysis
						</Button>
						<Button
							variant="outline"
							onClick={downloadAnalysisAsCsv}
							className="flex items-center gap-2"
						>
							<Download className="h-4 w-4" />
							Export CSV
						</Button>
					</div>
				</div>

				<DepositionAnalysisComponent analysis={analysisResult} />
			</div>
		);
	}

	return (
		<div className="space-y-6">
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<FileQuestion className="h-5 w-5" />
						Analyze Deposition Transcript
					</CardTitle>
					<CardDescription>
						Use AI to analyze a deposition transcript for credibility,
						inconsistencies, and cross-examination suggestions
					</CardDescription>
				</CardHeader>
				<CardContent className="space-y-6">
					<div className="grid gap-2">
						<Label htmlFor="transcript">Deposition Transcript</Label>
						<Textarea
							id="transcript"
							value={transcript}
							onChange={(e) => setTranscript(e.target.value)}
							placeholder="Paste the deposition transcript here"
							className="min-h-[300px]"
						/>
					</div>

					<div className="grid gap-2">
						<div className="flex items-center space-x-2">
							<Checkbox
								id="override-context"
								checked={overrideContext}
								onCheckedChange={(checked) =>
									setOverrideContext(checked === true)
								}
							/>
							<Label htmlFor="override-context">Override case context</Label>
						</div>
						{overrideContext ? (
							<Textarea
								value={customContext}
								onChange={(e) => setCustomContext(e.target.value)}
								placeholder="Enter custom case context"
								className="min-h-[100px]"
							/>
						) : (
							<div className="bg-muted p-3 rounded-md">
								<p className="text-sm text-muted-foreground whitespace-pre-wrap">
									{caseContext}
								</p>
							</div>
						)}
					</div>

					<div className="grid gap-2">
						<Label>Focus Areas</Label>
						<div className="flex flex-wrap gap-2 mb-2">
							{focusAreas.map((area, index) => (
								<div
									key={index}
									className="bg-secondary text-secondary-foreground px-3 py-1 rounded-md flex items-center gap-2"
								>
									<span>{area}</span>
									<button
										type="button"
										onClick={() => removeFocusArea(index)}
										className="text-secondary-foreground/70 hover:text-secondary-foreground"
									>
										×
									</button>
								</div>
							))}
						</div>
						<div className="flex gap-2">
							<Input
								value={newFocusArea}
								onChange={(e) => setNewFocusArea(e.target.value)}
								placeholder="Add a focus area"
								className="flex-1"
								onKeyDown={(e) => {
									if (e.key === "Enter") {
										e.preventDefault();
										addFocusArea();
									}
								}}
							/>
							<Button type="button" onClick={addFocusArea} variant="secondary">
								Add
							</Button>
						</div>
					</div>
				</CardContent>
				<CardFooter>
					<Button
						onClick={handleAnalyzeTranscript}
						disabled={
							loading ||
							!transcript.trim() ||
							focusAreas.length === 0 ||
							(!overrideContext && !caseContext) ||
							(overrideContext && !customContext.trim())
						}
						className="w-full"
					>
						{loading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Analyzing Transcript...
							</>
						) : (
							<>
								<FileQuestion className="mr-2 h-4 w-4" />
								Analyze Transcript
							</>
						)}
					</Button>
				</CardFooter>
			</Card>
		</div>
	);
}

export default AnalyzeTranscriptForm;
