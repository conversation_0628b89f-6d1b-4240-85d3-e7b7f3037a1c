import React from "react";
import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>eader, CardTitle } from "@/components/ui/card";
import { Progress } from "@/components/ui/progress";
import { Badge } from "@/components/ui/badge";
import { DepositionAnalysis as DepositionAnalysisType } from "@/lib/types/deposition";

interface DepositionAnalysisProps {
	analysis: DepositionAnalysisType;
	className?: string;
}

export function DepositionAnalysis({
	analysis,
	className,
}: DepositionAnalysisProps) {
	const {
		overallCredibilityScore,
		keyTestimonyAnalysis,
		crossExaminationSuggestions,
		inconsistencies,
		keyFindings,
		potentialImpeachmentOpportunities,
		timelineAnalysis,
		metadata,
	} = analysis;

	return (
		<div className={`space-y-6 ${className}`}>
			{/* Overall Credibility Score */}
			<Card>
				<CardHeader>
					<CardTitle>Overall Credibility Score</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-2">
						<div className="flex justify-between">
							<span className="text-sm font-medium">Credibility</span>
							<span className="text-sm font-medium">
								{Math.round(overallCredibilityScore * 100)}%
							</span>
						</div>
						<Progress value={overallCredibilityScore * 100} className="h-2" />
						<p className="text-sm text-muted-foreground">
							Based on analysis of testimony and evidence
						</p>
					</div>
				</CardContent>
			</Card>

			{/* Key Findings */}
			{keyFindings.length > 0 && (
				<Card>
					<CardHeader>
						<CardTitle>Key Findings</CardTitle>
					</CardHeader>
					<CardContent>
						<ul className="list-disc pl-5 space-y-2">
							{keyFindings.map((finding, index) => (
								<li key={index} className="text-sm">
									{finding}
								</li>
							))}
						</ul>
					</CardContent>
				</Card>
			)}

			{/* Key Testimony Analysis */}
			{keyTestimonyAnalysis.length > 0 && (
				<Card>
					<CardHeader>
						<CardTitle>Key Testimony Analysis</CardTitle>
					</CardHeader>
					<CardContent className="space-y-6">
						{keyTestimonyAnalysis.map((testimony, index) => (
							<div key={index} className="border-l-4 border-border pl-4 py-2">
								<div className="flex justify-between items-start">
									<h4 className="font-medium">{testimony.speaker}</h4>
									<Badge variant="outline" className="ml-2">
										{Math.round(testimony.credibilityScore * 100)}% Credible
									</Badge>
								</div>
								<p className="text-sm text-muted-foreground mt-1">
									{testimony.statement}
								</p>
								<div className="mt-2">
									<h5 className="text-sm font-medium">Reasoning:</h5>
									<p className="text-sm text-muted-foreground">
										{testimony.reasoning}
									</p>
								</div>
								<div className="mt-2">
									<h5 className="text-sm font-medium">Confidence:</h5>
									<p className="text-sm text-muted-foreground">
										{testimony.confidence}
									</p>
								</div>
								{testimony.supportingEvidence.length > 0 && (
									<div className="mt-2">
										<h5 className="text-sm font-medium mt-2">
											Supporting Evidence:
										</h5>
										<ul className="list-disc pl-5 space-y-1 mt-1">
											{testimony.supportingEvidence.map((evidence, i) => (
												<li key={i} className="text-sm">
													{evidence}
												</li>
											))}
										</ul>
									</div>
								)}
							</div>
						))}
					</CardContent>
				</Card>
			)}

			{/* Cross-Examination Suggestions */}
			{crossExaminationSuggestions.length > 0 && (
				<Card>
					<CardHeader>
						<CardTitle>Cross-Examination Suggestions</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						{crossExaminationSuggestions.map((suggestion, index) => (
							<div key={index} className="border-l-4 border-border pl-4 py-2">
								<div className="flex justify-between items-start">
									<h4 className="font-medium">{suggestion.topic}</h4>
								</div>
								<div className="mt-2">
									<h5 className="text-sm font-medium">Question:</h5>
									<p className="text-sm text-muted-foreground">
										{suggestion.question}
									</p>
								</div>
								<div className="mt-2">
									<h5 className="text-sm font-medium">Purpose:</h5>
									<p className="text-sm text-muted-foreground">
										{suggestion.purpose}
									</p>
								</div>
								<div className="mt-2">
									<h5 className="text-sm font-medium">Legal Basis:</h5>
									<p className="text-sm text-muted-foreground">
										{suggestion.legalBasis}
									</p>
								</div>
								{suggestion.suggestedFollowUps &&
									suggestion.suggestedFollowUps.length > 0 && (
										<div className="mt-2">
											<h5 className="text-sm font-medium">
												Follow-up Questions:
											</h5>
											<ul className="list-disc pl-5 space-y-1 mt-1">
												{suggestion.suggestedFollowUps.map((followUp, i) => (
													<li key={i} className="text-sm text-muted-foreground">
														{followUp}
													</li>
												))}
											</ul>
										</div>
									)}
							</div>
						))}
					</CardContent>
				</Card>
			)}

			{/* Inconsistencies */}
			{inconsistencies.length > 0 && (
				<Card>
					<CardHeader>
						<CardTitle>Identified Inconsistencies</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						{inconsistencies.map((inconsistency, index) => (
							<div key={index} className="border-l-4 border-border pl-4 py-2">
								<div className="flex justify-between items-start">
									<h4 className="font-medium">{inconsistency.witness}</h4>
									<Badge
										variant={
											inconsistency.severity === "HIGH"
												? "destructive"
												: inconsistency.severity === "MEDIUM"
												? "secondary"
												: "default"
										}
										className="ml-2"
									>
										{inconsistency.severity}
									</Badge>
								</div>
								<div className="mt-2 space-y-2">
									<div>
										<p className="text-sm font-medium">Statement 1:</p>
										<p className="text-sm text-muted-foreground">
											{inconsistency.statement1}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium">Statement 2:</p>
										<p className="text-sm text-muted-foreground">
											{inconsistency.statement2}
										</p>
									</div>
									<div>
										<p className="text-sm font-medium">Context:</p>
										<p className="text-sm text-muted-foreground">
											{inconsistency.context}
										</p>
									</div>
								</div>
							</div>
						))}
					</CardContent>
				</Card>
			)}

			{/* Potential Impeachment Opportunities */}
			{potentialImpeachmentOpportunities &&
				potentialImpeachmentOpportunities.length > 0 && (
					<Card>
						<CardHeader>
							<CardTitle>Potential Impeachment Opportunities</CardTitle>
						</CardHeader>
						<CardContent className="space-y-4">
							{potentialImpeachmentOpportunities.map((opportunity, index) => (
								<div key={index} className="border-l-4 border-border pl-4 py-2">
									<div className="mt-2 space-y-2">
										<div>
											<p className="text-sm font-medium">Statement:</p>
											<p className="text-sm text-muted-foreground">
												{opportunity.statement}
											</p>
										</div>
										<div>
											<p className="text-sm font-medium">
												Conflicting Evidence:
											</p>
											<p className="text-sm text-muted-foreground">
												{opportunity.conflictingEvidence}
											</p>
										</div>
										<div>
											<p className="text-sm font-medium">Suggested Approach:</p>
											<p className="text-sm text-muted-foreground">
												{opportunity.suggestedApproach}
											</p>
										</div>
									</div>
								</div>
							))}
						</CardContent>
					</Card>
				)}

			{/* Timeline Analysis */}
			{timelineAnalysis && timelineAnalysis.length > 0 && (
				<Card>
					<CardHeader>
						<CardTitle>Timeline Analysis</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						{timelineAnalysis.map((event, index) => (
							<div key={index} className="border-l-4 border-border pl-4 py-2">
								<div className="flex justify-between items-start">
									<h4 className="font-medium">{event.event}</h4>
									<Badge variant="outline" className="ml-2">
										{Math.round(event.relevance * 100)}% Relevant
									</Badge>
								</div>
								<div className="mt-2 space-y-2">
									<div>
										<p className="text-sm font-medium">Timestamp:</p>
										<p className="text-sm text-muted-foreground">
											{event.timestamp}
										</p>
									</div>
									{event.notes && (
										<div>
											<p className="text-sm font-medium">Notes:</p>
											<p className="text-sm text-muted-foreground">
												{event.notes}
											</p>
										</div>
									)}
								</div>
							</div>
						))}
					</CardContent>
				</Card>
			)}

			{/* Metadata */}
			<Card>
				<CardHeader>
					<CardTitle>Analysis Details</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="grid grid-cols-2 gap-4 text-sm">
						<div>
							<p className="font-medium">Analyzed At</p>
							<p className="text-muted-foreground">
								{new Date(metadata.analyzedAt).toLocaleString()}
							</p>
						</div>
						<div>
							<p className="font-medium">Analysis Duration</p>
							<p className="text-muted-foreground">
								{metadata.analysisDurationMs}ms
							</p>
						</div>
						<div>
							<p className="font-medium">Model Used</p>
							<p className="text-muted-foreground">{metadata.modelUsed}</p>
						</div>
						<div>
							<p className="font-medium">Confidence</p>
							<p className="text-muted-foreground">{metadata.confidence}</p>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
