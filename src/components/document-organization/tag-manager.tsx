"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { TagBadge } from "./tag-badge"  
import { tagService } from "@/lib/services/tag-service"
import type { Tag, CreateTagDto } from "@/lib/types/document-organization"
import { Plus, TagIcon, Loader2 } from "lucide-react"
import { HexColorPicker } from "react-colorful"

interface TagManagerProps {
  documentId?: string
  onTagsChange?: (tags: Tag[]) => void
}

export function TagManager({ documentId, onTagsChange }: TagManagerProps) {
  const [tags, setTags] = useState<Tag[]>([])
  const [documentTags, setDocumentTags] = useState<Tag[]>([])
  const [loading, setLoading] = useState(true)
  const [newTagName, setNewTagName] = useState("")
  const [newTagColor, setNewTagColor] = useState("#3366FF")
  const [creatingTag, setCreatingTag] = useState(false)
  const [colorPickerOpen, setColorPickerOpen] = useState(false)

  useEffect(() => {
    const loadTags = async () => {
      try {
        setLoading(true)
        const allTags = await tagService.getTags()
        setTags(allTags)

        if (documentId) {
          const docTags = await tagService.getTagsForDocument(documentId)
          setDocumentTags(docTags)
        }
      } catch (error) {
        console.error("Error loading tags:", error)
      } finally {
        setLoading(false)
      }
    }

    loadTags()
  }, [documentId])

  const handleCreateTag = async () => {
    if (!newTagName.trim()) return

    try {
      setCreatingTag(true)
      const tagData: CreateTagDto = {
        name: newTagName.trim(),
        color: newTagColor,
      }

      const newTag = await tagService.createTag(tagData)
      setTags([...tags, newTag])
      setNewTagName("")
      setNewTagColor("#3366FF")
    } catch (error) {
      console.error("Error creating tag:", error)
    } finally {
      setCreatingTag(false)
    }
  }

  const handleAddTagToDocument = async (tag: Tag) => {
    if (!documentId) return

    try {
      await tagService.addDocumentToTag(tag.id, documentId)
      const updatedDocTags = [...documentTags, tag]
      setDocumentTags(updatedDocTags)

      if (onTagsChange) {
        onTagsChange(updatedDocTags)
      }
    } catch (error) {
      console.error("Error adding tag to document:", error)
    }
  }

  const handleRemoveTagFromDocument = async (tag: Tag) => {
    if (!documentId) return

    try {
      await tagService.removeDocumentFromTag(tag.id, documentId)
      const updatedDocTags = documentTags.filter((t) => t.id !== tag.id)
      setDocumentTags(updatedDocTags)

      if (onTagsChange) {
        onTagsChange(updatedDocTags)
      }
    } catch (error) {
      console.error("Error removing tag from document:", error)
    }
  }

  const isTagAssignedToDocument = (tagId: string) => {
    return documentTags.some((tag) => tag.id === tagId)
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center">
        <TagIcon className="h-5 w-5 mr-2" />
        <h3 className="text-lg font-medium">Tags</h3>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-5 w-5 animate-spin mr-2" />
          <span>Loading tags...</span>
        </div>
      ) : (
        <>
          {documentId && (
            <div>
              <Label>Document Tags</Label>
              <div className="flex flex-wrap mt-2">
                {documentTags.length > 0 ? (
                  documentTags.map((tag) => (
                    <TagBadge key={tag.id} tag={tag} onRemove={() => handleRemoveTagFromDocument(tag)} />
                  ))
                ) : (
                  <span className="text-sm text-muted-foreground">No tags assigned to this document</span>
                )}
              </div>
            </div>
          )}

          <div>
            <div className="flex justify-between items-center mb-2">
              <Label>Available Tags</Label>
              <Popover open={colorPickerOpen} onOpenChange={setColorPickerOpen}>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="h-8">
                    <Plus className="h-4 w-4 mr-1" />
                    New Tag
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80">
                  <div className="space-y-4">
                    <h4 className="font-medium">Create New Tag</h4>
                    <div className="space-y-2">
                      <Label htmlFor="tag-name">Tag Name</Label>
                      <Input
                        id="tag-name"
                        value={newTagName}
                        onChange={(e) => setNewTagName(e.target.value)}
                        placeholder="Enter tag name"
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Tag Color</Label>
                      <div
                        className="w-full h-8 rounded cursor-pointer"
                        style={{ backgroundColor: newTagColor }}
                        onClick={() => setColorPickerOpen(true)}
                      />
                      <HexColorPicker color={newTagColor} onChange={setNewTagColor} />
                    </div>
                    <div className="flex justify-end">
                      <Button onClick={handleCreateTag} disabled={!newTagName.trim() || creatingTag}>
                        {creatingTag && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                        Create Tag
                      </Button>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>
            </div>

            <div className="flex flex-wrap mt-2 border rounded-md p-3 max-h-40 overflow-y-auto">
              {tags.length > 0 ? (
                tags.map((tag) => (
                  <div key={tag.id} className="mr-1 mb-1">
                    {documentId ? (
                      <TagBadge
                        tag={tag}
                        onClick={() => {
                          if (isTagAssignedToDocument(tag.id)) {
                            handleRemoveTagFromDocument(tag)
                          } else {
                            handleAddTagToDocument(tag)
                          }
                        }}
                      />
                    ) : (
                      <TagBadge tag={tag} />
                    )}
                  </div>
                ))
              ) : (
                <span className="text-sm text-muted-foreground">No tags available</span>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  )
}
