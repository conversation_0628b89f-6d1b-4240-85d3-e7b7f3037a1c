"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Switch } from "@/components/ui/switch"
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, <PERSON>alogTrigger } from "@/components/ui/dialog"
import { Card, CardContent, CardHeader, CardTitle, CardDescription, CardFooter } from "@/components/ui/card"
import { savedSearchService } from "@/lib/services/saved-search-service"
import type {
  SavedSearch,
  CreateSavedSearchDto,
  SearchCriteria,
  ExecuteSearchResult,
} from "@/lib/types/document-organization"
import { Search, Plus, Loader2, Calendar, FileText, Bell, Share2, Trash2 } from "lucide-react"

interface SavedSearchManagerProps {
  onSearchExecuted?: (results: ExecuteSearchResult) => void
}

export function SavedSearchManager({ onSearchExecuted }: SavedSearchManagerProps) {
  const [savedSearches, setSavedSearches] = useState<SavedSearch[]>([])
  const [loading, setLoading] = useState(true)
  const [executing, setExecuting] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)

  // New search form state
  const [newSearchName, setNewSearchName] = useState("")
  const [newSearchDescription, setNewSearchDescription] = useState("")
  const [searchText, setSearchText] = useState("")
  const [documentType, setDocumentType] = useState("")
  const [dateFrom, setDateFrom] = useState("")
  const [dateTo, setDateTo] = useState("")
  const [notificationsEnabled, setNotificationsEnabled] = useState(false)
  const [notificationFrequency, setNotificationFrequency] = useState<"daily" | "weekly" | "monthly">("daily")
  const [isCreating, setIsCreating] = useState(false)

  useEffect(() => {
    const loadSavedSearches = async () => {
      try {
        setLoading(true)
        const searches = await savedSearchService.getSavedSearches()
        setSavedSearches(searches)
      } catch (error) {
        console.error("Error loading saved searches:", error)
      } finally {
        setLoading(false)
      }
    }

    loadSavedSearches()
  }, [])

  const handleCreateSavedSearch = async () => {
    if (!newSearchName.trim()) return

    try {
      setIsCreating(true)

      const criteria: SearchCriteria = {}
      if (searchText) criteria.text = searchText
      if (documentType) criteria.documentType = documentType
      if (dateFrom) criteria.dateFrom = dateFrom
      if (dateTo) criteria.dateTo = dateTo

      const searchData: CreateSavedSearchDto = {
        name: newSearchName.trim(),
        description: newSearchDescription.trim() || undefined,
        criteria,
        notificationsEnabled,
        notificationFrequency: notificationsEnabled ? notificationFrequency : undefined,
      }

      const newSearch = await savedSearchService.createSavedSearch(searchData)
      setSavedSearches([...savedSearches, newSearch])

      // Reset form
      setNewSearchName("")
      setNewSearchDescription("")
      setSearchText("")
      setDocumentType("")
      setDateFrom("")
      setDateTo("")
      setNotificationsEnabled(false)
      setNotificationFrequency("daily")

      setIsDialogOpen(false)
    } catch (error) {
      console.error("Error creating saved search:", error)
    } finally {
      setIsCreating(false)
    }
  }

  const handleExecuteSearch = async (searchId: string) => {
    try {
      setExecuting(true)
      const results = await savedSearchService.executeSavedSearch(searchId)

      if (onSearchExecuted) {
        onSearchExecuted(results)
      }
    } catch (error) {
      console.error("Error executing saved search:", error)
    } finally {
      setExecuting(false)
    }
  }

  const handleDeleteSearch = async (searchId: string) => {
    try {
      await savedSearchService.deleteSavedSearch(searchId)
      setSavedSearches(savedSearches.filter((search) => search.id !== searchId))
    } catch (error) {
      console.error("Error deleting saved search:", error)
    }
  }

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <Search className="h-5 w-5 mr-2" />
          <h3 className="text-lg font-medium">Saved Searches</h3>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="h-8">
              <Plus className="h-4 w-4 mr-1" />
              New Search
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-md">
            <DialogHeader>
              <DialogTitle>Create Saved Search</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="search-name">Search Name</Label>
                <Input
                  id="search-name"
                  value={newSearchName}
                  onChange={(e) => setNewSearchName(e.target.value)}
                  placeholder="Enter search name"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="search-description">Description (Optional)</Label>
                <Input
                  id="search-description"
                  value={newSearchDescription}
                  onChange={(e) => setNewSearchDescription(e.target.value)}
                  placeholder="Enter search description"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="search-text">Search Text</Label>
                <Input
                  id="search-text"
                  value={searchText}
                  onChange={(e) => setSearchText(e.target.value)}
                  placeholder="Enter keywords"
                />
              </div>

              <div className="space-y-2">
                <Label htmlFor="document-type">Document Type</Label>
                <select
                  id="document-type"
                  className="w-full p-2 border rounded-md"
                  value={documentType}
                  onChange={(e) => setDocumentType(e.target.value)}
                >
                  <option value="">All Types</option>
                  <option value="contract">Contract</option>
                  <option value="agreement">Agreement</option>
                  <option value="policy">Policy</option>
                  <option value="legislation">Legislation</option>
                  <option value="court_filing">Court Filing</option>
                  <option value="legal_opinion">Legal Opinion</option>
                </select>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div className="space-y-2">
                  <Label htmlFor="date-from">Date From</Label>
                  <Input id="date-from" type="date" value={dateFrom} onChange={(e) => setDateFrom(e.target.value)} />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="date-to">Date To</Label>
                  <Input id="date-to" type="date" value={dateTo} onChange={(e) => setDateTo(e.target.value)} />
                </div>
              </div>

              <div className="space-y-2">
                <div className="flex items-center justify-between">
                  <Label htmlFor="notifications-enabled">Enable Notifications</Label>
                  <Switch
                    id="notifications-enabled"
                    checked={notificationsEnabled}
                    onCheckedChange={setNotificationsEnabled}
                  />
                </div>

                {notificationsEnabled && (
                  <div className="mt-2">
                    <Label htmlFor="notification-frequency">Notification Frequency</Label>
                    <select
                      id="notification-frequency"
                      className="w-full p-2 border rounded-md mt-1"
                      value={notificationFrequency}
                      onChange={(e) => setNotificationFrequency(e.target.value as "daily" | "weekly" | "monthly")}
                    >
                      <option value="daily">Daily</option>
                      <option value="weekly">Weekly</option>
                      <option value="monthly">Monthly</option>
                    </select>
                  </div>
                )}
              </div>

              <div className="flex justify-end">
                <Button onClick={handleCreateSavedSearch} disabled={!newSearchName.trim() || isCreating}>
                  {isCreating && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Save Search
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {loading ? (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-5 w-5 animate-spin mr-2" />
          <span>Loading saved searches...</span>
        </div>
      ) : (
        <div className="space-y-3 max-h-96 overflow-y-auto pr-1">
          {savedSearches.length > 0 ? (
            savedSearches.map((search) => (
              <Card key={search.id} className="bg-card dark:bg-[#1e1e1e]">
                <CardHeader className="pb-2">
                  <div className="flex justify-between items-start">
                    <div>
                      <CardTitle className="text-base">{search.name}</CardTitle>
                      {search.description && <CardDescription>{search.description}</CardDescription>}
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-8 w-8"
                      onClick={() => handleDeleteSearch(search.id)}
                    >
                      <Trash2 className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="pb-2">
                  <div className="space-y-2 text-sm">
                    {search.criteria.text && (
                      <div className="flex items-center">
                        <Search className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>{search.criteria.text}</span>
                      </div>
                    )}

                    {search.criteria.documentType && (
                      <div className="flex items-center">
                        <FileText className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>Type: {search.criteria.documentType}</span>
                      </div>
                    )}

                    {(search.criteria.dateFrom || search.criteria.dateTo) && (
                      <div className="flex items-center">
                        <Calendar className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>
                          {search.criteria.dateFrom && `From: ${search.criteria.dateFrom}`}
                          {search.criteria.dateFrom && search.criteria.dateTo && " | "}
                          {search.criteria.dateTo && `To: ${search.criteria.dateTo}`}
                        </span>
                      </div>
                    )}

                    {search.notificationsEnabled && (
                      <div className="flex items-center">
                        <Bell className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>Notifications: {search.notificationFrequency}</span>
                      </div>
                    )}

                    {search.sharedWith && search.sharedWith.length > 0 && (
                      <div className="flex items-center">
                        <Share2 className="h-4 w-4 mr-2 text-muted-foreground" />
                        <span>Shared with {search.sharedWith.length} users</span>
                      </div>
                    )}
                  </div>
                </CardContent>
                <CardFooter>
                  <Button
                    variant="outline"
                    size="sm"
                    className="w-full"
                    onClick={() => handleExecuteSearch(search.id)}
                    disabled={executing}
                  >
                    {executing && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                    Execute Search
                  </Button>
                </CardFooter>
              </Card>
            ))
          ) : (
            <div className="text-center p-4 border rounded-md">
              <Search className="h-10 w-10 mx-auto mb-2 text-muted-foreground opacity-50" />
              <p className="text-muted-foreground">No saved searches found</p>
              <p className="text-sm text-muted-foreground mt-1">Create a new search to get started</p>
            </div>
          )}
        </div>
      )}
    </div>
  )
}
