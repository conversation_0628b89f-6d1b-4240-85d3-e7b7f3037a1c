"use client"

import { useState, useEffect } from "react"
import { folderService } from "@/lib/services/folder-service"
import { type FolderDocument } from "@/lib/types/document-organization"
import { Loader2, File, Trash2 } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { useToast } from "@/hooks/use-toast"
import { useFolderDocuments } from "@/hooks/use-folder-documents"

interface FolderDocumentsProps {
  folderId?: string
}

export function FolderDocuments({ folderId }: FolderDocumentsProps) {
  const [folderDocuments, setFolderDocuments] = useState<FolderDocument[]>([])
  const [loading, setLoading] = useState(false)
  const { toast } = useToast()
  const { removeDocumentsFromFolder } = useFolderDocuments()

  useEffect(() => {
    const loadDocuments = async () => {
      if (!folderId) {
        setFolderDocuments([])
        return
      }

      try {
        setLoading(true)
        // Use the new API endpoint to get documents in the folder directly
        const documents = await folderService.getFolderDocuments(folderId)
        console.log("Folder documents loaded:", documents)
        setFolderDocuments(documents)
      } catch (error) {
        console.error("Error loading folder documents:", error)
        toast({
          title: "Error loading documents",
          description: "Could not load documents for this folder",
          variant: "destructive",
        })
      } finally {
        setLoading(false)
      }
    }

    loadDocuments()
  }, [folderId, toast])

  const handleRemoveDocument = async (documentId: string) => {
    if (!folderId) return
    
    try {
      await removeDocumentsFromFolder(folderId, [documentId])
      
      // Update the local state to remove the document
      setFolderDocuments(folderDocuments.filter(doc => doc.id !== documentId))
      
      toast({
        title: "Document removed",
        description: "Document has been removed from this folder",
        variant: "default",
      })
    } catch (error) {
      console.error("Error removing document:", error)
      toast({
        title: "Error removing document",
        description: "Could not remove document from folder",
        variant: "destructive",
      })
    }
  }

  if (loading) {
    return (
      <div className="flex items-center justify-center py-8">
        <Loader2 className="h-6 w-6 animate-spin mr-2" />
        <span>Loading documents...</span>
      </div>
    )
  }

  if (!folderId) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        Select a folder to view its documents
      </div>
    )
  }

  if (folderDocuments.length === 0) {
    return (
      <div className="text-center py-8 text-muted-foreground">
        No documents in this folder
      </div>
    )
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-medium">Documents in this folder</h3>
      <div className="grid gap-4">
        {folderDocuments.map((doc) => (
          <div 
            key={doc.id} 
            className="flex items-center justify-between p-3 border rounded-md hover:bg-secondary/20"
          >
            <div className="flex items-center">
              <File className="h-5 w-5 mr-3 text-blue-500" />
              <div>
                <div className="font-medium">{doc.title || doc.filename}</div>
                <div className="text-sm text-muted-foreground flex gap-3">
                  <span>{doc.extension.toUpperCase()} • {formatFileSize(doc.size)}</span>
                  <span>Uploaded: {formatDate(doc.uploadDate)}</span>
                </div>
              </div>
            </div>
            <Button 
              variant="ghost" 
              size="icon"
              onClick={() => handleRemoveDocument(doc.id)}
              title="Remove from folder"
            >
              <Trash2 className="h-4 w-4 text-destructive" />
            </Button>
          </div>
        ))}
      </div>
    </div>
  )
}

// Helper function to format file size
function formatFileSize(bytes: number): string {
  if (bytes < 1024) return bytes + ' B';
  const kb = bytes / 1024;
  if (kb < 1024) return kb.toFixed(1) + ' KB';
  const mb = kb / 1024;
  return mb.toFixed(1) + ' MB';
}

// Helper function to format date
function formatDate(dateString: string): string {
  const date = new Date(dateString);
  if (isNaN(date.getTime())) {
    return "Unknown date";
  }
  return date.toLocaleDateString('en-US', { 
    year: 'numeric', 
    month: 'short', 
    day: 'numeric' 
  });
}
