"use client"

import { useState, useEffect } from "react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { <PERSON><PERSON>, DialogContent, <PERSON>alogHeader, <PERSON><PERSON><PERSON><PERSON>le, DialogTrigger } from "@/components/ui/dialog"
import { folderService } from "@/lib/services/folder-service"
import type { Folder, CreateFolderDto, FolderDocument } from "@/lib/types/document-organization"
import { ChevronRight, ChevronDown, FolderIcon, Loader2, FolderPlus, Plus, File } from "lucide-react"
import { type DocumentMetadata } from "@/lib/services/document-service"
import { useToast } from "@/hooks/use-toast"
import { useFolderDocuments } from "@/hooks/use-folder-documents"

interface FolderTreeProps {
  documentId?: string
  onFolderSelect?: (folder: Folder) => void
}

interface FolderNodeProps {
  folder: Folder
  folders: Folder[]
  level: number
  documentId?: string
  selectedFolderId?: string
  onFolderSelect: (folder: Folder) => void
  onAddDocumentClick: (folderId: string) => void
}

function FolderNode({
  folder,
  folders,
  level,
  documentId,
  selectedFolderId,
  onFolderSelect,
  onAddDocumentClick,
}: FolderNodeProps) {
  const [isOpen, setIsOpen] = useState(false)
  const [folderDocuments, setFolderDocuments] = useState<FolderDocument[]>([])
  const [loadingDocuments, setLoadingDocuments] = useState(false)
  const childFolders = folders.filter((f) => f.parentId === folder.id)
  const hasChildren = childFolders.length > 0
  const isSelected = selectedFolderId === folder.id

  useEffect(() => {
    const loadDocuments = async () => {
      if (isOpen && isSelected) {
        try {
          setLoadingDocuments(true)
          const documents = await folderService.getFolderDocuments(folder.id)
          setFolderDocuments(documents)
        } catch (error) {
          console.error(`Error loading documents for folder ${folder.id}:`, error)
        } finally {
          setLoadingDocuments(false)
        }
      }
    }

    loadDocuments()
  }, [folder.id, isOpen, isSelected])

  const toggleFolder = () => {
    setIsOpen(!isOpen)
  }

  const handleFolderClick = (e: React.MouseEvent) => {
    e.stopPropagation()
    onFolderSelect(folder)
    if (!isOpen) {
      setIsOpen(true)
    }
  }

  return (
    <div className="select-none">
      <div
        className={`flex items-center justify-between py-1 px-2 rounded-sm hover:bg-secondary/50 cursor-pointer ${
          isSelected ? "bg-secondary/50" : ""
        }`}
        onClick={handleFolderClick}
      >
        <div className="flex items-center flex-1 overflow-hidden">
          <div className="w-4 h-4 flex items-center justify-center mr-1" onClick={(e) => {
            e.stopPropagation()
            toggleFolder()
          }}>
            {hasChildren && (
              isOpen ? <ChevronDown className="h-4 w-4" /> : <ChevronRight className="h-4 w-4" />
            )}
          </div>
          <FolderIcon className="h-4 w-4 mr-2 text-muted-foreground" />
          <span className="text-sm truncate">{folder.name}</span>
        </div>
        
        <Button 
          variant="ghost" 
          size="icon" 
          className="h-6 w-6 ml-1"
          onClick={(e) => {
            e.stopPropagation();
            onAddDocumentClick(folder.id);
          }}
          title="Add document to this folder"
        >
          <Plus className="h-4 w-4" />
        </Button>
      </div>

      {isOpen && (
        <div className="pl-4">
          {/* Show folder documents */}
          {isSelected && (
            <div className="mt-1 mb-2">
              {loadingDocuments ? (
                <div className="flex items-center py-1 text-xs text-muted-foreground">
                  <Loader2 className="h-3 w-3 animate-spin mr-1" />
                  <span>Loading documents...</span>
                </div>
              ) : folderDocuments.length > 0 ? (
                <div className="space-y-1">
                  {folderDocuments.map((doc) => (
                    <div 
                      key={doc.id} 
                      className="flex items-center justify-between py-1 px-2 rounded-sm text-xs hover:bg-secondary/30"
                    >
                      <div className="flex items-center overflow-hidden">
                        <File className="h-3 w-3 mr-2 text-blue-500" />
                        <span className="truncate">{doc.title || doc.filename}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="py-1 text-xs text-muted-foreground">
                  No documents in this folder
                </div>
              )}
            </div>
          )}

          {/* Show child folders */}
          {hasChildren && childFolders.map((childFolder) => (
            <FolderNode
              key={childFolder.id}
              folder={childFolder}
              folders={folders}
              level={level + 1}
              documentId={documentId}
              selectedFolderId={selectedFolderId}
              onFolderSelect={onFolderSelect}
              onAddDocumentClick={onAddDocumentClick}
            />
          ))}
        </div>
      )}
    </div>
  )
}

export function FolderTree({ documentId, onFolderSelect }: FolderTreeProps) {
  const {
    folders,
    documents,
    selectedDocument,
    setSelectedDocument,
    selectedFolderId,
    setSelectedFolderId,
    loading,
    loadingDocuments,
    addingToFolder,
    addDocumentToFolder
  } = useFolderDocuments(documentId);
  
  const [newFolderName, setNewFolderName] = useState("")
  const [newFolderParentId, setNewFolderParentId] = useState<string | null>(null)
  const [isCreatingFolder, setIsCreatingFolder] = useState(false)
  const [isDialogOpen, setIsDialogOpen] = useState(false)
  const [targetFolderId, setTargetFolderId] = useState<string | null>(null)
  const [isDocumentDialogOpen, setIsDocumentDialogOpen] = useState(false)
  const { toast } = useToast()

  const handleCreateFolder = async () => {
    if (!newFolderName.trim()) return

    try {
      setIsCreatingFolder(true)
      const folderData: CreateFolderDto = {
        name: newFolderName.trim(),
        parentId: newFolderParentId,
      }

      const newFolder = await folderService.createFolder(folderData)
      toast({
        title: "Folder created",
        description: `Folder "${newFolder.name}" has been created successfully.`,
        variant: "default",
      })
      setNewFolderName("")
      setNewFolderParentId(null)
      setIsDialogOpen(false)
    } catch (error) {
      console.error("Error creating folder:", error)
      toast({
        title: "Error creating folder",
        description: error instanceof Error 
          ? `Error: ${error.message}. Please try again.` 
          : "Unknown error occurred. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsCreatingFolder(false)
    }
  }

  const handleFolderSelect = (folder: Folder) => {
    setSelectedFolderId(folder.id)

    if (onFolderSelect) {
      onFolderSelect(folder)
    }
  }

  const handleAddDocumentClick = (folderId: string) => {
    if (documentId) {
      // If we already have a document ID (from props), use it directly
      addDocumentToFolder(folderId, documentId);
    } else {
      // Otherwise, open the document selector
      setTargetFolderId(folderId);
      setIsDocumentDialogOpen(true);
    }
  }

  const handleDocumentSelect = () => {
    console.log("handleDocumentSelect called");
    console.log("targetFolderId:", targetFolderId);
    console.log("selectedDocument:", selectedDocument);
    
    if (targetFolderId && selectedDocument) {
      addDocumentToFolder(targetFolderId, selectedDocument.id);
      setIsDocumentDialogOpen(false);
      setTargetFolderId(null);
      setSelectedDocument(null); // Reset selected document after adding
    }
  }

  const rootFolders = folders.filter((folder) => folder.parentId === null)

  return (
    <div className="space-y-4">
      <div className="flex items-center justify-between">
        <div className="flex items-center">
          <FolderIcon className="h-5 w-5 mr-2" />
          <h3 className="text-lg font-medium">Folders</h3>
        </div>

        <Dialog open={isDialogOpen} onOpenChange={setIsDialogOpen}>
          <DialogTrigger asChild>
            <Button variant="outline" size="sm" className="h-8">
              <FolderPlus className="h-4 w-4 mr-1" />
              New Folder
            </Button>
          </DialogTrigger>
          <DialogContent>
            <DialogHeader>
              <DialogTitle>Create New Folder</DialogTitle>
            </DialogHeader>
            <div className="space-y-4 py-2">
              <div className="space-y-2">
                <Label htmlFor="folder-name">Folder Name</Label>
                <Input
                  id="folder-name"
                  value={newFolderName}
                  onChange={(e) => setNewFolderName(e.target.value)}
                  placeholder="Enter folder name"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="parent-folder">Parent Folder (Optional)</Label>
                <select
                  id="parent-folder"
                  className="w-full p-2 border rounded-md"
                  value={newFolderParentId || ""}
                  onChange={(e) => setNewFolderParentId(e.target.value || null)}
                >
                  <option value="">Root (No Parent)</option>
                  {folders.map((folder) => (
                    <option key={folder.id} value={folder.id}>
                      {folder.path}
                    </option>
                  ))}
                </select>
              </div>
              <div className="flex justify-end">
                <Button onClick={handleCreateFolder} disabled={!newFolderName.trim() || isCreatingFolder}>
                  {isCreatingFolder && <Loader2 className="h-4 w-4 mr-2 animate-spin" />}
                  Create Folder
                </Button>
              </div>
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Document Selector Dialog */}
      <Dialog open={isDocumentDialogOpen} onOpenChange={setIsDocumentDialogOpen}>
        <DialogContent className="sm:max-w-md">
          <DialogHeader>
            <DialogTitle>Add Document to Folder</DialogTitle>
          </DialogHeader>
          <div className="space-y-4 py-4">
            {loadingDocuments ? (
              <div className="flex items-center justify-center py-4">
                <Loader2 className="h-5 w-5 animate-spin mr-2" />
                <span>Loading documents...</span>
              </div>
            ) : documents && documents.length > 0 ? (
              <div className="space-y-2">
                <Label htmlFor="document-select">Select Document</Label>
                <select
                  id="document-select"
                  className="w-full p-2 border rounded-md"
                  value={selectedDocument?.id || ""}
                  onChange={(e) => {
                    const docId = e.target.value;
                    if (docId) {
                      const doc = documents.find((d: DocumentMetadata) => d.id === docId);
                      console.log("Selected document from dropdown:", doc);
                      setSelectedDocument(doc || null);
                    } else {
                      setSelectedDocument(null);
                    }
                  }}
                >
                  <option value="">Choose a document</option>
                  {documents.map((doc: DocumentMetadata) => (
                    <option key={doc.id} value={doc.id}>
                      {doc.name || doc.filename || `Document ${doc.id}`}
                    </option>
                  ))}
                </select>
                <div className="flex justify-end gap-2 mt-4">
                  <Button 
                    variant="outline"
                    onClick={() => {
                      setIsDocumentDialogOpen(false);
                      setSelectedDocument(null);
                    }}
                  >
                    Cancel
                  </Button>
                  <Button 
                    onClick={handleDocumentSelect}
                    disabled={!selectedDocument}
                  >
                    Add to Folder
                  </Button>
                </div>
              </div>
            ) : (
              <p className="text-muted-foreground">No documents available</p>
            )}
          </div>
        </DialogContent>
      </Dialog>

      {(loading || loadingDocuments) ? (
        <div className="flex items-center justify-center py-4">
          <Loader2 className="h-5 w-5 animate-spin mr-2" />
          <span>Loading...</span>
        </div>
      ) : (
        <>
          <div className="bg-secondary/20 rounded-md p-3 mb-3 text-sm">
            <p>
              {documentId ? 
                "Click the + button next to a folder to add the current document to it." : 
                "Click on a folder to view its documents. Click the + button to add a document."
              }
            </p>
            {selectedFolderId && documentId && (
              <p className="mt-2">
                Document is currently in folder: <span className="font-medium">{folders.find(f => f.id === selectedFolderId)?.name}</span>
              </p>
            )}
            {addingToFolder && (
              <p className="mt-2 flex items-center">
                <Loader2 className="h-3 w-3 animate-spin mr-1" />
                <span>Adding document to folder...</span>
              </p>
            )}
          </div>
          
          <div className="border rounded-md p-2 max-h-[400px] overflow-y-auto">
            {rootFolders.length > 0 ? (
              rootFolders.map((folder) => (
                <FolderNode
                  key={folder.id}
                  folder={folder}
                  folders={folders}
                  level={0}
                  documentId={documentId}
                  selectedFolderId={selectedFolderId}
                  onFolderSelect={handleFolderSelect}
                  onAddDocumentClick={handleAddDocumentClick}
                />
              ))
            ) : (
              <div className="py-2 px-3 text-muted-foreground text-sm">
                No folders yet. Create a folder to get started.
              </div>
            )}
          </div>
        </>
      )}
    </div>
  )
}
