"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Skeleton } from "@/components/ui/skeleton";
import {
  documentService,
  type DocumentMetadata,
} from "@/lib/services/document-service";
import { TagBadge } from "./tag-badge";
import { tagService } from "@/lib/services/tag-service";
import { folderService } from "@/lib/services/folder-service";
import type { Tag, Folder } from "@/lib/types/document-organization";
import { FileText, Clock, FolderIcon } from "lucide-react";

interface DocumentActivityProps {
  limit?: number;
}

export function DocumentActivity({ limit = 5 }: DocumentActivityProps) {
  const [documents, setDocuments] = useState<DocumentMetadata[]>([]);
  const [documentTags, setDocumentTags] = useState<Record<string, Tag[]>>({});
  const [documentFolders, setDocumentFolders] = useState<
    Record<string, Folder[]>
  >({});
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    const loadDocuments = async () => {
      try {
        setLoading(true);
        const allDocuments = await documentService.getDocuments();

        // Sort by uploadDate date (newest first) and limit
        const sortedDocuments = allDocuments.items
          .sort(
            (a, b) =>
              new Date(b.uploadDate ?? "").getTime() - new Date(a.uploadDate ?? "").getTime()
          )
          .slice(0, limit);

        setDocuments(sortedDocuments);

        // Load tags and folders for each document
        const tagsPromises = sortedDocuments.map((doc) =>
          tagService
            .getTagsForDocument(doc.id)
            .then((tags) => ({ docId: doc.id, tags }))
            .catch(() => ({ docId: doc.id, tags: [] }))
        );

        const foldersPromises = sortedDocuments.map((doc) =>
          folderService
            .getFoldersForDocument(doc.id)
            .then((folders) => ({ docId: doc.id, folders }))
            .catch(() => ({ docId: doc.id, folders: [] }))
        );

        const tagsResults = await Promise.all(tagsPromises);
        const foldersResults = await Promise.all(foldersPromises);

        const tagsMap: Record<string, Tag[]> = {};
        const foldersMap: Record<string, Folder[]> = {};

        tagsResults.forEach((result) => {
          tagsMap[result.docId] = result.tags;
        });

        foldersResults.forEach((result) => {
          foldersMap[result.docId] = result.folders;
        });

        setDocumentTags(tagsMap);
        setDocumentFolders(foldersMap);
      } catch (error) {
        console.error("Error loading document activity:", error);
      } finally {
        setLoading(false);
      }
    };

    loadDocuments();
  }, [limit]);

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString);
      // Check if date is valid
      if (isNaN(date.getTime())) {
        return "No date";
      }
      return date.toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      });
    } catch (error) {
      console.error("Error formatting date:", error);
      return "Invalid date format";
    }
  };

  const getDocumentTypeLabel = (type?: string) => {
    if (!type) return "Document";

    // Convert snake_case or camelCase to Title Case with spaces
    return type
      .replace(/_/g, " ")
      .replace(/([A-Z])/g, " $1")
      .replace(/^\w/, (c) => c.toUpperCase())
      .trim();
  };

  if (loading) {
    return (
      <div className="space-y-3">
        {Array.from({ length: limit }).map((_, index) => (
          <div key={index} className="flex items-start space-x-3">
            <Skeleton className="h-10 w-10 rounded-md" />
            <div className="space-y-2 flex-1">
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-3 w-3/4" />
              <div className="flex space-x-2 mt-1">
                <Skeleton className="h-5 w-16 rounded-full" />
                <Skeleton className="h-5 w-20 rounded-full" />
              </div>
            </div>
          </div>
        ))}
      </div>
    );
  }

  if (documents.length === 0) {
    return (
      <div className="text-center p-8 border rounded-md">
        <FileText className="h-12 w-12 mx-auto mb-3 text-muted-foreground opacity-50" />
        <p className="text-muted-foreground">No documents found</p>
      </div>
    );
  }

  return (
    <div className="space-y-3">
      {documents.map((document) => (
        <Card key={document.id} className="bg-card dark:bg-[#252525]">
          <CardContent className="p-3">
            <div className="flex items-start">
              <FileText className="h-5 w-5 mr-3 mt-0.5 text-muted-foreground" />
              <div className="flex-1">
                <div className="font-medium">
                  {document.title || document.filename}
                </div>
                <div className="text-sm text-muted-foreground flex items-center mt-1">
                  <Badge variant="outline" className="mr-2">
                    {getDocumentTypeLabel(document.fileType)}
                  </Badge>
                  <Clock className="h-3 w-3 mr-1" />
                  <span>{formatDate(document.uploadDate)}</span>
                </div>

                {/* Tags and Folders */}
                <div className="flex flex-wrap mt-2">
                  {/* Tags */}
                  {documentTags[document.id]?.length > 0 && (
                    <div className="flex flex-wrap mr-3">
                      {documentTags[document.id].map((tag) => (
                        <TagBadge key={tag.id} tag={tag} />
                      ))}
                    </div>
                  )}

                  {/* Folders */}
                  {documentFolders[document.id]?.length > 0 && (
                    <div className="flex flex-wrap">
                      {documentFolders[document.id].map((folder) => (
                        <Badge
                          key={folder.id}
                          variant="outline"
                          className="mr-1 mb-1 flex items-center"
                        >
                          <FolderIcon className="h-3 w-3 mr-1" />
                          {folder.name}
                        </Badge>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}
