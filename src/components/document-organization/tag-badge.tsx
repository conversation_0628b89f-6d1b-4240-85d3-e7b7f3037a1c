"use client"

import { Badge } from "@/components/ui/badge"
import { X } from "lucide-react"
import type { Tag } from "@/lib/types/document-organization"

interface TagBadgeProps {
  tag: Tag
  onRemove?: () => void
  onClick?: () => void
}

export function TagBadge({ tag, onRemove, onClick }: TagBadgeProps) {
  return (
    <Badge
      className="mr-1 mb-1 cursor-pointer"
      style={{ backgroundColor: tag.color, color: getContrastColor(tag.color) }}
      onClick={onClick}
    >
      {tag.name}
      {onRemove && (
        <X
          className="ml-1 h-3 w-3"
          onClick={(e) => {
            e.stopPropagation()
            onRemove()
          }}
        />
      )}
    </Badge>
  )
}

// Helper function to determine text color based on background color
function getContrastColor(hexColor: string): string {
  // Remove the hash if it exists
  const color = hexColor.replace("#", "")

  // Convert to RGB
  const r = Number.parseInt(color.substr(0, 2), 16)
  const g = Number.parseInt(color.substr(2, 2), 16)
  const b = Number.parseInt(color.substr(4, 2), 16)

  // Calculate luminance
  const luminance = (0.299 * r + 0.587 * g + 0.114 * b) / 255

  // Return black or white based on luminance
  return luminance > 0.5 ? "#000000" : "#ffffff"
}
