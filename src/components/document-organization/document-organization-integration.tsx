"use client"

import { useState } from "react"
import { Card, Card<PERSON>ontent, Card<PERSON><PERSON>er, CardTitle, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { DocumentActivity } from "./document-activity"
import { TagManager } from "./tag-manager"
import { FolderTree } from "./folder-tree"
import { SavedSearchManager } from "./saved-search-manager"
import type { ExecuteSearchResult } from "@/lib/types/document-organization"
import { TagIcon, FolderIcon, Search, FileText, ArrowRight } from "lucide-react"
import Link from "next/link"

interface DocumentOrganizationIntegrationProps {
  documentId?: string
  showFullInterface?: boolean
}

export function DocumentOrganizationIntegration({ 
  documentId, 
  showFullInterface = true 
}: DocumentOrganizationIntegrationProps) {
  const [activeTab, setActiveTab] = useState("tags")
  const [searchResults, setSearchResults] = useState<ExecuteSearchResult | null>(null)

  const handleSearchExecuted = (results: ExecuteSearchResult) => {
    setSearchResults(results)
    setActiveTab("results")
  }

  return (
    <div className={`grid grid-cols-1 ${showFullInterface ? 'md:grid-cols-3' : ''} gap-6`}>
      <div className={showFullInterface ? 'md:col-span-1' : 'w-full'}>
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-3 mb-4">
            <TabsTrigger value="tags" className="flex items-center">
              <TagIcon className="h-4 w-4 mr-2" />
              Tags
            </TabsTrigger>
            <TabsTrigger value="folders" className="flex items-center">
              <FolderIcon className="h-4 w-4 mr-2" />
              Folders
            </TabsTrigger>
            <TabsTrigger value="searches" className="flex items-center">
              <Search className="h-4 w-4 mr-2" />
              Searches
            </TabsTrigger>
          </TabsList>

          <Card className="bg-card dark:bg-[#1e1e1e]">
            <CardContent className="p-4">
              <TabsContent value="tags" className="mt-0">
                <TagManager documentId={documentId} />
              </TabsContent>

              <TabsContent value="folders" className="mt-0">
                <FolderTree documentId={documentId} />
              </TabsContent>

              <TabsContent value="searches" className="mt-0">
                <SavedSearchManager onSearchExecuted={handleSearchExecuted} />
              </TabsContent>

              <TabsContent value="results" className="mt-0">
                <div className="space-y-4">
                  <div className="flex items-center">
                    <Search className="h-5 w-5 mr-2" />
                    <h3 className="text-lg font-medium">Search Results</h3>
                  </div>

                  {searchResults && (
                    <div className="space-y-3">
                      <div className="text-sm text-muted-foreground">
                        Found {searchResults.results.length} documents matching your search criteria
                      </div>

                      <div className="space-y-2 max-h-96 overflow-y-auto pr-1">
                        {searchResults.results.map((result) => (
                          <Card key={result.id} className="bg-card dark:bg-[#252525]">
                            <CardContent className="p-3">
                              <div className="flex items-start">
                                <FileText className="h-5 w-5 mr-3 mt-0.5 text-muted-foreground" />
                                <div>
                                  <div className="font-medium">{result.title}</div>
                                  <div className="text-sm text-muted-foreground flex items-center mt-1">
                                    <span className="capitalize">{result.documentType}</span>
                                    <span className="mx-2">•</span>
                                    <span>{new Date(result.createdAt).toLocaleDateString()}</span>
                                  </div>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    </div>
                  )}
                </div>
              </TabsContent>
            </CardContent>
          </Card>
        </Tabs>
      </div>

      {showFullInterface && (
        <div className="md:col-span-2">
          <Card className="bg-card dark:bg-[#1e1e1e]">
            <CardHeader>
              <CardTitle>Document Management</CardTitle>
              <CardDescription>Organize your legal documents with tags, folders, and saved searches</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                  <div>
                    <h3 className="text-lg font-medium mb-3 flex justify-between items-center">
                      <span>Recent Documents</span>
                      <Link href="/document-organization" passHref>
                        <Button variant="ghost" size="sm" className="h-8">
                          View All
                          <ArrowRight className="ml-1 h-4 w-4" />
                        </Button>
                      </Link>
                    </h3>
                    <DocumentActivity limit={3} />
                  </div>

                  <div>
                    <h3 className="text-lg font-medium mb-3">Document Activity</h3>
                    <DocumentActivity limit={3} />
                  </div>
                </div>

                <div>
                  <h3 className="text-lg font-medium mb-3">Organization Tips</h3>
                  <div className="space-y-3">
                    <div className="p-3 bg-secondary/20 rounded-md">
                      <h4 className="font-medium flex items-center">
                        <TagIcon className="h-4 w-4 mr-2" />
                        Using Tags
                      </h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Tags help you categorize documents by type, status, or any custom criteria. Create color-coded
                        tags for easy visual identification.
                      </p>
                    </div>

                    <div className="p-3 bg-secondary/20 rounded-md">
                      <h4 className="font-medium flex items-center">
                        <FolderIcon className="h-4 w-4 mr-2" />
                        Folder Structure
                      </h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Organize documents in a hierarchical folder structure. Create parent folders for broad
                        categories and nested subfolders for specific topics.
                      </p>
                    </div>

                    <div className="p-3 bg-secondary/20 rounded-md">
                      <h4 className="font-medium flex items-center">
                        <Search className="h-4 w-4 mr-2" />
                        Saved Searches
                      </h4>
                      <p className="text-sm text-muted-foreground mt-1">
                        Save complex search criteria for frequent use. Enable notifications to stay updated when new
                        documents match your search criteria.
                      </p>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  )
}
