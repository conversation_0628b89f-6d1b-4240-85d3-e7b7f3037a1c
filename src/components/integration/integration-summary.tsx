"use client";

import React, { useEffect } from 'react';
import { 
  Check<PERSON>ircle, 
  AlertCircle, 
  Clock, 
  TrendingUp,
  FileText,
  Target,
  Users,
  BarChart3,
  ArrowRight,
  Zap
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { useRouter } from 'next/navigation';
import { useNegotiationIntegration } from '@/hooks/use-negotiation-integration';

interface IntegrationSummaryProps {
  userId: string;
}

export function IntegrationSummary({ userId }: IntegrationSummaryProps) {
  const router = useRouter();
  const {
    userProfile,
    crossFeatureUsage,
    recommendations,
    hasAccess,
    getUserProfile,
    getCrossFeatureUsage,
    generateRecommendations
  } = useNegotiationIntegration();

  useEffect(() => {
    if (hasAccess && userId) {
      getUserProfile();
      getCrossFeatureUsage();
      generateRecommendations({
        userId,
        context: "general"
      });
    }
  }, [hasAccess, userId, getUserProfile, getCrossFeatureUsage, generateRecommendations]);

  const getIntegrationScore = () => {
    if (!userProfile || !crossFeatureUsage) return 0;
    
    let score = 0;
    
    // User profile completeness (30%)
    if (userProfile.totalSessions > 0) score += 10;
    if (userProfile.strongStrategies.length > 0) score += 10;
    if (userProfile.preferredStyle) score += 10;
    
    // Cross-feature usage (40%)
    if (crossFeatureUsage.playbookToSimulatorConversion.conversionRate > 0.2) score += 20;
    if (crossFeatureUsage.userEngagement.crossFeatureRetention > 0.3) score += 20;
    
    // Engagement level (30%)
    if (userProfile.completedSessions > 3) score += 15;
    if (userProfile.averageScore > 60) score += 15;
    
    return Math.min(score, 100);
  };

  const getIntegrationStatus = (score: number) => {
    if (score >= 80) return { 
      level: 'Excellent', 
      color: 'text-green-600', 
      bgColor: 'bg-green-100',
      icon: CheckCircle 
    };
    if (score >= 60) return { 
      level: 'Good', 
      color: 'text-blue-600', 
      bgColor: 'bg-blue-100',
      icon: TrendingUp 
    };
    if (score >= 40) return { 
      level: 'Developing', 
      color: 'text-yellow-600', 
      bgColor: 'bg-yellow-100',
      icon: Clock 
    };
    return { 
      level: 'Getting Started', 
      color: 'text-orange-600', 
      bgColor: 'bg-orange-100',
      icon: AlertCircle 
    };
  };

  const getNextSteps = () => {
    const steps = [];
    
    if (!userProfile || userProfile.totalSessions === 0) {
      steps.push({
        title: "Complete your first negotiation session",
        description: "Start with a beginner scenario to build your profile",
        action: "Start Session",
        url: "/negotiation-simulator"
      });
    }
    
    if (!userProfile || userProfile.strongStrategies.length === 0) {
      steps.push({
        title: "Update your negotiation profile",
        description: "Add your preferred strategies and areas for improvement",
        action: "Update Profile",
        url: "/negotiation-simulator?tab=recommendations"
      });
    }
    
    if (!crossFeatureUsage || crossFeatureUsage.playbookToSimulatorConversion.conversionRate === 0) {
      steps.push({
        title: "Create scenarios from playbooks",
        description: "Turn your document analyses into practice scenarios",
        action: "Browse Playbooks",
        url: "/negotiation-playbook"
      });
    }
    
    return steps.slice(0, 3); // Show max 3 steps
  };

  const integrationScore = getIntegrationScore();
  const status = getIntegrationStatus(integrationScore);
  const nextSteps = getNextSteps();
  const StatusIcon = status.icon;

  if (!hasAccess) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Zap className="h-5 w-5" />
            Integration Overview
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground">
              Integration features require PRO subscription
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Zap className="h-5 w-5" />
          Integration Overview
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Integration Score */}
        <div className="text-center space-y-4">
          <div className={`inline-flex items-center gap-2 px-4 py-2 rounded-full ${status.bgColor}`}>
            <StatusIcon className={`h-5 w-5 ${status.color}`} />
            <span className={`font-medium ${status.color}`}>
              {status.level} Integration
            </span>
          </div>
          
          <div className="space-y-2">
            <div className="flex items-center justify-between text-sm">
              <span>Integration Score</span>
              <span className="font-medium">{integrationScore}%</span>
            </div>
            <Progress value={integrationScore} className="h-2" />
          </div>
        </div>

        {/* Key Metrics */}
        {userProfile && crossFeatureUsage && (
          <div className="grid grid-cols-2 gap-4">
            <div className="text-center p-3 border rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-1">
                <Target className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Sessions</span>
              </div>
              <div className="text-xl font-bold">
                {userProfile.completedSessions}
              </div>
              <p className="text-xs text-muted-foreground">
                Completed
              </p>
            </div>

            <div className="text-center p-3 border rounded-lg">
              <div className="flex items-center justify-center gap-1 mb-1">
                <BarChart3 className="h-4 w-4 text-muted-foreground" />
                <span className="text-sm font-medium">Conversion</span>
              </div>
              <div className="text-xl font-bold">
                {(crossFeatureUsage.playbookToSimulatorConversion.conversionRate * 100).toFixed(0)}%
              </div>
              <p className="text-xs text-muted-foreground">
                Playbook to Sim
              </p>
            </div>
          </div>
        )}

        {/* Feature Usage */}
        <div className="space-y-3">
          <h4 className="text-sm font-medium">Feature Usage</h4>
          <div className="space-y-2">
            <div className="flex items-center justify-between p-2 border rounded">
              <div className="flex items-center gap-2">
                <FileText className="h-4 w-4" />
                <span className="text-sm">Document Analysis</span>
              </div>
              <Badge variant="outline">Active</Badge>
            </div>
            
            <div className="flex items-center justify-between p-2 border rounded">
              <div className="flex items-center gap-2">
                <Target className="h-4 w-4" />
                <span className="text-sm">Negotiation Simulator</span>
              </div>
              <Badge variant={userProfile?.totalSessions ? "default" : "secondary"}>
                {userProfile?.totalSessions ? "Active" : "Pending"}
              </Badge>
            </div>
            
            <div className="flex items-center justify-between p-2 border rounded">
              <div className="flex items-center gap-2">
                <Users className="h-4 w-4" />
                <span className="text-sm">Cross-Feature Analytics</span>
              </div>
              <Badge variant={crossFeatureUsage ? "default" : "secondary"}>
                {crossFeatureUsage ? "Active" : "Pending"}
              </Badge>
            </div>
          </div>
        </div>

        {/* Next Steps */}
        {nextSteps.length > 0 && (
          <div className="space-y-3">
            <h4 className="text-sm font-medium">Recommended Next Steps</h4>
            <div className="space-y-2">
              {nextSteps.map((step, index) => (
                <div key={index} className="p-3 border rounded-lg space-y-2">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <h5 className="text-sm font-medium">{step.title}</h5>
                      <p className="text-xs text-muted-foreground">
                        {step.description}
                      </p>
                    </div>
                    <Button 
                      size="sm" 
                      variant="outline"
                      onClick={() => router.push(step.url)}
                    >
                      {step.action}
                      <ArrowRight className="h-3 w-3 ml-1" />
                    </Button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Quick Actions */}
        <div className="flex gap-2 pt-4 border-t">
          <Button 
            variant="outline" 
            size="sm" 
            onClick={() => router.push('/negotiation-simulator?tab=integration')}
            className="flex-1"
          >
            View Analytics
          </Button>
          <Button 
            size="sm" 
            onClick={() => router.push('/negotiation-simulator?tab=recommendations')}
            className="flex-1"
          >
            Get Insights
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
