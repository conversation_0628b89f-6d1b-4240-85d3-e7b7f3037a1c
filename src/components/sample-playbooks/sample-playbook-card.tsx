"use client";

import React from "react";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON>, Eye, BookOpen, Clock, User, Tag } from "lucide-react";
import type { SampleNegotiationPlaybook, SampleContractPlaybook } from "@/lib/types/sample-playbooks";

interface SamplePlaybookCardProps {
  playbook: SampleNegotiationPlaybook | SampleContractPlaybook;
  type: "negotiation" | "contract";
  onView: () => void;
  onClone: () => void;
  isCloning?: boolean;
}

export function SamplePlaybookCard({ 
  playbook, 
  type, 
  onView, 
  onClone, 
  isCloning = false 
}: SamplePlaybookCardProps) {
  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case "beginner":
        return "bg-green-100 text-green-800";
      case "intermediate":
        return "bg-yellow-100 text-yellow-800";
      case "expert":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRiskProfileColor = (riskProfile?: string) => {
    switch (riskProfile) {
      case "Low":
        return "bg-green-100 text-green-800";
      case "Medium":
        return "bg-yellow-100 text-yellow-800";
      case "High":
        return "bg-orange-100 text-orange-800";
      case "Critical":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const isNegotiationPlaybook = (pb: any): pb is SampleNegotiationPlaybook => {
    return type === "negotiation" && "strategies" in pb; // Check for a distinctive property like 'strategies'
  };

  const isContractPlaybook = (pb: any): pb is SampleContractPlaybook => {
    return type === "contract" && "rules" in pb;
  };

  return (
    <Card className="h-full hover:shadow-md transition-shadow border-l-4 border-l-primary/20">
      <CardHeader className="pb-3">
        <div className="space-y-3">
          <div className="flex items-center justify-between gap-2">
            <Badge variant="secondary" className="text-xs bg-primary/10 text-primary">
              Template
            </Badge>
            <Badge variant="outline" className="text-xs shrink-0 max-w-[120px] truncate">
              {playbook.contractType.replace(/_/g, " ")}
            </Badge>
          </div>
          <div>
            <CardTitle className="text-lg font-semibold line-clamp-2">
              {playbook.name}
            </CardTitle>
            <CardDescription className="mt-1 line-clamp-2">
              {playbook.description}
            </CardDescription>
          </div>
        </div>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Metadata */}
        <div className="flex flex-wrap gap-2">
          {isNegotiationPlaybook(playbook) && (
            <Badge className={getDifficultyColor(playbook.difficulty)}>
              {playbook.difficulty}
            </Badge>
          )}
          {isContractPlaybook(playbook) && (playbook.riskProfile || playbook.metadata?.riskProfile) && (
            <Badge className={getRiskProfileColor(playbook.riskProfile || playbook.metadata?.riskProfile)}>
              {(playbook.riskProfile || playbook.metadata?.riskProfile)} Risk
            </Badge>
          )}
          {((isNegotiationPlaybook(playbook) && playbook.industry) || (isContractPlaybook(playbook) && playbook.metadata?.industry)) && (
            <Badge variant="secondary">
              {(playbook.industry || playbook.metadata?.industry)?.replace(/_/g, " ")}
            </Badge>
          )}
        </div>

        {/* Tags */}
        {((isNegotiationPlaybook(playbook) && playbook.tags && playbook.tags.length > 0) || (isContractPlaybook(playbook) && playbook.metadata?.tags && playbook.metadata.tags.length > 0)) && (
          <div className="flex items-center gap-1 text-sm text-muted-foreground">
            <Tag className="h-3 w-3" />
            <span className="line-clamp-1">
              {(isNegotiationPlaybook(playbook) ? playbook.tags : (isContractPlaybook(playbook) ? playbook.metadata?.tags : []) || []).slice(0, 3).join(", ")}
              {(isNegotiationPlaybook(playbook) ? playbook.tags : (isContractPlaybook(playbook) ? playbook.metadata?.tags : []) || []).length > 3 && ` +${((isNegotiationPlaybook(playbook) ? playbook.tags : (isContractPlaybook(playbook) ? playbook.metadata?.tags : []) || []).length - 3)} more`}
            </span>
          </div>
        )}

        {/* Stats */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          <div className="flex items-center gap-2">
            <User className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">
              {(isContractPlaybook(playbook) && playbook.metadata?.author) || "Expert Team"}
            </span>
          </div>
          <div className="flex items-center gap-2">
            <Clock className="h-4 w-4 text-muted-foreground" />
            <span className="text-muted-foreground">
              Quick setup
            </span>
          </div>
        </div>

        {/* Content Stats */}
        <div className="grid grid-cols-2 gap-4 text-sm">
          {isNegotiationPlaybook(playbook) && (
            <>
              <div className="text-center p-2 bg-muted rounded">
                <div className="font-semibold">{playbook.strategies.length}</div>
                <div className="text-xs text-muted-foreground">Strategies</div>
              </div>
              <div className="text-center p-2 bg-muted rounded">
                <div className="font-semibold">
                  {playbook.strategies.reduce((total, strategy) => total + strategy.simulationScenarios.length, 0)}
                </div>
                <div className="text-xs text-muted-foreground">Scenarios</div>
              </div>
            </>
          )}
          {isContractPlaybook(playbook) && (
            <>
              <div className="text-center p-2 bg-muted rounded">
                <div className="font-semibold">{playbook.rules?.length || 0}</div>
                <div className="text-xs text-muted-foreground">Rules</div>
              </div>
              <div className="text-center p-2 bg-muted rounded">
                <div className="font-semibold">
                  {playbook.metadata?.riskProfile || "Medium"}
                </div>
                <div className="text-xs text-muted-foreground">Risk Level</div>
              </div>
            </>
          )}
        </div>

        {/* Actions */}
        <div className="flex gap-2 pt-2">
          <Button variant="outline" size="sm" onClick={onView} className="flex-1">
            <Eye className="h-4 w-4 mr-2" />
            View Details
          </Button>
          <Button 
            size="sm" 
            onClick={onClone} 
            disabled={isCloning}
            className="flex-1"
          >
            <Copy className="h-4 w-4 mr-2" />
            {isCloning ? "Cloning..." : "Use Template"}
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
