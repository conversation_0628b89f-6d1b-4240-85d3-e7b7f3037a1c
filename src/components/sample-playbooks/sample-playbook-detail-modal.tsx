"use client";

import React from "react";
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  Copy,
  User,
  Clock,
  Tag,
  Shield,
  CheckCircle,
  XCircle,
  BookOpen,
  Target,
  Lightbulb
} from "lucide-react";
import type { SampleNegotiationPlaybook, SampleContractPlaybook } from "@/lib/types/sample-playbooks";
// Updated to use correct API response structure

interface SamplePlaybookDetailModalProps {
  playbook: SampleNegotiationPlaybook | SampleContractPlaybook | null;
  type: "negotiation" | "contract";
  isOpen: boolean;
  onClose: () => void;
  onClone: () => void;
  isCloning?: boolean;
}

export function SamplePlaybookDetailModal({
  playbook,
  type,
  isOpen,
  onClose,
  onClone,
  isCloning = false
}: SamplePlaybookDetailModalProps) {
  if (!playbook) return null;

  const isNegotiationPlaybook = (pb: any): pb is SampleNegotiationPlaybook => {
    return type === "negotiation" && "templateName" in pb;
  };

  const isContractPlaybook = (pb: any): pb is SampleContractPlaybook => {
    return type === "contract" && "rules" in pb;
  };

  const getDifficultyColor = (difficulty?: string) => {
    switch (difficulty) {
      case "beginner":
        return "bg-green-100 text-green-800";
      case "intermediate":
        return "bg-yellow-100 text-yellow-800";
      case "expert":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getRiskProfileColor = (riskProfile?: string) => {
    switch (riskProfile) {
      case "Low":
        return "bg-green-100 text-green-800";
      case "Medium":
        return "bg-yellow-100 text-yellow-800";
      case "High":
        return "bg-orange-100 text-orange-800";
      case "Critical":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  const getSeverityColor = (severity: string) => {
    switch (severity) {
      case "LOW":
        return "bg-green-100 text-green-800";
      case "MEDIUM":
        return "bg-yellow-100 text-yellow-800";
      case "HIGH":
        return "bg-orange-100 text-orange-800";
      case "CRITICAL":
        return "bg-red-100 text-red-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh]">
        <DialogHeader>
          <div className="flex items-start justify-between">
            <div className="flex-1">
              <div className="flex items-center gap-2 mb-2">
                <Badge variant="secondary" className="text-xs bg-primary/10 text-primary">
                  Template
                </Badge>
                <Badge variant="outline" className="text-xs">
                  {playbook.contractType.replace(/_/g, " ")}
                </Badge>
              </div>
              <DialogTitle className="text-xl">
                {isNegotiationPlaybook(playbook) ? playbook.templateName : playbook.name}
              </DialogTitle>
              <DialogDescription className="mt-1">
                {isNegotiationPlaybook(playbook) ? playbook.templateDescription : playbook.description}
              </DialogDescription>
            </div>
            <Button onClick={onClone} disabled={isCloning} className="ml-4 mr-4">
              <Copy className="h-4 w-4 mr-2" />
              {isCloning ? "Cloning..." : "Use Template"}
            </Button>
          </div>
        </DialogHeader>

        <ScrollArea className="h-[60vh] mt-4">
          <div className="space-y-6 pr-4 pb-4">
            {/* Metadata */}
            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
              <div className="flex items-center gap-2 text-sm">
                <User className="h-4 w-4 text-muted-foreground" />
                <span>{isContractPlaybook(playbook) ? playbook.metadata?.author || "Expert Team" : "Expert Team"}</span>
              </div>
              {isNegotiationPlaybook(playbook) && (
                <>
                  <div className="flex items-center gap-2 text-sm">
                    <Target className="h-4 w-4 text-muted-foreground" />
                    <Badge className={getDifficultyColor(playbook.difficulty)}>
                      {playbook.difficulty}
                    </Badge>
                  </div>
                  <div className="flex items-center gap-2 text-sm">
                    <Clock className="h-4 w-4 text-muted-foreground" />
                    <span>Quick setup</span>
                  </div>
                </>
              )}
              {isContractPlaybook(playbook) && (
                <div className="flex items-center gap-2 text-sm">
                  <Shield className="h-4 w-4 text-muted-foreground" />
                  <Badge className={getRiskProfileColor(playbook.metadata?.riskProfile)}>
                    {playbook.metadata?.riskProfile} Risk
                  </Badge>
                </div>
              )}
              {(isNegotiationPlaybook(playbook) ? playbook.tags : playbook.tags || playbook.metadata?.tags) && (
                <div className="flex items-center gap-2 text-sm">
                  <Tag className="h-4 w-4 text-muted-foreground" />
                  <span className="truncate">
                    {(isNegotiationPlaybook(playbook) ? playbook.tags : playbook.tags || playbook.metadata?.tags || []).slice(0, 2).join(", ")}
                  </span>
                </div>
              )}
            </div>

            <Separator />

            {/* Negotiation Playbook Content */}
            {isNegotiationPlaybook(playbook) && (
              <div className="space-y-6">
                {/* Strategies */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Lightbulb className="h-5 w-5" />
                    Negotiation Strategies ({playbook.strategies.length})
                  </h3>
                  <div className="space-y-4">
                    {playbook.strategies.map((strategy, index) => (
                      <Card key={index}>
                        <CardHeader className="pb-3">
                          <CardTitle className="text-base">{strategy.section}</CardTitle>
                        </CardHeader>
                        <CardContent className="space-y-3">
                          <div className="flex items-center gap-2 mb-3">
                            <Badge variant="outline" className="text-xs">
                              Priority {strategy.priority}
                            </Badge>
                            <Badge variant="outline" className="text-xs">
                              {strategy.riskLevel} risk
                            </Badge>
                          </div>

                          <div className="space-y-3">
                            <div>
                              <strong className="text-sm">Recommendations:</strong>
                              <ul className="mt-2 space-y-1">
                                {strategy.recommendations.map((rec, recIndex) => (
                                  <li key={recIndex} className="text-sm text-muted-foreground flex items-start gap-2">
                                    <span className="text-primary mt-1">•</span>
                                    {rec}
                                  </li>
                                ))}
                              </ul>
                            </div>

                            {strategy.alternativeLanguage && (
                              <div>
                                <strong className="text-sm">Alternative Language:</strong>
                                <p className="text-sm text-muted-foreground mt-1 italic">
                                  "{strategy.alternativeLanguage}"
                                </p>
                              </div>
                            )}
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>

                {/* Simulation Scenarios */}
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <BookOpen className="h-5 w-5" />
                    Simulation Scenarios ({playbook.strategies.reduce((total, strategy) => total + strategy.simulationScenarios.length, 0)})
                  </h3>
                  <div className="space-y-3">
                    {playbook.strategies.map((strategy, strategyIndex) =>
                      strategy.simulationScenarios.map((scenario, scenarioIndex) => (
                        <Card key={`${strategyIndex}-${scenarioIndex}`}>
                          <CardHeader className="pb-3">
                            <CardTitle className="text-sm text-muted-foreground">
                              {strategy.section} - {scenario.type}
                            </CardTitle>
                          </CardHeader>
                          <CardContent className="pt-0">
                            <div className="space-y-3">
                              <div>
                                <strong className="text-sm">Trigger:</strong>
                                <p className="text-sm text-muted-foreground mt-1">{scenario.trigger}</p>
                              </div>
                              <div>
                                <strong className="text-sm">Response Strategy:</strong>
                                <p className="text-sm text-muted-foreground mt-1">{scenario.responseStrategy}</p>
                              </div>
                              <div>
                                <strong className="text-sm">Expected Outcome:</strong>
                                <p className="text-sm text-muted-foreground mt-1">{scenario.expectedOutcome}</p>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))
                    )}
                  </div>
                </div>

                {/* Overall Assessment */}
                <div>
                  <h3 className="text-lg font-semibold mb-4">Overall Assessment</h3>
                  <Card>
                    <CardContent className="pt-4 space-y-4">
                      <div>
                        <strong className="text-sm flex items-center gap-2 mb-2">
                          <CheckCircle className="h-4 w-4 text-green-600" />
                          Key Leverage Points
                        </strong>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {playbook.keyLeveragePoints.map((point, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-green-600 mt-1">•</span>
                              {point}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <strong className="text-sm flex items-center gap-2 mb-2">
                          <XCircle className="h-4 w-4 text-red-600" />
                          Deal Breakers
                        </strong>
                        <ul className="text-sm text-muted-foreground space-y-1">
                          {playbook.dealBreakers.map((breaker, index) => (
                            <li key={index} className="flex items-start gap-2">
                              <span className="text-red-600 mt-1">•</span>
                              {breaker}
                            </li>
                          ))}
                        </ul>
                      </div>
                      <div>
                        <strong className="text-sm mb-2 block">Strategic Guidance</strong>
                        <p className="text-sm text-muted-foreground">{playbook.overallAssessment}</p>
                      </div>
                    </CardContent>
                  </Card>
                </div>
              </div>
            )}

            {/* Contract Playbook Content */}
            {isContractPlaybook(playbook) && (
              <div className="space-y-6">
                <div>
                  <h3 className="text-lg font-semibold mb-4 flex items-center gap-2">
                    <Shield className="h-5 w-5" />
                    Compliance Rules ({playbook.rules?.length || 0})
                  </h3>
                  <div className="space-y-4">
                    {playbook.rules?.map((rule, index) => (
                      <Card key={index}>
                        <CardHeader className="pb-3">
                          <div className="flex items-start justify-between">
                            <div>
                              <CardTitle className="text-base">{rule.name}</CardTitle>
                              <CardDescription>{rule.description}</CardDescription>
                            </div>
                            <div className="flex gap-2">
                              <Badge className={getSeverityColor(rule.severity)}>
                                {rule.severity}
                              </Badge>
                              <Badge variant="outline" className="text-xs">
                                {rule.category}
                              </Badge>
                            </div>
                          </div>
                        </CardHeader>
                        <CardContent className="space-y-4">
                          {/* Acceptable Language */}
                          <div>
                            <strong className="text-sm flex items-center gap-2 mb-2">
                              <CheckCircle className="h-4 w-4 text-green-600" />
                              Acceptable Language
                            </strong>
                            <div className="text-xs space-y-1">
                              {rule.acceptableLanguage.preferred && (
                                <div>
                                  <span className="font-medium">Preferred:</span>
                                  <ul className="ml-4 mt-1 space-y-1">
                                    {rule.acceptableLanguage.preferred.map((lang, i) => (
                                      <li key={i} className="text-muted-foreground">• {lang}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Unacceptable Terms */}
                          <div>
                            <strong className="text-sm flex items-center gap-2 mb-2">
                              <XCircle className="h-4 w-4 text-red-600" />
                              Unacceptable Terms
                            </strong>
                            <div className="text-xs space-y-1">
                              {rule.unacceptableTerms.prohibited && (
                                <div>
                                  <span className="font-medium">Prohibited:</span>
                                  <ul className="ml-4 mt-1 space-y-1">
                                    {rule.unacceptableTerms.prohibited.map((term, i) => (
                                      <li key={i} className="text-muted-foreground">• {term}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </div>

                          {/* Negotiation Guidance */}
                          <div>
                            <strong className="text-sm flex items-center gap-2 mb-2">
                              <Lightbulb className="h-4 w-4 text-blue-600" />
                              Negotiation Guidance
                            </strong>
                            <div className="text-xs space-y-2">
                              <p><strong>Strategy:</strong> {rule.negotiationGuidance.strategy}</p>
                              <p><strong>Business Impact:</strong> {rule.negotiationGuidance.businessImpact}</p>
                              {rule.negotiationGuidance.alternatives && (
                                <div>
                                  <strong>Alternatives:</strong>
                                  <ul className="ml-4 mt-1 space-y-1">
                                    {rule.negotiationGuidance.alternatives.map((alt, i) => (
                                      <li key={i} className="text-muted-foreground">• {alt}</li>
                                    ))}
                                  </ul>
                                </div>
                              )}
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    ))}
                  </div>
                </div>
              </div>
            )}
          </div>
        </ScrollArea>
      </DialogContent>
    </Dialog>
  );
}
