"use client";

import React, { useState } from "react";
import { Search, Filter, BookOpen, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { SamplePlaybookCard } from "./sample-playbook-card";
import { SamplePlaybookDetailModal } from "./sample-playbook-detail-modal";
import { negotiationPlaybookService } from "@/lib/services/negotiation-playbook-service";
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import type { SampleNegotiationPlaybookFilters, SampleNegotiationPlaybook } from "@/lib/types/sample-playbooks";

interface SampleNegotiationPlaybooksBrowserProps {
  documentId?: string;
  onPlaybookCloned?: () => void;
}

export function SampleNegotiationPlaybooksBrowser({ 
  documentId, 
  onPlaybookCloned 
}: SampleNegotiationPlaybooksBrowserProps) {
  const { toast } = useToast();
  const queryClient = useQueryClient();
  const [filters, setFilters] = useState<SampleNegotiationPlaybookFilters>({});
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPlaybookId, setSelectedPlaybookId] = useState<string | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  const { data: sampleData, isLoading, error } = useQuery({
    queryKey: ["sample-negotiation-playbooks", filters],
    queryFn: async () => {
      const playbooks = await negotiationPlaybookService.getSamplePlaybooks(filters);
      // Assuming the service will be updated to return SampleNegotiationPlaybook[] directly
      // If it still returns SamplePlaybooksResponse, this needs to be playbooks.samples
      return playbooks; 
    },
    staleTime: 1000 * 60 * 10, // 10 minutes
  });

  const { data: selectedPlaybook } = useQuery({
    queryKey: ["sample-negotiation-playbook", selectedPlaybookId],
    queryFn: () => negotiationPlaybookService.getSamplePlaybook(selectedPlaybookId!),
    enabled: !!selectedPlaybookId,
    staleTime: 1000 * 60 * 10, // 10 minutes
  });

  const cloneMutation = useMutation({
    mutationFn: ({ playbookId, docId }: { playbookId: string; docId: string }) =>
      negotiationPlaybookService.cloneSamplePlaybook(playbookId, docId),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["negotiation-playbooks"] });
      toast({
        title: "Playbook cloned successfully",
        description: "The sample playbook has been added to your document.",
      });
      onPlaybookCloned?.();
    },
    onError: (error: any) => {
      toast({
        title: "Failed to clone playbook",
        description: error.message || "Please try again or contact support if the issue persists.",
        variant: "destructive",
      });
    },
  });

  const handleFilterChange = (key: keyof SampleNegotiationPlaybookFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
  };

  const handleClone = async (playbookId: string) => {
    if (!documentId) {
      toast({
        title: "Feature Coming Soon",
        description: "Negotiation playbook cloning will be available when integrated with document analysis.",
        variant: "default",
      });
      return;
    }

    cloneMutation.mutate({ playbookId, docId: documentId });
  };

  const handleView = (playbookId: string) => {
    setSelectedPlaybookId(playbookId);
    setIsDetailModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsDetailModalOpen(false);
    setSelectedPlaybookId(null);
  };

  const filteredPlaybooks = React.useMemo(() => {
    if (!sampleData || sampleData.length === 0) return [];

    return sampleData.filter((playbook: SampleNegotiationPlaybook) => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        playbook.name.toLowerCase().includes(query) ||
        playbook.description.toLowerCase().includes(query) ||
        playbook.tags.some(tag => tag.toLowerCase().includes(query))
      );
    });
  }, [sampleData, searchQuery]);

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Failed to load sample playbooks. Please try again.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center gap-2">
        <Sparkles className="h-5 w-5 text-primary" />
        <h3 className="text-lg font-semibold">Sample Negotiation Playbooks</h3>
        <Badge variant="secondary">{sampleData?.length || 0} templates</Badge>
      </div>

      {/* Filters */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="text-base">Browse Expert Templates</CardTitle>
          <CardDescription>
            Get started quickly with professionally crafted negotiation strategies and tactics
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search playbooks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filter Controls */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              value={filters.contractType || "all"}
              onValueChange={(value) => handleFilterChange("contractType", value === "all" ? "" : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Contract Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="NDA">NDA</SelectItem>
                <SelectItem value="SERVICE_AGREEMENT">Service Agreement</SelectItem>
                <SelectItem value="EMPLOYMENT_CONTRACT">Employment Contract</SelectItem>
                <SelectItem value="CONSULTING_AGREEMENT">Consulting Agreement</SelectItem>
                <SelectItem value="SOFTWARE_LICENSE">Software License</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.difficulty || "all"}
              onValueChange={(value) => handleFilterChange("difficulty", value === "all" ? "" : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Difficulty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Levels</SelectItem>
                <SelectItem value="beginner">Beginner</SelectItem>
                <SelectItem value="intermediate">Intermediate</SelectItem>
                <SelectItem value="expert">Expert</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.industry || "all"}
              onValueChange={(value) => handleFilterChange("industry", value === "all" ? "" : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Industry" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Industries</SelectItem>
                <SelectItem value="GENERAL">General</SelectItem>
                <SelectItem value="TECHNOLOGY">Technology</SelectItem>
                <SelectItem value="HEALTHCARE">Healthcare</SelectItem>
                <SelectItem value="FINANCE">Finance</SelectItem>
                <SelectItem value="LEGAL">Legal</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => {
                setFilters({});
                setSearchQuery("");
              }}
              className="gap-2"
            >
              <Filter className="h-4 w-4" />
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredPlaybooks.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No playbooks found</h3>
            <p className="text-muted-foreground mb-4">
              Try adjusting your filters or search terms.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setFilters({});
                setSearchQuery("");
              }}
            >
              Clear all filters
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPlaybooks.map((playbook) => (
            <SamplePlaybookCard
              key={playbook.id} // Changed to 'id'
              playbook={playbook}
              type="negotiation"
              onView={() => handleView(playbook.id)} // Changed to 'id'
              onClone={() => handleClone(playbook.id)} // Changed to 'id'
              isCloning={cloneMutation.isPending}
            />
          ))}
        </div>
      )}

      {/* Detail Modal */}
      <SamplePlaybookDetailModal
        playbook={selectedPlaybook || null}
        type="negotiation"
        isOpen={isDetailModalOpen}
        onClose={handleCloseModal}
        onClone={() => {
          if (selectedPlaybookId) {
            handleClone(selectedPlaybookId);
          }
        }}
        isCloning={cloneMutation.isPending}
      />
    </div>
  );
}
