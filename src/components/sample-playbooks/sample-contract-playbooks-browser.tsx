"use client";

import React, { useState } from "react";
import { Search, Filter, BookOpen, Sparkles } from "lucide-react";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { useToast } from "@/hooks/use-toast";
import { SamplePlaybookCard } from "./sample-playbook-card";
import { SamplePlaybookDetailModal } from "./sample-playbook-detail-modal";
import { useSamplePlaybooks, useCloneSamplePlaybook, useSamplePlaybook } from "@/lib/services/contract-playbooks-service";
import type { SampleContractPlaybookFilters, SampleContractPlaybook } from "@/lib/types/sample-playbooks";

interface SampleContractPlaybooksBrowserProps {
  onPlaybookCloned?: () => void;
}

export function SampleContractPlaybooksBrowser({ onPlaybookCloned }: SampleContractPlaybooksBrowserProps) {
  const { toast } = useToast();
  const [filters, setFilters] = useState<SampleContractPlaybookFilters>({});
  const [searchQuery, setSearchQuery] = useState("");
  const [selectedPlaybookId, setSelectedPlaybookId] = useState<string | null>(null);
  const [isDetailModalOpen, setIsDetailModalOpen] = useState(false);

  const { data: sampleData, isLoading, error } = useSamplePlaybooks(filters);
  const { data: selectedPlaybook } = useSamplePlaybook(selectedPlaybookId || "");
  const cloneMutation = useCloneSamplePlaybook();

  const handleFilterChange = (key: keyof SampleContractPlaybookFilters, value: string) => {
    setFilters(prev => ({
      ...prev,
      [key]: value || undefined
    }));
  };

  const handleClone = async (playbookId: string) => {
    try {
      await cloneMutation.mutateAsync(playbookId);
      toast({
        title: "Playbook cloned successfully",
        description: "The sample playbook has been added to your collection.",
      });
      onPlaybookCloned?.();
    } catch (error) {
      toast({
        title: "Failed to clone playbook",
        description: "Please try again or contact support if the issue persists.",
        variant: "destructive",
      });
    }
  };

  const handleView = (playbookId: string) => {
    setSelectedPlaybookId(playbookId);
    setIsDetailModalOpen(true);
  };

  const handleCloseModal = () => {
    setIsDetailModalOpen(false);
    setSelectedPlaybookId(null);
  };

  const filteredPlaybooks = React.useMemo(() => {
    if (!sampleData) return [];

    // Handle both array response and object with samples property
    const playbooks = Array.isArray(sampleData) ? sampleData : sampleData.samples || [];

    return playbooks.filter(playbook => {
      if (!searchQuery) return true;
      const query = searchQuery.toLowerCase();
      return (
        playbook.name.toLowerCase().includes(query) ||
        playbook.description.toLowerCase().includes(query) ||
        playbook.metadata?.tags?.some(tag => tag.toLowerCase().includes(query))
      );
    });
  }, [sampleData, searchQuery]);

  if (error) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="text-center text-muted-foreground">
            Failed to load sample playbooks. Please try again.
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-primary" />
          <h2 className="text-xl font-semibold">Expert Sample Templates</h2>
          <Badge variant="secondary">
            {Array.isArray(sampleData) ? sampleData.length : sampleData?.total || 0} templates
          </Badge>
        </div>
        <Badge variant="outline" className="text-xs">
          Free to Use
        </Badge>
      </div>

      <p className="text-muted-foreground text-sm">
        Get started quickly with professionally crafted contract analysis playbooks from legal experts.
      </p>

      {/* Filters */}
      <Card>
        <CardHeader className="pb-4">
          <CardTitle className="text-base">Filter Templates</CardTitle>
          <CardDescription>
            Find the perfect template for your contract type and requirements
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          {/* Search */}
          <div className="relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search playbooks..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="pl-10"
            />
          </div>

          {/* Filter Controls */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <Select
              value={filters.contractType || "all"}
              onValueChange={(value) => handleFilterChange("contractType", value === "all" ? "" : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Contract Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Types</SelectItem>
                <SelectItem value="NDA">NDA</SelectItem>
                <SelectItem value="SERVICE_AGREEMENT">Service Agreement</SelectItem>
                <SelectItem value="EMPLOYMENT_CONTRACT">Employment Contract</SelectItem>
                <SelectItem value="LICENSING_AGREEMENT">Licensing Agreement</SelectItem>
                <SelectItem value="PURCHASE_AGREEMENT">Purchase Agreement</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.riskProfile || "all"}
              onValueChange={(value) => handleFilterChange("riskProfile", value === "all" ? "" : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Risk Profile" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Risk Levels</SelectItem>
                <SelectItem value="Low">Low Risk</SelectItem>
                <SelectItem value="Medium">Medium Risk</SelectItem>
                <SelectItem value="High">High Risk</SelectItem>
                <SelectItem value="Critical">Critical Risk</SelectItem>
              </SelectContent>
            </Select>

            <Select
              value={filters.industry || "all"}
              onValueChange={(value) => handleFilterChange("industry", value === "all" ? "" : value)}
            >
              <SelectTrigger>
                <SelectValue placeholder="Industry" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Industries</SelectItem>
                <SelectItem value="GENERAL">General</SelectItem>
                <SelectItem value="TECHNOLOGY">Technology</SelectItem>
                <SelectItem value="HEALTHCARE">Healthcare</SelectItem>
                <SelectItem value="FINANCE">Finance</SelectItem>
                <SelectItem value="LEGAL">Legal</SelectItem>
              </SelectContent>
            </Select>

            <Button
              variant="outline"
              onClick={() => {
                setFilters({});
                setSearchQuery("");
              }}
              className="gap-2"
            >
              <Filter className="h-4 w-4" />
              Clear Filters
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Results */}
      {isLoading ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {[...Array(6)].map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardHeader>
                <div className="h-4 bg-muted rounded w-3/4"></div>
                <div className="h-3 bg-muted rounded w-1/2"></div>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  <div className="h-3 bg-muted rounded"></div>
                  <div className="h-3 bg-muted rounded w-2/3"></div>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      ) : filteredPlaybooks.length === 0 ? (
        <Card>
          <CardContent className="p-12 text-center">
            <BookOpen className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
            <h3 className="text-lg font-semibold mb-2">No playbooks found</h3>
            <p className="text-muted-foreground mb-4">
              Try adjusting your filters or search terms.
            </p>
            <Button
              variant="outline"
              onClick={() => {
                setFilters({});
                setSearchQuery("");
              }}
            >
              Clear all filters
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {filteredPlaybooks.map((playbook) => (
            <SamplePlaybookCard
              key={playbook.id}
              playbook={playbook}
              type="contract"
              onView={() => handleView(playbook.id)}
              onClone={() => handleClone(playbook.id)}
              isCloning={cloneMutation.isPending}
            />
          ))}
        </div>
      )}

      {/* Detail Modal */}
      <SamplePlaybookDetailModal
        playbook={selectedPlaybook || null}
        type="contract"
        isOpen={isDetailModalOpen}
        onClose={handleCloseModal}
        onClone={() => {
          if (selectedPlaybookId) {
            handleClone(selectedPlaybookId);
          }
        }}
        isCloning={cloneMutation.isPending}
      />
    </div>
  );
}
