"use client";

import { useState, useEffect } from "react";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	<PERSON><PERSON><PERSON><PERSON>,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import { Label } from "@/components/ui/label";
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group";
import { Loader2, FileText, AlertTriangle } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
	documentService,
	type DocumentMetadata,
} from "@/lib/services/document-service";

interface DocumentComparisonSelectorProps {
	onCompare: (primaryDocumentId: string, relatedDocumentIds: string[]) => void;
	onCancel: () => void;
}

export function DocumentComparisonSelector({
	onCompare,
	onCancel,
}: DocumentComparisonSelectorProps) {
	const [documents, setDocuments] = useState<DocumentMetadata[]>([]);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [primaryDocumentId, setPrimaryDocumentId] = useState<string | null>(
		null
	);
	const [selectedDocumentIds, setSelectedDocumentIds] = useState<string[]>([]);

	useEffect(() => {
		const fetchDocuments = async () => {
			try {
				setIsLoading(true);
				setError(null);

				const response = await documentService.getDocuments(1, 100);
				const docs = response.items || [];
				setDocuments(docs);

				if (docs.length > 0) {
					setPrimaryDocumentId(docs[0].id);
				}
			} catch (error) {
				console.error("Error fetching documents:", error);
				setError("Failed to load documents. Please try again.");
				setDocuments([]);
			} finally {
				setIsLoading(false);
			}
		};

		fetchDocuments();
	}, []);

	const handleDocumentSelect = (documentId: string, checked: boolean) => {
		if (checked) {
			setSelectedDocumentIds((prev) => [...prev, documentId]);
		} else {
			setSelectedDocumentIds((prev) => prev.filter((id) => id !== documentId));
		}
	};

	const handleCompare = () => {
		if (primaryDocumentId && selectedDocumentIds.length > 0) {
			// Filter out the primary document from the related documents
			const relatedDocs = selectedDocumentIds.filter(
				(id) => id !== primaryDocumentId
			);
			onCompare(primaryDocumentId, relatedDocs);
		}
	};

	if (isLoading) {
		return (
			<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
				<CardContent className="flex flex-col items-center justify-center p-12">
					<Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
					<p className="text-lg font-medium">Loading Documents...</p>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
				<CardContent className="flex flex-col items-center justify-center p-12">
					<AlertTriangle className="h-12 w-12 text-destructive mb-4" />
					<p className="text-lg font-medium">Error Loading Documents</p>
					<p className="text-sm text-muted-foreground mt-2">{error}</p>
					<Button onClick={onCancel} className="mt-6">
						Go Back
					</Button>
				</CardContent>
			</Card>
		);
	}

	if (documents.length === 0) {
		return (
			<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
				<CardContent className="flex flex-col items-center justify-center p-12">
					<FileText className="h-12 w-12 text-muted-foreground mb-4" />
					<p className="text-lg font-medium">No Documents Available</p>
					<p className="text-sm text-muted-foreground mt-2">
						Upload documents to your account before using the comparison
						feature.
					</p>
					<Button onClick={onCancel} className="mt-6">
						Go Back
					</Button>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
			<CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
				<CardTitle className="text-lg font-semibold text-foreground">
					Compare Documents
				</CardTitle>
				<CardDescription className="text-muted-foreground">
					Select a primary document and one or more related documents to compare
				</CardDescription>
			</CardHeader>

			<CardContent className="p-4">
				<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
					<div className="space-y-3">
						<h3 className="text-sm font-medium">Primary Document</h3>
						<div className="border rounded-md p-3 bg-background/50 dark:bg-[#252525]/50">
							<RadioGroup
								value={primaryDocumentId || ""}
								onValueChange={(value) => setPrimaryDocumentId(value)}
								className="space-y-2"
							>
								<ScrollArea className="h-[250px] pr-3">
									{documents.map((doc) => (
										<div
											key={doc.id}
											className="flex items-center space-x-2 py-1"
										>
											<RadioGroupItem value={doc.id} id={`primary-${doc.id}`} />
											<Label
												htmlFor={`primary-${doc.id}`}
												className="text-sm cursor-pointer"
											>
												{doc.filename}
											</Label>
										</div>
									))}
								</ScrollArea>
							</RadioGroup>
						</div>
					</div>

					<div className="space-y-3">
						<h3 className="text-sm font-medium">Related Documents</h3>
						<div className="border rounded-md p-3 bg-background/50 dark:bg-[#252525]/50">
							<ScrollArea className="h-[250px] pr-3">
								<div className="space-y-2">
									{documents.map((doc) => (
										<div
											key={doc.id}
											className="flex items-center space-x-2 py-1"
										>
											<Checkbox
												id={`related-${doc.id}`}
												checked={selectedDocumentIds.includes(doc.id)}
												onCheckedChange={(checked) =>
													handleDocumentSelect(doc.id, checked === true)
												}
												disabled={doc.id === primaryDocumentId}
											/>
											<Label
												htmlFor={`related-${doc.id}`}
												className={`text-sm cursor-pointer ${
													doc.id === primaryDocumentId
														? "text-muted-foreground"
														: ""
												}`}
											>
												{doc.filename}
												{doc.id === primaryDocumentId && " (Primary Document)"}
											</Label>
										</div>
									))}
								</div>
							</ScrollArea>
						</div>
					</div>
				</div>
			</CardContent>

			<CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
				<Button variant="outline" onClick={onCancel}>
					Cancel
				</Button>
				<Button
					onClick={handleCompare}
					disabled={
						!primaryDocumentId ||
						selectedDocumentIds.filter((id) => id !== primaryDocumentId)
							.length === 0
					}
				>
					Compare Documents
				</Button>
			</CardFooter>
		</Card>
	);
}
