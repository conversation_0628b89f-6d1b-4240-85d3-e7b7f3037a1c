"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, FileText, Download, FileOutput, Edit2 } from "lucide-react"
import type { EnhancedComparisonResult } from "@/lib/services/document-comparison-service"
import { cn } from "@/lib/utils"
import styles from "./enhanced-comparison.module.css"
import { CorrectionForm } from "@/components/feedback"

interface EnhancedComparisonProps {
  result: EnhancedComparisonResult
  onExport?: (format: "pdf" | "docx" | "html") => void
  onSummary?: () => void
}

export function EnhancedComparison({ result, onExport, onSummary }: EnhancedComparisonProps) {
  const [activeTab, setActiveTab] = useState<string>("visual")
  const [showCorrectionForm, setShowCorrectionForm] = useState(false)

  // Function to render the visual diff HTML safely
  const renderVisualDiff = () => {
    if (result.visualization?.htmlDiff) {
      // Log the HTML content to inspect its structure
      console.log("Visual Diff HTML:", result.visualization.htmlDiff);
      
      // Process the HTML to make it dark mode compatible
      // This more aggressively transforms the HTML to ensure dark mode compatibility
      const processedHtml = result.visualization.htmlDiff
        // Add classes to elements with specific styles
        .replace(/<span style="background-color:(.*?);?">/g, '<span class="diff-highlight" style="background-color:$1;">')
        .replace(/<span style="color:(.*?);?">/g, '<span class="diff-text" style="color:$1;">')
        // Target specific colors commonly used in diffs
        .replace(/<span style="background-color: ?#98ff98;?">/g, '<span class="diff-add">')
        .replace(/<span style="background-color: ?#ff9898;?">/g, '<span class="diff-delete">')
        .replace(/<span style="background-color: ?rgb\(152, 255, 152\);?">/g, '<span class="diff-add">')
        .replace(/<span style="background-color: ?rgb\(255, 152, 152\);?">/g, '<span class="diff-delete">')
        // Target spans with green/red text
        .replace(/<span style="color: ?green;?">/g, '<span class="diff-add-text">')
        .replace(/<span style="color: ?red;?">/g, '<span class="diff-delete-text">');
      
      return (
        <div
          className={cn("p-4 border rounded-md bg-white dark:bg-black overflow-auto max-h-[600px]", styles["visual-diff-container"])}
          dangerouslySetInnerHTML={{ __html: processedHtml }}
        />
      )
    }
    return <p className="text-muted-foreground">Visual diff not available</p>
  }

  // Function to render the text diffs
  const renderTextDiffs = () => {
    return (
      <div className="space-y-2 p-4 border rounded-md overflow-auto max-h-[600px]">
        {result.diffs.map((diff, index) => {
          const classes = {
            equal: "text-foreground",
            delete: "bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-200 line-through",
            insert: "bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-200",
          }

          return (
            <div key={index} className={cn("py-1 px-2 rounded", classes[diff.type])}>
              {diff.text}
            </div>
          )
        })}
      </div>
    )
  }

  // Function to render section diffs
  const renderSectionDiffs = () => {
    if (!result.sectionDiffs || result.sectionDiffs.length === 0) {
      return <p className="text-muted-foreground">Section analysis not available</p>
    }

    return (
      <div className="space-y-4">
        {result.sectionDiffs.map((sectionDiff, index) => (
          <Collapsible key={index} className="w-full">
            <Card>
              <CollapsibleTrigger className="w-full text-left">
                <CardHeader className="flex flex-row items-center justify-between p-4">
                  <div>
                    <CardTitle className="text-base flex items-center">
                      {sectionDiff.sectionA.title || `Section ${index + 1}`}
                      <Badge variant="outline" className="ml-2">
                        {sectionDiff.differences.length} changes
                      </Badge>
                    </CardTitle>
                  </div>
                  <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                </CardHeader>
              </CollapsibleTrigger>
              <CollapsibleContent>
                <CardContent className="pt-0 pb-4">
                  <div className="space-y-4">
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="text-sm font-medium mb-2">Original</h4>
                        <div className="p-3 bg-secondary/50 rounded-md">
                          <p className="text-xs text-muted-foreground mb-1">
                            Lines {sectionDiff.sectionA.lineRange.start}-{sectionDiff.sectionA.lineRange.end}
                          </p>
                          <p className="text-sm whitespace-pre-wrap">{sectionDiff.sectionA.content}</p>
                        </div>
                      </div>
                      <div>
                        <h4 className="text-sm font-medium mb-2">Modified</h4>
                        <div className="p-3 bg-secondary/50 rounded-md">
                          <p className="text-xs text-muted-foreground mb-1">
                            Lines {sectionDiff.sectionB.lineRange.start}-{sectionDiff.sectionB.lineRange.end}
                          </p>
                          <p className="text-sm whitespace-pre-wrap">{sectionDiff.sectionB.content}</p>
                        </div>
                      </div>
                    </div>

                    <div>
                      <h4 className="text-sm font-medium mb-2">Differences</h4>
                      <div className="space-y-2">
                        {sectionDiff.differences.map((diff, diffIndex) => {
                          return (
                            <div key={diffIndex} className="p-3 rounded-md border">
                              <p className="text-xs text-muted-foreground mb-1">
                                Line {diff.lineNumber} - {diff.diffType}
                              </p>
                              <div className="grid grid-cols-2 gap-4 mt-2">
                                <div className="p-2 rounded bg-red-100/50 dark:bg-red-900/20">
                                  <p className="text-sm whitespace-pre-wrap">{diff.original}</p>
                                </div>
                                <div className="p-2 rounded bg-green-100/50 dark:bg-green-900/20">
                                  <p className="text-sm whitespace-pre-wrap">{diff.modified}</p>
                                </div>
                              </div>
                            </div>
                          )
                        })}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </CollapsibleContent>
            </Card>
          </Collapsible>
        ))}
      </div>
    )
  }

  return (
    <div className={styles.container}>
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Document Comparison</h2>
        <div className="flex gap-2">
          {onSummary && (
            <Button onClick={onSummary} variant="outline" size="sm">
              Executive Summary
            </Button>
          )}
          {onExport && (
            <div className="flex gap-1">
              <Button onClick={() => onExport("pdf")} variant="outline" size="sm">
                <Download className="h-4 w-4 mr-1" />
                PDF
              </Button>
              <Button onClick={() => onExport("docx")} variant="outline" size="sm">
                <FileText className="h-4 w-4 mr-1" />
                DOCX
              </Button>
              <Button onClick={() => onExport("html")} variant="outline" size="sm">
                <FileOutput className="h-4 w-4 mr-1" />
                HTML
              </Button>
            </div>
          )}
        </div>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Summary</CardTitle>
          <CardDescription>Overview of document changes</CardDescription>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
            <div className="p-4 bg-secondary/50 rounded-md text-center">
              <p className="text-2xl font-bold">{result.metadata.summary.addedLines}</p>
              <p className="text-sm text-muted-foreground">Added Lines</p>
            </div>
            <div className="p-4 bg-secondary/50 rounded-md text-center">
              <p className="text-2xl font-bold">{result.metadata.summary.removedLines}</p>
              <p className="text-sm text-muted-foreground">Removed Lines</p>
            </div>
            <div className="p-4 bg-secondary/50 rounded-md text-center">
              <p className="text-2xl font-bold">{result.metadata.summary.modifiedLines}</p>
              <p className="text-sm text-muted-foreground">Modified Lines</p>
            </div>
            <div className="p-4 bg-secondary/50 rounded-md text-center">
              <p className="text-2xl font-bold">{result.metadata.summary.totalChanges}</p>
              <p className="text-sm text-muted-foreground">Total Changes</p>
            </div>
          </div>

          <div className="grid grid-cols-2 gap-4 mt-4">
            <div className="p-4 bg-secondary/50 rounded-md">
              <p className="text-sm font-medium">Document A</p>
              <p className="text-sm text-muted-foreground">
                {result.metadata.documentStats.documentA.length} characters
              </p>
            </div>
            <div className="p-4 bg-secondary/50 rounded-md">
              <p className="text-sm font-medium">Document B</p>
              <p className="text-sm text-muted-foreground">
                {result.metadata.documentStats.documentB.length} characters
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-3 mb-6">
          <TabsTrigger value="visual">Visual Diff</TabsTrigger>
          <TabsTrigger value="text">Text Diff</TabsTrigger>
          <TabsTrigger value="sections">Section Analysis</TabsTrigger>
        </TabsList>

        <TabsContent value="visual" className="space-y-6">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between">
              <div>
                <CardTitle>Visual Comparison</CardTitle>
                <CardDescription>Side-by-side visual comparison with highlighted changes</CardDescription>
              </div>
              <Button
                variant="outline"
                size="sm"
                className="flex items-center gap-1"
                onClick={() => setShowCorrectionForm(true)}
              >
                <Edit2 className="h-3.5 w-3.5" />
                Suggest Correction
              </Button>
            </CardHeader>
            <CardContent>{renderVisualDiff()}</CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="text" className="space-y-6">
          {renderTextDiffs()}
        </TabsContent>

        <TabsContent value="sections" className="space-y-6">
          {renderSectionDiffs()}
        </TabsContent>
      </Tabs>
      
      {/* Correction form for visual diff */}
      <CorrectionForm
        sourceId={result.id || "visual-diff"}
        sourceType="comparison"
        contextData={{ 
          comparisonType: "visual-diff",
          documentStats: {
            documentA: result.metadata?.documentStats?.documentA || { length: 0 },
            documentB: result.metadata?.documentStats?.documentB || { length: 0 }
          }
        }}
        originalContent={result.visualization?.htmlDiff || ""}
        open={showCorrectionForm}
        onOpenChange={setShowCorrectionForm}
      />
    </div>
  )
}
