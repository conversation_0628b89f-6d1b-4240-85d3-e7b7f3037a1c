"use client";

import { useState, useEffect } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import { documentService, type DocumentMetadata } from "@/lib/services/document-service";
import { Loader2, FileText, Search } from "lucide-react";

interface SimpleDocumentSelectorProps {
  onSelect: (documentId: string) => void;
  title?: string;
  description?: string;
}

export function SimpleDocumentSelector({ 
  onSelect, 
  title = "Select a Document", 
  description = "Choose a document from your library" 
}: SimpleDocumentSelectorProps) {
  const [isLoading, setIsLoading] = useState(true);
  const [documents, setDocuments] = useState<DocumentMetadata[]>([]);
  const [searchQuery, setSearchQuery] = useState("");
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchDocuments = async () => {
      try {
        setIsLoading(true);
        setError(null);
        const response = await documentService.getDocuments();
        setDocuments(response.items);
      } catch (err) {
        console.error("Error fetching documents:", err);
        setError("Failed to load documents. Please try again.");
      } finally {
        setIsLoading(false);
      }
    };

    fetchDocuments();
  }, []);

  // Filter documents based on search query
  const filteredDocuments = documents.filter((doc) => {
    const searchLower = searchQuery.toLowerCase();
    return (
      doc.title?.toLowerCase().includes(searchLower) ||
      doc.id.toLowerCase().includes(searchLower)
    );
  });

  return (
    <Card className="w-full">
      <CardContent className="p-4">
        {(title || description) && (
          <div className="mb-4">
            {title && <h3 className="text-lg font-semibold mb-1">{title}</h3>}
            {description && <p className="text-sm text-muted-foreground">{description}</p>}
          </div>
        )}
        <div className="flex items-center gap-2 mb-4">
          <Search className="h-4 w-4 text-muted-foreground" />
          <Input
            placeholder="Search documents..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="h-9"
          />
        </div>

        {isLoading ? (
          <div className="flex justify-center items-center py-8">
            <Loader2 className="h-6 w-6 animate-spin text-primary" />
            <span className="ml-2">Loading documents...</span>
          </div>
        ) : error ? (
          <div className="text-center py-8 text-destructive">{error}</div>
        ) : filteredDocuments.length === 0 ? (
          <div className="text-center py-8 text-muted-foreground">
            {searchQuery ? "No documents match your search" : "No documents found"}
          </div>
        ) : (
          <ScrollArea className="h-[300px] pr-4">
            <div className="space-y-2">
              {filteredDocuments.map((doc) => (
                <Button
                  key={doc.id}
                  variant="outline"
                  className="w-full justify-start text-left h-auto py-3"
                  onClick={() => onSelect(doc.id)}
                >
                  <FileText className="h-4 w-4 mr-2 flex-shrink-0" />
                  <div className="truncate">
                    <span className="font-medium">
                      {doc.filename || doc.title || doc.id}
                    </span>
                  </div>
                </Button>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}
