"use client"

import { useState } from "react"
import { Card, CardContent } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { TagManager } from "@/components/document-organization/tag-manager"
import { FolderTree } from "@/components/document-organization/folder-tree"
import { TagIcon, FolderIcon } from "lucide-react"
import type { DocumentMetadata } from "@/lib/services/document-service"

interface DocumentOrganizationPanelProps {
  document?: DocumentMetadata | null
}

export function DocumentOrganizationPanel({ document }: DocumentOrganizationPanelProps) {
  const [activeTab, setActiveTab] = useState("tags")

  if (!document) {
    return (
      <Card className="mt-4">
        <CardContent className="p-4 text-center text-muted-foreground">
          Select a document to organize it with tags and folders
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="mt-4">
      <CardContent className="p-4">
        <Tabs value={activeTab} onValueChange={setActiveTab}>
          <TabsList className="grid grid-cols-2 mb-4">
            <TabsTrigger value="tags" className="flex items-center">
              <TagIcon className="h-4 w-4 mr-2" />
              Tags
            </TabsTrigger>
            <TabsTrigger value="folders" className="flex items-center">
              <FolderIcon className="h-4 w-4 mr-2" />
              Folders
            </TabsTrigger>
          </TabsList>

          <TabsContent value="tags" className="mt-0">
            <TagManager documentId={document.id} />
          </TabsContent>

          <TabsContent value="folders" className="mt-0">
            <FolderTree documentId={document.id} />
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
