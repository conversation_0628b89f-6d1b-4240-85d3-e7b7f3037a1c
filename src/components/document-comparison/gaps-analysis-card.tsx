"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, AlertCircle, Info } from "lucide-react"

interface Gap {
  area: string
  description: string
  recommendation: string
  priority: "low" | "medium" | "high"
}

interface GapsAnalysisCardProps {
  gap: Gap
}

export function GapsAnalysisCard({ gap }: GapsAnalysisCardProps) {
  const getPriorityBadge = (priority: "low" | "medium" | "high") => {
    switch (priority) {
      case "high":
        return (
          <Badge variant="outline" className="bg-red-500/20 text-red-500 border-red-500/30 flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            High Priority
          </Badge>
        )
      case "medium":
        return (
          <Badge
            variant="outline"
            className="bg-amber-500/20 text-amber-500 border-amber-500/30 flex items-center gap-1"
          >
            <AlertCircle className="h-3 w-3" />
            Medium Priority
          </Badge>
        )
      case "low":
        return (
          <Badge variant="outline" className="bg-blue-500/20 text-blue-500 border-blue-500/30 flex items-center gap-1">
            <Info className="h-3 w-3" />
            Low Priority
          </Badge>
        )
    }
  }

  return (
    <Card className="border-border dark:border-neutral-700">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-base font-medium">{gap.area}</CardTitle>
          {getPriorityBadge(gap.priority)}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          <div className="p-3 bg-secondary/20 dark:bg-[#252525] rounded-md">
            <h4 className="text-sm font-medium mb-1">Gap Description:</h4>
            <p className="text-sm">{gap.description}</p>
          </div>

          <div className="p-3 bg-secondary/30 dark:bg-[#2a2a2a] rounded-md">
            <h4 className="text-sm font-medium mb-1">Recommendation:</h4>
            <p className="text-sm">{gap.recommendation}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
