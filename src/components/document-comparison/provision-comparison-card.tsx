"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { AlertTriangle, AlertCircle, Info } from "lucide-react"
import { cn } from "@/lib/utils"

interface Provision {
  topic: string
  provisions: Array<{
    document: string
    content: string
    sectionReference: string
    conflicts: string
  }>
  significance: "low" | "medium" | "high"
  recommendation: string
}

interface ProvisionComparisonCardProps {
  provision: Provision
}

export function ProvisionComparisonCard({ provision }: ProvisionComparisonCardProps) {
  const getSignificanceBadge = (significance: "low" | "medium" | "high") => {
    switch (significance) {
      case "high":
        return (
          <Badge variant="outline" className="bg-red-500/20 text-red-500 border-red-500/30 flex items-center gap-1">
            <AlertTriangle className="h-3 w-3" />
            High Significance
          </Badge>
        )
      case "medium":
        return (
          <Badge
            variant="outline"
            className="bg-amber-500/20 text-amber-500 border-amber-500/30 flex items-center gap-1"
          >
            <AlertCircle className="h-3 w-3" />
            Medium Significance
          </Badge>
        )
      case "low":
        return (
          <Badge variant="outline" className="bg-blue-500/20 text-blue-500 border-blue-500/30 flex items-center gap-1">
            <Info className="h-3 w-3" />
            Low Significance
          </Badge>
        )
    }
  }

  return (
    <Card className="border-border dark:border-neutral-700">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <CardTitle className="text-base font-medium">{provision.topic}</CardTitle>
          {getSignificanceBadge(provision.significance)}
        </div>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {provision.provisions.map((item, index) => (
            <div
              key={index}
              className={cn(
                "p-3 rounded-md",
                item.conflicts
                  ? "bg-red-500/10 dark:bg-red-950/20 border border-red-500/20"
                  : "bg-secondary/20 dark:bg-[#252525]",
              )}
            >
              <div className="flex justify-between items-start mb-2">
                <Badge variant="outline">{item.document}</Badge>
                <span className="text-xs text-muted-foreground">{item.sectionReference}</span>
              </div>
              <p className="text-sm mb-2">{item.content}</p>
              {item.conflicts && (
                <div className="mt-2 text-xs text-red-500 dark:text-red-400">
                  <AlertTriangle className="h-3 w-3 inline-block mr-1" />
                  <span>{item.conflicts}</span>
                </div>
              )}
            </div>
          ))}

          <div className="mt-4 p-3 bg-secondary/30 dark:bg-[#2a2a2a] rounded-md">
            <h4 className="text-sm font-medium mb-1">Recommendation:</h4>
            <p className="text-sm">{provision.recommendation}</p>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
