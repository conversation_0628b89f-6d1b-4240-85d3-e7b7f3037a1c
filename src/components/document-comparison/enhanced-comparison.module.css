/* Enhanced comparison styles - DEBUGGING VISUAL DIFF */

.container {
  margin-bottom: 1.5rem;
  display: flex;
  flex-direction: column;
  gap: 1.5rem;
}

.visual-diff-container :global(.diff-highlight) {
  border-radius: 2px;
  padding: 0 2px;
}

.visual-diff-container :global(.diff-text) {
  display: inline;
}

.visual-diff-container :global(.diff-add) {
  background-color: rgba(74, 222, 128, 0.2) !important;
  color: inherit !important;
  border-radius: 2px;
  padding: 0 2px;
}

.visual-diff-container :global(.diff-delete) {
  background-color: rgba(248, 113, 113, 0.2) !important;
  color: inherit !important;
  border-radius: 2px;
  padding: 0 2px;
}

.visual-diff-container :global(.diff-add-text) {
  color: rgb(22, 163, 74) !important;
}

.visual-diff-container :global(.diff-delete-text) {
  color: rgb(220, 38, 38) !important;
}

:global(.dark) .visual-diff-container :global(.diff-add) {
  background-color: rgba(22, 163, 74, 0.85) !important;
  color: white !important;
  border: 1px solid rgb(34, 197, 94) !important;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

:global(.dark) .visual-diff-container :global(.diff-delete) {
  background-color: rgba(220, 38, 38, 0.85) !important;
  color: white !important;
  border: 1px solid rgb(239, 68, 68) !important;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

:global(.dark) .visual-diff-container :global(.diff-add-text) {
  color: rgb(134, 239, 172) !important;
  background-color: rgba(22, 163, 74, 0.6) !important;
  padding: 0 2px;
  border-radius: 2px;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

:global(.dark) .visual-diff-container :global(.diff-delete-text) {
  color: rgb(252, 165, 165) !important;
  background-color: rgba(220, 38, 38, 0.6) !important;
  padding: 0 2px;
  border-radius: 2px;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

:global(.dark)
  .visual-diff-container
  :global(.diff-highlight[style*="background-color: rgb(152, 255, 152)"]),
:global(.dark)
  .visual-diff-container
  :global(.diff-highlight[style*="background-color:#98ff98"]),
:global(.dark)
  .visual-diff-container
  :global(.diff-highlight[style*="background-color: #98ff98"]),
:global(.dark)
  .visual-diff-container
  :global(.diff-highlight[style*="background-color:green"]) {
  background-color: rgba(22, 163, 74, 0.85) !important;
  color: white !important;
  border: 1px solid rgb(34, 197, 94) !important;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

:global(.dark)
  .visual-diff-container
  :global(.diff-highlight[style*="background-color: rgb(255, 152, 152)"]),
:global(.dark)
  .visual-diff-container
  :global(.diff-highlight[style*="background-color:#ff9898"]),
:global(.dark)
  .visual-diff-container
  :global(.diff-highlight[style*="background-color: #ff9898"]),
:global(.dark)
  .visual-diff-container
  :global(.diff-highlight[style*="background-color:red"]) {
  background-color: rgba(220, 38, 38, 0.85) !important;
  color: white !important;
  border: 1px solid rgb(239, 68, 68) !important;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

:global(.dark)
  .visual-diff-container
  :global(.diff-text[style*="color: green"]),
:global(.dark) .visual-diff-container :global(.diff-text[style*="color:#00"]),
:global(.dark) .visual-diff-container :global(.diff-text[style*="color: #00"]) {
  color: rgb(134, 239, 172) !important;
  background-color: rgba(22, 163, 74, 0.6) !important;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
  padding: 0 2px;
  border-radius: 2px;
}

:global(.dark) .visual-diff-container :global(.diff-text[style*="color: red"]),
:global(.dark) .visual-diff-container :global(.diff-text[style*="color:#ff"]),
:global(.dark) .visual-diff-container :global(.diff-text[style*="color: #ff"]) {
  color: rgb(252, 165, 165) !important;
  background-color: rgba(220, 38, 38, 0.6) !important;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
  padding: 0 2px;
  border-radius: 2px;
}

.visual-diff-container :global(span),
.visual-diff-container :global(p),
.visual-diff-container :global(div),
.visual-diff-container :global(*) {
  color: inherit;
}

:global(.dark) .visual-diff-container :global(span[style*="color"]),
:global(.dark) .visual-diff-container :global(p[style*="color"]),
:global(.dark) .visual-diff-container :global(div[style*="color"]),
:global(.dark) .visual-diff-container :global(*[style*="color"]) {
  color: inherit !important;
}

:global(.dark) .visual-diff-container :global(span[style*="color: green"]),
:global(.dark) .visual-diff-container :global(*[style*="color: green"]),
:global(.dark)
  .visual-diff-container
  :global(*[style*="background-color: green"]) {
  color: white !important;
  background-color: rgba(22, 163, 74, 0.85) !important;
  text-decoration: none !important;
  display: inline-block;
  border-radius: 2px;
  padding: 0 2px;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

:global(.dark) .visual-diff-container :global(span[style*="color: red"]),
:global(.dark) .visual-diff-container :global(*[style*="color: red"]),
:global(.dark)
  .visual-diff-container
  :global(*[style*="background-color: red"]) {
  color: white !important;
  background-color: rgba(220, 38, 38, 0.85) !important;
  text-decoration: line-through !important;
  display: inline-block;
  border-radius: 2px;
  padding: 0 2px;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

/* Target specific background colors from the API's HTML */
:global(.dark)
  .visual-diff-container
  :global(span[style*="background-color: #e6ffe6"]),
:global(.dark)
  .visual-diff-container
  :global(span[style*="background-color:#e6ffe6"]) {
  background-color: rgba(22, 163, 74, 0.85) !important;
  color: white !important;
  border: 1px solid rgb(34, 197, 94) !important;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

:global(.dark)
  .visual-diff-container
  :global(span[style*="background-color: #ffe6e6"]),
:global(.dark)
  .visual-diff-container
  :global(span[style*="background-color:#ffe6e6"]) {
  background-color: rgba(220, 38, 38, 0.85) !important;
  color: white !important;
  border: 1px solid rgb(239, 68, 68) !important;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

/* Ensure text is visible in dark mode */
:global(.dark) .visual-diff-container {
  color: #eee;
}

/* Make sure transparent backgrounds don't inherit dark styling */
:global(.dark)
  .visual-diff-container
  :global(span[style*="background-color: transparent"]),
:global(.dark)
  .visual-diff-container
  :global(span[style*="background-color:transparent"]) {
  background-color: transparent !important;
  color: #eee !important;
  border: none !important;
  text-shadow: none !important;
}

.visual-diff-container :global(ins),
.visual-diff-container :global(.insert),
.visual-diff-container :global([data-diff-type="insert"]),
.visual-diff-container :global(.addition) {
  background-color: rgba(74, 222, 128, 0.2) !important;
  text-decoration: none !important;
  color: inherit !important;
}

.visual-diff-container :global(del),
.visual-diff-container :global(.delete),
.visual-diff-container :global([data-diff-type="delete"]),
.visual-diff-container :global(.deletion) {
  background-color: rgba(248, 113, 113, 0.2) !important;
  text-decoration: line-through !important;
  color: inherit !important;
}

:global(.dark) .visual-diff-container :global(ins),
:global(.dark) .visual-diff-container :global(.insert),
:global(.dark) .visual-diff-container :global([data-diff-type="insert"]),
:global(.dark) .visual-diff-container :global(.addition) {
  background-color: rgba(22, 163, 74, 0.85) !important;
  color: white !important;
  border-bottom: 1px solid rgb(34, 197, 94) !important;
  padding: 1px 0 !important;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

:global(.dark) .visual-diff-container :global(del),
:global(.dark) .visual-diff-container :global(.delete),
:global(.dark) .visual-diff-container :global([data-diff-type="delete"]),
:global(.dark) .visual-diff-container :global(.deletion) {
  background-color: rgba(220, 38, 38, 0.85) !important;
  color: white !important;
  border-bottom: 1px solid rgb(239, 68, 68) !important;
  padding: 1px 0 !important;
  text-shadow: 0 0 2px rgba(0, 0, 0, 0.7) !important;
}

:global(.dark) .visual-diff-container {
  color: rgb(229, 231, 235) !important;
}

:global(.dark) .visual-diff-container :global([style*="color: #000"]),
:global(.dark) .visual-diff-container :global([style*="color: black"]),
:global(.dark) .visual-diff-container :global([style*="color: rgb(0, 0, 0)"]) {
  color: rgb(229, 231, 235) !important;
}

:global(.dark) .visual-diff-container :global(*) {
  color: inherit;
}
