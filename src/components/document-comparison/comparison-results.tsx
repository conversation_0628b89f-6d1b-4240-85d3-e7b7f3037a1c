"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { ChevronDown, AlertTriangle, Info } from "lucide-react"
import type { DocumentComparisonResult } from "@/lib/services/document-comparison-service"
import { cn } from "@/lib/utils"

interface ComparisonResultsProps {
  result: DocumentComparisonResult
  onExport?: () => void
}

export function ComparisonResults({ result, onExport }: ComparisonResultsProps) {
  const [activeTab, setActiveTab] = useState<string>("overview")

  const getSignificanceBadge = (significance: "low" | "medium" | "high") => {
    const classes = {
      low: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
      medium: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100",
      high: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
    }

    return (
      <Badge variant="outline" className={cn("ml-2", classes[significance])}>
        {significance}
      </Badge>
    )
  }

  const getPriorityBadge = (priority: "low" | "medium" | "high") => {
    const classes = {
      low: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
      medium: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100",
      high: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
    }

    return (
      <Badge variant="outline" className={cn("ml-2", classes[priority])}>
        {priority}
      </Badge>
    )
  }

  const getCoherenceBadge = (coherence: "low" | "medium" | "high") => {
    const classes = {
      low: "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100",
      medium: "bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-100",
      high: "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100",
    }

    return (
      <Badge variant="outline" className={cn("ml-2", classes[coherence])}>
        {coherence}
      </Badge>
    )
  }

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">Document Comparison Results</h2>
        {onExport && (
          <Button onClick={onExport} variant="outline" size="sm">
            Export Results
          </Button>
        )}
      </div>

      <Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
        <TabsList className="grid grid-cols-4 mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="provisions">Key Provisions</TabsTrigger>
          <TabsTrigger value="gaps">Gaps Analysis</TabsTrigger>
          <TabsTrigger value="summary">Summary</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Document Set</CardTitle>
              <CardDescription>Overview of the documents being compared</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <p className="text-sm font-medium">Total Documents: {result.documentSet.count}</p>
                  <p className="text-sm text-muted-foreground">
                    Document Types: {result.documentSet.documentTypes.join(", ")}
                  </p>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Document Relationships</h4>
                  <div className="space-y-3">
                    {result.documentSet.relationships.map((relationship, index) => (
                      <div key={index} className="p-3 bg-secondary/50 rounded-md">
                        <p className="text-sm">
                          <span className="font-medium">{relationship.primaryDocument}</span> and{" "}
                          <span className="font-medium">{relationship.relatedDocument}</span> have a{" "}
                          <Badge variant="outline">{relationship.relationship}</Badge> relationship
                        </p>
                        <p className="text-sm text-muted-foreground mt-1">{relationship.description}</p>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Document Type Analysis</CardTitle>
              <CardDescription>Analysis specific to each document type</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm">
                  Dominant Document Type:{" "}
                  <Badge variant="outline" className="ml-1">
                    {result.typeSpecificAnalysis.dominantDocumentType}
                  </Badge>
                </p>

                <div className="space-y-3">
                  {result.typeSpecificAnalysis.findings.map((finding, index) => (
                    <div key={index} className="p-3 bg-secondary/50 rounded-md">
                      <p className="text-sm font-medium">{finding.documentType}</p>
                      <p className="text-sm mt-1">
                        <span className="text-muted-foreground">Observation:</span> {finding.observation}
                      </p>
                      <p className="text-sm mt-1">
                        <span className="text-muted-foreground">Implication:</span> {finding.implication}
                      </p>
                    </div>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="provisions" className="space-y-6">
          <div className="space-y-4">
            {result.keyProvisions.map((provision, index) => (
              <Collapsible key={index} className="w-full">
                <Card>
                  <CollapsibleTrigger className="w-full text-left">
                    <CardHeader className="flex flex-row items-center justify-between p-4">
                      <div>
                        <CardTitle className="text-base flex items-center">
                          {provision.topic}
                          {getSignificanceBadge(provision.significance)}
                        </CardTitle>
                      </div>
                      <ChevronDown className="h-4 w-4 shrink-0 transition-transform duration-200 group-data-[state=open]:rotate-180" />
                    </CardHeader>
                  </CollapsibleTrigger>
                  <CollapsibleContent>
                    <CardContent className="pt-0 pb-4">
                      <div className="space-y-4">
                        {provision.provisions.map((doc, docIndex) => (
                          <div key={docIndex} className="p-3 bg-secondary/50 rounded-md">
                            <div className="flex justify-between">
                              <p className="text-sm font-medium">{doc.document}</p>
                              <p className="text-xs text-muted-foreground">{doc.sectionReference}</p>
                            </div>
                            <p className="text-sm mt-2 whitespace-pre-wrap">{doc.content}</p>
                            {doc.conflicts && (
                              <Alert className="mt-2 bg-red-50 dark:bg-red-900/20">
                                <AlertTriangle className="h-4 w-4" />
                                <AlertTitle>Conflict</AlertTitle>
                                <AlertDescription>{doc.conflicts}</AlertDescription>
                              </Alert>
                            )}
                          </div>
                        ))}

                        <div className="mt-4">
                          <p className="text-sm font-medium">Recommendation:</p>
                          <p className="text-sm">{provision.recommendation}</p>
                        </div>
                      </div>
                    </CardContent>
                  </CollapsibleContent>
                </Card>
              </Collapsible>
            ))}
          </div>
        </TabsContent>

        <TabsContent value="gaps" className="space-y-6">
          <Card>
            <CardHeader>
              <CardTitle>Identified Gaps</CardTitle>
              <CardDescription>Missing elements across the document set</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                {result.gaps.map((gap, index) => (
                  <div key={index} className="p-4 border rounded-md">
                    <div className="flex items-center">
                      <h4 className="text-base font-medium">{gap.area}</h4>
                      {getPriorityBadge(gap.priority)}
                    </div>
                    <p className="text-sm mt-2">{gap.description}</p>
                    <Alert className="mt-3">
                      <Info className="h-4 w-4" />
                      <AlertTitle>Recommendation</AlertTitle>
                      <AlertDescription>{gap.recommendation}</AlertDescription>
                    </Alert>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="summary" className="space-y-6">
          <Card>
            <CardHeader>
              <div className="flex items-center">
                <CardTitle>Overall Assessment</CardTitle>
                {getCoherenceBadge(result.summary.overallCoherence)}
              </div>
              <CardDescription>Summary of document comparison analysis</CardDescription>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Key Insights</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {result.summary.keyInsights.map((insight, index) => (
                      <li key={index} className="text-sm">
                        {insight}
                      </li>
                    ))}
                  </ul>
                </div>

                <div>
                  <h4 className="text-sm font-medium mb-2">Recommendations</h4>
                  <ul className="list-disc pl-5 space-y-1">
                    {result.summary.recommendations.map((recommendation, index) => (
                      <li key={index} className="text-sm">
                        {recommendation}
                      </li>
                    ))}
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  )
}
