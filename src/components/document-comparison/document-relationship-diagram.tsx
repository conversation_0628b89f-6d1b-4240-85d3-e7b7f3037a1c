"use client"

import { useEffect, useRef } from "react"

interface Document {
  type: string
  id: string
}

interface Relationship {
  primaryDocument: string
  relatedDocument: string
  relationship: string
  description: string
}

interface DocumentRelationshipDiagramProps {
  documents: Document[]
  relationships: Relationship[]
}

export function DocumentRelationshipDiagram({ documents, relationships }: DocumentRelationshipDiagramProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)

  useEffect(() => {
    const canvas = canvasRef.current
    if (!canvas) return

    const ctx = canvas.getContext("2d")
    if (!ctx) return

    // Set canvas dimensions
    canvas.width = canvas.offsetWidth
    canvas.height = 300

    // Clear canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height)

    // Define colors for different relationship types
    const relationshipColors: Record<string, string> = {
      amendment: "#3b82f6", // blue
      supplement: "#10b981", // green
      replacement: "#ef4444", // red
      reference: "#8b5cf6", // purple
      independent: "#6b7280", // gray
    }

    // Calculate node positions
    const nodeRadius = 50
    const nodeSpacing = canvas.width / (documents.length + 1)
    const nodeY = canvas.height / 2

    const nodePositions: Record<string, { x: number; y: number }> = {}

    // Draw nodes
    documents.forEach((doc, index) => {
      const x = nodeSpacing * (index + 1)
      const y = nodeY
      nodePositions[doc.type] = { x, y }

      // Draw circle
      ctx.beginPath()
      ctx.arc(x, y, nodeRadius, 0, Math.PI * 2)
      ctx.fillStyle = "#1e1e1e"
      ctx.fill()
      ctx.strokeStyle = "#6b7280"
      ctx.lineWidth = 2
      ctx.stroke()

      // Draw text
      ctx.fillStyle = "#ffffff"
      ctx.font = "12px sans-serif"
      ctx.textAlign = "center"
      ctx.textBaseline = "middle"
      ctx.fillText(doc.type, x, y)
    })

    // Draw relationships
    relationships.forEach((rel) => {
      const startPos = nodePositions[rel.primaryDocument]
      const endPos = nodePositions[rel.relatedDocument]

      if (!startPos || !endPos) return

      // Calculate arrow points
      const angle = Math.atan2(endPos.y - startPos.y, endPos.x - startPos.x)
      const startX = startPos.x + nodeRadius * Math.cos(angle)
      const startY = startPos.y + nodeRadius * Math.sin(angle)
      const endX = endPos.x - nodeRadius * Math.cos(angle)
      const endY = endPos.y - nodeRadius * Math.sin(angle)

      // Draw line
      ctx.beginPath()
      ctx.moveTo(startX, startY)
      ctx.lineTo(endX, endY)
      ctx.strokeStyle = relationshipColors[rel.relationship] || "#6b7280"
      ctx.lineWidth = 2
      ctx.stroke()

      // Draw arrowhead
      const arrowSize = 10
      ctx.beginPath()
      ctx.moveTo(endX, endY)
      ctx.lineTo(endX - arrowSize * Math.cos(angle - Math.PI / 6), endY - arrowSize * Math.sin(angle - Math.PI / 6))
      ctx.lineTo(endX - arrowSize * Math.cos(angle + Math.PI / 6), endY - arrowSize * Math.sin(angle + Math.PI / 6))
      ctx.closePath()
      ctx.fillStyle = relationshipColors[rel.relationship] || "#6b7280"
      ctx.fill()

      // Draw relationship label
      const labelX = (startX + endX) / 2
      const labelY = (startY + endY) / 2 - 15
      ctx.fillStyle = "#ffffff"
      ctx.font = "10px sans-serif"
      ctx.textAlign = "center"
      ctx.textBaseline = "middle"
      ctx.fillText(rel.relationship, labelX, labelY)
    })
  }, [documents, relationships])

  return (
    <div className="w-full bg-secondary/20 dark:bg-[#252525] p-4 rounded-lg">
      <h3 className="text-lg font-medium mb-4">Document Relationship Diagram</h3>
      <div className="relative w-full">
        <canvas
          ref={canvasRef}
          className="w-full h-[300px] bg-[#1a1a1a] rounded-md"
          style={{ width: "100%", height: "300px" }}
        />
      </div>
      <div className="mt-4 flex flex-wrap gap-3">
        {Object.entries({
          amendment: "Amendment",
          supplement: "Supplement",
          replacement: "Replacement",
          reference: "Reference",
          independent: "Independent",
        }).map(([key, label]) => (
          <div key={key} className="flex items-center">
            <div
              className="w-3 h-3 rounded-full mr-1"
              style={{
                backgroundColor:
                  key === "amendment"
                    ? "#3b82f6"
                    : key === "supplement"
                      ? "#10b981"
                      : key === "replacement"
                        ? "#ef4444"
                        : key === "reference"
                          ? "#8b5cf6"
                          : "#6b7280",
              }}
            ></div>
            <span className="text-xs">{label}</span>
          </div>
        ))}
      </div>
    </div>
  )
}
