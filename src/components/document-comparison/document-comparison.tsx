"use client";

import { useState, useEffect } from "react";
import { useRouter } from "next/navigation";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
	Loader2,
	ArrowLeft,
	AlertTriangle,
	Info,
	CheckCircle,
	Lock,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
	documentComparisonService,
	type DocumentComparisonResult,
} from "@/lib/services/document-comparison-service";
import { DocumentRelationshipDiagram } from "./document-relationship-diagram";
import { ProvisionComparisonCard } from "./provision-comparison-card";
import { GapsAnalysisCard } from "./gaps-analysis-card";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { 
	<PERSON><PERSON><PERSON>,
	Too<PERSON><PERSON><PERSON>ontent,
	<PERSON><PERSON><PERSON>Provider,
	TooltipTrigger 
} from "@/components/ui/tooltip";

interface DocumentComparisonProps {
	primaryDocumentId: string;
	relatedDocumentIds: string[];
	onBack: () => void;
}

export function DocumentComparison({
	primaryDocumentId,
	relatedDocumentIds,
	onBack,
}: DocumentComparisonProps) {
	const router = useRouter();
	const { hasFeature } = useSubscription();
	const [comparisonData, setComparisonData] =
		useState<DocumentComparisonResult | null>(null);
	const [isLoading, setIsLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [activeTab, setActiveTab] = useState("overview");

	useEffect(() => {
		const fetchComparisonData = async () => {
			try {
				setIsLoading(true);
				setError(null);

				const response = await documentComparisonService.compareMultipleDocuments({
					primaryDocumentId,
					relatedDocumentIds,
				});

				setComparisonData(response);
			} catch (err) {
				console.error("Error comparing documents:", err);
				setError("Failed to compare documents. Please try again.");
			} finally {
				setIsLoading(false);
			}
		};

		fetchComparisonData();
	}, [primaryDocumentId, relatedDocumentIds]);

	if (isLoading) {
		return (
			<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
				<CardContent className="flex flex-col items-center justify-center p-12">
					<Loader2 className="h-12 w-12 animate-spin text-primary mb-4" />
					<p className="text-lg font-medium">Comparing Documents...</p>
					<p className="text-sm text-muted-foreground mt-2">
						This may take a moment as we analyze the relationships.
					</p>
				</CardContent>
			</Card>
		);
	}

	if (error) {
		return (
			<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
				<CardContent className="flex flex-col items-center justify-center p-12">
					<AlertTriangle className="h-12 w-12 text-destructive mb-4" />
					<p className="text-lg font-medium">Error Comparing Documents</p>
					<p className="text-sm text-muted-foreground mt-2">{error}</p>
					<Button onClick={onBack} className="mt-6">
						Go Back
					</Button>
				</CardContent>
			</Card>
		);
	}

	if (!comparisonData) {
		return null;
	}

	const getCoherenceBadge = (coherence: "low" | "medium" | "high") => {
		switch (coherence) {
			case "high":
				return (
					<Badge
						variant="outline"
						className="bg-green-500/20 text-green-500 border-green-500/30 flex items-center gap-1"
					>
						<CheckCircle className="h-3 w-3" />
						High Coherence
					</Badge>
				);
			case "medium":
				return (
					<Badge
						variant="outline"
						className="bg-amber-500/20 text-amber-500 border-amber-500/30 flex items-center gap-1"
					>
						<Info className="h-3 w-3" />
						Medium Coherence
					</Badge>
				);
			case "low":
				return (
					<Badge
						variant="outline"
						className="bg-destructive/20 text-destructive border-destructive/30 flex items-center gap-1"
					>
						<AlertTriangle className="h-3 w-3" />
						Low Coherence
					</Badge>
				);
		}
	};

	return (
		<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
			<CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
				<div className="flex items-start justify-between">
					<Button
						variant="ghost"
						size="sm"
						onClick={onBack}
						className="mr-2 -ml-2"
					>
						<ArrowLeft className="h-4 w-4 mr-1" />
						Back
					</Button>
					{getCoherenceBadge(comparisonData.summary.overallCoherence)}
				</div>
				<CardTitle className="text-xl font-bold mt-2">
					Document Comparison Analysis
				</CardTitle>
				<CardDescription className="text-muted-foreground">
					Comparing {comparisonData.documentSet.count} documents
				</CardDescription>
			</CardHeader>

			<CardContent className="p-0">
				<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
					<TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
						<TabsTrigger
							value="overview"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Overview
						</TabsTrigger>
						<TabsTrigger
							value="provisions"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Key Provisions
						</TabsTrigger>
						<TabsTrigger
							value="gaps"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Gaps Analysis
						</TabsTrigger>
						<TabsTrigger
							value="relationships"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Relationships
						</TabsTrigger>
					</TabsList>

					<TabsContent value="overview" className="p-4 mt-0">
						<div className="space-y-6">
							<div>
								<h3 className="text-lg font-medium mb-3">Summary</h3>
								<div className="bg-secondary/20 dark:bg-[#252525] p-4 rounded-lg">
									<div className="flex items-center mb-3">
										<h4 className="text-sm font-medium">Overall Coherence:</h4>
										<div className="ml-2">
											{getCoherenceBadge(
												comparisonData.summary.overallCoherence
											)}
										</div>
									</div>

									<div className="mb-4">
										<h4 className="text-sm font-medium mb-2">Key Insights:</h4>
										<ul className="space-y-1 pl-5 list-disc">
											{comparisonData.summary.keyInsights.map(
												(insight, index) => (
													<li key={index} className="text-sm">
														{insight}
													</li>
												)
											)}
										</ul>
									</div>

									<div>
										<h4 className="text-sm font-medium mb-2">
											Recommendations:
										</h4>
										<ul className="space-y-1 pl-5 list-disc">
											{comparisonData.summary.recommendations.map(
												(recommendation, index) => (
													<li key={index} className="text-sm">
														{recommendation}
													</li>
												)
											)}
										</ul>
									</div>
								</div>
							</div>

							<div>
								<h3 className="text-lg font-medium mb-3">Document Set</h3>
								<div className="bg-secondary/20 dark:bg-[#252525] p-4 rounded-lg">
									<div className="grid grid-cols-1 md:grid-cols-2 gap-4 mb-4">
										<div>
											<h4 className="text-sm font-medium mb-2">
												Document Types:
											</h4>
											<div className="flex flex-wrap gap-2">
												{comparisonData.documentSet.documentTypes.map(
													(type, index) => (
														<Badge key={index} variant="outline">
															{type}
														</Badge>
													)
												)}
											</div>
										</div>
										<div>
											<h4 className="text-sm font-medium mb-2">
												Dominant Document:
											</h4>
											<Badge
												variant="outline"
												className="bg-blue-500/20 text-blue-500 border-blue-500/30"
											>
												{
													comparisonData.typeSpecificAnalysis
														.dominantDocumentType
												}
											</Badge>
										</div>
									</div>

									<div>
										<h4 className="text-sm font-medium mb-2">
											Document Findings:
										</h4>
										<div className="space-y-3">
											{comparisonData.typeSpecificAnalysis.findings.map(
												(finding, index) => (
													<div
														key={index}
														className="bg-secondary/30 dark:bg-[#2a2a2a] p-3 rounded-md"
													>
														<div className="flex items-center mb-1">
															<Badge variant="outline" className="mr-2">
																{finding.documentType}
															</Badge>
															<span className="text-sm font-medium">
																{finding.observation}
															</span>
														</div>
														<p className="text-xs text-muted-foreground">
															{finding.implication}
														</p>
													</div>
												)
											)}
										</div>
									</div>
								</div>
							</div>

							<DocumentRelationshipDiagram
								documents={comparisonData.documentSet.documentTypes.map(
									(type) => ({ type, id: type })
								)}
								relationships={comparisonData.documentSet.relationships}
							/>
						</div>
					</TabsContent>

					<TabsContent value="provisions" className="mt-0">
						<ScrollArea className="h-[600px]">
							<div className="p-4 space-y-6">
								{comparisonData.keyProvisions.map((provision, index) => (
									<ProvisionComparisonCard key={index} provision={provision} />
								))}
							</div>
						</ScrollArea>
					</TabsContent>

					<TabsContent value="gaps" className="p-4 mt-0">
						<div className="space-y-4">
							{comparisonData.gaps.map((gap, index) => (
								<GapsAnalysisCard key={index} gap={gap} />
							))}
						</div>
					</TabsContent>

					<TabsContent value="relationships" className="p-4 mt-0">
						<div className="space-y-6">
							<h3 className="text-lg font-medium mb-3">
								Document Relationships
							</h3>
							<div className="bg-secondary/20 dark:bg-[#252525] p-4 rounded-lg">
								<div className="space-y-4">
									{comparisonData.documentSet.relationships.map(
										(relationship, index) => (
											<div
												key={index}
												className="bg-secondary/30 dark:bg-[#2a2a2a] p-3 rounded-md"
											>
												<div className="flex items-center mb-2">
													<Badge variant="outline" className="mr-2">
														{relationship.primaryDocument}
													</Badge>
													<span className="text-sm">→</span>
													<Badge variant="outline" className="mx-2">
														{relationship.relatedDocument}
													</Badge>
													<Badge
														variant="outline"
														className="bg-blue-500/20 text-blue-500 border-blue-500/30 capitalize"
													>
														{relationship.relationship}
													</Badge>
												</div>
												<p className="text-sm">{relationship.description}</p>
											</div>
										)
									)}
								</div>
							</div>

							<div className="mt-8">
								<DocumentRelationshipDiagram
									documents={comparisonData.documentSet.documentTypes.map(
										(type) => ({ type, id: type })
									)}
									relationships={comparisonData.documentSet.relationships}
								/>
							</div>
						</div>
					</TabsContent>
				</Tabs>
			</CardContent>

			<CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
				<div className="text-xs text-muted-foreground">
					{new Date().toLocaleDateString()} • Document Comparison Analysis
				</div>
				<div className="flex gap-2">
					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<span className="inline-block">
									<Button
										variant="outline"
										size="sm"
										className="h-7 text-xs bg-secondary/50 dark:bg-[#2a2a2a]"
										onClick={() => {
											if (hasFeature("enhanced_comparison") && relatedDocumentIds.length > 0) {
												router.push(`/document-comparison/enhanced?docA=${primaryDocumentId}&docB=${relatedDocumentIds[0]}`);
											}
										}}
										disabled={!hasFeature("enhanced_comparison")}
									>
										{!hasFeature("enhanced_comparison") && <Lock className="h-3 w-3 mr-1" />}
										Enhanced View
									</Button>
								</span>
							</TooltipTrigger>
							<TooltipContent side="top">
								<p>Available for Pro users only</p>
							</TooltipContent>
						</Tooltip>
					</TooltipProvider>
					<Button
						variant="outline"
						size="sm"
						className="h-7 text-xs bg-secondary/50 dark:bg-[#2a2a2a]"
					>
						Export Report
					</Button>
				</div>
			</CardFooter>
		</Card>
	);
}
