"use client";

import React from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert";
import { AlertTriangle, RefreshCcw } from "lucide-react";

interface Props {
  children: React.ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
}

async function reportError(error: Error, errorInfo?: React.ErrorInfo) {
  // TODO: Integrate with an error reporting service
  console.error("Error captured by boundary:", error);
  if (errorInfo) {
    console.error("Component stack:", errorInfo.componentStack);
  }
}

export class ErrorBoundary extends React.Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return {
      hasError: true,
      error,
    };
  }

  componentDidCatch(error: Error, errorInfo: React.ErrorInfo) {
    reportError(error, errorInfo);
  }

  private handleRetry = () => {
    this.setState({ hasError: false, error: undefined });
  };

  private handleReport = async () => {
    if (this.state.error) {
      await reportError(this.state.error);
      alert("Error has been reported to our team.");
    }
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="min-h-[400px] flex items-center justify-center p-8">
          <Alert variant="destructive" className="max-w-2xl">
            <AlertTriangle className="h-4 w-4" />
            <AlertTitle>Something went wrong</AlertTitle>
            <AlertDescription className="mt-4">
              <div className="space-y-4">
                <p>
                  An unexpected error has occurred. Our team has been notified and
                  will look into it.
                </p>
                {this.state.error && (
                  <pre className="mt-2 p-4 bg-destructive/10 rounded text-sm overflow-auto">
                    {this.state.error.message}
                  </pre>
                )}
                <div className="flex gap-4 mt-4">
                  <Button onClick={this.handleRetry} variant="secondary">
                    <RefreshCcw className="mr-2 h-4 w-4" />
                    Try Again
                  </Button>
                  <Button onClick={this.handleReport} variant="outline">
                    Report Issue
                  </Button>
                </div>
              </div>
            </AlertDescription>
          </Alert>
        </div>
      );
    }

    return this.props.children;
  }
}