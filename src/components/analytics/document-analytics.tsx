"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/analytics/charts"
import { StatCard } from "@/components/analytics/stat-card"
import type { DocumentAnalytics } from "@/lib/services/analytics-service"
import { BarChartIcon, PieChartIcon, FileText, CheckCircle, GitCompare } from "lucide-react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs"

interface DocumentAnalyticsProps {
  analytics: DocumentAnalytics
  previousPeriodAnalytics?: DocumentAnalytics | null
}

export function DocumentAnalyticsComponent({ analytics, previousPeriodAnalytics }: DocumentAnalyticsProps) {
  // Calculate trend percentages if previous period data is available
  const calculateTrend = (current: number, previous?: number): number | undefined => {
    if (previous === undefined || previous === 0) return undefined;
    return Math.round(((current - previous) / previous) * 100);
  };

  // Calculate trends for overview metrics
  const totalDocumentsTrend = calculateTrend(
    analytics.documentAnalysis.totalDocuments,
    previousPeriodAnalytics?.documentAnalysis.totalDocuments
  );

  const analyzedDocumentsTrend = calculateTrend(
    analytics.documentAnalysis.analyzedDocuments,
    previousPeriodAnalytics?.documentAnalysis.analyzedDocuments
  );

  const comparedDocumentsTrend = calculateTrend(
    analytics.documentAnalysis.comparedDocuments,
    previousPeriodAnalytics?.documentAnalysis.comparedDocuments
  );

  const analysisTimeTrend = calculateTrend(
    analytics.documentAnalysis.averageAnalysisTime,
    previousPeriodAnalytics?.documentAnalysis.averageAnalysisTime
  );

  // Calculate trends for classification metrics
  const automaticClassificationsTrend = analytics.documentAnalysis.classificationMetrics && previousPeriodAnalytics?.documentAnalysis.classificationMetrics
    ? calculateTrend(
        analytics.documentAnalysis.classificationMetrics.automaticClassifications,
        previousPeriodAnalytics.documentAnalysis.classificationMetrics.automaticClassifications
      )
    : undefined;

  const manualClassificationsTrend = analytics.documentAnalysis.classificationMetrics && previousPeriodAnalytics?.documentAnalysis.classificationMetrics
    ? calculateTrend(
        analytics.documentAnalysis.classificationMetrics.manualClassifications,
        previousPeriodAnalytics.documentAnalysis.classificationMetrics.manualClassifications
      )
    : undefined;

  const unclassifiedDocumentsTrend = analytics.documentAnalysis.classificationMetrics && previousPeriodAnalytics?.documentAnalysis.classificationMetrics
    ? calculateTrend(
        analytics.documentAnalysis.classificationMetrics.unclassifiedDocuments,
        previousPeriodAnalytics.documentAnalysis.classificationMetrics.unclassifiedDocuments
      )
    : undefined;

  const classificationAccuracyTrend = analytics.documentAnalysis.classificationMetrics && previousPeriodAnalytics?.documentAnalysis.classificationMetrics
    ? calculateTrend(
        analytics.documentAnalysis.classificationMetrics.classificationAccuracy * 100,
        previousPeriodAnalytics.documentAnalysis.classificationMetrics.classificationAccuracy * 100
      )
    : undefined;

  // Calculate trends for comparison metrics
  const totalComparisonsTrend = analytics.documentAnalysis.comparisonMetrics && previousPeriodAnalytics?.documentAnalysis.comparisonMetrics
    ? calculateTrend(
        analytics.documentAnalysis.comparisonMetrics.totalComparisons,
        previousPeriodAnalytics.documentAnalysis.comparisonMetrics.totalComparisons
      )
    : undefined;

  const basicComparisonsTrend = analytics.documentAnalysis.comparisonMetrics && previousPeriodAnalytics?.documentAnalysis.comparisonMetrics
    ? calculateTrend(
        analytics.documentAnalysis.comparisonMetrics.basicComparisons,
        previousPeriodAnalytics.documentAnalysis.comparisonMetrics.basicComparisons
      )
    : undefined;

  const enhancedComparisonsTrend = analytics.documentAnalysis.comparisonMetrics && previousPeriodAnalytics?.documentAnalysis.comparisonMetrics
    ? calculateTrend(
        analytics.documentAnalysis.comparisonMetrics.enhancedComparisons,
        previousPeriodAnalytics.documentAnalysis.comparisonMetrics.enhancedComparisons
      )
    : undefined;

  const comparisonRatioTrend = analytics.documentAnalysis.comparisonMetrics && previousPeriodAnalytics?.documentAnalysis.comparisonMetrics
    ? calculateTrend(
        (analytics.documentAnalysis.comparisonMetrics.enhancedComparisons / analytics.documentAnalysis.comparisonMetrics.totalComparisons) * 100,
        (previousPeriodAnalytics.documentAnalysis.comparisonMetrics.enhancedComparisons / previousPeriodAnalytics.documentAnalysis.comparisonMetrics.totalComparisons) * 100
      )
    : undefined;

  // Prepare data for document uploads chart
  const documentUploadsData = {
    labels: analytics.timeSeriesData.documentUploads.map((item) => new Date(item.date).toLocaleDateString()),
    datasets: [
      {
        label: "Document Uploads",
        data: analytics.timeSeriesData.documentUploads.map((item) => item.count),
        backgroundColor: "rgba(54, 162, 235, 0.5)",
        borderColor: "rgb(54, 162, 235)",
      },
    ],
  }

  // Prepare data for document types pie chart
  const documentTypesData = {
    labels: analytics.documentAnalysis.documentsByType.map((item) => item.type),
    datasets: [
      {
        label: "Document Types",
        data: analytics.documentAnalysis.documentsByType.map((item) => item.count),
        backgroundColor: [
          "rgba(255, 99, 132, 0.5)",
          "rgba(54, 162, 235, 0.5)",
          "rgba(255, 206, 86, 0.5)",
          "rgba(75, 192, 192, 0.5)",
          "rgba(153, 102, 255, 0.5)",
        ],
        borderColor: [
          "rgb(255, 99, 132)",
          "rgb(54, 162, 235)",
          "rgb(255, 206, 86)",
          "rgb(75, 192, 192)",
          "rgb(153, 102, 255)",
        ],
      },
    ],
  }

  // Prepare data for document classifications
  const documentClassificationsData = analytics.timeSeriesData.documentClassifications
    ? {
        labels: analytics.timeSeriesData.documentClassifications.map((item) =>
          new Date(item.date).toLocaleDateString(),
        ),
        datasets: [
          {
            label: "Automatic",
            data: analytics.timeSeriesData.documentClassifications.map((item) => item.automatic || 0),
            backgroundColor: "rgba(75, 192, 192, 0.5)",
            borderColor: "rgb(75, 192, 192)",
          },
          {
            label: "Manual",
            data: analytics.timeSeriesData.documentClassifications.map((item) => item.manual || 0),
            backgroundColor: "rgba(153, 102, 255, 0.5)",
            borderColor: "rgb(153, 102, 255)",
          },
        ],
      }
    : null

  // Prepare data for document comparisons
  const documentComparisonsData = {
    labels: analytics.timeSeriesData.documentComparisons.map((item) => new Date(item.date).toLocaleDateString()),
    datasets: [
      {
        label: "Basic",
        data: analytics.timeSeriesData.documentComparisons.map((item) => item.basic || 0),
        backgroundColor: "rgba(255, 159, 64, 0.5)",
        borderColor: "rgb(255, 159, 64)",
      },
      {
        label: "Enhanced",
        data: analytics.timeSeriesData.documentComparisons.map((item) => item.enhanced || 0),
        backgroundColor: "rgba(54, 162, 235, 0.5)",
        borderColor: "rgb(54, 162, 235)",
      },
    ],
  }

  // Prepare data for comparison types
  const comparisonTypesData = analytics.documentAnalysis.comparisonMetrics
    ? {
        labels: analytics.documentAnalysis.comparisonMetrics.comparisonsByType.map((item) => item.type),
        datasets: [
          {
            label: "Comparison Types",
            data: analytics.documentAnalysis.comparisonMetrics.comparisonsByType.map((item) => item.count),
            backgroundColor: ["rgba(255, 99, 132, 0.5)", "rgba(54, 162, 235, 0.5)", "rgba(255, 206, 86, 0.5)"],
            borderColor: ["rgb(255, 99, 132)", "rgb(54, 162, 235)", "rgb(255, 206, 86)"],
          },
        ],
      }
    : null

  // Prepare data for classification metrics
  const classificationMetricsData = analytics.documentAnalysis.classificationMetrics
    ? {
        labels: ["Automatic", "Manual", "Unclassified"],
        datasets: [
          {
            label: "Classification Distribution",
            data: [
              analytics.documentAnalysis.classificationMetrics.automaticClassifications,
              analytics.documentAnalysis.classificationMetrics.manualClassifications,
              analytics.documentAnalysis.classificationMetrics.unclassifiedDocuments,
            ],
            backgroundColor: ["rgba(75, 192, 192, 0.5)", "rgba(153, 102, 255, 0.5)", "rgba(255, 159, 64, 0.5)"],
            borderColor: ["rgb(75, 192, 192)", "rgb(153, 102, 255)", "rgb(255, 159, 64)"],
          },
        ],
      }
    : null

  return (
    <div className="space-y-8">
      <Tabs defaultValue="overview" className="w-full">
        <TabsList className="w-full mb-6">
          <TabsTrigger value="overview">Overview</TabsTrigger>
          <TabsTrigger value="classifications">Classifications</TabsTrigger>
          <TabsTrigger value="comparisons">Comparisons</TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="space-y-6">
          {/* Key metrics */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
            <StatCard
              title="Total Documents"
              value={analytics.documentAnalysis.totalDocuments}
              icon={<FileText className="h-4 w-4" />}
              description="Documents uploaded"
              trend={totalDocumentsTrend}
            />
            <StatCard
              title="Analyzed Documents"
              value={analytics.documentAnalysis.analyzedDocuments}
              icon={<FileText className="h-4 w-4" />}
              description="Documents analyzed"
              secondaryValue={analytics.documentAnalysis.totalDocuments}
              trend={analyzedDocumentsTrend}
            />
            <StatCard
              title="Compared Documents"
              value={analytics.documentAnalysis.comparedDocuments}
              icon={<GitCompare className="h-4 w-4" />}
              description="Documents compared"
              secondaryValue={analytics.documentAnalysis.totalDocuments}
              trend={comparedDocumentsTrend}
            />
            <StatCard
              title="Avg. Analysis Time"
              value={analytics.documentAnalysis.averageAnalysisTime}
              icon={<FileText className="h-4 w-4" />}
              description="In seconds"
              trend={analysisTimeTrend}
              valueSuffix="s"
            />
          </div>

          {/* Charts */}
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-base">Document Activity</CardTitle>
                    <CardDescription>Document uploads over time</CardDescription>
                  </div>
                  <BarChartIcon className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <BarChart data={documentUploadsData} height={300} />
              </CardContent>
            </Card>

            <Card>
              <CardHeader className="pb-2">
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-base">Document Types</CardTitle>
                    <CardDescription>Distribution by document type</CardDescription>
                  </div>
                  <PieChartIcon className="h-4 w-4 text-muted-foreground" />
                </div>
              </CardHeader>
              <CardContent>
                <DonutChart data={documentTypesData} height={300} />
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="classifications" className="space-y-6">
          {analytics.documentAnalysis.classificationMetrics ? (
            <>
              {/* Classification metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <StatCard
                  title="Automatic Classifications"
                  value={analytics.documentAnalysis.classificationMetrics.automaticClassifications}
                  icon={<CheckCircle className="h-4 w-4" />}
                  description="AI classified"
                  secondaryValue={analytics.documentAnalysis.totalDocuments}
                  trend={automaticClassificationsTrend}
                />
                <StatCard
                  title="Manual Classifications"
                  value={analytics.documentAnalysis.classificationMetrics.manualClassifications}
                  icon={<CheckCircle className="h-4 w-4" />}
                  description="User classified"
                  secondaryValue={analytics.documentAnalysis.totalDocuments}
                  trend={manualClassificationsTrend}
                />
                <StatCard
                  title="Unclassified Documents"
                  value={analytics.documentAnalysis.classificationMetrics.unclassifiedDocuments}
                  icon={<FileText className="h-4 w-4" />}
                  description="Pending classification"
                  secondaryValue={analytics.documentAnalysis.totalDocuments}
                  trend={unclassifiedDocumentsTrend}
                />
                <StatCard
                  title="Classification Accuracy"
                  value={analytics.documentAnalysis.classificationMetrics.classificationAccuracy * 100}
                  icon={<CheckCircle className="h-4 w-4" />}
                  description="Automatic classification"
                  valueSuffix="%"
                  trend={classificationAccuracyTrend}
                />
              </div>

              {/* Classification charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {documentClassificationsData && (
                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-base">Classification Activity</CardTitle>
                          <CardDescription>Automatic vs. manual classifications over time</CardDescription>
                        </div>
                        <BarChartIcon className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <StackedBarChart data={documentClassificationsData} height={300} />
                    </CardContent>
                  </Card>
                )}

                {classificationMetricsData && (
                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-base">Classification Distribution</CardTitle>
                          <CardDescription>Automatic vs. manual vs. unclassified</CardDescription>
                        </div>
                        <PieChartIcon className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <PieChart data={classificationMetricsData} height={300} />
                    </CardContent>
                  </Card>
                )}
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No classification data available</p>
            </div>
          )}
        </TabsContent>

        <TabsContent value="comparisons" className="space-y-6">
          {analytics.documentAnalysis.comparisonMetrics ? (
            <>
              {/* Comparison metrics */}
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
                <StatCard
                  title="Total Comparisons"
                  value={analytics.documentAnalysis.comparisonMetrics.totalComparisons}
                  icon={<GitCompare className="h-4 w-4" />}
                  description="Document comparisons"
                  trend={totalComparisonsTrend}
                />
                <StatCard
                  title="Basic Comparisons"
                  value={analytics.documentAnalysis.comparisonMetrics.basicComparisons}
                  icon={<GitCompare className="h-4 w-4" />}
                  description="Standard analysis"
                  secondaryValue={analytics.documentAnalysis.comparisonMetrics.totalComparisons}
                  trend={basicComparisonsTrend}
                />
                <StatCard
                  title="Enhanced Comparisons"
                  value={analytics.documentAnalysis.comparisonMetrics.enhancedComparisons}
                  icon={<GitCompare className="h-4 w-4" />}
                  description="Advanced analysis"
                  secondaryValue={analytics.documentAnalysis.comparisonMetrics.totalComparisons}
                  trend={enhancedComparisonsTrend}
                />
                <StatCard
                  title="Comparison Ratio"
                  value={
                    (analytics.documentAnalysis.comparisonMetrics.enhancedComparisons /
                      analytics.documentAnalysis.comparisonMetrics.totalComparisons) *
                    100
                  }
                  icon={<GitCompare className="h-4 w-4" />}
                  description="Enhanced vs. total"
                  valueSuffix="%"
                  trend={comparisonRatioTrend}
                />
              </div>

              {/* Comparison charts */}
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                <Card>
                  <CardHeader className="pb-2">
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle className="text-base">Comparison Activity</CardTitle>
                        <CardDescription>Basic vs. enhanced comparisons over time</CardDescription>
                      </div>
                      <BarChartIcon className="h-4 w-4 text-muted-foreground" />
                    </div>
                  </CardHeader>
                  <CardContent>
                    <StackedBarChart data={documentComparisonsData} height={300} />
                  </CardContent>
                </Card>

                {comparisonTypesData && (
                  <Card>
                    <CardHeader className="pb-2">
                      <div className="flex items-center justify-between">
                        <div>
                          <CardTitle className="text-base">Comparison Types</CardTitle>
                          <CardDescription>Distribution by comparison focus</CardDescription>
                        </div>
                        <PieChartIcon className="h-4 w-4 text-muted-foreground" />
                      </div>
                    </CardHeader>
                    <CardContent>
                      <PieChart data={comparisonTypesData} height={300} />
                    </CardContent>
                  </Card>
                )}
              </div>
            </>
          ) : (
            <div className="text-center py-12">
              <p className="text-muted-foreground">No comparison data available</p>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
