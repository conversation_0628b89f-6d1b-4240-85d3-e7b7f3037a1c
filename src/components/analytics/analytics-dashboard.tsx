"use client";

import {
	<PERSON>,
	<PERSON><PERSON>onte<PERSON>,
	Card<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import {
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	StackedBar<PERSON><PERSON>,
} from "@/components/analytics/charts";
import { StatCard } from "@/components/analytics/stat-card";
import { TopItemsList } from "@/components/analytics/top-items-list";
import type { OrganizationAnalytics } from "@/lib/services/analytics-service";
import {
	BarChartIcon,
	LineChartIcon,
	PieChartIcon,
	Users,
	FileText,
	MessageSquare,
	Clock,
	CheckCircle,
	GitCompare,
} from "lucide-react";

interface AnalyticsDashboardProps {
	analytics: OrganizationAnalytics;
	previousPeriodAnalytics?: OrganizationAnalytics | null;
}

export function AnalyticsDashboard({
	analytics,
	previousPeriodAnalytics,
}: AnalyticsDashboardProps) {
	// Calculate trend percentages if previous period data is available
	const calculateTrend = (
		current: number,
		previous?: number
	): number | undefined => {
		if (previous === undefined || previous === 0) return undefined;
		return Math.round(((current - previous) / previous) * 100);
	};

	// Get trend values for key metrics
	const documentsTrend = calculateTrend(
		analytics.documentAnalysis.totalDocuments,
		previousPeriodAnalytics?.documentAnalysis.totalDocuments
	);

	const activeUsersTrend = calculateTrend(
		analytics.userEngagement.activeUsers,
		previousPeriodAnalytics?.userEngagement.activeUsers
	);

	const queriesTrend = calculateTrend(
		analytics.userEngagement.totalQueries,
		previousPeriodAnalytics?.userEngagement.totalQueries
	);

	const responseTimeTrend = calculateTrend(
		analytics.userEngagement.averageResponseTime,
		previousPeriodAnalytics?.userEngagement.averageResponseTime
	);

	// Get trend values for classification and comparison metrics
	const classifiedDocumentsTrend =
		analytics.documentAnalysis.classificationMetrics &&
		previousPeriodAnalytics?.documentAnalysis.classificationMetrics
			? calculateTrend(
					analytics.documentAnalysis.classificationMetrics
						.automaticClassifications +
						analytics.documentAnalysis.classificationMetrics
							.manualClassifications,
					previousPeriodAnalytics.documentAnalysis.classificationMetrics
						.automaticClassifications +
						previousPeriodAnalytics.documentAnalysis.classificationMetrics
							.manualClassifications
			  )
			: undefined;

	const classificationAccuracyTrend =
		analytics.documentAnalysis.classificationMetrics &&
		previousPeriodAnalytics?.documentAnalysis.classificationMetrics
			? calculateTrend(
					analytics.documentAnalysis.classificationMetrics
						.classificationAccuracy,
					previousPeriodAnalytics.documentAnalysis.classificationMetrics
						.classificationAccuracy
			  )
			: undefined;

	const comparisonsTrend =
		analytics.documentAnalysis.comparisonMetrics &&
		previousPeriodAnalytics?.documentAnalysis.comparisonMetrics
			? calculateTrend(
					analytics.documentAnalysis.comparisonMetrics.totalComparisons,
					previousPeriodAnalytics.documentAnalysis.comparisonMetrics
						.totalComparisons
			  )
			: undefined;

	const enhancedComparisonsTrend =
		analytics.documentAnalysis.comparisonMetrics &&
		previousPeriodAnalytics?.documentAnalysis.comparisonMetrics
			? calculateTrend(
					analytics.documentAnalysis.comparisonMetrics.enhancedComparisons,
					previousPeriodAnalytics.documentAnalysis.comparisonMetrics
						.enhancedComparisons
			  )
			: undefined;

	// Prepare data for document uploads chart
	const documentUploadsData = {
		labels: analytics.timeSeriesData.documentUploads.map((item) =>
			new Date(item.date).toLocaleDateString()
		),
		datasets: [
			{
				label: "Document Uploads",
				data: analytics.timeSeriesData.documentUploads.map(
					(item) => item.count
				),
				backgroundColor: "rgba(54, 162, 235, 0.5)",
				borderColor: "rgb(54, 162, 235)",
			},
		],
	};

	// Prepare data for document types pie chart
	const documentTypesData = {
		labels: analytics.documentAnalysis.documentsByType.map((item) => item.type),
		datasets: [
			{
				label: "Document Types",
				data: analytics.documentAnalysis.documentsByType.map(
					(item) => item.count
				),
				backgroundColor: [
					"rgba(255, 99, 132, 0.5)",
					"rgba(54, 162, 235, 0.5)",
					"rgba(255, 206, 86, 0.5)",
					"rgba(75, 192, 192, 0.5)",
					"rgba(153, 102, 255, 0.5)",
				],
				borderColor: [
					"rgb(255, 99, 132)",
					"rgb(54, 162, 235)",
					"rgb(255, 206, 86)",
					"rgb(75, 192, 192)",
					"rgb(153, 102, 255)",
				],
			},
		],
	};

	// Prepare data for user queries line chart
	const userQueriesData = {
		labels: analytics.timeSeriesData.userQueries.map((item) =>
			new Date(item.date).toLocaleDateString()
		),
		datasets: [
			{
				label: "User Queries",
				data: analytics.timeSeriesData.userQueries.map((item) => item.count),
				borderColor: "rgb(75, 192, 192)",
				backgroundColor: "rgba(75, 192, 192, 0.5)",
				tension: 0.3,
			},
		],
	};

	// Prepare data for response times line chart
	const responseTimesData = {
		labels: analytics.timeSeriesData.averageResponseTimes.map((item) =>
			new Date(item.date).toLocaleDateString()
		),
		datasets: [
			{
				label: "Average Response Time (seconds)",
				data: analytics.timeSeriesData.averageResponseTimes.map(
					(item) => item.value
				),
				borderColor: "rgb(255, 159, 64)",
				backgroundColor: "rgba(255, 159, 64, 0.5)",
				tension: 0.3,
			},
		],
	};

	// Prepare data for citations by jurisdiction
	const citationsByJurisdictionData = {
		labels: analytics.citationMetrics.citationsByJurisdiction.map(
			(item) => item.jurisdiction
		),
		datasets: [
			{
				label: "Citations by Jurisdiction",
				data: analytics.citationMetrics.citationsByJurisdiction.map(
					(item) => item.count
				),
				backgroundColor: [
					"rgba(153, 102, 255, 0.5)",
					"rgba(255, 159, 64, 0.5)",
					"rgba(255, 99, 132, 0.5)",
					"rgba(54, 162, 235, 0.5)",
					"rgba(255, 206, 86, 0.5)",
				],
				borderColor: [
					"rgb(153, 102, 255)",
					"rgb(255, 159, 64)",
					"rgb(255, 99, 132)",
					"rgb(54, 162, 235)",
					"rgb(255, 206, 86)",
				],
			},
		],
	};

	// Add new chart data for document classifications
	const documentClassificationsData = analytics.timeSeriesData
		.documentClassifications
		? {
				labels: analytics.timeSeriesData.documentClassifications.map((item) =>
					new Date(item.date).toLocaleDateString()
				),
				datasets: [
					{
						label: "Automatic",
						data: analytics.timeSeriesData.documentClassifications.map(
							(item) => item.automatic || 0
						),
						backgroundColor: "rgba(75, 192, 192, 0.5)",
						borderColor: "rgb(75, 192, 192)",
					},
					{
						label: "Manual",
						data: analytics.timeSeriesData.documentClassifications.map(
							(item) => item.manual || 0
						),
						backgroundColor: "rgba(153, 102, 255, 0.5)",
						borderColor: "rgb(153, 102, 255)",
					},
				],
		  }
		: null;

	// Add new chart data for document comparisons
	const documentComparisonsData = {
		labels: analytics.timeSeriesData.documentComparisons.map((item) =>
			new Date(item.date).toLocaleDateString()
		),
		datasets: [
			{
				label: "Basic",
				data: analytics.timeSeriesData.documentComparisons.map(
					(item) => item.basic || 0
				),
				backgroundColor: "rgba(255, 159, 64, 0.5)",
				borderColor: "rgb(255, 159, 64)",
			},
			{
				label: "Enhanced",
				data: analytics.timeSeriesData.documentComparisons.map(
					(item) => item.enhanced || 0
				),
				backgroundColor: "rgba(54, 162, 235, 0.5)",
				borderColor: "rgb(54, 162, 235)",
			},
		],
	};

	// Add new chart data for comparison types
	const comparisonTypesData = analytics.documentAnalysis.comparisonMetrics
		? {
				labels:
					analytics.documentAnalysis.comparisonMetrics.comparisonsByType.map(
						(item) => item.type
					),
				datasets: [
					{
						label: "Comparison Types",
						data: analytics.documentAnalysis.comparisonMetrics.comparisonsByType.map(
							(item) => item.count
						),
						backgroundColor: [
							"rgba(255, 99, 132, 0.5)",
							"rgba(54, 162, 235, 0.5)",
							"rgba(255, 206, 86, 0.5)",
						],
						borderColor: [
							"rgb(255, 99, 132)",
							"rgb(54, 162, 235)",
							"rgb(255, 206, 86)",
						],
					},
				],
		  }
		: null;

	// Add new chart data for classification metrics
	const classificationMetricsData = analytics.documentAnalysis
		.classificationMetrics
		? {
				labels: ["Automatic", "Manual", "Unclassified"],
				datasets: [
					{
						label: "Classification Distribution",
						data: [
							analytics.documentAnalysis.classificationMetrics
								.automaticClassifications,
							analytics.documentAnalysis.classificationMetrics
								.manualClassifications,
							analytics.documentAnalysis.classificationMetrics
								.unclassifiedDocuments,
						],
						backgroundColor: [
							"rgba(75, 192, 192, 0.5)",
							"rgba(153, 102, 255, 0.5)",
							"rgba(255, 159, 64, 0.5)",
						],
						borderColor: [
							"rgb(75, 192, 192)",
							"rgb(153, 102, 255)",
							"rgb(255, 159, 64)",
						],
					},
				],
		  }
		: null;

	return (
		<div className="space-y-8">
			{/* Key metrics */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				<StatCard
					title="Total Documents"
					value={analytics.documentAnalysis.totalDocuments}
					icon={<FileText className="h-4 w-4" />}
					description="Documents uploaded"
					trend={documentsTrend}
				/>
				<StatCard
					title="Active Users"
					value={analytics.userEngagement.activeUsers}
					icon={<Users className="h-4 w-4" />}
					description="Out of total users"
					secondaryValue={analytics.userEngagement.totalUsers}
					trend={activeUsersTrend}
				/>
				<StatCard
					title="Total Queries"
					value={analytics.userEngagement.totalQueries}
					icon={<MessageSquare className="h-4 w-4" />}
					description="User queries"
					trend={queriesTrend}
				/>
				<StatCard
					title="Avg. Response Time"
					value={analytics.userEngagement.averageResponseTime}
					icon={<Clock className="h-4 w-4" />}
					description="In seconds"
					trend={responseTimeTrend}
					trendSuffix="s"
					valueSuffix="s"
				/>
			</div>

			{/* Add new metrics row */}
			{(analytics.documentAnalysis.classificationMetrics ||
				analytics.documentAnalysis.comparisonMetrics) && (
				<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
					{analytics.documentAnalysis.classificationMetrics && (
						<>
							<StatCard
								title="Classified Documents"
								value={
									analytics.documentAnalysis.classificationMetrics
										.automaticClassifications +
									analytics.documentAnalysis.classificationMetrics
										.manualClassifications
								}
								icon={<CheckCircle className="h-4 w-4" />}
								description="Auto & manual"
								secondaryValue={analytics.documentAnalysis.totalDocuments}
								trend={classifiedDocumentsTrend}
							/>
							<StatCard
								title="Classification Accuracy"
								value={
									analytics.documentAnalysis.classificationMetrics
										.classificationAccuracy * 100
								}
								icon={<CheckCircle className="h-4 w-4" />}
								description="Automatic classification"
								valueSuffix="%"
								trend={classificationAccuracyTrend}
							/>
						</>
					)}
					{analytics.documentAnalysis.comparisonMetrics && (
						<>
							<StatCard
								title="Document Comparisons"
								value={
									analytics.documentAnalysis.comparisonMetrics.totalComparisons
								}
								icon={<GitCompare className="h-4 w-4" />}
								description="Total comparisons"
								trend={comparisonsTrend}
							/>
							<StatCard
								title="Enhanced Comparisons"
								value={
									analytics.documentAnalysis.comparisonMetrics
										.enhancedComparisons
								}
								icon={<GitCompare className="h-4 w-4" />}
								description="Advanced analysis"
								secondaryValue={
									analytics.documentAnalysis.comparisonMetrics.totalComparisons
								}
								trend={enhancedComparisonsTrend}
							/>
						</>
					)}
				</div>
			)}

			{/* Charts section */}
			<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
				<Card>
					<CardHeader className="pb-2">
						<div className="flex items-center justify-between">
							<div>
								<CardTitle className="text-base">Document Activity</CardTitle>
								<CardDescription>Document uploads over time</CardDescription>
							</div>
							<BarChartIcon className="h-4 w-4 text-muted-foreground" />
						</div>
					</CardHeader>
					<CardContent>
						<BarChart data={documentUploadsData} height={300} />
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="pb-2">
						<div className="flex items-center justify-between">
							<div>
								<CardTitle className="text-base">Document Types</CardTitle>
								<CardDescription>Distribution by document type</CardDescription>
							</div>
							<PieChartIcon className="h-4 w-4 text-muted-foreground" />
						</div>
					</CardHeader>
					<CardContent>
						<DonutChart data={documentTypesData} height={300} />
					</CardContent>
				</Card>

				{/* Add new charts for classification and comparison metrics */}
				{documentClassificationsData && (
					<Card>
						<CardHeader className="pb-2">
							<div className="flex items-center justify-between">
								<div>
									<CardTitle className="text-base">
										Document Classifications
									</CardTitle>
									<CardDescription>
										Automatic vs. manual classifications
									</CardDescription>
								</div>
								<BarChartIcon className="h-4 w-4 text-muted-foreground" />
							</div>
						</CardHeader>
						<CardContent>
							<StackedBarChart
								data={documentClassificationsData}
								height={300}
							/>
						</CardContent>
					</Card>
				)}

				{documentComparisonsData && (
					<Card>
						<CardHeader className="pb-2">
							<div className="flex items-center justify-between">
								<div>
									<CardTitle className="text-base">
										Document Comparisons
									</CardTitle>
									<CardDescription>
										Basic vs. enhanced comparisons
									</CardDescription>
								</div>
								<BarChartIcon className="h-4 w-4 text-muted-foreground" />
							</div>
						</CardHeader>
						<CardContent>
							<StackedBarChart data={documentComparisonsData} height={300} />
						</CardContent>
					</Card>
				)}

				<Card>
					<CardHeader className="pb-2">
						<div className="flex items-center justify-between">
							<div>
								<CardTitle className="text-base">User Queries</CardTitle>
								<CardDescription>Query volume over time</CardDescription>
							</div>
							<LineChartIcon className="h-4 w-4 text-muted-foreground" />
						</div>
					</CardHeader>
					<CardContent>
						<LineChart data={userQueriesData} height={300} />
					</CardContent>
				</Card>

				<Card>
					<CardHeader className="pb-2">
						<div className="flex items-center justify-between">
							<div>
								<CardTitle className="text-base">Response Times</CardTitle>
								<CardDescription>
									Average response time in seconds
								</CardDescription>
							</div>
							<LineChartIcon className="h-4 w-4 text-muted-foreground" />
						</div>
					</CardHeader>
					<CardContent>
						<LineChart data={responseTimesData} height={300} />
					</CardContent>
				</Card>
			</div>

			{/* Lists and additional metrics */}
			<div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
				{/* Add new charts for classification and comparison metrics */}
				{classificationMetricsData && (
					<Card className="lg:col-span-1">
						<CardHeader>
							<CardTitle className="text-base">
								Classification Distribution
							</CardTitle>
							<CardDescription>
								Automatic vs. manual classifications
							</CardDescription>
						</CardHeader>
						<CardContent>
							<PieChart data={classificationMetricsData} height={220} />
						</CardContent>
					</Card>
				)}

				{comparisonTypesData && (
					<Card className="lg:col-span-1">
						<CardHeader>
							<CardTitle className="text-base">Comparison Types</CardTitle>
							<CardDescription>
								Distribution by comparison focus
							</CardDescription>
						</CardHeader>
						<CardContent>
							<PieChart data={comparisonTypesData} height={220} />
						</CardContent>
					</Card>
				)}

				<Card className="lg:col-span-1">
					<CardHeader>
						<CardTitle className="text-base">Top Topics</CardTitle>
						<CardDescription>Most frequent topics in documents</CardDescription>
					</CardHeader>
					<CardContent>
						<TopItemsList
							items={analytics.topicAnalysis.topTopics.map((item) => ({
								label: item.topic,
								value: item.count,
								suffix: "docs",
							}))}
						/>
					</CardContent>
				</Card>

				<Card className="lg:col-span-1">
					<CardHeader>
						<CardTitle className="text-base">Top Cited Cases</CardTitle>
						<CardDescription>Most frequently cited cases</CardDescription>
					</CardHeader>
					<CardContent>
						<TopItemsList
							items={analytics.citationMetrics.topCitedCases.map((item) => ({
								label: item.caseName,
								value: item.count,
								suffix: "citations",
								url: item.url,
							}))}
						/>
					</CardContent>
				</Card>

				<Card className="lg:col-span-1">
					<CardHeader>
						<CardTitle className="text-base">
							Citations by Jurisdiction
						</CardTitle>
						<CardDescription>
							Distribution of citations by jurisdiction
						</CardDescription>
					</CardHeader>
					<CardContent>
						<PieChart data={citationsByJurisdictionData} height={220} />
					</CardContent>
				</Card>
			</div>
		</div>
	);
}
