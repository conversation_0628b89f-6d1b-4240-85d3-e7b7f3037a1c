"use client"

import type React from "react"

import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { ArrowDownIcon, ArrowUpIcon } from "lucide-react"

interface StatCardProps {
  title: string
  value: number
  icon?: React.ReactNode
  description?: string
  secondaryValue?: number
  trend?: number | undefined
  trendSuffix?: string
  valueSuffix?: string
  className?: string
}

export function StatCard({
  title,
  value,
  icon,
  description,
  secondaryValue,
  trend,
  trendSuffix = "%",
  valueSuffix = "",
  className,
}: StatCardProps) {
  const formatValue = (val: number): string => {
    if (val >= 1000000) {
      return (val / 1000000).toFixed(1) + "M"
    } else if (val >= 1000) {
      return (val / 1000).toFixed(1) + "K"
    }
    return val?.toString()
  }

  return (
    <Card className={cn("overflow-hidden", className)}>
      <CardContent className="p-6">
        <div className="flex items-center justify-between">
          <p className="text-sm font-medium text-muted-foreground">{title}</p>
          {icon && <div className="h-8 w-8 rounded-md bg-secondary flex items-center justify-center">{icon}</div>}
        </div>
        <div className="mt-2">
          <div className="flex items-baseline">
            <h3 className="text-2xl font-bold">
              {formatValue(value)}
              {valueSuffix && <span className="text-sm font-normal text-muted-foreground ml-1">{valueSuffix}</span>}
            </h3>
            {secondaryValue !== undefined && (
              <span className="text-sm text-muted-foreground ml-2">/ {formatValue(secondaryValue)}</span>
            )}
          </div>
          <div className="flex items-center justify-between mt-2">
            {description && <p className="text-xs text-muted-foreground">{description}</p>}
            {trend !== undefined && (
              <div
                className={cn(
                  "flex items-center text-xs font-medium",
                  trend > 0 ? "text-green-500" : trend < 0 ? "text-red-500" : "text-muted-foreground",
                )}
              >
                {trend > 0 ? (
                  <ArrowUpIcon className="h-3 w-3 mr-1" />
                ) : trend < 0 ? (
                  <ArrowDownIcon className="h-3 w-3 mr-1" />
                ) : null}
                {Math.abs(trend)}
                {trendSuffix}
              </div>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
