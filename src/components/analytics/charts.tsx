"use client"

import { useEffect, useRef } from "react"
import { Chart, registerables, ChartOptions } from "chart.js"
import { cn } from "@/lib/utils"

// Register all Chart.js components
Chart.register(...registerables)

interface ChartProps {
  data: {
    labels?: string[];
    datasets: {
      label?: string;
      data: number[];
      backgroundColor?: string | string[];
      borderColor?: string | string[];
      borderWidth?: number;
    }[];
  };
  options?: ChartOptions;
  height?: number;
  width?: number;
  className?: string;
}

export function BarChart({ data, options, height = 400, width, className }: ChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const chartRef = useRef<Chart | null>(null)

  useEffect(() => {
    if (!canvasRef.current) return

    // Destroy existing chart
    if (chartRef.current) {
      chartRef.current.destroy()
    }

    // Create new chart
    const ctx = canvasRef.current.getContext("2d")
    if (ctx) {
      chartRef.current = new Chart(ctx, {
        type: "bar",
        data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: "top",
              labels: {
                boxWidth: 10,
                usePointStyle: true,
              },
            },
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: "rgba(200, 200, 200, 0.1)",
              },
            },
            x: {
              grid: {
                display: false,
              },
            },
          },
          ...options,
        },
      })
    }

    // Cleanup
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy()
      }
    }
  }, [data, options])

  return (
    <div className={cn("w-full", className)} style={{ height: `${height}px`, width: width ? `${width}px` : "100%" }}>
      <canvas ref={canvasRef} />
    </div>
  )
}

export function LineChart({ data, options, height = 400, width, className }: ChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const chartRef = useRef<Chart | null>(null)

  useEffect(() => {
    if (!canvasRef.current) return

    // Destroy existing chart
    if (chartRef.current) {
      chartRef.current.destroy()
    }

    // Create new chart
    const ctx = canvasRef.current.getContext("2d")
    if (ctx) {
      chartRef.current = new Chart(ctx, {
        type: "line",
        data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: "top",
              labels: {
                boxWidth: 10,
                usePointStyle: true,
              },
            },
          },
          scales: {
            y: {
              beginAtZero: true,
              grid: {
                color: "rgba(200, 200, 200, 0.1)",
              },
            },
            x: {
              grid: {
                display: false,
              },
            },
          },
          ...options,
        },
      })
    }

    // Cleanup
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy()
      }
    }
  }, [data, options])

  return (
    <div className={cn("w-full", className)} style={{ height: `${height}px`, width: width ? `${width}px` : "100%" }}>
      <canvas ref={canvasRef} />
    </div>
  )
}

export function PieChart({ data, options, height = 400, width, className }: ChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const chartRef = useRef<Chart | null>(null)

  useEffect(() => {
    if (!canvasRef.current) return

    // Destroy existing chart
    if (chartRef.current) {
      chartRef.current.destroy()
    }

    // Create new chart
    const ctx = canvasRef.current.getContext("2d")
    if (ctx) {
      chartRef.current = new Chart(ctx, {
        type: "pie",
        data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: "right",
              labels: {
                boxWidth: 10,
                usePointStyle: true,
              },
            },
          },
          ...options,
        },
      })
    }

    // Cleanup
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy()
      }
    }
  }, [data, options])

  return (
    <div className={cn("w-full", className)} style={{ height: `${height}px`, width: width ? `${width}px` : "100%" }}>
      <canvas ref={canvasRef} />
    </div>
  )
}

interface DonutChartProps extends Omit<ChartProps, 'options'> {
  options?: ChartOptions<'doughnut'> & { cutout?: string };
}

export function DonutChart({ data, options, height = 400, width, className }: DonutChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const chartRef = useRef<Chart | null>(null)

  useEffect(() => {
    if (!canvasRef.current) return

    // Destroy existing chart
    if (chartRef.current) {
      chartRef.current.destroy()
    }

    // Create new chart
    const ctx = canvasRef.current.getContext("2d")
    if (ctx) {
      chartRef.current = new Chart(ctx, {
        type: "doughnut",
        data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          cutout: options?.cutout || "60%",
          plugins: {
            legend: {
              position: "right",
              labels: {
                boxWidth: 10,
                usePointStyle: true,
              },
            },
          },
          ...options,
        },
      })
    }

    // Cleanup
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy()
      }
    }
  }, [data, options])

  return (
    <div className={cn("w-full", className)} style={{ height: `${height}px`, width: width ? `${width}px` : "100%" }}>
      <canvas ref={canvasRef} />
    </div>
  )
}

// Add the StackedBarChart component after the existing chart components

export function StackedBarChart({ data, options, height = 400, width, className }: ChartProps) {
  const canvasRef = useRef<HTMLCanvasElement>(null)
  const chartRef = useRef<Chart | null>(null)

  useEffect(() => {
    if (!canvasRef.current) return

    // Destroy existing chart
    if (chartRef.current) {
      chartRef.current.destroy()
    }

    // Create new chart
    const ctx = canvasRef.current.getContext("2d")
    if (ctx) {
      chartRef.current = new Chart(ctx, {
        type: "bar",
        data,
        options: {
          responsive: true,
          maintainAspectRatio: false,
          plugins: {
            legend: {
              position: "top",
              labels: {
                boxWidth: 10,
                usePointStyle: true,
              },
            },
          },
          scales: {
            x: {
              stacked: true,
              grid: {
                display: false,
              },
            },
            y: {
              stacked: true,
              beginAtZero: true,
              grid: {
                color: "rgba(200, 200, 200, 0.1)",
              },
            },
          },
          ...options,
        },
      })
    }

    // Cleanup
    return () => {
      if (chartRef.current) {
        chartRef.current.destroy()
      }
    }
  }, [data, options])

  return (
    <div className={cn("w-full", className)} style={{ height: `${height}px`, width: width ? `${width}px` : "100%" }}>
      <canvas ref={canvasRef} />
    </div>
  )
}
