"use client"

import { ExternalLink } from "lucide-react"
import { cn } from "@/lib/utils"

interface TopItemsListProps {
  items: Array<{
    label: string
    value: number
    suffix?: string
    url?: string
  }>
  className?: string
}

export function TopItemsList({ items, className }: TopItemsListProps) {
  return (
    <div className={cn("space-y-4", className)}>
      {items.map((item, index) => (
        <div key={index} className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <div className="flex h-6 w-6 items-center justify-center rounded-full bg-secondary text-xs font-medium">
              {index + 1}
            </div>
            <div className="flex items-center">
              {item.url ? (
                <a
                  href={item.url}
                  target="_blank"
                  rel="noopener noreferrer"
                  className="text-sm font-medium hover:underline text-primary flex items-center"
                >
                  {item.label}
                  <ExternalLink className="h-3 w-3 ml-1" />
                </a>
              ) : (
                <span className="text-sm font-medium">{item.label}</span>
              )}
            </div>
          </div>
          <div className="text-sm text-muted-foreground">
            {item.value} {item.suffix}
          </div>
        </div>
      ))}
    </div>
  )
}
