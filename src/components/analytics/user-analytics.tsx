"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/analytics/charts"
import { StatCard } from "@/components/analytics/stat-card"
import { TopItemsList } from "@/components/analytics/top-items-list"
import type { UserAnalytics } from "@/lib/services/analytics-service"
import { Users, MessageSquare, LineChartIcon } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { format } from "date-fns"

interface UserAnalyticsProps {
  analytics: UserAnalytics
  previousPeriodAnalytics?: UserAnalytics | null
}

export function UserAnalyticsComponent({ analytics, previousPeriodAnalytics }: UserAnalyticsProps) {
  // Calculate trend percentages if previous period data is available
  const calculateTrend = (current: number, previous?: number): number | undefined => {
    if (previous === undefined || previous === 0) return undefined;
    return Math.round(((current - previous) / previous) * 100);
  };

  // Calculate trends
  const totalUsersTrend = calculateTrend(
    analytics.userEngagement.totalUsers,
    previousPeriodAnalytics?.userEngagement.totalUsers
  );

  const activeUsersTrend = calculateTrend(
    analytics.userEngagement.activeUsers,
    previousPeriodAnalytics?.userEngagement.activeUsers
  );

  const totalQueriesTrend = calculateTrend(
    analytics.userEngagement.totalQueries,
    previousPeriodAnalytics?.userEngagement.totalQueries
  );

  const avgQueriesPerUserTrend = calculateTrend(
    analytics.userEngagement.averageQueriesPerUser,
    previousPeriodAnalytics?.userEngagement.averageQueriesPerUser
  );

  // Prepare data for user queries chart
  const userQueriesData = {
    labels: analytics.timeSeriesData.userQueries.map((item) => new Date(item.date).toLocaleDateString()),
    datasets: [
      {
        label: "User Queries",
        data: analytics.timeSeriesData.userQueries.map((item) => item.count),
        borderColor: "rgb(75, 192, 192)",
        backgroundColor: "rgba(75, 192, 192, 0.5)",
        tension: 0.3,
      },
    ],
  }

  // Prepare data for response times chart
  const responseTimesData = {
    labels: analytics.timeSeriesData.averageResponseTimes.map((item) => new Date(item.date).toLocaleDateString()),
    datasets: [
      {
        label: "Average Response Time (seconds)",
        data: analytics.timeSeriesData.averageResponseTimes.map((item) => item.value),
        borderColor: "rgb(255, 159, 64)",
        backgroundColor: "rgba(255, 159, 64, 0.5)",
        tension: 0.3,
      },
    ],
  }

  // Prepare data for user activity
  const userActivityData = analytics.userEngagement.userActivity
    ? {
        labels: analytics.userEngagement.userActivity.map((user) => user.name),
        datasets: [
          {
            label: "Queries",
            data: analytics.userEngagement.userActivity.map((user) => user.queryCount),
            backgroundColor: "rgba(54, 162, 235, 0.5)",
            borderColor: "rgb(54, 162, 235)",
          },
          {
            label: "Documents",
            data: analytics.userEngagement.userActivity.map((user) => user.documentCount),
            backgroundColor: "rgba(255, 99, 132, 0.5)",
            borderColor: "rgb(255, 99, 132)",
          },
        ],
      }
    : null

  return (
    <div className="space-y-8">
      {/* Key metrics */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <StatCard
          title="Total Users"
          value={analytics.userEngagement.totalUsers}
          icon={<Users className="h-4 w-4" />}
          description="Registered users"
          trend={totalUsersTrend}
        />
        <StatCard
          title="Active Users"
          value={analytics.userEngagement.activeUsers}
          icon={<Users className="h-4 w-4" />}
          description="Out of total users"
          secondaryValue={analytics.userEngagement.totalUsers}
          trend={activeUsersTrend}
        />
        <StatCard
          title="Total Queries"
          value={analytics.userEngagement.totalQueries}
          icon={<MessageSquare className="h-4 w-4" />}
          description="User queries"
          trend={totalQueriesTrend}
        />
        <StatCard
          title="Avg. Queries Per User"
          value={analytics.userEngagement.averageQueriesPerUser}
          icon={<MessageSquare className="h-4 w-4" />}
          description="Per active user"
          trend={avgQueriesPerUserTrend}
        />
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-base">User Queries</CardTitle>
                <CardDescription>Query volume over time</CardDescription>
              </div>
              <LineChartIcon className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <LineChart data={userQueriesData} height={300} />
          </CardContent>
        </Card>

        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-base">Response Times</CardTitle>
                <CardDescription>Average response time in seconds</CardDescription>
              </div>
              <LineChartIcon className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <LineChart data={responseTimesData} height={300} />
          </CardContent>
        </Card>
      </div>

      {/* User activity */}
      {analytics.userEngagement.userActivity && (
        <div className="grid grid-cols-1 gap-6">
          <Card>
            <CardHeader>
              <CardTitle className="text-base">User Activity</CardTitle>
              <CardDescription>Activity by user</CardDescription>
            </CardHeader>
            <CardContent>
              {userActivityData && (
                <div className="mb-6">
                  <BarChart data={userActivityData} height={300} />
                </div>
              )}
              <div className="rounded-md border">
                <Table>
                  <TableHeader>
                    <TableRow>
                      <TableHead>User</TableHead>
                      <TableHead className="text-right">Queries</TableHead>
                      <TableHead className="text-right">Documents</TableHead>
                      <TableHead className="text-right">Last Active</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {analytics.userEngagement.userActivity.map((user) => (
                      <TableRow key={user.userId}>
                        <TableCell className="font-medium">{user.name}</TableCell>
                        <TableCell className="text-right">{user.queryCount}</TableCell>
                        <TableCell className="text-right">{user.documentCount}</TableCell>
                        <TableCell className="text-right">{format(new Date(user.lastActive), "MMM d, yyyy")}</TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Topics */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader>
            <CardTitle className="text-base">Top Topics</CardTitle>
            <CardDescription>Most frequent topics in user queries</CardDescription>
          </CardHeader>
          <CardContent>
            <TopItemsList
              items={analytics.topicAnalysis.topTopics.map((item) => ({
                label: item.topic,
                value: item.count,
                suffix: "queries",
              }))}
            />
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
