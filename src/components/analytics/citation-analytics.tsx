"use client"

import { <PERSON>, <PERSON><PERSON>ontent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON> } from "@/components/analytics/charts"
import { StatCard } from "@/components/analytics/stat-card"
import { TopItemsList } from "@/components/analytics/top-items-list"
import type { CitationAnalytics } from "@/lib/services/analytics-service"
import { BookOpen, BarChartIcon, PieChartIcon } from "lucide-react"

interface CitationAnalyticsProps {
  analytics: CitationAnalytics
  previousPeriodAnalytics?: CitationAnalytics | null
}

export function CitationAnalyticsComponent({ analytics, previousPeriodAnalytics }: CitationAnalyticsProps) {
  // Calculate trend percentages if previous period data is available
  const calculateTrend = (current: number, previous?: number): number | undefined => {
    if (previous === undefined || previous === 0) return undefined;
    return Math.round(((current - previous) / previous) * 100);
  };

  // Calculate trends
  const totalCitationsTrend = calculateTrend(
    analytics.citationMetrics.totalCitations,
    previousPeriodAnalytics?.citationMetrics.totalCitations
  );

  const jurisdictionsTrend = calculateTrend(
    analytics.citationMetrics.citationsByJurisdiction.length,
    previousPeriodAnalytics?.citationMetrics.citationsByJurisdiction.length
  );

  const yearsSpanTrend = analytics.citationMetrics.citationsByYear && previousPeriodAnalytics?.citationMetrics.citationsByYear
    ? calculateTrend(
        analytics.citationMetrics.citationsByYear.length,
        previousPeriodAnalytics.citationMetrics.citationsByYear.length
      )
    : undefined;

  // Prepare data for citations by jurisdiction
  const citationsByJurisdictionData = {
    labels: analytics.citationMetrics.citationsByJurisdiction.map((item) => item.jurisdiction),
    datasets: [
      {
        label: "Citations by Jurisdiction",
        data: analytics.citationMetrics.citationsByJurisdiction.map((item) => item.count),
        backgroundColor: [
          "rgba(153, 102, 255, 0.5)",
          "rgba(255, 159, 64, 0.5)",
          "rgba(255, 99, 132, 0.5)",
          "rgba(54, 162, 235, 0.5)",
          "rgba(255, 206, 86, 0.5)",
        ],
        borderColor: [
          "rgb(153, 102, 255)",
          "rgb(255, 159, 64)",
          "rgb(255, 99, 132)",
          "rgb(54, 162, 235)",
          "rgb(255, 206, 86)",
        ],
      },
    ],
  }

  // Prepare data for citations by year
  const citationsByYearData = analytics.citationMetrics.citationsByYear
    ? {
        labels: analytics.citationMetrics.citationsByYear.map((item) => item.year.toString()),
        datasets: [
          {
            label: "Citations by Year",
            data: analytics.citationMetrics.citationsByYear.map((item) => item.count),
            backgroundColor: "rgba(54, 162, 235, 0.5)",
            borderColor: "rgb(54, 162, 235)",
          },
        ],
      }
    : null

  return (
    <div className="space-y-8">
      {/* Key metrics */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <StatCard
          title="Total Citations"
          value={analytics.citationMetrics.totalCitations}
          icon={<BookOpen className="h-4 w-4" />}
          description="Citations found"
          trend={totalCitationsTrend}
        />
        <StatCard
          title="Jurisdictions"
          value={analytics.citationMetrics.citationsByJurisdiction.length}
          icon={<BookOpen className="h-4 w-4" />}
          description="Unique jurisdictions"
          trend={jurisdictionsTrend}
        />
        {analytics.citationMetrics.citationsByYear && (
          <StatCard
            title="Years Span"
            value={analytics.citationMetrics.citationsByYear.length}
            icon={<BookOpen className="h-4 w-4" />}
            description="Years with citations"
            trend={yearsSpanTrend}
          />
        )}
      </div>

      {/* Charts */}
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <Card>
          <CardHeader className="pb-2">
            <div className="flex items-center justify-between">
              <div>
                <CardTitle className="text-base">Citations by Jurisdiction</CardTitle>
                <CardDescription>Distribution of citations by jurisdiction</CardDescription>
              </div>
              <PieChartIcon className="h-4 w-4 text-muted-foreground" />
            </div>
          </CardHeader>
          <CardContent>
            <PieChart data={citationsByJurisdictionData} height={300} />
          </CardContent>
        </Card>

        {citationsByYearData && (
          <Card>
            <CardHeader className="pb-2">
              <div className="flex items-center justify-between">
                <div>
                  <CardTitle className="text-base">Citations by Year</CardTitle>
                  <CardDescription>Distribution of citations by year</CardDescription>
                </div>
                <BarChartIcon className="h-4 w-4 text-muted-foreground" />
              </div>
            </CardHeader>
            <CardContent>
              <BarChart data={citationsByYearData} height={300} />
            </CardContent>
          </Card>
        )}
      </div>

      {/* Top cited cases */}
      <Card>
        <CardHeader>
          <CardTitle className="text-base">Top Cited Cases</CardTitle>
          <CardDescription>Most frequently cited cases</CardDescription>
        </CardHeader>
        <CardContent>
          <TopItemsList
            items={analytics.citationMetrics.topCitedCases.map((item) => ({
              label: item.caseName,
              value: item.count,
              suffix: "citations",
              url: item.url,
            }))}
          />
        </CardContent>
      </Card>
    </div>
  )
}
