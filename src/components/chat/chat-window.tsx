"use client";

import { useState } from "react";
import type React from "react";
import { useRef, useEffect } from "react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useChatContext } from "@/lib/chat/chat-context";
import { Loader2, FileText, Upload, MessageSquare } from "lucide-react";
import type { ChatMessage as ChatMessageType } from "@/lib/services/chat-service";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { AnalysisPanel } from "../analysis/analysis-panel";
import { ChatMessage } from "./chat-message";

// New component for the welcome screen
const WelcomeScreen = () => {
	const { uploadDocument, isLoading } = useChatContext();
	const [dragActive, setDragActive] = useState(false);
	const fileInputRef = useRef<HTMLInputElement>(null);

	const handleDrag = (e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		if (e.type === "dragenter" || e.type === "dragover") {
			setDragActive(true);
		} else if (e.type === "dragleave") {
			setDragActive(false);
		}
	};

	const handleDrop = async (e: React.DragEvent) => {
		e.preventDefault();
		e.stopPropagation();
		setDragActive(false);

		if (e.dataTransfer.files && e.dataTransfer.files[0]) {
			await uploadDocument(e.dataTransfer.files[0]);
		}
	};

	const handleFileSelect = async (e: React.ChangeEvent<HTMLInputElement>) => {
		if (e.target.files && e.target.files[0]) {
			await uploadDocument(e.target.files[0]);
		}
	};

	const triggerFileInput = () => {
		fileInputRef.current?.click();
	};

	return (
		<div
			className={cn(
				"h-full flex flex-col items-center justify-center p-4 sm:p-6 lg:p-8 text-center overflow-y-auto",
				isLoading && "pointer-events-none opacity-50"
			)}
			onDragEnter={handleDrag}
			onDragLeave={handleDrag}
			onDragOver={handleDrag}
			onDrop={handleDrop}
		>
			{isLoading ? (
				<div className="flex flex-col items-center justify-center space-y-4">
					<Loader2 className="h-8 w-8 animate-spin text-primary" />
					<h2 className="text-lg sm:text-xl font-medium">
						Processing your document...
					</h2>
					<p className="text-sm text-muted-foreground">
						This may take a few moments
					</p>
				</div>
			) : (
				<div
					className={`max-w-2xl w-full mx-auto transition-all duration-200 mt-16 ${
						dragActive ? "scale-105" : "scale-100"
					}`}
				>
					<h1 className="text-2xl sm:text-3xl lg:text-4xl font-bold mb-4 text-foreground">
						Docgic Assistant
					</h1>

					<p className="text-sm sm:text-base lg:text-lg text-muted-foreground mb-6 max-w-2xl mx-auto">
						Upload a document to start analyzing and get insights from your
						content.
					</p>

					<div
						className={cn(
							"border-2 border-dashed rounded-xl p-6 sm:p-8 mb-6 transition-colors",
							dragActive
								? "border-primary bg-secondary/50 dark:bg-[#252525]"
								: "border-border bg-secondary/30 dark:bg-[#1e1e1e]"
						)}
					>
						<input
							ref={fileInputRef}
							type="file"
							id="file-upload"
							className="hidden"
							onChange={handleFileSelect}
							accept=".pdf,.doc,.docx,.txt"
						/>

						<Upload className="h-10 w-10 sm:h-12 sm:w-12 mx-auto mb-4 text-muted-foreground" />

						<h2 className="text-lg sm:text-xl font-medium text-foreground mb-2">
							Drag and drop your document here
						</h2>

						<p className="text-sm sm:text-base text-muted-foreground mb-6">
							Support for PDF, DOC, DOCX, and TXT files
						</p>

						<Button
							onClick={triggerFileInput}
							className="bg-primary hover:bg-primary/90 text-primary-foreground px-8 py-3 text-base"
						>
							Browse Files
						</Button>
					</div>

					<div className="grid grid-cols-1 sm:grid-cols-3 gap-4 text-left">
						<FeatureCard
							icon={<FileText className="h-6 w-6" />}
							title="Document Analysis"
							description="Get detailed insights and summaries from your documents"
						/>
						<FeatureCard
							icon={<MessageSquare className="h-6 w-6" />}
							title="Interactive Chat"
							description="Ask questions about your document and get instant answers"
						/>
						<FeatureCard
							icon={<Upload className="h-6 w-6" />}
							title="Multiple Formats"
							description="Support for PDF, DOC, DOCX, and TXT files"
						/>
					</div>
				</div>
			)}
		</div>
	);
};

// Feature card component for the welcome screen
const FeatureCard = ({
	icon,
	title,
	description,
}: {
	icon: React.ReactNode;
	title: string;
	description: string;
}) => {
	return (
		<div className="bg-card rounded-lg p-4 border border-border">
			<div className="bg-secondary dark:bg-neutral-700 rounded-full w-8 h-8 flex items-center justify-center mb-2">
				{icon}
			</div>
			<h3 className="font-medium text-foreground text-base mb-1">{title}</h3>
			<p className="text-muted-foreground text-sm">{description}</p>
		</div>
	);
};

export function ChatWindow() {
	const {
		messages,
		loadMoreMessages,
		isLoading,
		hasNextPage,
		currentSession,
		documentUploaded,
		currentAnalysis,
		isAnalysisPanelOpen,
		openAnalysisPanel,
		closeAnalysisPanel,
	} = useChatContext();

	// Type annotation for messages
	const typedMessages = messages as ChatMessageType[];
	const [showWelcome, setShowWelcome] = useState(true);

	const observerRef = useRef<IntersectionObserver | null>(null);
	const loadingRef = useRef<HTMLDivElement>(null);

	useEffect(() => {
		// If we have a session or document uploaded, don't show welcome screen
		if (currentSession || documentUploaded) {
			setShowWelcome(false);
		} else {
			setShowWelcome(true);
		}
	}, [currentSession, documentUploaded]);

	useEffect(() => {
		const currentLoadingRef = loadingRef.current;

		if (currentLoadingRef) {
			observerRef.current = new IntersectionObserver(
				(entries) => {
					if (entries[0].isIntersecting && hasNextPage && !isLoading) {
						loadMoreMessages();
					}
				},
				{ threshold: 0.1 }
			);

			observerRef.current.observe(currentLoadingRef);
		}

		return () => {
			if (observerRef.current && currentLoadingRef) {
				observerRef.current.unobserve(currentLoadingRef);
			}
		};
	}, [hasNextPage, isLoading, loadMoreMessages]);

	// Show welcome screen when no messages and no session
	if (showWelcome) {
		return <WelcomeScreen />;
	}
	const processedMessages = typedMessages.map((message: ChatMessageType) => {
		// Extract citations using regex
		// If the message already has citations, use those
		if (message.citations && message.citations.length > 0) {
			return message;
		}

		const citationRegex =
			/(\d+\s+U\.S\.\s+\d+|\d+\s+S\.\s*Ct\.\s+\d+|\d+\s+F\.\d+d\s+\d+)/g;
		const citations = [];
		let match;

		// Create a copy of the content for regex operations
		const content = message.content;

		// Find all citations in the message
		while ((match = citationRegex.exec(content)) !== null) {
			citations.push({
				rawText: match[0],
				title: match[0], // Using the citation text as title
				url: `https://www.courtlistener.com/?q=${encodeURIComponent(match[0])}`,
				confidence: 1,
				metadata: {
					type: "case",
					source: "regex-extraction",
					normalizedCitation: match[0],
				},
			});
		}

		return {
			...message,
			citations: citations.length > 0 ? citations : undefined,
		};
	});
	return (
		<>
			<div className="h-full flex flex-col bg-background relative">
				<ScrollArea className="flex-1 px-3 sm:px-4 lg:px-6">
					<div className="max-w-4xl mx-auto py-4">
						{hasNextPage && (
							<div
								ref={loadingRef}
								className="h-8 flex items-center justify-center"
							>
								{isLoading && (
									<Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
								)}
							</div>
						)}

						{isLoading && typedMessages.length === 0 ? (
							<div className="space-y-4 py-8">
								{[...Array(3)].map((_, i) => (
									<div key={i} className="flex items-center space-x-4">
										<Skeleton className="h-10 w-10 rounded-full" />
										<div className="space-y-2 flex-1">
											<Skeleton className="h-4 w-full max-w-[300px]" />
											<Skeleton className="h-4 w-full max-w-[250px]" />
										</div>
									</div>
								))}
							</div>
						) : typedMessages.length === 0 ? (
							<div className="flex flex-col items-center justify-center min-h-[60vh] py-16 text-center text-muted-foreground">
								<h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold mb-4 text-foreground">
									Ready to analyze your document
								</h2>
								<p className="text-base sm:text-lg max-w-md">
									Start chatting to get assistance with your document analysis.
								</p>
							</div>
						) : (
							<div className="space-y-4 sm:space-y-6 py-4">
								{[...processedMessages]
									.reverse()
									.map((message: ChatMessageType, index: number) => (
										<ChatMessage
											key={message.id}
											message={message}
											isLastMessage={index === typedMessages.length - 1}
											onViewAnalysis={openAnalysisPanel}
										/>
									))}

								{isLoading && typedMessages.length > 0 && (
									<div className="flex justify-start">
										<div className="flex gap-3">
											<div className="h-8 w-8 shrink-0 rounded-full flex items-center justify-center bg-secondary dark:bg-neutral-700 text-secondary-foreground dark:text-neutral-200">
												<span className="text-xs font-medium">A</span>
											</div>

											<Card className="px-4 py-3 bg-secondary/10 dark:bg-[#1e1e1e] text-foreground dark:text-neutral-200 border-border dark:border-neutral-700">
												<div className="flex items-center space-x-2">
													<Loader2 className="h-4 w-4 animate-spin" />
													<span>Thinking...</span>
												</div>
											</Card>
										</div>
									</div>
								)}
							</div>
						)}
					</div>
				</ScrollArea>
			</div>

			{/* Analysis Panel */}
			<AnalysisPanel
				isOpen={isAnalysisPanelOpen}
				onClose={closeAnalysisPanel}
				analysis={currentAnalysis}
			/>
		</>
	);
}
