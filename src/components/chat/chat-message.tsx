"use client"

import { useRef, useEffect } from "react"
import { cn } from "@/lib/utils"
import { Card } from "../ui/card"
import { Button } from "@/components/ui/button"
import { PanelRight } from "lucide-react"
import { format } from "date-fns"
import Markdown<PERSON>enderer from "@/components/MarkdownRenderer"
import type { Citation } from "@/lib/services/chat-service"
import { InteractiveCitation } from "./interaction-citation"

interface ChatMessageProps {
  message: {
    id: string
    role: "user" | "assistant"
    content: string
    timestamp: string
    citations?: Citation[]
    attachments?: Array<{
      filename: string
      uploadedAt: string
    }>
    references?: Array<{
      text: string
    }>
  }
  isLastMessage: boolean
  onViewAnalysis: () => void
}

export function ChatMessage({ message, isLastMessage, onViewAnalysis }: ChatMessageProps) {
  const isUser = message?.role === "user"
  const ref = useRef<HTMLDivElement>(null)

  useEffect(() => {
    if (isLastMessage && ref.current) {
      ref.current.scrollIntoView({ behavior: "smooth" })
    }
  }, [isLastMessage])

  // Check if this is an analysis notification message
  const isAnalysisNotification =
    message?.role === "assistant" &&
    message.content.includes("I've analyzed your document") &&
    message.content.includes("panel on the right")

  // Function to process content and highlight citations
  const renderContentWithCitations = () => {
    // If no citations, just use the MarkdownRenderer
    if (!message?.citations || message.citations.length === 0) {
      return <MarkdownRenderer markdownContent={message?.content} />
    }

    // For now, we'll just use the MarkdownRenderer and show citations separately
    // A more advanced implementation would replace citations in the content with interactive components
    return <MarkdownRenderer markdownContent={message?.content} />
  }

  return (
    <div ref={ref} className={`flex ${isUser ? "justify-end" : "justify-start"} mb-4`}>
      <div className={`flex gap-3 ${isUser ? "flex-row-reverse" : "flex-row"}`}>
        <div
          className={`h-8 w-8 shrink-0 rounded-full flex items-center justify-center ${
            isUser
              ? "bg-primary text-primary-foreground"
              : "bg-secondary text-secondary-foreground dark:bg-neutral-700 dark:text-neutral-200"
          }`}
        >
          <span className="text-xs font-medium">{isUser ? "U" : "A"}</span>
        </div>

        <div className={`flex flex-col space-y-1 ${isUser ? "items-end" : "items-start"}`}>
          <div className="group flex flex-col">
            <Card
              className={cn(
                "px-4 py-3 break-words max-w-[85%] min-w-[360px] lg:max-w-[75%] border-border",
                isUser
                  ? "bg-secondary/90 text-foreground dark:bg-[#252525] dark:text-neutral-200 dark:border-neutral-700 "
                  : "bg-secondary/10 text-foreground dark:bg-[#1e1e1e] dark:text-neutral-200 dark:border-neutral-700",
              )}
            >
              <div className="mb-2">
                <span className="text-sm font-medium">{isUser ? "You" : "Assistant"}</span>
                <span className="text-xs ml-2 opacity-70">{format(new Date(), "h:mm a")}</span>
              </div>
              <div className="text-sm whitespace-pre-wrap">{renderContentWithCitations()}</div>

              {/* Citations section */}
              {message?.citations && message?.citations.length > 0 && (
                <div className="mt-3 pt-3 border-t border-border dark:border-neutral-700">
                  <h5 className="text-xs font-medium mb-2">Citations:</h5>
                  <ul className="space-y-2">
                    {message?.citations.map((citation, index) => (
                      <li key={index} className="text-xs">
                        <InteractiveCitation citation={citation} />
                      </li>
                    ))}
                  </ul>
                </div>
              )}

              {isAnalysisNotification && (
                <div className="mt-3">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={onViewAnalysis}
                    className="flex items-center gap-1.5 bg-secondary/50 dark:bg-[#252525] hover:bg-secondary dark:hover:bg-[#2a2a2a]"
                  >
                    <PanelRight className="h-3.5 w-3.5" />
                    View Analysis
                  </Button>
                </div>
              )}

              {message?.attachments && message?.attachments.length > 0 && (
                <div className="mt-2 space-y-1">
                  {message?.attachments.map((attachment, index) => (
                    <div key={index} className="text-sm flex items-center space-x-2">
                      <span>{attachment.filename}</span>
                      <span className="text-xs opacity-70">({new Date(attachment.uploadedAt).toLocaleString()})</span>
                    </div>
                  ))}
                </div>
              )}

              {message?.references && message?.references.length > 0 && (
                <div className="mt-2 text-sm opacity-70">
                  <div>References:</div>
                  {message.references.map((ref, index) => (
                    <div key={index} className="ml-2">
                      • {ref.text}
                    </div>
                  ))}
                </div>
              )}
            </Card>

            <span className="text-xs text-muted-foreground px-2 pt-1 opacity-0 group-hover:opacity-100 transition-opacity">
              {format(new Date(), "h:mm a")}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
