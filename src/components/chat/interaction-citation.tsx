"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { Dialog, DialogContent } from "@/components/ui/dialog"
import { Loader2, ExternalLink, BookOpen } from "lucide-react"
import {
  citationAnalysisService,
  type CitationAnalysisResponse,
  type CitationRelationship
} from "@/lib/services/citation-analysis-service"
import { courtListenerService } from "@/lib/services/court-listener-service"
import type { Citation as ChatCitation } from "@/lib/services/chat-service"
import { CitationAnalysisPanel } from "../analysis/citation-analysis-panel"

interface InteractiveCitationProps {
  citation: ChatCitation | string
}

export function InteractiveCitation({ citation }: InteractiveCitationProps) {
  const [isLoading, setIsLoading] = useState(false)
  const [citationData, setCitationData] = useState<CitationAnalysisResponse | null>(null)
  const [error, setError] = useState<string | null>(null)
  const [showFullAnalysis, setShowFullAnalysis] = useState(false)

  // Handle the case where citation is a string
  const citationText = typeof citation === "string" ? citation : citation.rawText

  const handleAnalyze = async () => {
    setIsLoading(true)
    setError(null)

    try {
      // First try to get enhanced citation analysis
      let result = await citationAnalysisService.analyzeCitationEnhanced({
        citation: citationText,
        includeRelationships: true,
        includeImpact: true,
        includePrecedentChains: true,
      })

      // If enhanced fails, try basic analysis
      if (!result) {
        result = await citationAnalysisService.analyzeCitationBasic({
          citation: citationText,
          includeRelationships: true,
        })
      }

      // If basic also fails, try the general analyze endpoint
      if (!result) {
        result = await citationAnalysisService.analyzeCitation({
          citation: citationText,
        })
      }

      if (result) {
        setCitationData(result)
      } else {
        // Fallback to Court Listener
        const courtListenerResult = await courtListenerService.lookupCitation(citationText)
        if (courtListenerResult?.data?.cases?.length > 0) {
          const firstCase = courtListenerResult.data.cases[0];
          const citationHash = `${firstCase.case_name}_${firstCase.court}_${firstCase.date_filed}`.replace(/\s+/g, '_');
          
          setCitationData({
            citation: {
              id: `cl_${Buffer.from(citationHash).toString('base64')}`,
              citation: citationText,
              type: "case",
              title: firstCase.case_name,
              jurisdiction: firstCase.court || "Unknown",
              year: new Date(firstCase.date_filed || Date.now()).getFullYear(),
            },
          })
        } else {
          setError("No information found for this citation")
        }
      }
    } catch (err) {
      console.error("Error analyzing citation:", err)
      setError("Failed to analyze citation")
    } finally {
      setIsLoading(false)
    }
  }

  // Get URL for the citation
  const getCitationUrl = () => {
    if (typeof citation !== "string" && citation.url) {
      return citation.url
    }
    return `https://www.courtlistener.com/?q=${encodeURIComponent(citationText)}`
  }

  // Format the relationship strength for display
  const formatStrength = (strength: string) => {
    return strength.charAt(0).toUpperCase() + strength.slice(1)
  }

  return (
    <>
      <Popover>
        <PopoverTrigger asChild>
          <Button
            variant="link"
            className="p-0 h-auto text-primary underline font-normal"
            onClick={() => {
              if (!citationData && !isLoading) {
                handleAnalyze()
              }
            }}
          >
            {citationText}
          </Button>
        </PopoverTrigger>
        <PopoverContent className="w-80 p-4">
          {isLoading ? (
            <div className="flex flex-col items-center justify-center py-4">
              <Loader2 className="h-6 w-6 animate-spin text-primary mb-2" />
              <p className="text-sm text-muted-foreground">Analyzing citation...</p>
            </div>
          ) : error ? (
            <div className="text-sm text-destructive">{error}</div>
          ) : citationData ? (
            <div className="space-y-3">
              <div>
                <h4 className="font-medium">{citationData.citation.title || citationText}</h4>
                <p className="text-sm text-muted-foreground">{citationData.citation.citation || citationText}</p>
                {citationData.citation.court && (
                  <p className="text-xs text-muted-foreground mt-1">{citationData.citation.court}</p>
                )}
                {citationData.citation.year && (
                  <p className="text-xs text-muted-foreground">{citationData.citation.year}</p>
                )}
              </div>

              {citationData.impact && (
                <div className="text-sm">
                  <p className="font-medium mb-1">Impact</p>
                  <div className="grid grid-cols-2 gap-2 text-xs">
                    <div>Citations: {citationData.impact.totalCitations}</div>
                    <div>Recent: {citationData.impact.recentCitations}</div>
                    <div>Positive: {citationData.impact.positiveReferences}</div>
                    <div>Negative: {citationData.impact.negativeReferences}</div>
                  </div>
                  <div className="mt-2">
                    <div className="text-xs mb-1">Impact Score: {citationData.impact.impactScore}/100</div>
                    <div className="w-full bg-secondary h-2 rounded-full overflow-hidden">
                      <div className="bg-primary h-full" style={{ width: `${citationData.impact.impactScore}%` }}></div>
                    </div>
                  </div>
                </div>
              )}

              {citationData.relationships && citationData.relationships.length > 0 && (
                <div className="text-sm">
                  <p className="font-medium mb-1">Key Relationships</p>
                  <ul className="text-xs space-y-1">
                    {citationData.relationships.slice(0, 2).map((rel: CitationRelationship, idx: number) => (
                      <li key={idx} className="flex justify-between">
                        <span>
                          {rel.relationshipType === "cites"
                            ? rel.metadata.citedCase?.name || "Cites case"
                            : rel.metadata.citingCase?.name || "Cited by case"}
                        </span>
                        <span className="text-muted-foreground">{formatStrength(rel.strength)}</span>
                      </li>
                    ))}
                    {citationData.relationships.length > 2 && (
                      <li className="text-xs text-muted-foreground">
                        +{citationData.relationships.length - 2} more relationships
                      </li>
                    )}
                  </ul>
                </div>
              )}

              <div className="flex justify-between pt-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs"
                  onClick={() => window.open(getCitationUrl(), "_blank")}
                >
                  <ExternalLink className="h-3.5 w-3.5 mr-1" />
                  View
                </Button>

                <Button
                  variant="default"
                  size="sm"
                  className="text-xs"
                  onClick={() => {
                    setShowFullAnalysis(true)
                  }}
                >
                  <BookOpen className="h-3.5 w-3.5 mr-1" />
                  Full Analysis
                </Button>
              </div>
            </div>
          ) : (
            <div className="flex flex-col items-center justify-center py-4">
              <Button onClick={handleAnalyze}>Analyze Citation</Button>
            </div>
          )}
        </PopoverContent>
      </Popover>

      {/* Full Analysis Dialog */}
      <Dialog open={showFullAnalysis} onOpenChange={setShowFullAnalysis}>
        <DialogContent className="max-w-3xl p-0 bg-transparent border-0 shadow-none">
          <CitationAnalysisPanel citation={citationText} />
        </DialogContent>
      </Dialog>
    </>
  )
}
