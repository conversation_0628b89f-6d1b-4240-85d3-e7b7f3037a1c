"use client";

import type React from "react";
import { useState, useCallback, useRef, useEffect } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { useChatContext } from "@/lib/chat/chat-context";
import {
	Loader2,
	ArrowUp,
	Link,
	Maximize2,
	FileText,
	PanelRight,
} from "lucide-react";
import { cn } from "@/lib/utils";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

export function MessageInput() {
	const {
		documentUploaded,
		isLoading,
		documentId,
		sendMessage,
		analyzeDocument,
		currentAnalysis,
		toggleAnalysisPanel,
	} = useChatContext();

	const [message, setMessage] = useState("");
	const textareaRef = useRef<HTMLTextAreaElement>(null);

	const adjustTextareaHeight = useCallback(() => {
		const textarea = textareaRef.current;
		if (textarea) {
			textarea.style.height = "auto";
			textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
		}
	}, []);

	useEffect(() => {
		adjustTextareaHeight();
	}, [message, adjustTextareaHeight]);

	const handleSubmit = async (e: React.FormEvent) => {
		e.preventDefault();
		if (!message.trim() || isLoading || !documentUploaded) return;

		try {
			await sendMessage(message.trim(), documentId ? [documentId] : undefined);
			setMessage("");
		} catch (error) {
			console.error("Failed to send message:", error);
		}
	};

	const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
		if (e.key === "Enter" && !e.shiftKey) {
			e.preventDefault();
			handleSubmit(e);
		}
	};

	const handleAnalyzeDocument = async () => {
		if (!documentId) return;
		try {
			await analyzeDocument(documentId);
		} catch (error) {
			console.error("Failed to analyze document:", error);
		}
	};

	return (
		<div className="p-3 sm:p-4 border-t border-border bg-background/95 backdrop-blur supports-[backdrop-filter]:bg-background/60">
			<div className="max-w-4xl mx-auto">
				<div className="flex items-center mb-2 space-x-2 text-sm text-muted-foreground justify-end">
					{documentUploaded && documentId && (
						<Button
							variant="ghost"
							size="sm"
							className="h-7 text-xs mr-auto hidden sm:flex"
							onClick={handleAnalyzeDocument}
							disabled={isLoading}
						>
							<FileText className="h-3.5 w-3.5 mr-1.5" />
							Analyze Document
						</Button>
					)}

					{currentAnalysis && (
						<Button
							variant="ghost"
							size="sm"
							className="h-7 text-xs hidden sm:flex"
							onClick={toggleAnalysisPanel}
						>
							<PanelRight className="h-3.5 w-3.5 mr-1.5" />
							Toggle Analysis
						</Button>
					)}

					<Maximize2 className="h-4 w-4 hidden sm:block" />
					<Link className="h-4 w-4 hidden sm:block" />
				</div>
				<div className="relative">
					<div className="relative bg-background dark:bg-[#1e1e1e] rounded-lg border border-border dark:border-neutral-700 overflow-hidden">
						<Textarea
							ref={textareaRef}
							value={message}
							onChange={(e) => {
								setMessage(e.target.value);
								adjustTextareaHeight();
							}}
							onKeyDown={handleKeyDown}
							placeholder="Ask a follow up..."
							className="min-h-[50px] sm:min-h-[56px] max-h-[200px] resize-none border-0 bg-transparent text-foreground placeholder:text-muted-foreground focus-visible:ring-0 py-3 px-4 pr-20 text-sm sm:text-base"
							disabled={!documentUploaded || isLoading}
						/>

						<div className="absolute bottom-0 right-0 flex items-center p-2 space-x-1">
							<DropdownMenu>
								<DropdownMenuTrigger asChild>
									<Button
										type="button"
										variant="ghost"
										size="icon"
										className={cn(
											"h-8 w-8 rounded-md text-muted-foreground hover:text-foreground",
											"hover:bg-muted dark:hover:bg-neutral-700"
										)}
									>
										<Link className="h-4 w-4" />
										<span className="sr-only">Add link</span>
									</Button>
								</DropdownMenuTrigger>
								<DropdownMenuContent align="end" className="w-48">
									<DropdownMenuItem
										onClick={handleAnalyzeDocument}
										disabled={!documentId || isLoading}
										className="cursor-pointer"
									>
										<FileText className="h-4 w-4 mr-2" />
										Analyze Document
									</DropdownMenuItem>

									{currentAnalysis && (
										<DropdownMenuItem
											onClick={toggleAnalysisPanel}
											className="cursor-pointer"
										>
											<PanelRight className="h-4 w-4 mr-2" />
											Toggle Analysis Panel
										</DropdownMenuItem>
									)}
								</DropdownMenuContent>
							</DropdownMenu>

							<Button
								onClick={handleSubmit}
								disabled={!message.trim() || isLoading || !documentUploaded}
								type="button"
								variant="ghost"
								size="icon"
								className={cn(
									"h-8 w-8 rounded-md text-muted-foreground hover:text-foreground",
									"hover:bg-muted dark:hover:bg-neutral-700"
								)}
							>
								{isLoading ? (
									<Loader2 className="h-4 w-4 animate-spin" />
								) : (
									<ArrowUp className="h-4 w-4" />
								)}
								<span className="sr-only">Send message</span>
							</Button>
						</div>
					</div>
				</div>
				<div className="mt-2 text-xs text-muted-foreground text-center sm:text-left">
					Press Enter to send, Shift+Enter for new line
				</div>
			</div>
		</div>
	);
}
