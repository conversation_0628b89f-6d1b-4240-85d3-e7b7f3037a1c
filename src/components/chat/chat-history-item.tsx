"use client";

import type React from "react";
import { MoreHorizontal } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button } from "@/components/ui/button";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";

interface ChatHistoryItemProps {
	title: string;
	timestamp: string;
	isActive: boolean;
	onClick: () => void;
	onDelete?: () => void;
	onRename?: () => void;
}

export const ChatHistoryItem: React.FC<ChatHistoryItemProps> = ({
	title,
	timestamp,
	isActive,
	onClick,
	onDelete,
	onRename,
}) => {
	const handleActionClick = (e: React.MouseEvent, action: () => void) => {
		e.stopPropagation();
		action();
	};

	return (
		<div
			onClick={onClick}
			className={cn(
				"w-94 text-left px-3 py-2.5 rounded-lg transition-colors flex items-start group relative cursor-pointer",
				isActive
					? "bg-secondary dark:bg-neutral-800 text-foreground"
					: "text-muted-foreground hover:bg-secondary/50 dark:hover:bg-neutral-800/50 hover:text-foreground"
			)}
		>
			{/* <MessageSquare className="h-4 w-4 mt-0.5 mr-3 flex-shrink-0" /> */}

			<div className="flex-1 min-w-0 max-w-[320px]">	
				<div className="text-xs font-medium truncate">{title}</div>
				<div className="text-xs text-muted-foreground truncate">
					{timestamp}
				</div>
			</div>

			{(onDelete || onRename) && (
				<div
					className={cn(
						"opacity-0 group-hover:opacity-100 transition-opacity ml-2 flex-shrink-0",
						isActive && "opacity-100"
					)}
				>
					<DropdownMenu>
						<DropdownMenuTrigger asChild>
							<Button
								variant="ghost"
								size="icon"
								className="h-6 w-6 rounded-full hover:bg-secondary dark:hover:bg-neutral-700"
								onClick={(e) => e.stopPropagation()}
							>
								<MoreHorizontal className="h-3.5 w-3.5" />
								<span className="sr-only">More options</span>
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end" className="w-40">
							{onRename && (
								<DropdownMenuItem
									onClick={(e) => handleActionClick(e, onRename)}
								>
									Rename
								</DropdownMenuItem>
							)}
							{onDelete && (
								<DropdownMenuItem
									onClick={(e) => handleActionClick(e, onDelete)}
									className="text-destructive focus:text-destructive"
								>
									Delete
								</DropdownMenuItem>
							)}
						</DropdownMenuContent>
					</DropdownMenu>
				</div>
			)}
		</div>
	);
};
