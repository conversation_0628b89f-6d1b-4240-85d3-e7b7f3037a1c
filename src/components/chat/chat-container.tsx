"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { ChatWindow } from "./chat-window";
import { MessageInput } from "./message-input";
import { useChatContext } from "@/lib/chat/chat-context";
import {
	Loader2,
	PlusCircle,
	MessageSquare,
	FileText,
	ChevronDown,
	ChevronRight,
	Settings,
	Menu,
	Sparkles,
	LineChart,
	Gavel,
} from "lucide-react";
import { format } from "date-fns";
import { usePathname, useRouter } from "next/navigation";
import { chatService } from "@/lib/services/chat-service";
import { ChatHistoryItem } from "./chat-history-item";
import { ThemeToggle } from "../../app/theme-toggle";
import { ResizablePanel } from "@/components/ui/resizable-panel";
import Link from "next/link";
import Image from "next/image";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "../ui/collapsible";
import { cn } from "../../lib/utils";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { useMediaQuery } from "@/hooks/use-media-query";

export function ChatContainer() {
	const { createSession, currentSession, documentUploaded, clearSession } =
		useChatContext();
	const router = useRouter();
	const pathname = usePathname();
	const [chatSessions, setChatSessions] = useState<
		Array<{
			id: string;
			title: string;
			createdAt: string;
			documentId?: string;
		}>
	>([]);

	const [loadingSessions, setLoadingSessions] = useState(false);
	const [isDocumentsOpen, setIsDocumentsOpen] = useState(false);
	const [isAIAutomationOpen, setIsAIAutomationOpen] = useState(false);
	const [isAnalysisOpen, setIsAnalysisOpen] = useState(false);
	const [isLegalToolsOpen, setIsLegalToolsOpen] = useState(false);
	const [isAccountOpen, setIsAccountOpen] = useState(false);

	const switchSession = useCallback(
		async (sessionId: string) => {
			try {
				setLoadingSessions(true);
				const session = chatSessions.find((s) => s.id === sessionId);
				if (session && session.documentId) {
					await createSession(session.id);
					router.push(`/chat/${sessionId}`);
				}
			} catch (error) {
				console.error("Failed to switch session:", error);
			} finally {
				setLoadingSessions(false);
			}
		},
		[createSession, router, chatSessions]
	);

	const deleteSession = useCallback(
		async (sessionId: string) => {
			try {
				await chatService.deleteSession(sessionId);
				setChatSessions(chatSessions.filter((s) => s.id !== sessionId));
				if (currentSession?.id === sessionId) {
					clearSession();
					router.replace("/chat", { scroll: false });
				}
			} catch (error) {
				console.error("Failed to delete session:", error);
			}
		},
		[chatSessions, clearSession, currentSession, router]
	);

	const renameSession = useCallback(async (sessionId: string) => {
		// This would typically open a dialog to rename
		// For now, we'll just log it
		console.log("Rename session:", sessionId);
		// In a real implementation, you would:
		// 1. Show a dialog with an input field
		// 2. Call an API to update the session title
		// 3. Update the local state with setChatSessions
	}, []);

	useEffect(() => {
		const loadSessions = async () => {
			try {
				setLoadingSessions(true);
				const sessions = await chatService.getSessions();
				// Sort sessions by creation date (newest first)
				const sortedSessions = sessions.sort(
					(a, b) =>
						new Date(b.createdAt).getTime() - new Date(a.createdAt).getTime()
				);
				setChatSessions(sortedSessions);
			} catch (error) {
				console.error("Failed to load chat sessions:", error);
			} finally {
				setLoadingSessions(false);
			}
		};

		loadSessions();
	}, []);

	const startNewChat = () => {
		setLoadingSessions(false);
		clearSession();
		router.replace("/chat", { scroll: false });
	};

	const chatHistory = (
		<div className="h-full flex flex-col bg-background border-r border-border">
			<div className="border-b border-border flex justify-between items-center h-12">
				<Link href="/" className="flex items-center justify-center">
					<Image
						src="/logos/light-512.png"
						alt="Docgic Logo Light"
						width={300}
						height={300}
						className="w-32 object-cover block dark:hidden"
					/>

					<Image
						src="/logos/trans-512.png"
						alt="Docgic Logo Dark"
						width={300}
						height={300}
						className="w-32 object-cover hidden dark:block"
					/>
				</Link>
				<ThemeToggle />
			</div>
			<div className="px-3 py-2">
				<nav className="space-y-1">
					{/* Documents */}
					<Collapsible
						open={isDocumentsOpen}
						onOpenChange={setIsDocumentsOpen}
						className="w-full"
					>
						<CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm hover:bg-muted text-muted-foreground">
							<div className="flex items-center gap-3">
								<FileText className="h-4 w-4" />
								<span>Documents</span>
							</div>
							{isDocumentsOpen ? (
								<ChevronDown className="h-4 w-4" />
							) : (
								<ChevronRight className="h-4 w-4" />
							)}
						</CollapsibleTrigger>
						<CollapsibleContent className="pl-10 space-y-1">
							<Link
								href="/documents"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname === "/documents"
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								All Documents
							</Link>
							<Link
								href="/documents/upload"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname === "/documents/upload"
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Upload Document
							</Link>
							<Link
								href="/document-organization"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname === "/document-organization"
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Document Organization
							</Link>
						</CollapsibleContent>
					</Collapsible>

					{/* AI & Automation */}
					<Collapsible
						open={isAIAutomationOpen}
						onOpenChange={setIsAIAutomationOpen}
						className="w-full"
					>
						<CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm hover:bg-muted text-muted-foreground">
							<div className="flex items-center gap-3">
								<Sparkles className="h-4 w-4" />
								<span>AI & Automation</span>
							</div>
							{isAIAutomationOpen ? (
								<ChevronDown className="h-4 w-4" />
							) : (
								<ChevronRight className="h-4 w-4" />
							)}
						</CollapsibleTrigger>
						<CollapsibleContent className="pl-10 space-y-1">
							<Link
								href="/document-automation"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname === "/document-automation"
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Document Automation
							</Link>
							<Link
								href="/negotiation-simulator"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname?.startsWith("/negotiation-simulator")
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Negotiation Simulator
							</Link>
							<Link
								href="/compliance-auditor"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname?.startsWith("/compliance-auditor")
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Compliance Auditor
							</Link>
						</CollapsibleContent>
					</Collapsible>

					{/* Analysis & Comparison */}
					<Collapsible
						open={isAnalysisOpen}
						onOpenChange={setIsAnalysisOpen}
						className="w-full"
					>
						<CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm hover:bg-muted text-muted-foreground">
							<div className="flex items-center gap-3">
								<LineChart className="h-4 w-4" />
								<span>Analysis & Comparison</span>
							</div>
							{isAnalysisOpen ? (
								<ChevronDown className="h-4 w-4" />
							) : (
								<ChevronRight className="h-4 w-4" />
							)}
						</CollapsibleTrigger>
						<CollapsibleContent className="pl-10 space-y-1">
							<Link
								href="/document-comparison"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname?.startsWith("/document-comparison")
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Document Comparison
							</Link>
							<Link
								href="/compare/sections"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname === "/compare/sections"
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Compare Sections
							</Link>
							<Link
								href="/analytics"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname === "/analytics"
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Analytics Dashboard
							</Link>
						</CollapsibleContent>
					</Collapsible>

					{/* Legal Tools */}
					<Collapsible
						open={isLegalToolsOpen}
						onOpenChange={setIsLegalToolsOpen}
						className="w-full"
					>
						<CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm hover:bg-muted text-muted-foreground">
							<div className="flex items-center gap-3">
								<Gavel className="h-4 w-4" />
								<span>Legal Tools</span>
							</div>
							{isLegalToolsOpen ? (
								<ChevronDown className="h-4 w-4" />
							) : (
								<ChevronRight className="h-4 w-4" />
							)}
						</CollapsibleTrigger>
						<CollapsibleContent className="pl-10 space-y-1">
							<Link
								href="/contract-playbooks"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname?.startsWith("/contract-playbooks")
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Contract Playbooks
							</Link>
							<Link
								href="/depositions"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname?.startsWith("/depositions")
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Depositions
							</Link>
							<Link
								href="/case-search"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname?.startsWith("/case-search")
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Case Search
							</Link>
							<Link
								href="/clause-library"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname === "/clause-library"
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Clause Library
							</Link>
						</CollapsibleContent>
					</Collapsible>

					{/* Account & Settings */}
					<Collapsible
						open={isAccountOpen}
						onOpenChange={setIsAccountOpen}
						className="w-full"
					>
						<CollapsibleTrigger className="flex w-full items-center justify-between rounded-lg px-3 py-2 text-sm hover:bg-muted text-muted-foreground">
							<div className="flex items-center gap-3">
								<Settings className="h-4 w-4" />
								<span>Account & Settings</span>
							</div>
							{isAccountOpen ? (
								<ChevronDown className="h-4 w-4" />
							) : (
								<ChevronRight className="h-4 w-4" />
							)}
						</CollapsibleTrigger>
						<CollapsibleContent className="pl-10 space-y-1">
							<Link
								href="/profile"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname === "/profile"
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Profile
							</Link>
							<Link
								href="/subscription"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname?.startsWith("/subscription")
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Subscription
							</Link>
							<Link
								href="/feedback"
								className={cn(
									"flex items-center gap-2 rounded-lg px-3 py-2 text-sm transition-all hover:bg-muted",
									pathname === "/feedback"
										? "text-white"
										: "text-muted-foreground"
								)}
							>
								Feedback
							</Link>
						</CollapsibleContent>
					</Collapsible>
				</nav>
			</div>

			{/* Divider */}
			<div className="mt-2 mb-2 border-t border-border mx-3"></div>

			<div className="px-3 py-2 border-b border-border">
				<div className="flex items-center justify-between">
					<div className="flex items-center text-xs font-medium text-muted-foreground uppercase tracking-wider">
						<MessageSquare className="h-3.5 w-3.5 mr-1.5" />
						Recent Chats
					</div>
					{chatSessions.length > 0 && (
						<Button
							variant="ghost"
							size="sm"
							className="h-6 px-2 text-xs text-muted-foreground hover:text-foreground"
							onClick={startNewChat}
						>
							<PlusCircle className="h-3 w-3 mr-1" />
							New
						</Button>
					)}
				</div>
			</div>

			<ScrollArea className="flex-1 px-2 py-2">
				{loadingSessions ? (
					<div className="flex justify-center p-4">
						<Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
					</div>
				) : chatSessions.length === 0 ? (
					<div className="text-center py-6 text-muted-foreground text-sm">
						No recent chats
					</div>
				) : (
					<div className="space-y-1 py-2">
						{chatSessions.map((session) => (
							<ChatHistoryItem
								key={session.id}
								title={session.title}
								timestamp={format(new Date(session.createdAt), "MMM d, yyyy")}
								isActive={currentSession?.id === session.id}
								onClick={() => switchSession(session.id)}
								onDelete={() => deleteSession(session.id)}
								onRename={() => renameSession(session.id)}
							/>
						))}
					</div>
				)}
			</ScrollArea>
		</div>
	);

	const chatArea = (
		<div className="h-full w-full flex flex-col bg-background">
			<div className="flex-1 flex flex-col min-h-0">
				<div className="flex-1 overflow-hidden">
					<ChatWindow />
				</div>
				{(currentSession || documentUploaded) && (
					<div className="flex-shrink-0">
						<MessageInput />
					</div>
				)}
			</div>
		</div>
	);

	// Check if we're on mobile
	const isMobile = useMediaQuery("(max-width: 768px)");

	return (
		<div className="h-screen overflow-hidden bg-background text-foreground chat-interface">
			{isMobile ? (
				<div className="flex flex-col h-full">
					<div className="border-b border-border flex justify-between items-center h-14 px-4 flex-shrink-0">
						<Link href="/" className="flex items-center">
							<Image
								src="/logos/light-512.png"
								alt="Docgic Logo Light"
								width={100}
								height={100}
								className="w-20 object-cover block dark:hidden"
							/>
							<Image
								src="/logos/trans-512.png"
								alt="Docgic Logo Dark"
								width={100}
								height={100}
								className="w-20 object-cover hidden dark:block"
							/>
						</Link>
						<div className="flex items-center space-x-2">
							<ThemeToggle />
							<Sheet>
								<SheetTrigger asChild>
									<Button variant="ghost" size="icon" className="h-9 w-9">
										<Menu className="h-5 w-5" />
									</Button>
								</SheetTrigger>
								<SheetContent side="left" className="p-0 w-80 max-w-[85vw]">
									<div className="h-full pt-14">{chatHistory}</div>
								</SheetContent>
							</Sheet>
						</div>
					</div>
					<div className="flex-1 min-h-0">{chatArea}</div>
				</div>
			) : (
				<ResizablePanel
					leftPanel={chatHistory}
					rightPanel={chatArea}
					initialLeftWidth={320}
					minLeftWidth={280}
					maxLeftWidth={480}
				/>
			)}
		</div>
	);
}
