"use client";

import React from 'react';

interface DevOnlyWrapperProps {
  children: React.ReactNode;
  fallback?: React.ReactNode;
  showInProduction?: boolean;
}

/**
 * DevOnlyWrapper - Conditionally renders content based on environment
 * 
 * @param children - Content to show in development
 * @param fallback - Optional content to show in production (default: null)
 * @param showInProduction - Force show in production (default: false)
 * 
 * Usage:
 * <DevOnlyWrapper>
 *   <DevelopmentOnlyComponent />
 * </DevOnlyWrapper>
 * 
 * Or with fallback:
 * <DevOnlyWrapper fallback={<ProductionComponent />}>
 *   <DevelopmentOnlyComponent />
 * </DevOnlyWrapper>
 */
export function DevOnlyWrapper({ 
  children, 
  fallback = null, 
  showInProduction = false 
}: DevOnlyWrapperProps) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';
  
  // Show children in development or if explicitly allowed in production
  if (isDevelopment || showInProduction) {
    return <>{children}</>;
  }
  
  // Show fallback in production (or null if no fallback provided)
  if (isProduction) {
    return <>{fallback}</>;
  }
  
  // Default fallback for other environments
  return <>{children}</>;
}

/**
 * DevOnlyRoute - Wrapper for entire pages/routes that should only be available in development
 * 
 * Usage in page components:
 * export default function DevelopmentPage() {
 *   return (
 *     <DevOnlyRoute>
 *       <YourPageContent />
 *     </DevOnlyRoute>
 *   );
 * }
 */
export function DevOnlyRoute({ children }: { children: React.ReactNode }) {
  const isDevelopment = process.env.NODE_ENV === 'development';
  
  if (!isDevelopment) {
    return (
      <div className="min-h-screen flex items-center justify-center bg-background">
        <div className="text-center space-y-4 max-w-md mx-auto p-6">
          <div className="text-6xl">🚧</div>
          <h1 className="text-2xl font-bold text-foreground">
            Development Only
          </h1>
          <p className="text-muted-foreground">
            This page is only available in development mode.
          </p>
          <button 
            onClick={() => window.history.back()}
            className="px-4 py-2 bg-primary text-primary-foreground rounded-md hover:bg-primary/90 transition-colors"
          >
            Go Back
          </button>
        </div>
      </div>
    );
  }
  
  return <>{children}</>;
}

/**
 * FeatureFlag - Conditionally render content based on feature flags
 * 
 * @param feature - Feature flag name
 * @param enabled - Override enabled state
 * @param children - Content to render when enabled
 * @param fallback - Content to render when disabled
 */
interface FeatureFlagProps {
  feature: string;
  enabled?: boolean;
  children: React.ReactNode;
  fallback?: React.ReactNode;
}

export function FeatureFlag({ 
  feature, 
  enabled, 
  children, 
  fallback = null 
}: FeatureFlagProps) {
  // Check environment variables for feature flags
  const envFlag = process.env[`NEXT_PUBLIC_FEATURE_${feature.toUpperCase()}`];
  const isEnabled = enabled !== undefined ? enabled : envFlag === 'true';
  
  if (isEnabled) {
    return <>{children}</>;
  }
  
  return <>{fallback}</>;
}

/**
 * Environment-specific component visibility
 */
export const EnvWrapper = {
  Development: ({ children }: { children: React.ReactNode }) => (
    <DevOnlyWrapper>{children}</DevOnlyWrapper>
  ),
  
  Production: ({ children }: { children: React.ReactNode }) => (
    <DevOnlyWrapper showInProduction={true} fallback={children}>
      {null}
    </DevOnlyWrapper>
  ),
  
  Staging: ({ children }: { children: React.ReactNode }) => {
    const isStaging = process.env.NODE_ENV === 'development' || 
                     process.env.NEXT_PUBLIC_ENVIRONMENT === 'staging';
    return isStaging ? <>{children}</> : null;
  }
};

/**
 * Hook for checking environment
 */
export function useEnvironment() {
  const isDevelopment = process.env.NODE_ENV === 'development';
  const isProduction = process.env.NODE_ENV === 'production';
  const isStaging = process.env.NEXT_PUBLIC_ENVIRONMENT === 'staging';
  
  return {
    isDevelopment,
    isProduction,
    isStaging,
    environment: process.env.NODE_ENV,
  };
}
