import type React from "react";
import ReactMarkdown from "react-markdown";
import { cn } from "@/lib/utils";

interface MarkdownRendererProps {
	markdownContent: string;
}

const MarkdownRenderer: React.FC<MarkdownRendererProps> = ({
	markdownContent,
}) => {
	return (
		<div
			className={cn(
				"text-base leading-8",
				"prose prose-sm max-w-none",
				"prose-headings:text-foreground prose-p:text-foreground prose-strong:text-foreground",
				"prose-ol:text-foreground prose-ul:text-foreground",
				"prose-a:text-primary",
				"prose-code:bg-secondary/50 prose-code:rounded prose-code:px-1 prose-code:py-0.5 prose-code:text-foreground",
				"prose-pre:bg-secondary/50 dark:prose-pre:bg-neutral-800 prose-pre:text-foreground",
				"dark:prose-invert",
				"prose-p:my-1 prose-headings:my-2 prose-ul:my-1 prose-ol:my-1 prose-li:my-0.5 prose-pre:my-1"
			)}
		>
			<ReactMarkdown>{markdownContent}</ReactMarkdown>
		</div>
	);
};

export default MarkdownRenderer;
