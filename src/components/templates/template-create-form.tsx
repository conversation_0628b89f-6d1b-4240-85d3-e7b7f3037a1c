"use client";

import { useCreateTemplate } from "@/lib/services/template-service";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface FieldConfig {
  name: string;
  type: "text" | "number" | "date" | "select";
  required: boolean;
  options?: string[];
}

export default function TemplateCreateForm() {
  const router = useRouter();
  const createTemplate = useCreateTemplate();
  const [fields, setFields] = useState<FieldConfig[]>([]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const formData = new FormData(event.target as HTMLFormElement);
    
    const templateData = {
      name: formData.get("name") as string,
      description: formData.get("description") as string,
      schema: {
        fields: fields,
        layout: {} // Can be enhanced with layout configuration
      }
    };

    try {
      await createTemplate.mutateAsync(templateData);
      router.push("/templates");
    } catch (error) {
      console.error("Failed to create template:", error);
    }
  };

  const addField = () => {
    setFields([...fields, { name: "", type: "text", required: false }]);
  };

  const updateField = (index: number, updates: Partial<FieldConfig>) => {
    const newFields = [...fields];
    newFields[index] = { ...newFields[index], ...updates };
    setFields(newFields);
  };

  const removeField = (index: number) => {
    setFields(fields.filter((_, i) => i !== index));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block mb-2">Template Name</label>
        <Input
          name="name"
          placeholder="Enter template name"
          required
          className="w-full"
        />
      </div>

      <div>
        <label className="block mb-2">Description</label>
        <Textarea
          name="description"
          placeholder="Enter template description"
          className="w-full"
        />
      </div>

      <div>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Template Fields</h3>
          <Button type="button" onClick={addField} variant="outline">
            Add Field
          </Button>
        </div>

        <div className="space-y-4">
          {fields.map((field, index) => (
            <div key={index} className="flex gap-4 items-start p-4 border rounded">
              <div className="flex-1 space-y-4">
                <Input
                  value={field.name}
                  onChange={(e) => updateField(index, { name: e.target.value })}
                  placeholder="Field name"
                />
                <Select
                  value={field.type}
                  onValueChange={(value) => 
                    updateField(index, { 
                      type: value as FieldConfig["type"],
                      options: value === "select" ? [] : undefined
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select field type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="text">Text</SelectItem>
                    <SelectItem value="number">Number</SelectItem>
                    <SelectItem value="date">Date</SelectItem>
                    <SelectItem value="select">Select</SelectItem>
                  </SelectContent>
                </Select>

                {field.type === "select" && (
                  <Input
                    value={field.options?.join(", ") || ""}
                    onChange={(e) =>
                      updateField(index, {
                        options: e.target.value.split(",").map((s) => s.trim())
                      })
                    }
                    placeholder="Options (comma-separated)"
                  />
                )}
              </div>
              <div className="flex items-start gap-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={field.required}
                    onChange={(e) =>
                      updateField(index, { required: e.target.checked })
                    }
                    className="mr-2"
                  />
                  Required
                </label>
                <Button
                  type="button"
                  onClick={() => removeField(index)}
                  variant="destructive"
                  size="sm"
                >
                  Remove
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
        <Button type="submit" disabled={createTemplate.isPending}>
          {createTemplate.isPending ? "Creating..." : "Create Template"}
        </Button>
      </div>
    </form>
  );
}