"use client";

import { useTemplates } from "@/lib/services/template-service";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import Link from "next/link";
import { useRouter } from "next/navigation";

export default function TemplateList() {
  const { data: templates, isLoading, error } = useTemplates();
  const router = useRouter();

  if (isLoading) {
    return (
      <div className="flex items-center justify-center min-h-[200px]">
        <div className="text-muted-foreground">Loading templates...</div>
      </div>
    );
  }

  if (error) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[200px] space-y-4">
        <div className="text-destructive">
          {error instanceof Error ? error.message : 'Error loading templates'}
        </div>
        <Button 
          variant="outline" 
          onClick={() => router.refresh()}
        >
          Retry
        </Button>
      </div>
    );
  }

  if (!templates?.length) {
    return (
      <div>
        <div className="flex justify-end mb-6">
          <Link href="/templates/create">
            <Button>Create Template</Button>
          </Link>
        </div>

        <Card className="p-8">
          <div className="flex flex-col items-center justify-center text-center space-y-4">
            <p className="text-muted-foreground">No templates found.</p>
            <p className="text-sm text-muted-foreground">
              Get started by creating your first report template.
            </p>
            <Link href="/templates/create">
              <Button>Create First Template</Button>
            </Link>
          </div>
        </Card>
      </div>
    );
  }

  return (
    <div>
      <div className="flex justify-end mb-6">
        <Link href="/templates/create">
          <Button>Create Template</Button>
        </Link>
      </div>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-3">
        {templates.map((template) => (
          <Card key={template.id} className="p-4">
            <h3 className="font-semibold mb-2">{template.name}</h3>
            {template.description && (
              <p className="text-sm text-muted-foreground mb-4">
                {template.description}
              </p>
            )}
            <div className="flex justify-end gap-2">
              <Link href={`/templates/${template.id}/edit`}>
                <Button variant="outline" size="sm">
                  Edit
                </Button>
              </Link>
            </div>
          </Card>
        ))}
      </div>
    </div>
  );
}