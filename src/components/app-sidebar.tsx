import * as React from "react";
import { MessageSquare, BarChart3, FileText, GraduationCap, Upload, Zap, Setting<PERSON> } from "lucide-react";

import { SearchForm } from "@/components/search-form";
import { VersionSwitcher } from "@/components/version-switcher";
import {
	Sidebar,
	SidebarContent,
	SidebarGroup,
	SidebarGroupContent,
	SidebarGroupLabel,
	SidebarHeader,
	SidebarMenu,
	SidebarMenuButton,
	SidebarMenuItem,
	SidebarRail,
} from "@/components/ui/sidebar";
import { Badge } from "@/components/ui/badge";

// This is sample data.
const data = {
	versions: ["1.0.1", "1.1.0-alpha", "2.0.0-beta1"],
	navMain: [
		{
			title: "Document Tools",
			url: "#",
			items: [
				{
					title: "Document Automation",
					url: "/document-automation",
				},
				{
					title: "Clause Library",
					url: "/clause-library",
				},
				{
					title: "Document Comparison",
					url: "/document-comparison",
				},
			],
		},
		{
			title: "Collaboration",
			url: "#",
			items: [
				{
					title: "Collaboration Suite",
					url: "/collaboration",
				},
				{
					title: "Real-time Editing",
					url: "/collaboration?tab=editor",
				},
				{
					title: "Workflows",
					url: "/collaboration?tab=workflows",
				},
				{
					title: "Task Management",
					url: "/collaboration?tab=tasks",
				},
			],
		},
		{
			title: "Negotiation Practice",
			url: "#",
			items: [
				{
					title: "Quick Practice",
					url: "/negotiation",
					description: "Start practicing immediately",
					icon: MessageSquare,
					badge: "New"
				},
				{
					title: "Practice with Contract",
					url: "/negotiation/contract",
					description: "Upload and practice with your contract",
					icon: Upload
				},
				{
					title: "Learn Basics",
					url: "/negotiation/learn",
					description: "5-minute interactive tutorial",
					icon: GraduationCap
				},
			],
		},
		{
			title: "Advanced Negotiation",
			url: "#",
			items: [
				{
					title: "Negotiation Simulator",
					url: "/negotiation-simulator",
					description: "Full simulator with analytics",
					icon: BarChart3
				},
				{
					title: "Contract Playbooks",
					url: "/contract-playbooks",
					description: "Strategic contract analysis",
					icon: FileText
				},
				{
					title: "Negotiation Playbooks",
					url: "/negotiation-playbook",
					description: "Detailed negotiation strategies",
					icon: Settings
				},
			],
		},
		{
			title: "Demo & Testing",
			url: "#",
			items: [
				{
					title: "Chat Negotiations Demo",
					url: "/demo/chat-negotiation",
					description: "Try without signup",
					icon: Zap
				},
				{
					title: "Dynamic Move Forms Demo",
					url: "/demo/dynamic-moves",
					description: "Test advanced features",
					icon: Settings
				},
			],
		},
		{
			title: "Getting Started",
			url: "#",
			items: [
				{
					title: "Installation",
					url: "#",
				},
				{
					title: "Project Structure",
					url: "#",
				},
			],
		},
		{
			title: "Building Your Application",
			url: "#",
			items: [
				{
					title: "Routing",
					url: "#",
				},
				{
					title: "Data Fetching",
					url: "#",
					isActive: true,
				},
				{
					title: "Rendering",
					url: "#",
				},
				{
					title: "Caching",
					url: "#",
				},
				{
					title: "Styling",
					url: "#",
				},
				{
					title: "Optimizing",
					url: "#",
				},
				{
					title: "Configuring",
					url: "#",
				},
				{
					title: "Testing",
					url: "#",
				},
				{
					title: "Authentication",
					url: "#",
				},
				{
					title: "Deploying",
					url: "#",
				},
				{
					title: "Upgrading",
					url: "#",
				},
				{
					title: "Examples",
					url: "#",
				},
			],
		},
		{
			title: "API Reference",
			url: "#",
			items: [
				{
					title: "Components",
					url: "#",
				},
				{
					title: "File Conventions",
					url: "#",
				},
				{
					title: "Functions",
					url: "#",
				},
				{
					title: "next.config.js Options",
					url: "#",
				},
				{
					title: "CLI",
					url: "#",
				},
				{
					title: "Edge Runtime",
					url: "#",
				},
			],
		},
		{
			title: "Architecture",
			url: "#",
			items: [
				{
					title: "Accessibility",
					url: "#",
				},
				{
					title: "Fast Refresh",
					url: "#",
				},
				{
					title: "Next.js Compiler",
					url: "#",
				},
				{
					title: "Supported Browsers",
					url: "#",
				},
				{
					title: "Turbopack",
					url: "#",
				},
			],
		},
	],
};

export function AppSidebar({ ...props }: React.ComponentProps<typeof Sidebar>) {
	return (
		<Sidebar {...props}>
			<SidebarHeader>
				<VersionSwitcher
					versions={data.versions}
					defaultVersion={data.versions[0]}
				/>
				<SearchForm />
			</SidebarHeader>
			<SidebarContent>
				{/* We create a SidebarGroup for each parent. */}
				{data.navMain.map((item) => (
					<SidebarGroup key={item.title}>
						<SidebarGroupLabel>{item.title}</SidebarGroupLabel>
						<SidebarGroupContent>
							<SidebarMenu>
								{item.items.map((subItem) => (
									<SidebarMenuItem key={subItem.title}>
										<SidebarMenuButton asChild isActive={subItem.isActive}>
											<a href={subItem.url} className="flex items-center gap-3">
												{subItem.icon && (
													<subItem.icon className="h-4 w-4" />
												)}
												<div className="flex-1">
													<div className="flex items-center gap-2">
														<span>{subItem.title}</span>
														{subItem.badge && (
															<Badge variant="secondary" className="text-xs">
																{subItem.badge}
															</Badge>
														)}
													</div>
													{subItem.description && (
														<div className="text-xs text-muted-foreground">
															{subItem.description}
														</div>
													)}
												</div>
											</a>
										</SidebarMenuButton>
									</SidebarMenuItem>
								))}
							</SidebarMenu>
						</SidebarGroupContent>
					</SidebarGroup>
				))}
			</SidebarContent>
			<SidebarRail />
		</Sidebar>
	);
}
