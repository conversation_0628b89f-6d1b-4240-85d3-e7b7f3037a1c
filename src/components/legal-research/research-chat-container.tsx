"use client";

import { useState, useEffect, useCallback } from "react";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Card, CardContent } from "@/components/ui/card";
import { Skeleton } from "@/components/ui/skeleton";
import { useMediaQuery } from "@/hooks/use-media-query";
import { ResizablePanel } from "@/components/ui/resizable-panel";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import { ResearchMessage } from "./research-message";
import { ResearchInput } from "./research-input";
import { useResearchContext } from "@/lib/legal-research/research-context";
import {
  Loader2,
  PlusCircle,
  Search,
  Menu,
  MessageSquare,
  BookOpen,
  Scale,
  Sparkles,
} from "lucide-react";
import { format } from "date-fns";
import { useRouter } from "next/navigation";
import { cn } from "@/lib/utils";
import Link from "next/link";
import Image from "next/image";
import { ThemeToggle } from "@/app/theme-toggle";
import { useResearchSessions } from "@/hooks/use-legal-research";

export function ResearchChatContainer() {
  const {
    currentSession,
    messages,
    isLoading,
    createSession,
    loadSession,
    askFollowUp,
  } = useResearchContext();

  const { data: sessionsData, isLoading: loadingSessions } = useResearchSessions({
    limit: 20,
  });

  const [showWelcome, setShowWelcome] = useState(true);
  const router = useRouter();

  useEffect(() => {
    if (currentSession) {
      setShowWelcome(false);
    } else {
      setShowWelcome(true);
    }
  }, [currentSession]);

  const handleCreateSession = async () => {
    try {
      await createSession();
    } catch (error) {
      console.error("Failed to create session:", error);
    }
  };

  const handleLoadSession = async (sessionId: string) => {
    try {
      await loadSession(sessionId);
    } catch (error) {
      console.error("Failed to load session:", error);
    }
  };

  const handleFollowUpClick = async (question: string) => {
    try {
      await askFollowUp(question);
    } catch (error) {
      console.error("Failed to ask follow-up:", error);
    }
  };

  const researchSessions = sessionsData?.data.sessions || [];

  // Session sidebar
  const sessionSidebar = (
    <div className="h-full flex flex-col bg-card dark:bg-[#1a1a1a] border-r border-border">
      <div className="p-4 border-b border-border">
        <div className="flex items-center justify-between mb-4">
          <h2 className="font-semibold text-sm flex items-center">
            <Search className="w-4 h-4 mr-2" />
            Legal Research
          </h2>
        </div>

        <Button
          onClick={handleCreateSession}
          className="w-full"
          size="sm"
        >
          <PlusCircle className="w-4 h-4 mr-2" />
          New Research
        </Button>
      </div>

      <ScrollArea className="flex-1 px-2 py-2">
        {loadingSessions ? (
          <div className="flex justify-center p-4">
            <Loader2 className="h-4 w-4 animate-spin text-muted-foreground" />
          </div>
        ) : researchSessions.length === 0 ? (
          <div className="text-center py-6 text-muted-foreground text-sm">
            No research sessions
          </div>
        ) : (
          <div className="space-y-1 py-2">
            {researchSessions.map((session) => (
              <SessionHistoryItem
                key={session.sessionId}
                session={session}
                isActive={currentSession?.sessionId === session.sessionId}
                onClick={() => handleLoadSession(session.sessionId)}
              />
            ))}
          </div>
        )}
      </ScrollArea>
    </div>
  );

  // Main chat area
  const chatArea = (
    <div className="h-full w-full flex flex-col bg-background">
      <div className="flex-1 flex flex-col min-h-0">
        <div className="flex-1 overflow-hidden">
          <ResearchChatWindow 
            showWelcome={showWelcome}
            onCreateSession={handleCreateSession}
            onFollowUpClick={handleFollowUpClick}
          />
        </div>
        <div className="flex-shrink-0">
          <ResearchInput />
        </div>
      </div>
    </div>
  );

  // Check if we're on mobile
  const isMobile = useMediaQuery("(max-width: 768px)");

  return (
    <div className="h-screen overflow-hidden bg-background text-foreground">
      {isMobile ? (
        <div className="flex flex-col h-full">
          <div className="border-b border-border flex justify-between items-center h-14 px-4 flex-shrink-0">
            <Link href="/" className="flex items-center">
              <Image
                src="/logos/light-512.png"
                alt="Docgic Logo Light"
                width={100}
                height={100}
                className="w-20 object-cover block dark:hidden"
              />
              <Image
                src="/logos/trans-512.png"
                alt="Docgic Logo Dark"
                width={100}
                height={100}
                className="w-20 object-cover hidden dark:block"
              />
            </Link>
            <div className="flex items-center space-x-2">
              <ThemeToggle />
              <Sheet>
                <SheetTrigger asChild>
                  <Button variant="ghost" size="icon" className="h-9 w-9">
                    <Menu className="h-5 w-5" />
                  </Button>
                </SheetTrigger>
                <SheetContent side="left" className="p-0 w-80 max-w-[85vw]">
                  <div className="h-full pt-14">{sessionSidebar}</div>
                </SheetContent>
              </Sheet>
            </div>
          </div>
          <div className="flex-1 min-h-0">{chatArea}</div>
        </div>
      ) : (
        <ResizablePanel
          leftPanel={sessionSidebar}
          rightPanel={chatArea}
          initialLeftWidth={320}
          minLeftWidth={280}
          maxLeftWidth={480}
        />
      )}
    </div>
  );
}

interface SessionHistoryItemProps {
  session: any; // ResearchSession type
  isActive: boolean;
  onClick: () => void;
}

function SessionHistoryItem({ session, isActive, onClick }: SessionHistoryItemProps) {
  return (
    <button
      onClick={onClick}
      className={cn(
        "w-full text-left p-3 rounded-lg transition-colors group",
        isActive
          ? "bg-secondary dark:bg-[#252525] text-foreground"
          : "text-muted-foreground hover:bg-secondary/50 dark:hover:bg-[#252525]/50 hover:text-foreground"
      )}
    >
      <div className="flex items-start space-x-2">
        <MessageSquare className="w-4 h-4 mt-0.5 flex-shrink-0" />
        <div className="flex-1 min-w-0">
          <p className="text-sm font-medium truncate">{session.title}</p>
          <div className="flex items-center space-x-2 mt-1">
            <span className="text-xs text-muted-foreground">
              {session.queryCount} queries
            </span>
            <span className="text-xs text-muted-foreground">
              {format(new Date(session.updatedAt), "MMM d")}
            </span>
          </div>
        </div>
      </div>
    </button>
  );
}

interface ResearchChatWindowProps {
  showWelcome: boolean;
  onCreateSession: () => void;
  onFollowUpClick: (question: string) => void;
}

function ResearchChatWindow({ 
  showWelcome, 
  onCreateSession, 
  onFollowUpClick 
}: ResearchChatWindowProps) {
  const { messages, isLoading, currentSession } = useResearchContext();

  if (showWelcome || !currentSession) {
    return <WelcomeScreen onCreateSession={onCreateSession} />;
  }

  return (
    <ScrollArea className="flex-1 px-3 sm:px-4 lg:px-6">
      <div className="max-w-4xl mx-auto py-4">
        {isLoading && messages.length === 0 ? (
          <div className="space-y-4 py-8">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="flex items-center space-x-4">
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className="space-y-2 flex-1">
                  <Skeleton className="h-4 w-full max-w-[300px]" />
                  <Skeleton className="h-4 w-full max-w-[250px]" />
                </div>
              </div>
            ))}
          </div>
        ) : messages.length === 0 ? (
          <div className="flex flex-col items-center justify-center min-h-[60vh] py-16 text-center text-muted-foreground">
            <Search className="w-16 h-16 mb-4 text-muted-foreground/50" />
            <h2 className="text-2xl sm:text-3xl lg:text-4xl font-semibold mb-4 text-foreground">
              Ready for legal research
            </h2>
            <p className="text-base sm:text-lg max-w-md">
              Ask any legal question to get comprehensive research with sources and AI analysis.
            </p>
          </div>
        ) : (
          <div className="space-y-6 py-4">
            {[...messages].reverse().map((message, index) => (
              <ResearchMessage
                key={message.id}
                message={message}
                isLastMessage={index === messages.length - 1}
                onFollowUpClick={onFollowUpClick}
              />
            ))}
          </div>
        )}
      </div>
    </ScrollArea>
  );
}

function WelcomeScreen({ onCreateSession }: { onCreateSession: () => void }) {
  const exampleQuestions = [
    "What are the recent changes to CCPA privacy regulations?",
    "How do non-compete clauses vary by state jurisdiction?",
    "What are the key elements of a valid contract under common law?",
    "What are the latest developments in AI liability law?",
  ];

  return (
    <div className="flex flex-col items-center justify-center min-h-full py-16 px-4">
      <div className="max-w-2xl mx-auto text-center space-y-8">
        <div className="space-y-4">
          <div className="flex items-center justify-center space-x-2 mb-6">
            <Scale className="w-8 h-8 text-primary" />
            <h1 className="text-3xl sm:text-4xl font-bold text-foreground">
              Legal Research Assistant
            </h1>
          </div>
          
          <p className="text-lg text-muted-foreground">
            Get comprehensive legal research with AI-powered analysis, source citations, and follow-up suggestions.
          </p>
        </div>

        <div className="space-y-4">
          <h3 className="text-lg font-semibold text-foreground">Try asking:</h3>
          <div className="grid gap-3">
            {exampleQuestions.map((question, index) => (
              <Card key={index} className="hover:bg-muted/50 transition-colors cursor-pointer">
                <CardContent className="p-4">
                  <p className="text-sm text-left">{question}</p>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>

        <Button onClick={onCreateSession} size="lg" className="mt-8">
          <PlusCircle className="w-5 h-5 mr-2" />
          Start Research Session
        </Button>

        <div className="flex items-center justify-center space-x-6 text-sm text-muted-foreground">
          <div className="flex items-center space-x-1">
            <BookOpen className="w-4 h-4" />
            <span>Legal Sources</span>
          </div>
          <div className="flex items-center space-x-1">
            <Sparkles className="w-4 h-4" />
            <span>AI Analysis</span>
          </div>
          <div className="flex items-center space-x-1">
            <MessageSquare className="w-4 h-4" />
            <span>Follow-ups</span>
          </div>
        </div>
      </div>
    </div>
  );
}
