"use client";

import React, { useState } from "react";
import { Card, CardContent } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  User,
  Bot,
  ExternalLink,
  ChevronDown,
  ChevronUp,
  BookOpen,
  Scale,
  FileText,
  Newspaper,
  CreditCard,
  Lightbulb,
  TrendingUp,
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { ResearchMessage, SearchResult, AISynthesis } from "@/lib/types/legal-research";

interface ResearchMessageProps {
  message: ResearchMessage;
  isLastMessage?: boolean;
  onFollowUpClick?: (question: string) => void;
}

export function ResearchMessage({ 
  message, 
  isLastMessage = false, 
  onFollowUpClick 
}: ResearchMessageProps) {
  const [expandedSources, setExpandedSources] = useState<Set<string>>(new Set());
  const [showAllSources, setShowAllSources] = useState(false);

  const toggleSource = (sourceId: string) => {
    setExpandedSources(prev => {
      const newSet = new Set(prev);
      if (newSet.has(sourceId)) {
        newSet.delete(sourceId);
      } else {
        newSet.add(sourceId);
      }
      return newSet;
    });
  };

  if (message.role === 'user') {
    return <UserMessage message={message} />;
  }

  return (
    <div className="space-y-4">
      <AssistantMessage 
        message={message}
        expandedSources={expandedSources}
        showAllSources={showAllSources}
        onToggleSource={toggleSource}
        onToggleAllSources={() => setShowAllSources(!showAllSources)}
        onFollowUpClick={onFollowUpClick}
      />
    </div>
  );
}

function UserMessage({ message }: { message: ResearchMessage }) {
  return (
    <div className="flex items-start space-x-3 justify-end">
      <div className="max-w-[80%]">
        <Card className="bg-primary text-primary-foreground">
          <CardContent className="p-4">
            <p className="text-sm">{message.content}</p>
            {message.queryType && (
              <div className="mt-2 flex items-center space-x-2">
                <Badge variant="secondary" className="text-xs">
                  {message.queryType === 'research' ? 'Research Query' : 'Follow-up'}
                </Badge>
              </div>
            )}
          </CardContent>
        </Card>
      </div>
      <div className="flex-shrink-0">
        <div className="w-8 h-8 rounded-full bg-primary flex items-center justify-center">
          <User className="w-4 h-4 text-primary-foreground" />
        </div>
      </div>
    </div>
  );
}

interface AssistantMessageProps {
  message: ResearchMessage;
  expandedSources: Set<string>;
  showAllSources: boolean;
  onToggleSource: (sourceId: string) => void;
  onToggleAllSources: () => void;
  onFollowUpClick?: (question: string) => void;
}

function AssistantMessage({
  message,
  expandedSources,
  showAllSources,
  onToggleSource,
  onToggleAllSources,
  onFollowUpClick,
}: AssistantMessageProps) {
  return (
    <div className="flex items-start space-x-3">
      <div className="flex-shrink-0">
        <div className="w-8 h-8 rounded-full bg-secondary flex items-center justify-center">
          <Bot className="w-4 h-4 text-secondary-foreground" />
        </div>
      </div>
      <div className="flex-1 space-y-4 max-w-none">
        {/* Sources Section */}
        {message.searchResults && (
          <SourcesSection
            searchResults={message.searchResults}
            expandedSources={expandedSources}
            showAllSources={showAllSources}
            onToggleSource={onToggleSource}
            onToggleAllSources={onToggleAllSources}
          />
        )}

        {/* AI Synthesis Section */}
        {message.aiSynthesis && (
          <SynthesisSection synthesis={message.aiSynthesis} />
        )}

        {/* Follow-up Suggestions */}
        {message.followUpSuggestions && message.followUpSuggestions.length > 0 && (
          <FollowUpSection
            suggestions={message.followUpSuggestions}
            onFollowUpClick={onFollowUpClick}
          />
        )}

        {/* Credits Used */}
        {message.creditsUsed && (
          <div className="flex items-center space-x-2 text-xs text-muted-foreground">
            <CreditCard className="w-3 h-3" />
            <span>Credits used: {message.creditsUsed}</span>
          </div>
        )}
      </div>
    </div>
  );
}

function SourcesSection({
  searchResults,
  expandedSources,
  showAllSources,
  onToggleSource,
  onToggleAllSources,
}: {
  searchResults: { totalSources: number; sources: SearchResult[] };
  expandedSources: Set<string>;
  showAllSources: boolean;
  onToggleSource: (sourceId: string) => void;
  onToggleAllSources: () => void;
}) {
  const displaySources = showAllSources ? searchResults.sources : searchResults.sources.slice(0, 3);

  return (
    <Card>
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center space-x-2">
            <BookOpen className="w-4 h-4 text-muted-foreground" />
            <h4 className="font-medium text-sm">
              Sources ({searchResults.totalSources} found)
            </h4>
          </div>
          {searchResults.sources.length > 3 && (
            <Button
              variant="ghost"
              size="sm"
              onClick={onToggleAllSources}
              className="text-xs"
            >
              {showAllSources ? 'Show Less' : 'Show All'}
              {showAllSources ? (
                <ChevronUp className="w-3 h-3 ml-1" />
              ) : (
                <ChevronDown className="w-3 h-3 ml-1" />
              )}
            </Button>
          )}
        </div>

        <div className="space-y-2">
          {displaySources.map((source) => (
            <SourceCard
              key={source.id}
              source={source}
              isExpanded={expandedSources.has(source.id)}
              onToggle={() => onToggleSource(source.id)}
            />
          ))}
        </div>
      </CardContent>
    </Card>
  );
}

function SourceCard({
  source,
  isExpanded,
  onToggle,
}: {
  source: SearchResult;
  isExpanded: boolean;
  onToggle: () => void;
}) {
  const getSourceIcon = (type: string) => {
    switch (type) {
      case 'case_law':
        return <Scale className="w-4 h-4" />;
      case 'statute':
      case 'regulation':
        return <FileText className="w-4 h-4" />;
      case 'news':
        return <Newspaper className="w-4 h-4" />;
      default:
        return <BookOpen className="w-4 h-4" />;
    }
  };

  const getSourceTypeLabel = (type: string) => {
    switch (type) {
      case 'case_law':
        return 'Case Law';
      case 'statute':
        return 'Statute';
      case 'regulation':
        return 'Regulation';
      case 'news':
        return 'News';
      default:
        return 'Document';
    }
  };

  return (
    <div className="border rounded-lg p-3 hover:bg-muted/50 transition-colors">
      <div className="flex items-start justify-between">
        <div className="flex-1 min-w-0">
          <div className="flex items-center space-x-2 mb-1">
            {getSourceIcon(source.type)}
            <Badge variant="outline" className="text-xs">
              {getSourceTypeLabel(source.type)}
            </Badge>
            <Badge variant="secondary" className="text-xs">
              {source.jurisdiction}
            </Badge>
          </div>
          
          <h5 className="font-medium text-sm mb-1 line-clamp-2">
            {source.title}
          </h5>
          
          <p className="text-xs text-muted-foreground mb-2">
            {source.citation}
          </p>

          {isExpanded && (
            <div className="space-y-2">
              <p className="text-sm text-muted-foreground">
                {source.snippet}
              </p>
              
              <div className="flex items-center space-x-4 text-xs text-muted-foreground">
                <span>Relevance: {Math.round(source.relevanceScore * 100)}%</span>
                <span>Authority: {Math.round(source.authorityScore * 100)}%</span>
                {source.date && <span>{source.date}</span>}
              </div>
            </div>
          )}
        </div>

        <div className="flex items-center space-x-2 ml-2">
          <Button
            variant="ghost"
            size="sm"
            onClick={onToggle}
            className="h-6 w-6 p-0"
          >
            {isExpanded ? (
              <ChevronUp className="w-3 h-3" />
            ) : (
              <ChevronDown className="w-3 h-3" />
            )}
          </Button>
          
          <Button
            variant="ghost"
            size="sm"
            asChild
            className="h-6 w-6 p-0"
          >
            <a
              href={source.url}
              target="_blank"
              rel="noopener noreferrer"
            >
              <ExternalLink className="w-3 h-3" />
            </a>
          </Button>
        </div>
      </div>
    </div>
  );
}

function SynthesisSection({ synthesis }: { synthesis: AISynthesis }) {
  return (
    <Card>
      <CardContent className="p-4 space-y-4">
        <div className="flex items-center space-x-2">
          <Bot className="w-4 h-4 text-muted-foreground" />
          <h4 className="font-medium text-sm">AI Analysis</h4>
          <Badge variant="outline" className="text-xs">
            {Math.round(synthesis.confidenceScore * 100)}% confidence
          </Badge>
        </div>

        <div className="prose prose-sm max-w-none">
          <p className="text-sm leading-relaxed">{synthesis.summary}</p>
        </div>

        {synthesis.keyFindings.length > 0 && (
          <div>
            <h5 className="font-medium text-sm mb-2 flex items-center">
              <Lightbulb className="w-3 h-3 mr-1" />
              Key Findings
            </h5>
            <ul className="space-y-1">
              {synthesis.keyFindings.map((finding, index) => (
                <li key={index} className="text-sm text-muted-foreground flex items-start">
                  <span className="w-1 h-1 bg-current rounded-full mt-2 mr-2 flex-shrink-0" />
                  {finding}
                </li>
              ))}
            </ul>
          </div>
        )}

        {synthesis.practiceImplications.length > 0 && (
          <div>
            <h5 className="font-medium text-sm mb-2 flex items-center">
              <TrendingUp className="w-3 h-3 mr-1" />
              Practice Implications
            </h5>
            <ul className="space-y-1">
              {synthesis.practiceImplications.map((implication, index) => (
                <li key={index} className="text-sm text-muted-foreground flex items-start">
                  <span className="w-1 h-1 bg-current rounded-full mt-2 mr-2 flex-shrink-0" />
                  {implication}
                </li>
              ))}
            </ul>
          </div>
        )}

        {synthesis.connectionToPrevious && (
          <div className="bg-muted/50 rounded-lg p-3">
            <p className="text-sm text-muted-foreground">
              <strong>Context:</strong> {synthesis.connectionToPrevious}
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

function FollowUpSection({
  suggestions,
  onFollowUpClick,
}: {
  suggestions: string[];
  onFollowUpClick?: (question: string) => void;
}) {
  return (
    <Card>
      <CardContent className="p-4">
        <h4 className="font-medium text-sm mb-3 flex items-center">
          <Lightbulb className="w-4 h-4 mr-2 text-muted-foreground" />
          Follow-up Questions
        </h4>
        <div className="space-y-2">
          {suggestions.map((suggestion, index) => (
            <Button
              key={index}
              variant="outline"
              size="sm"
              className="h-auto p-3 text-left justify-start whitespace-normal"
              onClick={() => onFollowUpClick?.(suggestion)}
            >
              <span className="text-sm">{suggestion}</span>
            </Button>
          ))}
        </div>
      </CardContent>
    </Card>
  );
}
