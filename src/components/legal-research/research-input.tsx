"use client";

import React, { useState, useRef, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { Textarea } from "@/components/ui/textarea";
import { Badge } from "@/components/ui/badge";
import { Switch } from "@/components/ui/switch";
import { Label } from "@/components/ui/label";
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover";
import {
  ArrowUp,
  Loader2,
  Settings,
  CreditCard,
  Sparkles,
  Search,
} from "lucide-react";
import { cn } from "@/lib/utils";
import { useResearchContext } from "@/lib/legal-research/research-context";
import { useSubscription } from "@/lib/subscription/subscription-context";
import type { ResearchOptions } from "@/lib/types/legal-research";
import { RESEARCH_FEATURE_COSTS } from "@/lib/types/legal-research";

interface ResearchInputProps {
  placeholder?: string;
  disabled?: boolean;
}

export function ResearchInput({ 
  placeholder = "Ask a legal research question...",
  disabled = false 
}: ResearchInputProps) {
  const [query, setQuery] = useState("");
  const [options, setOptions] = useState<ResearchOptions>({
    includeSynthesis: true,
    maxSources: 10,
    synthesisStyle: 'comprehensive',
  });
  const [showOptions, setShowOptions] = useState(false);
  
  const textareaRef = useRef<HTMLTextAreaElement>(null);
  
  const { 
    sendResearchQuery, 
    isLoading, 
    isSearching, 
    isSynthesizing,
    currentSession,
    checkCreditsForQuery 
  } = useResearchContext();
  
  const { creditBalance, subscription } = useSubscription();

  // Auto-resize textarea
  const adjustTextareaHeight = useCallback(() => {
    const textarea = textareaRef.current;
    if (textarea) {
      textarea.style.height = "auto";
      textarea.style.height = `${Math.min(textarea.scrollHeight, 200)}px`;
    }
  }, []);

  useEffect(() => {
    adjustTextareaHeight();
  }, [query, adjustTextareaHeight]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (!query.trim() || isLoading || !currentSession) return;

    try {
      await sendResearchQuery(query.trim(), options);
      setQuery("");
    } catch (error) {
      console.error("Failed to send research query:", error);
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent<HTMLTextAreaElement>) => {
    if (e.key === "Enter" && !e.shiftKey) {
      e.preventDefault();
      handleSubmit(e);
    }
  };

  const getRequiredCredits = () => {
    return options.includeSynthesis 
      ? RESEARCH_FEATURE_COSTS.legal_research_synthesis.credits
      : RESEARCH_FEATURE_COSTS.legal_research_basic.credits;
  };

  const hasInsufficientCredits = () => {
    if (!creditBalance) return false;
    return creditBalance.balance < getRequiredCredits();
  };

  const canUseSynthesis = () => {
    if (!subscription) return false;
    // Check if user's tier supports AI synthesis
    return subscription.tier !== 'law_student';
  };

  return (
    <div className="border-t border-border bg-background p-4">
      <div className="max-w-4xl mx-auto">
        {/* Credit and Status Info */}
        <div className="flex items-center justify-between mb-3 text-xs text-muted-foreground">
          <div className="flex items-center space-x-4">
            {creditBalance && (
              <div className="flex items-center space-x-1">
                <CreditCard className="w-3 h-3" />
                <span>Credits: {creditBalance.balance}</span>
              </div>
            )}
            
            <div className="flex items-center space-x-1">
              <span>Cost: {getRequiredCredits()} credit{getRequiredCredits() > 1 ? 's' : ''}</span>
              {options.includeSynthesis && (
                <Badge variant="secondary" className="text-xs">
                  <Sparkles className="w-2 h-2 mr-1" />
                  AI Analysis
                </Badge>
              )}
            </div>
          </div>

          <div className="flex items-center space-x-2">
            {(isSearching || isSynthesizing) && (
              <div className="flex items-center space-x-1">
                <Loader2 className="w-3 h-3 animate-spin" />
                <span>
                  {isSearching && !isSynthesizing && "Searching..."}
                  {isSynthesizing && "Analyzing..."}
                </span>
              </div>
            )}
            
            <Popover open={showOptions} onOpenChange={setShowOptions}>
              <PopoverTrigger asChild>
                <Button variant="ghost" size="sm" className="h-6 px-2">
                  <Settings className="w-3 h-3" />
                </Button>
              </PopoverTrigger>
              <PopoverContent className="w-80" align="end">
                <ResearchOptions 
                  options={options} 
                  onOptionsChange={setOptions}
                  canUseSynthesis={canUseSynthesis()}
                />
              </PopoverContent>
            </Popover>
          </div>
        </div>

        {/* Input Form */}
        <form onSubmit={handleSubmit} className="relative">
          <div className="relative bg-background dark:bg-[#1e1e1e] rounded-lg border border-border dark:border-neutral-700 overflow-hidden">
            <Textarea
              ref={textareaRef}
              value={query}
              onChange={(e) => {
                setQuery(e.target.value);
                adjustTextareaHeight();
              }}
              onKeyDown={handleKeyDown}
              placeholder={placeholder}
              className="min-h-[56px] max-h-[200px] resize-none border-0 bg-transparent text-foreground placeholder:text-muted-foreground focus-visible:ring-0 py-4 px-4 pr-16 text-sm"
              disabled={disabled || isLoading || !currentSession}
            />
            
            <div className="absolute right-2 bottom-2 flex items-center space-x-1">
              <Button
                type="submit"
                disabled={
                  !query.trim() || 
                  isLoading || 
                  !currentSession || 
                  hasInsufficientCredits()
                }
                variant="ghost"
                size="icon"
                className={cn(
                  "h-8 w-8 rounded-md",
                  hasInsufficientCredits() 
                    ? "text-destructive hover:text-destructive" 
                    : "text-muted-foreground hover:text-foreground",
                  "hover:bg-muted dark:hover:bg-neutral-700"
                )}
              >
                {isLoading ? (
                  <Loader2 className="h-4 w-4 animate-spin" />
                ) : (
                  <ArrowUp className="h-4 w-4" />
                )}
                <span className="sr-only">Send research query</span>
              </Button>
            </div>
          </div>
        </form>

        {/* Warning Messages */}
        {hasInsufficientCredits() && (
          <div className="mt-2 text-xs text-destructive">
            Insufficient credits. You need {getRequiredCredits()} credits for this query.
          </div>
        )}

        {!currentSession && (
          <div className="mt-2 text-xs text-muted-foreground">
            Create a research session to start asking questions.
          </div>
        )}

        <div className="mt-2 text-xs text-muted-foreground text-center">
          Press Enter to send, Shift+Enter for new line
        </div>
      </div>
    </div>
  );
}

interface ResearchOptionsProps {
  options: ResearchOptions;
  onOptionsChange: (options: ResearchOptions) => void;
  canUseSynthesis: boolean;
}

function ResearchOptions({ options, onOptionsChange, canUseSynthesis }: ResearchOptionsProps) {
  const updateOption = <K extends keyof ResearchOptions>(
    key: K,
    value: ResearchOptions[K]
  ) => {
    onOptionsChange({ ...options, [key]: value });
  };

  return (
    <div className="space-y-4">
      <div className="space-y-3">
        <h4 className="font-medium text-sm">Research Options</h4>

        <div className="flex items-center justify-between">
          <div className="space-y-0.5">
            <Label className="text-sm">AI Analysis</Label>
            <p className="text-xs text-muted-foreground">
              Get comprehensive AI synthesis (+2 credits)
            </p>
          </div>
          <Switch
            checked={options.includeSynthesis && canUseSynthesis}
            onCheckedChange={(checked) => updateOption('includeSynthesis', checked)}
            disabled={!canUseSynthesis}
          />
        </div>

        {!canUseSynthesis && (
          <p className="text-xs text-muted-foreground bg-muted p-2 rounded">
            AI synthesis requires Lawyer tier or higher
          </p>
        )}

        <div className="space-y-2">
          <Label className="text-sm">Max Sources</Label>
          <div className="flex space-x-2">
            {[5, 10, 15, 25].map((num) => (
              <Button
                key={num}
                variant={options.maxSources === num ? "default" : "outline"}
                size="sm"
                onClick={() => updateOption('maxSources', num)}
                className="h-8 px-3 text-xs"
              >
                {num}
              </Button>
            ))}
          </div>
        </div>

        {options.includeSynthesis && (
          <div className="space-y-2">
            <Label className="text-sm">Analysis Style</Label>
            <div className="flex space-x-2">
              {[
                { value: 'brief', label: 'Brief' },
                { value: 'comprehensive', label: 'Detailed' },
                { value: 'analytical', label: 'Analytical' },
              ].map((style) => (
                <Button
                  key={style.value}
                  variant={options.synthesisStyle === style.value ? "default" : "outline"}
                  size="sm"
                  onClick={() => updateOption('synthesisStyle', style.value as any)}
                  className="h-8 px-3 text-xs"
                >
                  {style.label}
                </Button>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
}
