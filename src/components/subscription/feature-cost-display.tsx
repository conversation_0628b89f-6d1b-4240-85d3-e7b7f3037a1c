"use client";

import { useSubscription } from "@/lib/subscription/subscription-context";
import { Badge } from "@/components/ui/badge";
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from "@/components/ui/tooltip";
import { Coins, Info, AlertTriangle } from "lucide-react";
import { FeatureCost } from "@/lib/types/subscription";

interface FeatureCostDisplayProps {
  featureName: string;
  showDescription?: boolean;
  showCategory?: boolean;
  variant?: "default" | "inline" | "tooltip" | "warning";
  className?: string;
}

export function FeatureCostDisplay({
  featureName,
  showDescription = false,
  showCategory = false,
  variant = "default",
  className = ""
}: FeatureCostDisplayProps) {
  const { getFeatureCost, creditBalance } = useSubscription();
  
  const featureCost = getFeatureCost(featureName);
  
  if (!featureCost) {
    return null;
  }

  const hasInsufficientCredits = creditBalance && creditBalance.current < featureCost.credits;

  const getCategoryColor = (category: FeatureCost['category']) => {
    switch (category) {
      case 'free':
        return 'bg-gray-100 text-gray-800 border-gray-200';
      case 'basic':
        return 'bg-green-100 text-green-800 border-green-200';
      case 'advanced':
        return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'premium':
        return 'bg-purple-100 text-purple-800 border-purple-200';
      default:
        return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getCategoryLabel = (category: FeatureCost['category']) => {
    switch (category) {
      case 'free':
        return 'Free';
      case 'basic':
        return 'Basic AI (1 Credit)';
      case 'advanced':
        return 'Advanced AI (2 Credits)';
      case 'premium':
        return 'Premium AI (3 Credits)';
      default:
        return 'Unknown';
    }
  };

  // Inline variant - just the cost
  if (variant === "inline") {
    return (
      <span className={`inline-flex items-center gap-1 text-sm ${className}`}>
        <Coins className="h-3 w-3" />
        {featureCost.credits}
      </span>
    );
  }

  // Tooltip variant
  if (variant === "tooltip") {
    return (
      <TooltipProvider>
        <Tooltip>
          <TooltipTrigger asChild>
            <Badge 
              variant="outline" 
              className={`cursor-help ${hasInsufficientCredits ? 'border-red-200 text-red-700' : ''} ${className}`}
            >
              <Coins className="h-3 w-3 mr-1" />
              {featureCost.credits}
              {hasInsufficientCredits && <AlertTriangle className="h-3 w-3 ml-1" />}
            </Badge>
          </TooltipTrigger>
          <TooltipContent>
            <div className="space-y-1">
              <p className="font-medium">{featureCost.description}</p>
              <p className="text-xs">
                Category: {getCategoryLabel(featureCost.category)}
              </p>
              {hasInsufficientCredits && (
                <p className="text-xs text-red-400">
                  Insufficient credits (need {featureCost.credits}, have {creditBalance?.current || 0})
                </p>
              )}
            </div>
          </TooltipContent>
        </Tooltip>
      </TooltipProvider>
    );
  }

  // Warning variant - for insufficient credits
  if (variant === "warning" && hasInsufficientCredits) {
    return (
      <div className={`flex items-center gap-2 p-2 bg-red-50 border border-red-200 rounded-md ${className}`}>
        <AlertTriangle className="h-4 w-4 text-red-500" />
        <div className="text-sm">
          <p className="text-red-700 font-medium">
            Insufficient credits for {featureCost.description}
          </p>
          <p className="text-red-600">
            Need {featureCost.credits} credits, have {creditBalance?.current || 0}
          </p>
        </div>
      </div>
    );
  }

  // Default variant - full display
  return (
    <div className={`space-y-2 ${className}`}>
      <div className="flex items-center gap-2">
        <Badge 
          variant="outline"
          className={hasInsufficientCredits ? 'border-red-200 text-red-700' : ''}
        >
          <Coins className="h-3 w-3 mr-1" />
          {featureCost.credits} credits
        </Badge>
        
        {showCategory && (
          <Badge 
            variant="outline"
            className={getCategoryColor(featureCost.category)}
          >
            {getCategoryLabel(featureCost.category)}
          </Badge>
        )}
        
        {hasInsufficientCredits && (
          <Badge variant="destructive">
            <AlertTriangle className="h-3 w-3 mr-1" />
            Insufficient
          </Badge>
        )}
      </div>
      
      {showDescription && (
        <p className="text-sm text-muted-foreground">
          {featureCost.description}
        </p>
      )}
      
      {hasInsufficientCredits && (
        <p className="text-xs text-red-600">
          You need {featureCost.credits - (creditBalance?.current || 0)} more credits to use this feature.
        </p>
      )}
    </div>
  );
}

// Component to show feature costs in a list/grid
export function FeatureCostList({ 
  features, 
  title = "Feature Costs",
  showDescriptions = true 
}: { 
  features: string[];
  title?: string;
  showDescriptions?: boolean;
}) {
  const { getFeatureCost } = useSubscription();

  const featureCosts = features
    .map(feature => getFeatureCost(feature))
    .filter((cost): cost is FeatureCost => cost !== null)
    .sort((a, b) => a.credits - b.credits);

  if (featureCosts.length === 0) {
    return null;
  }

  return (
    <div className="space-y-4">
      <h3 className="text-lg font-semibold flex items-center gap-2">
        <Info className="h-5 w-5" />
        {title}
      </h3>
      
      <div className="space-y-3">
        {featureCosts.map((cost) => (
          <div 
            key={cost.featureName}
            className="flex items-center justify-between p-3 border rounded-lg"
          >
            <div className="space-y-1">
              <p className="font-medium">{cost.description}</p>
              {showDescriptions && (
                <p className="text-sm text-muted-foreground">
                  Category: {cost.category.replace('_', ' ')}
                </p>
              )}
            </div>
            
            <FeatureCostDisplay 
              featureName={cost.featureName}
              variant="tooltip"
            />
          </div>
        ))}
      </div>
    </div>
  );
}

// Simple credit requirement indicator
export function CreditRequirement({ 
  featureName, 
  showIcon = true 
}: { 
  featureName: string;
  showIcon?: boolean;
}) {
  const { getFeatureCost } = useSubscription();
  const cost = getFeatureCost(featureName);

  if (!cost) return null;

  return (
    <span className="inline-flex items-center gap-1 text-xs text-muted-foreground">
      {showIcon && <Coins className="h-3 w-3" />}
      {cost.credits} credits required
    </span>
  );
}
