"use client";

import { useState } from "react";
import { useRouter } from "next/navigation";
import { Subscription, SUBSCRIPTION_PLANS } from "@/lib/types/subscription";
import { useSubscription } from "@/lib/subscription/subscription-context";
import {
	Card,
	CardContent,
	CardDescription,
	CardFooter,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
	AlertDialogTrigger,
} from "@/components/ui/alert-dialog";
import { format } from "date-fns";
import {
	CreditCardIcon,
	AlertTriangleIcon,
	CheckCircleIcon,
	XCircleIcon,
	ClockIcon,
	Coins,
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { CreditBalance } from "./credit-balance";
import { TIER_CREDIT_ALLOCATIONS } from "@/lib/types/subscription";

interface SubscriptionDetailsProps {
	subscription: Subscription | null;
	onNavigateToCredits?: () => void;
}

export function SubscriptionDetails({
	subscription,
	onNavigateToCredits,
}: SubscriptionDetailsProps) {
	const [isLoading, setIsLoading] = useState(false);
	const [isDowngrading, setIsDowngrading] = useState(false);
	const {
		cancelSubscription,
		getBillingPortalUrl,
		isInTrial,
		getRemainingTrialDays,
		downgradeToFreeTier,
	} = useSubscription();
	const { toast } = useToast();
	const router = useRouter();

	const handlePurchaseCredits = () => {
		if (onNavigateToCredits) {
			onNavigateToCredits();
		} else {
			// Fallback to URL navigation if no prop provided
			router.push('/subscription?tab=credits');
		}
	};

	if (!subscription) {
		return null;
	}

	const plan = SUBSCRIPTION_PLANS.find((p) => p.id === subscription.tier);
	const inTrial = isInTrial();
	const trialDays = getRemainingTrialDays();

	const handleCancelSubscription = async () => {
		try {
			setIsLoading(true);
			await cancelSubscription();
			toast({
				title: "Subscription canceled",
				description:
					"Your subscription has been canceled and will end at the current billing period.",
			});
		} catch (error) {
			console.error("Error canceling subscription:", error);
			toast({
				variant: "destructive",
				title: "Cancellation failed",
				description: "Failed to cancel subscription. Please try again.",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const handleDowngradeToFree = async () => {
		try {
			setIsDowngrading(true);
			await downgradeToFreeTier();
			toast({
				title: "Subscription downgraded",
				description: "Your subscription has been downgraded to the free tier.",
			});
			// Refresh the page to show updated subscription details
			router.refresh();
		} catch (error) {
			console.error("Error downgrading subscription:", error);
			toast({
				variant: "destructive",
				title: "Downgrade failed",
				description: "Failed to downgrade subscription. Please try again.",
			});
		} finally {
			setIsDowngrading(false);
		}
	};

	const handleManageBilling = async () => {
		try {
			setIsLoading(true);
			const url = await getBillingPortalUrl();
			window.location.href = url;
		} catch (error) {
			console.error("Error getting billing portal URL:", error);
			toast({
				variant: "destructive",
				title: "Error",
				description: "Failed to access billing portal. Please try again.",
			});
		} finally {
			setIsLoading(false);
		}
	};

	const formatDate = (dateString: string) => {
		return format(new Date(dateString), "MMMM d, yyyy");
	};

	const getStatusBadge = () => {
		switch (subscription.status) {
			case "active":
				return (
					<Badge className="bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300">
						Active
					</Badge>
				);
			case "canceled":
				return (
					<Badge className="bg-amber-100 text-amber-800 dark:bg-amber-900/30 dark:text-amber-300">
						Canceled
					</Badge>
				);
			case "past_due":
				return (
					<Badge className="bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300">
						Past Due
					</Badge>
				);
			default:
				return <Badge variant="outline">Unknown</Badge>;
		}
	};

	return (
		<div className="space-y-6">
			{/* Credit Balance Card */}
			<CreditBalance onPurchaseCredits={handlePurchaseCredits} />

			<Card>
				<CardHeader>
					<div className="flex items-center justify-between">
						<div>
							<CardTitle>Current Subscription</CardTitle>
							<CardDescription>
								Your subscription details and billing information
							</CardDescription>
						</div>
						{getStatusBadge()}
					</div>
				</CardHeader>

				<CardContent className="space-y-6">
					{inTrial && (
						<div className="bg-amber-50 dark:bg-amber-950/30 border border-amber-200 dark:border-amber-800 rounded-lg p-4">
							<div className="flex items-start">
								<AlertTriangleIcon className="h-5 w-5 text-amber-500 mt-0.5" />
								<div className="ml-3">
									<h3 className="text-sm font-medium text-amber-800 dark:text-amber-300">
										Pro Trial Active
									</h3>
									<div className="mt-1 text-sm text-amber-700 dark:text-amber-400">
										<p>
											You are currently on a Pro trial that will expire on{" "}
											{formatDate(subscription.trialEndDate!)}. Upgrade now to
											keep access to all Pro features.
										</p>
									</div>
								</div>
							</div>
						</div>
					)}

					<div className="grid grid-cols-1 md:grid-cols-2 gap-6">
						<div>
							<h3 className="text-sm font-medium mb-2">Plan Details</h3>
							<dl className="space-y-2">
								<div className="flex justify-between">
									<dt className="text-sm text-muted-foreground">Plan</dt>
									<dd className="text-sm font-medium">
										{plan?.name || subscription.tier}
									</dd>
								</div>
								<div className="flex justify-between">
									<dt className="text-sm text-muted-foreground">Price</dt>
									<dd className="text-sm font-medium">
										${plan?.price || 0}/month
									</dd>
								</div>
								<div className="flex justify-between">
									<dt className="text-sm text-muted-foreground">Status</dt>
									<dd className="text-sm font-medium flex items-center">
										{subscription.status === "active" ? (
											<>
												<CheckCircleIcon className="h-4 w-4 text-green-500 mr-1" />
												Active
											</>
										) : subscription.status === "canceled" ? (
											<>
												<XCircleIcon className="h-4 w-4 text-amber-500 mr-1" />
												Canceled
											</>
										) : (
											<>
												<AlertTriangleIcon className="h-4 w-4 text-red-500 mr-1" />
												{subscription.status.charAt(0).toUpperCase() +
													subscription.status.slice(1).replace("_", " ")}
											</>
										)}
									</dd>
								</div>
								<div className="flex justify-between">
									<dt className="text-sm text-muted-foreground">
										Document Limit
									</dt>
									<dd className="text-sm font-medium">
										{plan?.limits.documentLimit || 0}
									</dd>
								</div>
								<div className="flex justify-between">
									<dt className="text-sm text-muted-foreground">
										Analysis Limit
									</dt>
									<dd className="text-sm font-medium">
										Unlimited (within credit allocation)
									</dd>
								</div>
								<div className="flex justify-between">
									<dt className="text-sm text-muted-foreground">
										Monthly Credits
									</dt>
									<dd className="text-sm font-medium flex items-center">
										<Coins className="h-3 w-3 mr-1" />
										{TIER_CREDIT_ALLOCATIONS[subscription.tier] || 0}
									</dd>
								</div>
							</dl>
						</div>

						<div>
							<h3 className="text-sm font-medium mb-2">Billing Information</h3>
							<dl className="space-y-2">
								<div className="flex justify-between">
									<dt className="text-sm text-muted-foreground">
										Current Period
									</dt>
									<dd className="text-sm font-medium">
										{formatDate(subscription.currentPeriodStart)} -{" "}
										{formatDate(subscription.currentPeriodEnd)}
									</dd>
								</div>
								{subscription.cancelAtPeriodEnd && (
									<div className="flex justify-between">
										<dt className="text-sm text-muted-foreground">
											Cancellation
										</dt>
										<dd className="text-sm font-medium flex items-center">
											<ClockIcon className="h-4 w-4 text-amber-500 mr-1" />
											Ends on {formatDate(subscription.currentPeriodEnd)}
										</dd>
									</div>
								)}
								{inTrial && (
									<div className="flex justify-between">
										<dt className="text-sm text-muted-foreground">Trial End</dt>
										<dd className="text-sm font-medium">
											{formatDate(subscription.trialEndDate!)} ({trialDays} days
											left)
										</dd>
									</div>
								)}
							</dl>
						</div>
					</div>
				</CardContent>

				<CardFooter className="flex flex-col sm:flex-row gap-3 pt-6">
					<Button
						variant="outline"
						className="w-full sm:w-auto"
						onClick={handleManageBilling}
						disabled={
							isLoading || isDowngrading || subscription.tier === "law_student"
						}
					>
						{isLoading ? (
							<Spinner className="mr-2" size="sm" />
						) : (
							<CreditCardIcon className="h-4 w-4 mr-2" />
						)}
						Manage Billing
					</Button>

					{subscription.tier !== "law_student" && !subscription.cancelAtPeriodEnd && (
						<>
							<AlertDialog>
								<AlertDialogTrigger asChild>
									<Button
										variant="outline"
										className="w-full sm:w-auto text-red-500 hover:text-red-600 border-red-200 hover:border-red-300 dark:border-red-800 dark:hover:border-red-700"
										disabled={isLoading || isDowngrading}
									>
										Cancel Subscription
									</Button>
								</AlertDialogTrigger>
								<AlertDialogContent>
									<AlertDialogHeader>
										<AlertDialogTitle>Cancel Subscription</AlertDialogTitle>
										<AlertDialogDescription>
											Are you sure you want to cancel your subscription? You
											will lose access to premium features at the end of your
											current billing period on{" "}
											{formatDate(subscription.currentPeriodEnd)}.
										</AlertDialogDescription>
									</AlertDialogHeader>
									<AlertDialogFooter>
										<AlertDialogCancel>Keep Subscription</AlertDialogCancel>
										<AlertDialogAction
											onClick={handleCancelSubscription}
											className="bg-red-500 hover:bg-red-600 text-white"
										>
											{isLoading ? (
												<Spinner className="mr-2" size="sm" />
											) : null}
											Cancel Subscription
										</AlertDialogAction>
									</AlertDialogFooter>
								</AlertDialogContent>
							</AlertDialog>

							<AlertDialog>
								<AlertDialogTrigger asChild>
									<Button
										variant="outline"
										className="w-full sm:w-auto text-amber-500 hover:text-amber-600 border-amber-200 hover:border-amber-300 dark:border-amber-800 dark:hover:border-amber-700"
										disabled={isLoading || isDowngrading}
									>
										Downgrade to Free
									</Button>
								</AlertDialogTrigger>
								<AlertDialogContent>
									<AlertDialogHeader>
										<AlertDialogTitle>Downgrade to Free Tier</AlertDialogTitle>
										<AlertDialogDescription>
											Are you sure you want to downgrade to the free tier? This
											will take effect immediately and you will lose access to
											premium features.
										</AlertDialogDescription>
									</AlertDialogHeader>
									<AlertDialogFooter>
										<AlertDialogCancel>Cancel</AlertDialogCancel>
										<AlertDialogAction
											onClick={handleDowngradeToFree}
											className="bg-amber-500 hover:bg-amber-600 text-white"
										>
											{isDowngrading ? (
												<Spinner className="mr-2" size="sm" />
											) : null}
											Downgrade Now
										</AlertDialogAction>
									</AlertDialogFooter>
								</AlertDialogContent>
							</AlertDialog>
						</>
					)}

					<Button
						className="w-full sm:w-auto sm:ml-auto"
						onClick={() => router.push("/subscription?tab=plans")}
						disabled={isLoading || isDowngrading}
					>
						{subscription.tier === "law_student" ? "Upgrade Plan" : "Change Plan"}
					</Button>
				</CardFooter>
			</Card>
		</div>
	);
}
