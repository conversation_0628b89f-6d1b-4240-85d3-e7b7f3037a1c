"use client";

import { useState } from "react";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON>ton } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { Spinner } from "@/components/ui/spinner";
import { 
  CreditCard, 
  Coins, 
  TrendingUp, 
  Calendar,
  ShoppingCart,
  RefreshCw
} from "lucide-react";
import { TIER_CREDIT_ALLOCATIONS } from "@/lib/types/subscription";
import { formatDate } from "@/lib/utils";

interface CreditBalanceProps {
  onPurchaseCredits?: () => void;
  showPurchaseButton?: boolean;
}

export function CreditBalance({ 
  onPurchaseCredits, 
  showPurchaseButton = true 
}: CreditBalanceProps) {
  const { 
    subscription, 
    creditBalance, 
    isLoading, 
    refreshCreditBalance 
  } = useSubscription();
  
  const [isRefreshing, setIsRefreshing] = useState(false);

  const handleRefresh = async () => {
    setIsRefreshing(true);
    try {
      await refreshCreditBalance();
    } catch (error) {
      console.error("Error refreshing credit balance:", error);
    } finally {
      setIsRefreshing(false);
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center p-6">
          <Spinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  if (!subscription || !creditBalance || typeof creditBalance.current !== 'number') {
    return (
      <Card>
        <CardContent className="p-6">
          <p className="text-muted-foreground text-center">
            Credit information not available
          </p>
        </CardContent>
      </Card>
    );
  }

  const monthlyAllocation = TIER_CREDIT_ALLOCATIONS[subscription.tier] || 0;
  const totalSpent = creditBalance.totalSpent || 0;
  const totalEarned = creditBalance.totalEarned || 0;
  const current = creditBalance.current || 0;

  const usagePercentage = monthlyAllocation > 0
    ? Math.min((totalSpent / monthlyAllocation) * 100, 100)
    : 0;

  const isLowBalance = current < (monthlyAllocation * 0.2); // Less than 20% of monthly allocation

  return (
    <Card className={isLowBalance ? "border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20" : ""}>
      <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
        <CardTitle className="text-sm font-medium flex items-center gap-2">
          <Coins className="h-4 w-4" />
          Credit Balance
        </CardTitle>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleRefresh}
          disabled={isRefreshing}
        >
          <RefreshCw className={`h-4 w-4 ${isRefreshing ? "animate-spin" : ""}`} />
        </Button>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* Current Balance */}
        <div className="flex items-center justify-between">
          <div>
            <p className="text-2xl font-bold flex items-center gap-2">
              {current.toLocaleString()}
              {isLowBalance && (
                <Badge variant="outline" className="text-orange-600 border-orange-600 dark:text-orange-400 dark:border-orange-400">
                  Low
                </Badge>
              )}
            </p>
            <p className="text-xs text-muted-foreground">Available Credits</p>
          </div>
          
          {showPurchaseButton && (
            <Button 
              onClick={onPurchaseCredits}
              size="sm"
              className="flex items-center gap-2"
            >
              <ShoppingCart className="h-4 w-4" />
              Buy Credits
            </Button>
          )}
        </div>

        {/* Monthly Allocation Progress */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span className="text-muted-foreground">Monthly Usage</span>
            <span className="font-medium">
              {totalSpent} / {monthlyAllocation}
            </span>
          </div>
          <Progress value={usagePercentage} className="h-2" />
        </div>

        {/* Credit Stats Grid */}
        <div className="grid grid-cols-2 gap-4 pt-2">
          <div className="space-y-1">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <TrendingUp className="h-3 w-3" />
              Total Earned
            </div>
            <p className="text-lg font-semibold">
              {totalEarned.toLocaleString()}
            </p>
          </div>
          
          <div className="space-y-1">
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Calendar className="h-3 w-3" />
              Monthly Allocation
            </div>
            <p className="text-lg font-semibold">
              {monthlyAllocation.toLocaleString()}
            </p>
          </div>
        </div>

        {/* Last Allocation */}
        {creditBalance.lastAllocation && (
          <div className="pt-2 border-t">
            <p className="text-xs text-muted-foreground">
              Last allocation: {formatDate(creditBalance.lastAllocation)}
            </p>
          </div>
        )}

        {/* Low Balance Warning */}
        {isLowBalance && (
          <div className="p-3 bg-orange-100 border border-orange-200 rounded-md dark:bg-orange-950/20 dark:border-orange-800">
            <p className="text-sm text-orange-800 dark:text-orange-200">
              <strong>Low credit balance!</strong> Consider purchasing more credits to continue using premium features.
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Compact version for dashboard widgets
export function CreditBalanceCompact() {
  const { creditBalance, subscription } = useSubscription();

  if (!creditBalance || !subscription || typeof creditBalance.current !== 'number') {
    return null;
  }

  const monthlyAllocation = TIER_CREDIT_ALLOCATIONS[subscription.tier] || 0;
  const current = creditBalance.current || 0;
  const isLowBalance = current < (monthlyAllocation * 0.2);

  return (
    <div className="flex items-center gap-3 p-3 bg-card rounded-lg border">
      <div className="flex items-center gap-2">
        <Coins className="h-4 w-4 text-muted-foreground" />
        <span className="font-medium">{current.toLocaleString()}</span>
        <span className="text-sm text-muted-foreground">credits</span>
      </div>
      
      {isLowBalance && (
        <Badge variant="outline" className="text-orange-600 border-orange-600 dark:text-orange-400 dark:border-orange-400">
          Low
        </Badge>
      )}
    </div>
  );
}
