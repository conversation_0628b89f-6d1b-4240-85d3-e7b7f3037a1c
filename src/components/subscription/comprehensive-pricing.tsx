"use client";

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Ta<PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
  Co<PERSON>,
  Star,
  Zap,
  Crown,
  Gift,
  TrendingUp,
  Calculator,
  Info
} from "lucide-react";
import { FEATURE_COSTS, CREDIT_PACKAGES, TIER_CREDIT_ALLOCATIONS } from "@/lib/types/subscription";
import { CreditPackageGrid } from "./unified-credit-package-card";

export function ComprehensivePricing() {
  // Organize features by category
  const freeFeatures = Object.values(FEATURE_COSTS).filter(f => f.category === 'free');
  const basicFeatures = Object.values(FEATURE_COSTS).filter(f => f.category === 'basic');
  const advancedFeatures = Object.values(FEATURE_COSTS).filter(f => f.category === 'advanced');
  const premiumFeatures = Object.values(FEATURE_COSTS).filter(f => f.category === 'premium');

  const FeatureCard = ({ feature, icon }: { feature: typeof FEATURE_COSTS[string], icon: React.ReactNode }) => (
    <div className="flex items-center justify-between p-3 border rounded-lg hover:bg-muted/50 transition-colors">
      <div className="flex items-center gap-3">
        {icon}
        <div>
          <p className="font-medium">{feature.description}</p>
          <p className="text-sm text-muted-foreground">
            {feature.featureName.replace(/_/g, ' ')}
          </p>
        </div>
      </div>
      <Badge variant={feature.credits === 0 ? "secondary" : "outline"} className="gap-1">
        <Coins className="h-3 w-3" />
        {feature.credits} {feature.credits === 1 ? 'credit' : 'credits'}
      </Badge>
    </div>
  );



  return (
    <div className="space-y-8">
      <div className="text-center space-y-4">
        <h1 className="text-4xl font-bold">💰 Complete Feature Pricing Guide</h1>
        <p className="text-xl text-muted-foreground max-w-3xl mx-auto">
          Transparent pricing for all features. Credits are only consumed for AI-powered features - 
          all basic operations are completely free!
        </p>
      </div>

      <Tabs defaultValue="features" className="space-y-6">
        <TabsList className="grid grid-cols-4 w-full max-w-2xl mx-auto">
          <TabsTrigger value="features">Features</TabsTrigger>
          <TabsTrigger value="packages">Packages</TabsTrigger>
          <TabsTrigger value="tiers">Tiers</TabsTrigger>
          <TabsTrigger value="examples">Examples</TabsTrigger>
        </TabsList>

        <TabsContent value="features" className="space-y-8">
          <div className="grid gap-8">
            {/* Free Features */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-2xl">
                  <Gift className="h-6 w-6 text-green-600" />
                  🆓 FREE FEATURES (0 Credits)
                </CardTitle>
                <p className="text-muted-foreground">
                  Basic CRUD operations and essential functionality - {freeFeatures.length} features
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3 md:grid-cols-2">
                  {freeFeatures.map((feature) => (
                    <FeatureCard 
                      key={feature.featureName} 
                      feature={feature} 
                      icon={<Gift className="h-4 w-4 text-green-600" />} 
                    />
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Basic AI Features */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-2xl">
                  <Zap className="h-6 w-6 text-blue-600" />
                  💡 BASIC AI FEATURES (1 Credit)
                </CardTitle>
                <p className="text-muted-foreground">
                  Essential AI-powered functionality - {basicFeatures.length} features
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3 md:grid-cols-2">
                  {basicFeatures.map((feature) => (
                    <FeatureCard 
                      key={feature.featureName} 
                      feature={feature} 
                      icon={<Zap className="h-4 w-4 text-blue-600" />} 
                    />
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Advanced AI Features */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-2xl">
                  <TrendingUp className="h-6 w-6 text-purple-600" />
                  🚀 ADVANCED AI FEATURES (2 Credits)
                </CardTitle>
                <p className="text-muted-foreground">
                  Sophisticated AI analysis and automation - {advancedFeatures.length} features
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3 md:grid-cols-2">
                  {advancedFeatures.map((feature) => (
                    <FeatureCard 
                      key={feature.featureName} 
                      feature={feature} 
                      icon={<TrendingUp className="h-4 w-4 text-purple-600" />} 
                    />
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* Premium AI Features */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2 text-2xl">
                  <Crown className="h-6 w-6 text-orange-600" />
                  💎 PREMIUM AI FEATURES (3 Credits)
                </CardTitle>
                <p className="text-muted-foreground">
                  Most advanced AI-powered capabilities - {premiumFeatures.length} features
                </p>
              </CardHeader>
              <CardContent>
                <div className="grid gap-3 md:grid-cols-2">
                  {premiumFeatures.map((feature) => (
                    <FeatureCard 
                      key={feature.featureName} 
                      feature={feature} 
                      icon={<Crown className="h-4 w-4 text-orange-600" />} 
                    />
                  ))}
                </div>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="packages" className="space-y-8">
          <div className="text-center space-y-4">
            <h2 className="text-3xl font-bold">💳 Credit Packages for Purchase</h2>
            <p className="text-muted-foreground">
              Purchase additional credits with bonus rewards for larger packages
            </p>
          </div>
          
          <CreditPackageGrid
            packages={CREDIT_PACKAGES}
            showPurchaseButtons={false}
          />
        </TabsContent>

        <TabsContent value="tiers" className="space-y-8">
          <div className="text-center space-y-4">
            <h2 className="text-3xl font-bold">📈 Monthly Credit Allocations by Tier</h2>
            <p className="text-muted-foreground">
              Each subscription tier includes monthly credit allocations
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-3">
            <Card>
              <CardHeader>
                <CardTitle className="text-center">🎓 Law Student Tier</CardTitle>
                <div className="text-center text-3xl font-bold text-green-600">FREE</div>
              </CardHeader>
              <CardContent className="space-y-3 text-center">
                <div className="flex items-center justify-center gap-2">
                  <Coins className="h-4 w-4" />
                  <span>{TIER_CREDIT_ALLOCATIONS.law_student} Monthly Credits</span>
                </div>
                <p className="text-sm text-muted-foreground">Document Limit: 10</p>
                <p className="text-sm text-muted-foreground">Analysis Limit: 50/month</p>
              </CardContent>
            </Card>

            <Card className="border-primary">
              <CardHeader>
                <CardTitle className="text-center">⚖️ Lawyer Tier</CardTitle>
                <div className="text-center text-3xl font-bold text-blue-600">$29.99</div>
                <div className="text-center text-sm text-muted-foreground">/month</div>
              </CardHeader>
              <CardContent className="space-y-3 text-center">
                <div className="flex items-center justify-center gap-2">
                  <Coins className="h-4 w-4" />
                  <span>{TIER_CREDIT_ALLOCATIONS.lawyer} Monthly Credits</span>
                </div>
                <p className="text-sm text-muted-foreground">Document Limit: 200</p>
                <p className="text-sm text-muted-foreground">Analysis Limit: Unlimited</p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="text-center">🏢 Law Firm Tier</CardTitle>
                <div className="text-center text-3xl font-bold text-purple-600">$99.99</div>
                <div className="text-center text-sm text-muted-foreground">/month</div>
              </CardHeader>
              <CardContent className="space-y-3 text-center">
                <div className="flex items-center justify-center gap-2">
                  <Coins className="h-4 w-4" />
                  <span>{TIER_CREDIT_ALLOCATIONS.law_firm} Monthly Credits</span>
                </div>
                <p className="text-sm text-muted-foreground">Document Limit: Unlimited</p>
                <p className="text-sm text-muted-foreground">Analysis Limit: Unlimited</p>
              </CardContent>
            </Card>
          </div>
        </TabsContent>

        <TabsContent value="examples" className="space-y-8">
          <div className="text-center space-y-4">
            <h2 className="text-3xl font-bold">🎯 Cost Examples for Common Workflows</h2>
            <p className="text-muted-foreground">
              See how credits are used in typical legal workflows
            </p>
          </div>
          
          <div className="grid gap-6 md:grid-cols-2">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Basic Document Review Workflow
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Document Upload</span>
                  <Badge variant="secondary">0 credits ✅</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Basic Analysis</span>
                  <Badge variant="outline">1 credit</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Chat Questions</span>
                  <Badge variant="outline">1 credit</Badge>
                </div>
                <div className="border-t pt-3 flex justify-between font-semibold">
                  <span>Total</span>
                  <Badge>2 credits</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Advanced Legal Research Workflow
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Document Upload</span>
                  <Badge variant="secondary">0 credits ✅</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Advanced Analysis</span>
                  <Badge variant="outline">2 credits</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Precedent Analysis</span>
                  <Badge variant="outline">2 credits</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Citation Analysis</span>
                  <Badge variant="outline">2 credits</Badge>
                </div>
                <div className="border-t pt-3 flex justify-between font-semibold">
                  <span>Total</span>
                  <Badge>6 credits</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Comprehensive Litigation Prep
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Document Upload</span>
                  <Badge variant="secondary">0 credits ✅</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Privilege Detection</span>
                  <Badge variant="outline">3 credits</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Deposition Analysis</span>
                  <Badge variant="outline">3 credits</Badge>
                </div>
                <div className="flex justify-between">
                  <span>AI Question Generation</span>
                  <Badge variant="outline">3 credits</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Litigation Support</span>
                  <Badge variant="outline">3 credits</Badge>
                </div>
                <div className="border-t pt-3 flex justify-between font-semibold">
                  <span>Total</span>
                  <Badge>12 credits</Badge>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Calculator className="h-5 w-5" />
                  Contract Analysis & Negotiation
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div className="flex justify-between">
                  <span>Document Upload</span>
                  <Badge variant="secondary">0 credits ✅</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Contract Risk Scoring</span>
                  <Badge variant="outline">3 credits</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Deviation Detection</span>
                  <Badge variant="outline">2 credits</Badge>
                </div>
                <div className="flex justify-between">
                  <span>Negotiation Simulator</span>
                  <Badge variant="outline">3 credits</Badge>
                </div>
                <div className="border-t pt-3 flex justify-between font-semibold">
                  <span>Total</span>
                  <Badge>8 credits</Badge>
                </div>
              </CardContent>
            </Card>
          </div>

          <Card className="bg-muted/50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Info className="h-5 w-5" />
                💰 Pricing Philosophy
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="grid gap-4 md:grid-cols-3">
                <div>
                  <h4 className="font-semibold text-green-600">Free Operations</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• All basic CRUD operations</li>
                    <li>• Document uploads and organization</li>
                    <li>• Basic system functionality</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-blue-600">1-2 Credit Features</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Basic to advanced AI analysis</li>
                    <li>• Simple to complex AI operations</li>
                    <li>• Professional-grade AI tools</li>
                  </ul>
                </div>
                <div>
                  <h4 className="font-semibold text-purple-600">3 Credit Features</h4>
                  <ul className="text-sm text-muted-foreground space-y-1">
                    <li>• Premium AI capabilities</li>
                    <li>• Specialized legal AI tools</li>
                    <li>• Advanced automation and simulation</li>
                  </ul>
                </div>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
