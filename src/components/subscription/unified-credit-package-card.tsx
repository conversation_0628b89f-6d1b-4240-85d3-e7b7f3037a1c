"use client";

import { useState } from "react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import { 
  Coins, 
  Star,
  Gift,
  CreditCard
} from "lucide-react";
import { CreditPackage } from "@/lib/types/subscription";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { useToast } from "@/hooks/use-toast";

interface UnifiedCreditPackageCardProps {
  pkg: CreditPackage;
  showPurchaseButton?: boolean;
  onPurchaseComplete?: () => void;
  className?: string;
}

export function UnifiedCreditPackageCard({ 
  pkg, 
  showPurchaseButton = false,
  onPurchaseComplete,
  className = ""
}: UnifiedCreditPackageCardProps) {
  const { createCreditCheckoutSession } = useSubscription();
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  // Create safe fallbacks for all numeric values
  const credits = pkg.credits || 0;
  const bonus = pkg.bonus || 0;
  const total = pkg.total || credits + bonus;
  const price = (pkg.price || 0) / 100; // Convert from cents to dollars

  const handlePurchase = async () => {
    if (!showPurchaseButton) return;

    try {
      setIsLoading(true);
      const checkoutUrl = await createCreditCheckoutSession(pkg.id);

      // Redirect to Stripe checkout
      window.location.href = checkoutUrl;

      // Call completion callback if provided
      onPurchaseComplete?.();
    } catch (error) {
      console.error("Error creating checkout session:", error);
      toast({
        title: "Error",
        description: "Failed to start checkout process",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Card 
      className={`relative transition-all hover:shadow-md ${
        pkg.popular
          ? 'border-primary bg-primary/5 ring-2 ring-primary/20'
          : 'border-border hover:border-primary/50'
      } ${className}`}
    >
      {pkg.popular && (
        <div className="absolute -top-2 left-1/2 transform -translate-x-1/2">
          <Badge className="bg-primary text-primary-foreground">
            <Star className="h-3 w-3 mr-1" />
            Popular
          </Badge>
        </div>
      )}

      <CardHeader className="text-center pb-4">
        <CardTitle className="text-lg">{pkg.name || 'Credit Package'}</CardTitle>
        <div className="text-3xl font-bold">${price.toFixed(2)}</div>
        <p className="text-sm text-muted-foreground">
          {total > 0 ? `$${(price / total).toFixed(3)} per credit` : 'Free'}
        </p>
      </CardHeader>

      <CardContent className="space-y-4">
        {/* Credit Breakdown */}
        <div className="text-center space-y-3">
          <div className="flex items-center justify-center gap-2">
            <Coins className="h-4 w-4 text-muted-foreground" />
            <span className="font-medium">{credits.toLocaleString()} Credits</span>
          </div>

          {bonus > 0 && (
            <div className="flex items-center justify-center gap-2 text-green-600">
              <Gift className="h-4 w-4" />
              <span className="font-medium">
                +{bonus.toLocaleString()} Bonus ({Math.round((bonus / credits) * 100)}%)
              </span>
            </div>
          )}

          <div className="text-lg font-semibold text-primary">
            Total: {total.toLocaleString()} Credits
          </div>
        </div>

        {/* Detailed Breakdown for Purchase View */}
        {showPurchaseButton && (
          <div className="space-y-2 pt-2 border-t">
            <div className="flex justify-between text-sm">
              <span className="text-muted-foreground">Base Credits:</span>
              <span>{credits.toLocaleString()}</span>
            </div>

            {bonus > 0 && (
              <div className="flex justify-between text-sm">
                <span className="text-muted-foreground">Bonus:</span>
                <span className="text-green-600 font-medium">
                  +{bonus.toLocaleString()}
                </span>
              </div>
            )}

            <div className="flex justify-between text-sm font-medium pt-2 border-t">
              <span>Total:</span>
              <span>{total.toLocaleString()} credits</span>
            </div>
          </div>
        )}

        {/* Purchase Button */}
        {showPurchaseButton && (
          <Button
            onClick={handlePurchase}
            disabled={isLoading}
            className="w-full"
            variant={pkg.popular ? "default" : "outline"}
          >
            {isLoading ? (
              <>
                <Spinner size="sm" className="mr-2" />
                Processing...
              </>
            ) : (
              <>
                <CreditCard className="h-4 w-4 mr-2" />
                Purchase
              </>
            )}
          </Button>
        )}

        {/* Package Description */}
        {pkg.description && (
          <p className="text-xs text-muted-foreground text-center">
            {pkg.description}
          </p>
        )}
      </CardContent>
    </Card>
  );
}

// Grid container for multiple packages
interface CreditPackageGridProps {
  packages: CreditPackage[];
  showPurchaseButtons?: boolean;
  onPurchaseComplete?: () => void;
  className?: string;
}

export function CreditPackageGrid({ 
  packages, 
  showPurchaseButtons = false,
  onPurchaseComplete,
  className = ""
}: CreditPackageGridProps) {
  return (
    <div className={`grid gap-6 md:grid-cols-2 lg:grid-cols-4 ${className}`}>
      {packages.map((pkg) => (
        <UnifiedCreditPackageCard
          key={pkg.id}
          pkg={pkg}
          showPurchaseButton={showPurchaseButtons}
          onPurchaseComplete={onPurchaseComplete}
        />
      ))}
    </div>
  );
}
