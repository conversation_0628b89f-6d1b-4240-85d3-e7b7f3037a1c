"use client";

import { useState, useEffect } from "react";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { But<PERSON> } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  History, 
  Plus, 
  Minus, 
  RotateCcw, 
  ShoppingCart,
  Calendar,
  RefreshCw
} from "lucide-react";
import { CreditTransaction } from "@/lib/types/subscription";
import { formatDate, formatDateTime } from "@/lib/utils";

interface CreditHistoryProps {
  limit?: number;
  showHeader?: boolean;
  className?: string;
}

export function CreditHistory({ 
  limit = 50, 
  showHeader = true,
  className = ""
}: CreditHistoryProps) {
  const { getCreditHistory } = useSubscription();
  const [transactions, setTransactions] = useState<CreditTransaction[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchHistory = async () => {
    try {
      setIsLoading(true);
      setError(null);
      const history = await getCreditHistory();
      setTransactions(history.slice(0, limit));
    } catch (err) {
      console.error("Error fetching credit history:", err);
      setError("Failed to load credit history");
    } finally {
      setIsLoading(false);
    }
  };

  useEffect(() => {
    fetchHistory();
  }, [limit]);

  const getTransactionIcon = (type: CreditTransaction['type']) => {
    switch (type) {
      case 'allocation':
        return <Calendar className="h-4 w-4 text-blue-500" />;
      case 'purchase':
        return <ShoppingCart className="h-4 w-4 text-green-500" />;
      case 'usage':
        return <Minus className="h-4 w-4 text-orange-500" />;
      case 'refund':
        return <Plus className="h-4 w-4 text-green-500" />;
      case 'expiration':
        return <RotateCcw className="h-4 w-4 text-red-500" />;
      default:
        return <History className="h-4 w-4 text-gray-500" />;
    }
  };

  const getTransactionBadgeVariant = (type: CreditTransaction['type']) => {
    switch (type) {
      case 'allocation':
        return 'default';
      case 'purchase':
        return 'default';
      case 'usage':
        return 'secondary';
      case 'refund':
        return 'default';
      case 'expiration':
        return 'destructive';
      default:
        return 'outline';
    }
  };

  const getAmountDisplay = (transaction: CreditTransaction) => {
    const isPositive = ['allocation', 'purchase', 'refund'].includes(transaction.type);
    const sign = isPositive ? '+' : '-';
    const amount = Math.abs(transaction.amount);
    
    return (
      <span className={isPositive ? 'text-green-600' : 'text-red-600'}>
        {sign}{amount.toLocaleString()}
      </span>
    );
  };

  if (isLoading) {
    return (
      <Card className={className}>
        {showHeader && (
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              Credit History
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="flex items-center justify-center p-6">
          <Spinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card className={className}>
        {showHeader && (
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <History className="h-5 w-5" />
              Credit History
            </CardTitle>
          </CardHeader>
        )}
        <CardContent className="p-6">
          <div className="text-center space-y-2">
            <p className="text-muted-foreground">{error}</p>
            <Button onClick={fetchHistory} variant="outline" size="sm">
              <RefreshCw className="h-4 w-4 mr-2" />
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      {showHeader && (
        <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
          <CardTitle className="flex items-center gap-2">
            <History className="h-5 w-5" />
            Credit History
          </CardTitle>
          <Button onClick={fetchHistory} variant="ghost" size="sm">
            <RefreshCw className="h-4 w-4" />
          </Button>
        </CardHeader>
      )}
      
      <CardContent className="p-0">
        {transactions.length === 0 ? (
          <div className="p-6 text-center text-muted-foreground">
            No credit transactions found
          </div>
        ) : (
          <ScrollArea className="h-[400px]">
            <div className="space-y-1 p-4">
              {transactions.map((transaction, index) => (
                <div
                  key={`${transaction.timestamp}-${index}`}
                  className="flex items-center justify-between p-3 rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    {getTransactionIcon(transaction.type)}
                    <div className="space-y-1">
                      <div className="flex items-center gap-2">
                        <p className="text-sm font-medium">
                          {transaction.description}
                        </p>
                        <Badge 
                          variant={getTransactionBadgeVariant(transaction.type)}
                          className="text-xs"
                        >
                          {transaction.type}
                        </Badge>
                      </div>
                      
                      <div className="flex items-center gap-2 text-xs text-muted-foreground">
                        <span>{formatDateTime(transaction.timestamp)}</span>
                        {transaction.featureName && (
                          <>
                            <span>•</span>
                            <span>{transaction.featureName}</span>
                          </>
                        )}
                        {transaction.transactionId && (
                          <>
                            <span>•</span>
                            <span className="font-mono">
                              {transaction.transactionId.slice(-8)}
                            </span>
                          </>
                        )}
                      </div>
                    </div>
                  </div>
                  
                  <div className="text-right space-y-1">
                    <div className="text-sm font-medium">
                      {getAmountDisplay(transaction)}
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Balance: {transaction.balance.toLocaleString()}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </ScrollArea>
        )}
      </CardContent>
    </Card>
  );
}

// Compact version for smaller spaces
export function CreditHistoryCompact({ limit = 5 }: { limit?: number }) {
  return (
    <CreditHistory 
      limit={limit} 
      showHeader={false} 
      className="border-0 shadow-none"
    />
  );
}
