"use client";

import { useState, useEffect } from "react";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Spinner } from "@/components/ui/spinner";
import {
  ShoppingCart,
  Check
} from "lucide-react";
import { CreditPackage } from "@/lib/types/subscription";
import { useToast } from "@/hooks/use-toast";
import { CreditPackageGrid } from "./unified-credit-package-card";

interface CreditPurchaseProps {
  onPurchaseComplete?: () => void;
  className?: string;
}

export function CreditPurchase({ 
  onPurchaseComplete,
  className = ""
}: CreditPurchaseProps) {
  const { getCreditPackages } = useSubscription();
  const [packages, setPackages] = useState<CreditPackage[]>([]);
  const [isLoading, setIsLoading] = useState(true);
  const { toast } = useToast();

  useEffect(() => {
    const fetchPackages = async () => {
      try {
        setIsLoading(true);
        const packageData = await getCreditPackages();
        setPackages(packageData);
      } catch (error) {
        console.error("Error fetching credit packages:", error);
        toast({
          title: "Error",
          description: "Failed to load credit packages",
          variant: "destructive",
        });
      } finally {
        setIsLoading(false);
      }
    };

    fetchPackages();
  }, [getCreditPackages, toast]);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <ShoppingCart className="h-5 w-5" />
            Purchase Credits
          </CardTitle>
        </CardHeader>
        <CardContent className="flex items-center justify-center p-6">
          <Spinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  return (
    <Card className={className}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <ShoppingCart className="h-5 w-5" />
          Purchase Credits
        </CardTitle>
        <p className="text-sm text-muted-foreground">
          Choose a credit package to continue using premium features
        </p>
      </CardHeader>
      
      <CardContent className="space-y-4">
        <CreditPackageGrid
          packages={packages}
          showPurchaseButtons={true}
          onPurchaseComplete={onPurchaseComplete}
        />

        <div className="mt-6 p-4 bg-muted/50 rounded-lg">
          <h4 className="font-medium mb-2 flex items-center gap-2">
            <Check className="h-4 w-4 text-green-500" />
            What you get:
          </h4>
          <ul className="text-sm text-muted-foreground space-y-1">
            <li>• Credits never expire</li>
            <li>• Use credits for any premium feature</li>
            <li>• Instant activation after purchase</li>
            <li>• Secure payment processing via Stripe</li>
          </ul>
        </div>
      </CardContent>
    </Card>
  );
}

// Simplified purchase button for inline use
export function CreditPurchaseButton({ 
  packageId, 
  children,
  variant = "default",
  size = "default",
  className = ""
}: {
  packageId: string;
  children: React.ReactNode;
  variant?: "default" | "outline" | "secondary" | "ghost" | "link" | "destructive";
  size?: "default" | "sm" | "lg" | "icon";
  className?: string;
}) {
  const { createCreditCheckoutSession } = useSubscription();
  const [isLoading, setIsLoading] = useState(false);
  const { toast } = useToast();

  const handlePurchase = async () => {
    try {
      setIsLoading(true);
      const checkoutUrl = await createCreditCheckoutSession(packageId);
      window.location.href = checkoutUrl;
    } catch (error) {
      console.error("Error creating checkout session:", error);
      toast({
        title: "Error",
        description: "Failed to start checkout process",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  return (
    <Button
      onClick={handlePurchase}
      disabled={isLoading}
      variant={variant}
      size={size}
      className={className}
    >
      {isLoading ? <Spinner size="sm" className="mr-2" /> : null}
      {children}
    </Button>
  );
}
