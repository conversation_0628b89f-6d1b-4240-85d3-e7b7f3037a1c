"use client";

import React, { useState, useEffect } from "react";
import { useSubscription } from "@/lib/subscription/subscription-context";

import { But<PERSON> } from "@/components/ui/button";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Spinner } from "@/components/ui/spinner";
import { LockIcon, Coins, AlertTriangle, CreditCard, Zap } from "lucide-react";
import Link from "next/link";
import { FeatureCostDisplay } from "./feature-cost-display";
import { CreditPurchaseButton } from "./credit-purchase";

interface FeatureGuardProps {
	featureId: string;
	children: React.ReactNode;
	fallback?: React.ReactNode;
	showUpgradePrompt?: boolean;
	showCreditPurchase?: boolean;
	className?: string;
}

export function FeatureGuard({
	featureId,
	children,
	fallback,
	showUpgradePrompt = true,
	showCreditPurchase = true,
	className = "",
}: FeatureGuardProps) {
	const {
		hasFeature,
		isInTrial,
		getRemainingTrialDays,
		creditBalance,
		getFeatureCost,
		checkCreditsForFeature,
	} = useSubscription();

	const [hasCredits, setHasCredits] = useState<boolean | null>(null);
	const [isCheckingCredits, setIsCheckingCredits] = useState(true);

	const hasAccess = hasFeature(featureId);
	const inTrial = isInTrial();
	const trialDays = getRemainingTrialDays();
	const featureCost = getFeatureCost(featureId);

	useEffect(() => {
		const checkCredits = async () => {
			if (!hasAccess || !featureCost) {
				setIsCheckingCredits(false);
				return;
			}

			try {
				const result = await checkCreditsForFeature(featureId);
				setHasCredits(result);
			} catch (error) {
				console.error("Error checking credits:", error);
				// Fallback to local check
				setHasCredits(
					creditBalance ? creditBalance.current >= featureCost.credits : false
				);
			} finally {
				setIsCheckingCredits(false);
			}
		};

		checkCredits();
	}, [
		featureId,
		hasAccess,
		featureCost,
		creditBalance,
		checkCreditsForFeature,
	]);

	// Show loading state while checking credits
	if (isCheckingCredits) {
		return (
			<div className={`flex items-center justify-center p-4 ${className}`}>
				<Spinner size="sm" className="mr-2" />
				<span className="text-sm text-muted-foreground">
					Checking access...
				</span>
			</div>
		);
	}

	// If user doesn't have feature access at all
	if (!hasAccess) {
		if (fallback) {
			return <>{fallback}</>;
		}

		const defaultFallback = (
			<Card className={`border-amber-200 bg-amber-50 ${className}`}>
				<CardHeader className="pb-3">
					<CardTitle className="flex items-center gap-2 text-amber-800">
						<LockIcon className="h-5 w-5" />
						Feature Not Available
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<p className="text-sm text-amber-700">
						This feature is not available in your current subscription plan.
						{inTrial && (
							<span className="block mt-2">
								Your Pro trial ends in{" "}
								<span className="font-medium">{trialDays} days</span>.
							</span>
						)}
					</p>

					{showUpgradePrompt && (
						<div className="flex flex-col sm:flex-row gap-2">
							<Link href="/subscription?tab=plans">
								<Button className="flex items-center gap-2">
									<Zap className="h-4 w-4" />
									{inTrial ? "Upgrade Now" : "View Plans"}
								</Button>
							</Link>
						</div>
					)}
				</CardContent>
			</Card>
		);

		return <>{defaultFallback}</>;
	}

	// If user has feature access but insufficient credits
	if (hasAccess && hasCredits === false) {
		if (fallback) {
			return <>{fallback}</>;
		}

		return (
			<Card className={`border-red-200 bg-red-50 ${className}`}>
				<CardHeader className="pb-3">
					<CardTitle className="flex items-center gap-2 text-red-800">
						<AlertTriangle className="h-5 w-5" />
						Insufficient Credits
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					<div className="space-y-2">
						<p className="text-sm text-red-700">
							You don&apos;t have enough credits to use this feature.
						</p>

						{featureCost && (
							<FeatureCostDisplay featureName={featureId} variant="warning" />
						)}
					</div>

					{showCreditPurchase && (
						<div className="flex flex-col sm:flex-row gap-2">
							<CreditPurchaseButton
								packageId="lawyer_small"
								variant="default"
								className="flex items-center gap-2"
							>
								<CreditCard className="h-4 w-4" />
								Buy Credits
							</CreditPurchaseButton>

							<Link href="/subscription?tab=credits">
								<Button variant="outline" className="flex items-center gap-2">
									<Coins className="h-4 w-4" />
									View Packages
								</Button>
							</Link>
						</div>
					)}
				</CardContent>
			</Card>
		);
	}

	// User has access and sufficient credits
	return <>{children}</>;
}
