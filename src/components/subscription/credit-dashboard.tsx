"use client";

import { useState } from "react";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import { Spinner } from "@/components/ui/spinner";
import { 
  Coins, 
  TrendingUp, 
  ShoppingCart, 
  History,
  AlertTriangle,
  Calendar,
  Zap
} from "lucide-react";
import { CreditBalance } from "./credit-balance";
import { CreditHistory } from "./credit-history";
import { CreditPurchase } from "./credit-purchase";
import { FeatureCostList } from "./feature-cost-display";
import { TIER_CREDIT_ALLOCATIONS, FEATURE_COSTS } from "@/lib/types/subscription";

interface CreditDashboardProps {
  className?: string;
  defaultTab?: "purchase" | "history" | "features";
}

export function CreditDashboard({
  className = "",
  defaultTab = "purchase"
}: CreditDashboardProps) {
  const { subscription, creditBalance, isLoading } = useSubscription();
  const [activeTab, setActiveTab] = useState(defaultTab);

  if (isLoading) {
    return (
      <Card className={className}>
        <CardContent className="flex items-center justify-center p-8">
          <Spinner size="lg" />
        </CardContent>
      </Card>
    );
  }

  if (!subscription) {
    return (
      <Card className={className}>
        <CardContent className="p-8 text-center">
          <p className="text-muted-foreground">
            Subscription information not available
          </p>
        </CardContent>
      </Card>
    );
  }

  const monthlyAllocation = creditBalance?.monthlyAllocation || TIER_CREDIT_ALLOCATIONS[subscription.tier] || 0;
  const currentBalance = creditBalance?.balance || creditBalance?.current || 0;
  const totalEarned = creditBalance?.totalEarned || 0;
  const totalSpent = creditBalance?.totalSpent || 0;
  const isLowBalance = currentBalance < (monthlyAllocation * 0.2);

  return (
    <div className={`space-y-6 ${className}`}>
      {/* Header with quick stats */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Current Balance</p>
                <p className="text-2xl font-bold flex items-center gap-2">
                  <Coins className="h-5 w-5 text-blue-500" />
                  {currentBalance.toLocaleString()}
                  {isLowBalance && (
                    <Badge variant="outline" className="text-orange-600 border-orange-600 dark:text-orange-400 dark:border-orange-400">
                      Low
                    </Badge>
                  )}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Monthly Allocation</p>
                <p className="text-2xl font-bold flex items-center gap-2">
                  <Calendar className="h-5 w-5 text-green-500" />
                  {monthlyAllocation.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <p className="text-sm text-muted-foreground">Total Earned</p>
                <p className="text-2xl font-bold flex items-center gap-2">
                  <TrendingUp className="h-5 w-5 text-purple-500" />
                  {totalEarned.toLocaleString()}
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Low balance warning */}
      {isLowBalance && (
        <Card className="border-orange-200 bg-orange-50 dark:border-orange-800 dark:bg-orange-950/20">
          <CardContent className="p-4">
            <div className="flex items-center gap-3">
              <AlertTriangle className="h-5 w-5 text-orange-500 dark:text-orange-400" />
              <div className="flex-1">
                <p className="font-medium text-orange-800 dark:text-orange-200">Low Credit Balance</p>
                <p className="text-sm text-orange-700 dark:text-orange-300">
                  Consider purchasing more credits to continue using premium features.
                </p>
              </div>
              <Button
                onClick={() => setActiveTab("purchase")}
                size="sm"
                className="bg-orange-600 hover:bg-orange-700 dark:bg-orange-600 dark:hover:bg-orange-700"
              >
                <ShoppingCart className="h-4 w-4 mr-2" />
                Buy Credits
              </Button>
            </div>
          </CardContent>
        </Card>
      )}

      {/* Main content tabs */}
      <Tabs value={activeTab} onValueChange={setActiveTab} className="space-y-4">
        <TabsList className="grid grid-cols-3 w-full max-w-2xl">
          <TabsTrigger value="purchase">Purchase</TabsTrigger>
          <TabsTrigger value="history">History</TabsTrigger>
          <TabsTrigger value="features">Features</TabsTrigger>
        </TabsList>

        <TabsContent value="purchase" className="space-y-6">
          <CreditPurchase onPurchaseComplete={() => setActiveTab("history")} />
        </TabsContent>

        <TabsContent value="history" className="space-y-6">
          <CreditHistory />
        </TabsContent>

        <TabsContent value="features" className="space-y-6">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
            <FeatureCostList
              features={Object.keys(FEATURE_COSTS).filter(f => FEATURE_COSTS[f].category === 'free')}
              title="🆓 Free Features (0 Credits)"
            />
            <FeatureCostList
              features={Object.keys(FEATURE_COSTS).filter(f => FEATURE_COSTS[f].category === 'basic')}
              title="💡 Basic AI Features (1 Credit)"
            />
            <FeatureCostList
              features={Object.keys(FEATURE_COSTS).filter(f => FEATURE_COSTS[f].category === 'advanced')}
              title="🚀 Advanced AI Features (2 Credits)"
            />
            <FeatureCostList
              features={Object.keys(FEATURE_COSTS).filter(f => FEATURE_COSTS[f].category === 'premium')}
              title="💎 Premium AI Features (3 Credits)"
            />
          </div>
        </TabsContent>
      </Tabs>
    </div>
  );
}

// Compact version for sidebar or smaller spaces
export function CreditDashboardCompact() {
  const { subscription, creditBalance } = useSubscription();

  if (!subscription || !creditBalance) {
    return null;
  }

  const monthlyAllocation = creditBalance?.monthlyAllocation || TIER_CREDIT_ALLOCATIONS[subscription.tier] || 0;
  const currentBalance = creditBalance?.balance || creditBalance?.current || 0;
  const totalSpent = creditBalance?.totalSpent || 0;
  const isLowBalance = currentBalance < (monthlyAllocation * 0.2);
  const usagePercentage = monthlyAllocation > 0
    ? Math.min((totalSpent / monthlyAllocation) * 100, 100)
    : 0;

  return (
    <Card className="w-full">
      <CardHeader className="pb-2">
        <CardTitle className="text-sm flex items-center gap-2">
          <Coins className="h-4 w-4" />
          Credits
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-3">
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Balance</span>
          <div className="flex items-center gap-2">
            <span className="font-medium">{currentBalance.toLocaleString()}</span>
            {isLowBalance && (
              <Badge variant="outline" className="text-xs text-orange-600 border-orange-600 dark:text-orange-400 dark:border-orange-400">
                Low
              </Badge>
            )}
          </div>
        </div>
        
        <div className="flex items-center justify-between">
          <span className="text-sm text-muted-foreground">Monthly</span>
          <span className="text-sm font-medium">{monthlyAllocation.toLocaleString()}</span>
        </div>

        <div className="space-y-1">
          <div className="flex justify-between text-xs">
            <span className="text-muted-foreground">Usage</span>
            <span>{Math.round(usagePercentage)}%</span>
          </div>
          <div className="w-full bg-muted rounded-full h-1.5">
            <div 
              className="bg-primary h-1.5 rounded-full transition-all"
              style={{ width: `${Math.min(usagePercentage, 100)}%` }}
            />
          </div>
        </div>

        <Button 
          size="sm" 
          variant="outline" 
          className="w-full"
          onClick={() => window.location.href = '/subscription?tab=credits'}
        >
          <ShoppingCart className="h-3 w-3 mr-2" />
          Manage Credits
        </Button>
      </CardContent>
    </Card>
  );
}
