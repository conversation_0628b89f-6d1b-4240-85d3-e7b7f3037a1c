"use client";

import Link from "next/link";
import { But<PERSON> } from "@/components/ui/button";
import { useAuth } from "@/lib/auth/auth-context";
import { ArrowRight, MessageSquare, Play } from "lucide-react";

export function HeroButtons() {
  const { isAuthenticated, isLoading } = useAuth();

  // Show loading state
  if (isLoading) {
    return (
      <div className="flex flex-col sm:flex-row gap-3">
        <div className="h-12 w-40 bg-muted animate-pulse rounded-lg" />
        <div className="h-12 w-32 bg-muted animate-pulse rounded-lg" />
      </div>
    );
  }

  // Show authenticated state - direct access to chat
  if (isAuthenticated) {
    return (
      <div className="flex flex-col sm:flex-row gap-3">
        <Button size="lg" className="gap-1.5" asChild>
          <Link href="/chat">
            <MessageSquare className="h-4 w-4" />
            Start Chatting
            <ArrowRight className="h-4 w-4" />
          </Link>
        </Button>
        <Button variant="outline" size="lg" asChild>
          <Link href="/dashboard">
            <Play className="h-4 w-4 mr-1.5" />
            Go to Dashboard
          </Link>
        </Button>
      </div>
    );
  }

  // Show unauthenticated state - trial and demo buttons
  return (
    <div className="flex flex-col sm:flex-row gap-3">
      <Button size="lg" className="gap-1.5" asChild>
        <Link href="/register">
          Start Free Trial <ArrowRight className="h-4 w-4" />
        </Link>
      </Button>
      <Button variant="outline" size="lg">
        See Live Demo
      </Button>
    </div>
  );
}
