"use client";

import React, { useState } from 'react';
import { Shield, RefreshCw, AlertTriangle, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { usePrivilegeLog } from '@/hooks/use-privilege-log';
import { useFeatureAccess } from '@/hooks/use-feature-access';
import { AnalysisOptionsForm } from './analysis-options-form';
import { AnalysisSummary } from './analysis-summary';
import { PrivilegeContentList } from './privilege-content-list';
import { FeatureGuard } from '@/components/subscription/feature-guard';
import type { AnalysisOptions } from '@/lib/types/privilege-log';

interface PrivilegeLogAutomationProps {
  documentId: string;
}

export function PrivilegeLogAutomation({ documentId }: PrivilegeLogAutomationProps) {
  const [showAnalysisForm, setShowAnalysisForm] = useState(false);
  const { canAccessFeature } = useFeatureAccess();
  
  const {
    privilegeLog,
    analysisResult,
    loading,
    analyzing,
    error,
    hasExisting,
    analyzeDocument,
    reviewContent,
    applyRedaction,
    applyBulkRedaction,
    refresh,
    clearError,
    clearAnalysisResult
  } = usePrivilegeLog(documentId);

  const hasAccess = canAccessFeature('privilege_log_automation');

  const handleAnalyze = async (options: AnalysisOptions) => {
    await analyzeDocument(options);
    setShowAnalysisForm(false);
  };

  const handleRefresh = () => {
    clearError();
    clearAnalysisResult();
    refresh();
  };

  // Determine what data to display
  const displayData = analysisResult || privilegeLog;
  const privilegedContent = displayData?.privilegedContent || [];
  const summary = analysisResult?.summary || (privilegeLog ? {
    totalItemsFound: privilegeLog.totalPrivilegedItems,
    highConfidenceItems: privilegedContent.filter(item => item.confidenceScore >= 0.8).length,
    requiresManualReview: privilegedContent.filter(item => item.status === 'detected').length,
    autoRedactable: 0
  } : null);

  return (
    <FeatureGuard featureId="privilege_log_automation">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Shield className="h-6 w-6" />
              Privilege Log Automation
            </h2>
            <p className="text-muted-foreground">
              AI-powered detection and management of privileged content
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            {(hasExisting || analysisResult) && (
              <Button
                variant="outline"
                onClick={handleRefresh}
                disabled={loading || analyzing || !hasAccess}
                className="gap-2"
              >
                <RefreshCw className="h-4 w-4" />
                Refresh
              </Button>
            )}
            
            <Button
              onClick={() => setShowAnalysisForm(true)}
              disabled={analyzing || !hasAccess}
              className="gap-2"
            >
              {analyzing ? (
                <>
                  <Loader2 className="h-4 w-4 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Shield className="h-4 w-4" />
                  {hasExisting ? 'Re-analyze' : 'Analyze Document'}
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Error Display */}
        {error && (
          <Alert variant="destructive">
            <AlertTriangle className="h-4 w-4" />
            <AlertDescription className="flex items-center justify-between">
              <span>{error}</span>
              <Button
                variant="outline"
                size="sm"
                onClick={clearError}
                className="ml-4"
              >
                Dismiss
              </Button>
            </AlertDescription>
          </Alert>
        )}

        {/* Loading State */}
        {(loading || analyzing) && !displayData && (
          <Card>
            <CardContent className="flex flex-col items-center justify-center py-12 space-y-4">
              <div className="relative">
                <Shield className="h-12 w-12 text-primary/20" />
                <Loader2 className="h-6 w-6 text-primary animate-spin absolute top-3 left-3" />
              </div>
              <div className="text-center space-y-2">
                <h3 className="text-lg font-medium">
                  {analyzing ? 'Analyzing Document' : 'Loading Privilege Log'}
                </h3>
                <p className="text-sm text-muted-foreground max-w-md">
                  {analyzing 
                    ? 'Scanning document for privileged content using AI and pattern detection...'
                    : 'Retrieving existing privilege log data...'
                  }
                </p>
                {analyzing && (
                  <p className="text-xs text-muted-foreground">
                    This may take 15-30 seconds depending on document complexity.
                  </p>
                )}
              </div>
            </CardContent>
          </Card>
        )}

        {/* Content Display */}
        {displayData && summary && (
          <div className="space-y-6">
            {/* Analysis Summary */}
            <AnalysisSummary 
              summary={summary}
              metadata={analysisResult?.analysisMetadata}
            />
            
            {/* Privileged Content List */}
            <PrivilegeContentList
              privilegedContent={privilegedContent}
              onReview={reviewContent}
              onRedact={applyRedaction}
              onBulkRedact={applyBulkRedaction}
              loading={loading}
            />
          </div>
        )}

        {/* Empty State */}
        {!loading && !analyzing && !displayData && !error && (
          <Card>
            <CardContent className="py-12">
              <div className="text-center space-y-4">
                <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                  <Shield className="h-8 w-8 text-muted-foreground" />
                </div>
                <div className="space-y-2">
                  <h3 className="text-lg font-medium">No Privilege Analysis</h3>
                  <p className="text-sm text-muted-foreground max-w-md mx-auto">
                    This document hasn't been analyzed for privileged content yet. 
                    Start an analysis to detect attorney-client privilege, work product, 
                    and other protected information.
                  </p>
                </div>
                <Button
                  onClick={() => setShowAnalysisForm(true)}
                  disabled={!hasAccess}
                  className="gap-2"
                >
                  <Shield className="h-4 w-4" />
                  Start Analysis
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Analysis Options Form */}
        <AnalysisOptionsForm
          isOpen={showAnalysisForm}
          onClose={() => setShowAnalysisForm(false)}
          onAnalyze={handleAnalyze}
          loading={analyzing}
        />
      </div>
    </FeatureGuard>
  );
}
