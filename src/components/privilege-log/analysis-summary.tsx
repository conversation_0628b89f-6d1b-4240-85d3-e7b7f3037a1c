"use client";

import React from 'react';
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>gle, CheckCircle, Clock, Zap } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import type { AnalysisSummary, AnalysisMetadata } from '@/lib/types/privilege-log';

interface AnalysisSummaryProps {
  summary: AnalysisSummary;
  metadata?: AnalysisMetadata;
}

export function AnalysisSummary({ summary, metadata }: AnalysisSummaryProps) {
  const formatProcessingTime = (ms: number): string => {
    if (ms < 1000) return `${ms}ms`;
    if (ms < 60000) return `${(ms / 1000).toFixed(1)}s`;
    return `${(ms / 60000).toFixed(1)}m`;
  };

  const getStatusColor = (count: number, total: number): string => {
    if (count === 0) return 'text-green-600';
    const ratio = count / total;
    if (ratio > 0.7) return 'text-red-600';
    if (ratio > 0.3) return 'text-yellow-600';
    return 'text-blue-600';
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Shield className="h-5 w-5" />
          Analysis Summary
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-6">
          {/* Total Items Found */}
          <div className="text-center p-3 bg-blue-50 rounded-lg border border-blue-200">
            <div className="flex items-center justify-center mb-2">
              <Shield className="h-5 w-5 text-blue-600" />
            </div>
            <div className="text-2xl font-bold text-blue-600">
              {summary.totalItemsFound}
            </div>
            <div className="text-sm text-blue-700">
              Total Items
            </div>
          </div>

          {/* High Confidence Items */}
          <div className="text-center p-3 bg-green-50 rounded-lg border border-green-200">
            <div className="flex items-center justify-center mb-2">
              <CheckCircle className="h-5 w-5 text-green-600" />
            </div>
            <div className="text-2xl font-bold text-green-600">
              {summary.highConfidenceItems}
            </div>
            <div className="text-sm text-green-700">
              High Confidence
            </div>
          </div>

          {/* Requires Manual Review */}
          <div className="text-center p-3 bg-yellow-50 rounded-lg border border-yellow-200">
            <div className="flex items-center justify-center mb-2">
              <AlertTriangle className="h-5 w-5 text-yellow-600" />
            </div>
            <div className="text-2xl font-bold text-yellow-600">
              {summary.requiresManualReview}
            </div>
            <div className="text-sm text-yellow-700">
              Needs Review
            </div>
          </div>

          {/* Auto Redactable */}
          <div className="text-center p-3 bg-purple-50 rounded-lg border border-purple-200">
            <div className="flex items-center justify-center mb-2">
              <Zap className="h-5 w-5 text-purple-600" />
            </div>
            <div className="text-2xl font-bold text-purple-600">
              {summary.autoRedactable}
            </div>
            <div className="text-sm text-purple-700">
              Auto Redactable
            </div>
          </div>
        </div>

        {/* Analysis Metadata */}
        {metadata && (
          <div className="space-y-4">
            <div className="border-t pt-4">
              <h4 className="text-sm font-medium mb-3">Analysis Details</h4>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 text-sm">
                {/* Analysis Date */}
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">Analyzed:</span>
                  <span>{new Date(metadata.analysisDate).toLocaleString()}</span>
                </div>

                {/* Processing Time */}
                <div className="flex items-center gap-2">
                  <Zap className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">Processing Time:</span>
                  <span>{formatProcessingTime(metadata.processingTime)}</span>
                </div>

                {/* Detection Methods */}
                <div className="flex items-center gap-2">
                  <Shield className="h-4 w-4 text-muted-foreground" />
                  <span className="text-muted-foreground">Methods:</span>
                  <div className="flex gap-1">
                    {metadata.detectionMethods.map((method) => (
                      <Badge key={method} variant="outline" className="text-xs">
                        {method.toUpperCase()}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* AI Model Used */}
                {metadata.aiModelUsed && (
                  <div className="flex items-center gap-2">
                    <CheckCircle className="h-4 w-4 text-muted-foreground" />
                    <span className="text-muted-foreground">AI Model:</span>
                    <Badge variant="secondary" className="text-xs">
                      {metadata.aiModelUsed}
                    </Badge>
                  </div>
                )}
              </div>
            </div>

            {/* Status Indicators */}
            <div className="border-t pt-4">
              <h4 className="text-sm font-medium mb-3">Review Status</h4>
              <div className="space-y-2">
                {summary.totalItemsFound === 0 ? (
                  <div className="flex items-center gap-2 text-green-600">
                    <CheckCircle className="h-4 w-4" />
                    <span className="text-sm">No privileged content detected</span>
                  </div>
                ) : (
                  <>
                    {summary.requiresManualReview > 0 && (
                      <div className="flex items-center gap-2 text-yellow-600">
                        <AlertTriangle className="h-4 w-4" />
                        <span className="text-sm">
                          {summary.requiresManualReview} item(s) require manual review
                        </span>
                      </div>
                    )}
                    
                    {summary.highConfidenceItems > 0 && (
                      <div className="flex items-center gap-2 text-green-600">
                        <CheckCircle className="h-4 w-4" />
                        <span className="text-sm">
                          {summary.highConfidenceItems} high-confidence detection(s)
                        </span>
                      </div>
                    )}

                    {summary.autoRedactable > 0 && (
                      <div className="flex items-center gap-2 text-purple-600">
                        <Zap className="h-4 w-4" />
                        <span className="text-sm">
                          {summary.autoRedactable} item(s) ready for auto-redaction
                        </span>
                      </div>
                    )}
                  </>
                )}
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
