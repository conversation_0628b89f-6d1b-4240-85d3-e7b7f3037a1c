"use client";

import React, { useState } from 'react';
import { 
  Shield, 
  Filter, 
  CheckSquare, 
  Square, 
  Lock, 
  Download,
  AlertTriangle
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { PrivilegeContentItem } from './privilege-content-item';
import type { 
  PrivilegedContent, 
  PrivilegeStatus,
  PrivilegeType 
} from '@/lib/types/privilege-log';
import { PRIVILEGE_TYPE_OPTIONS } from '@/lib/types/privilege-log';

interface PrivilegeContentListProps {
  privilegedContent: PrivilegedContent[];
  onReview: (contentId: string, status: PrivilegeStatus, reason?: string, applyRedaction?: boolean) => Promise<void>;
  onRedact: (contentId: string, reason: string, redactionText?: string) => Promise<void>;
  onBulkRedact: (contentIds: string[], reason: string, redactionText?: string) => Promise<void>;
  loading?: boolean;
}

export function PrivilegeContentList({
  privilegedContent,
  onReview,
  onRedact,
  onBulkRedact,
  loading = false
}: PrivilegeContentListProps) {
  const [selectedItems, setSelectedItems] = useState<string[]>([]);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const [filterType, setFilterType] = useState<string>('all');
  const [showBulkRedactDialog, setShowBulkRedactDialog] = useState(false);
  const [bulkRedactionReason, setBulkRedactionReason] = useState('');
  const [bulkRedactionText, setBulkRedactionText] = useState('[REDACTED]');

  // Filter content based on selected filters
  const filteredContent = privilegedContent.filter(item => {
    const statusMatch = filterStatus === 'all' || item.status === filterStatus;
    const typeMatch = filterType === 'all' || item.privilegeType === filterType;
    return statusMatch && typeMatch;
  });

  // Handle item selection
  const toggleItemSelection = (itemId: string) => {
    setSelectedItems(prev => 
      prev.includes(itemId)
        ? prev.filter(id => id !== itemId)
        : [...prev, itemId]
    );
  };

  const selectAll = () => {
    const selectableItems = filteredContent
      .filter(item => item.status !== 'redacted')
      .map(item => item.id);
    setSelectedItems(selectableItems);
  };

  const clearSelection = () => {
    setSelectedItems([]);
  };

  const handleBulkRedact = async () => {
    if (selectedItems.length === 0 || !bulkRedactionReason.trim()) return;

    try {
      await onBulkRedact(selectedItems, bulkRedactionReason, bulkRedactionText);
      setShowBulkRedactDialog(false);
      setBulkRedactionReason('');
      setBulkRedactionText('[REDACTED]');
      setSelectedItems([]);
    } catch (error) {
      // Error handling is done in the hook
    }
  };

  const exportPrivilegeLog = () => {
    const csvContent = [
      ['Type', 'Content', 'Confidence', 'Status', 'Detection Method', 'Position'].join(','),
      ...filteredContent.map(item => [
        item.privilegeType,
        `"${item.content.replace(/"/g, '""')}"`,
        (item.confidenceScore * 100).toFixed(1) + '%',
        item.status,
        item.detectionMethod,
        `${item.startPosition}-${item.endPosition}`
      ].join(','))
    ].join('\n');

    const blob = new Blob([csvContent], { type: 'text/csv' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `privilege-log-${new Date().toISOString().slice(0, 10)}.csv`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  if (privilegedContent.length === 0) {
    return (
      <Card>
        <CardContent className="py-8">
          <div className="text-center space-y-4">
            <div className="mx-auto w-16 h-16 bg-green-100 rounded-full flex items-center justify-center">
              <Shield className="h-8 w-8 text-green-600" />
            </div>
            <div className="space-y-2">
              <h3 className="text-lg font-medium">No Privileged Content Detected</h3>
              <p className="text-sm text-muted-foreground max-w-md mx-auto">
                The analysis did not find any privileged content in this document. 
                This could mean the document is clear for disclosure or you may want 
                to adjust the analysis settings.
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <>
      <Card>
        <CardHeader>
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
            <CardTitle className="flex items-center gap-2">
              <Shield className="h-5 w-5" />
              Privileged Content ({filteredContent.length})
            </CardTitle>
            
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={exportPrivilegeLog}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Export
              </Button>
              
              {selectedItems.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setShowBulkRedactDialog(true)}
                  className="gap-2"
                >
                  <Lock className="h-4 w-4" />
                  Bulk Redact ({selectedItems.length})
                </Button>
              )}
            </div>
          </div>
        </CardHeader>
        
        <CardContent className="space-y-4">
          {/* Filters and Selection */}
          <div className="flex flex-col sm:flex-row gap-4 p-4 bg-muted/30 rounded-lg">
            <div className="flex items-center gap-2">
              <Filter className="h-4 w-4 text-muted-foreground" />
              <span className="text-sm font-medium">Filters:</span>
            </div>
            
            <div className="flex flex-wrap gap-2">
              <Select value={filterStatus} onValueChange={setFilterStatus}>
                <SelectTrigger className="w-[140px]">
                  <SelectValue placeholder="Status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Status</SelectItem>
                  <SelectItem value="detected">Detected</SelectItem>
                  <SelectItem value="under_review">Under Review</SelectItem>
                  <SelectItem value="confirmed">Confirmed</SelectItem>
                  <SelectItem value="rejected">Rejected</SelectItem>
                  <SelectItem value="redacted">Redacted</SelectItem>
                </SelectContent>
              </Select>

              <Select value={filterType} onValueChange={setFilterType}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Privilege Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="all">All Types</SelectItem>
                  {PRIVILEGE_TYPE_OPTIONS.map(option => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex items-center gap-2 ml-auto">
              <Button
                variant="outline"
                size="sm"
                onClick={selectAll}
                disabled={filteredContent.filter(item => item.status !== 'redacted').length === 0}
                className="gap-1"
              >
                <CheckSquare className="h-3 w-3" />
                Select All
              </Button>
              
              {selectedItems.length > 0 && (
                <Button
                  variant="outline"
                  size="sm"
                  onClick={clearSelection}
                  className="gap-1"
                >
                  <Square className="h-3 w-3" />
                  Clear ({selectedItems.length})
                </Button>
              )}
            </div>
          </div>

          {/* Content List */}
          <div className="space-y-3">
            {filteredContent.map((item) => (
              <div key={item.id} className="relative">
                {item.status !== 'redacted' && (
                  <div className="absolute top-4 left-4 z-10">
                    <Checkbox
                      checked={selectedItems.includes(item.id)}
                      onCheckedChange={() => toggleItemSelection(item.id)}
                    />
                  </div>
                )}
                
                <div className={item.status !== 'redacted' ? 'ml-8' : ''}>
                  <PrivilegeContentItem
                    content={item}
                    onReview={onReview}
                    onRedact={onRedact}
                    loading={loading}
                  />
                </div>
              </div>
            ))}
          </div>

          {filteredContent.length === 0 && (
            <div className="text-center py-8 text-muted-foreground">
              <AlertTriangle className="h-8 w-8 mx-auto mb-2" />
              <p>No items match the current filters.</p>
            </div>
          )}
        </CardContent>
      </Card>

      {/* Bulk Redaction Dialog */}
      <Dialog open={showBulkRedactDialog} onOpenChange={setShowBulkRedactDialog}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Bulk Redaction</DialogTitle>
            <DialogDescription>
              Apply redactions to {selectedItems.length} selected items.
            </DialogDescription>
          </DialogHeader>
          
          <div className="space-y-4">
            <div className="space-y-2">
              <Label htmlFor="bulk-redaction-text">Redaction Text</Label>
              <Textarea
                id="bulk-redaction-text"
                value={bulkRedactionText}
                onChange={(e) => setBulkRedactionText(e.target.value)}
                className="min-h-[60px]"
              />
            </div>
            
            <div className="space-y-2">
              <Label htmlFor="bulk-redaction-reason">Reason for Redaction</Label>
              <Textarea
                id="bulk-redaction-reason"
                placeholder="Explain why these items are being redacted..."
                value={bulkRedactionReason}
                onChange={(e) => setBulkRedactionReason(e.target.value)}
                className="min-h-[80px]"
                required
              />
            </div>
          </div>

          <DialogFooter>
            <Button variant="outline" onClick={() => setShowBulkRedactDialog(false)}>
              Cancel
            </Button>
            <Button
              onClick={handleBulkRedact}
              disabled={loading || !bulkRedactionReason.trim()}
            >
              <Lock className="h-4 w-4 mr-2" />
              Apply Redactions
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  );
}
