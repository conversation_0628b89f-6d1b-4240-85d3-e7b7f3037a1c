"use client";

import React, { useState } from 'react';
import { Shield, X } from 'lucide-react';
import {
  <PERSON>alog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Slider } from '@/components/ui/slider';
import { Badge } from '@/components/ui/badge';
import type {
  AnalysisOptions,
  PrivilegeType
} from '@/lib/types/privilege-log';
import { PRIVILEGE_TYPE_OPTIONS } from '@/lib/types/privilege-log';

interface AnalysisOptionsFormProps {
  isOpen: boolean;
  onClose: () => void;
  onAnalyze: (options: AnalysisOptions) => Promise<void>;
  loading?: boolean;
}

export function AnalysisOptionsForm({
  isOpen,
  onClose,
  onAnalyze,
  loading = false
}: AnalysisOptionsFormProps) {
  const [includeAIAnalysis, setIncludeAIAnalysis] = useState(true);
  const [confidenceThreshold, setConfidenceThreshold] = useState([0.7]);
  const [selectedPrivilegeTypes, setSelectedPrivilegeTypes] = useState<PrivilegeType[]>([]);
  const [autoRedact, setAutoRedact] = useState(false);
  const [requireManualReview, setRequireManualReview] = useState(true);

  const handleSubmit = async () => {
    const options: AnalysisOptions = {
      includeAIAnalysis,
      confidenceThreshold: confidenceThreshold[0],
      privilegeTypes: selectedPrivilegeTypes.length > 0 ? selectedPrivilegeTypes : undefined,
      autoRedact,
      requireManualReview,
    };

    await onAnalyze(options);
  };

  const togglePrivilegeType = (type: PrivilegeType) => {
    setSelectedPrivilegeTypes(prev => 
      prev.includes(type)
        ? prev.filter(t => t !== type)
        : [...prev, type]
    );
  };

  const removePrivilegeType = (type: PrivilegeType) => {
    setSelectedPrivilegeTypes(prev => prev.filter(t => t !== type));
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Shield className="h-5 w-5" />
            Privilege Analysis Options
          </DialogTitle>
          <DialogDescription>
            Configure the AI analysis to detect privileged content in your document.
            Adjust settings based on your review requirements and confidence preferences.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* AI Analysis Toggle */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="ai-analysis"
              checked={includeAIAnalysis}
              onCheckedChange={(checked) => setIncludeAIAnalysis(checked === true)}
            />
            <Label htmlFor="ai-analysis" className="text-sm font-medium">
              Include AI-powered analysis (recommended)
            </Label>
          </div>

          {/* Confidence Threshold */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              Confidence Threshold: {(confidenceThreshold[0] * 100).toFixed(0)}%
            </Label>
            <div className="px-2">
              <Slider
                value={confidenceThreshold}
                onValueChange={setConfidenceThreshold}
                max={1}
                min={0.1}
                step={0.05}
                className="w-full"
              />
            </div>
            <p className="text-xs text-muted-foreground">
              Higher values require more confidence to flag content as privileged.
              Recommended: 70% for comprehensive detection, 90% for high precision.
            </p>
          </div>

          {/* Privilege Types Filter */}
          <div className="space-y-3">
            <Label className="text-sm font-medium">
              Privilege Types to Detect (Optional)
            </Label>
            <p className="text-xs text-muted-foreground">
              Leave empty to detect all types. Select specific types to focus the analysis.
            </p>
            
            {/* Type Selection Buttons */}
            <div className="grid grid-cols-2 gap-2">
              {PRIVILEGE_TYPE_OPTIONS.map((option) => (
                <Button
                  key={option.value}
                  type="button"
                  variant={selectedPrivilegeTypes.includes(option.value) ? "default" : "outline"}
                  size="sm"
                  onClick={() => togglePrivilegeType(option.value)}
                  className="text-xs justify-start"
                >
                  {option.label}
                </Button>
              ))}
            </div>

            {/* Selected Types Display */}
            {selectedPrivilegeTypes.length > 0 && (
              <div className="space-y-2">
                <Label className="text-xs">Selected Types:</Label>
                <div className="flex flex-wrap gap-1">
                  {selectedPrivilegeTypes.map((type) => {
                    const option = PRIVILEGE_TYPE_OPTIONS.find(opt => opt.value === type);
                    return (
                      <Badge key={type} variant="secondary" className="gap-1 text-xs">
                        {option?.label}
                        <button
                          type="button"
                          onClick={() => removePrivilegeType(type)}
                          className="ml-1 hover:text-destructive"
                        >
                          <X className="h-3 w-3" />
                        </button>
                      </Badge>
                    );
                  })}
                </div>
              </div>
            )}
          </div>

          {/* Auto Redaction */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="auto-redact"
              checked={autoRedact}
              onCheckedChange={(checked) => setAutoRedact(checked === true)}
            />
            <Label htmlFor="auto-redact" className="text-sm">
              Automatically redact high-confidence items
            </Label>
          </div>

          {/* Manual Review Requirement */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="manual-review"
              checked={requireManualReview}
              onCheckedChange={(checked) => setRequireManualReview(checked === true)}
            />
            <Label htmlFor="manual-review" className="text-sm">
              Require manual review for all detections
            </Label>
          </div>

          {/* Warning for Auto Redaction */}
          {autoRedact && (
            <div className="p-3 bg-yellow-50 border border-yellow-200 rounded-md">
              <p className="text-sm text-yellow-800">
                <strong>Warning:</strong> Auto-redaction will immediately apply redactions to 
                high-confidence items. This action cannot be undone. Consider enabling manual 
                review for better control.
              </p>
            </div>
          )}
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? (
              <>
                <Shield className="mr-2 h-4 w-4 animate-pulse" />
                Analyzing...
              </>
            ) : (
              <>
                <Shield className="mr-2 h-4 w-4" />
                Start Analysis
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
