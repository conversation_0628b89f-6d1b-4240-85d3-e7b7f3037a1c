"use client";

import React, { useState } from 'react';
import { <PERSON>, Loader2, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Clock, XCircle } from 'lucide-react';
import { mapContractTypeToDocumentType } from '@/lib/utils/contract-type-mapper';
import { But<PERSON> } from '@/components/ui/button';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Switch } from '@/components/ui/switch';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { useToast } from '@/hooks/use-toast';
import { usePlaybooks, useAnalyzeContract } from '@/lib/services/contract-playbooks-service';
import { FeatureGuard } from '@/components/subscription/feature-guard';
import { CONTRACT_TYPES } from '@/lib/types/contract-playbooks';
import type {
  ContractPlaybook,
  AnalyzeContractRequest,
  ContractAnalysis,
  ContractPlaybookError
} from '@/lib/types/contract-playbooks';

interface ContractAnalysisModalProps {
  documentId: string;
  documentName?: string;
  trigger?: React.ReactNode;
  onAnalysisComplete?: (analysis: ContractAnalysis) => void;
}

export function ContractAnalysisModal({ 
  documentId, 
  documentName, 
  trigger,
  onAnalysisComplete 
}: ContractAnalysisModalProps) {
  const { toast } = useToast();
  const [open, setOpen] = useState(false);
  const [selectedPlaybookId, setSelectedPlaybookId] = useState<string>('');
  const [analysisOptions, setAnalysisOptions] = useState({
    includeRecommendations: true,
    riskThreshold: 2,
    aiAnalysis: true,
    detailedReport: true,
  });

  // API hooks
  const { data: playbooksData, isLoading: loadingPlaybooks } = usePlaybooks({ isActive: true });
  const analyzeContract = useAnalyzeContract();

  const handleAnalyze = async () => {
    if (!selectedPlaybookId) {
      toast({
        title: "Validation Error",
        description: "Please select a playbook to analyze against.",
        variant: "destructive",
      });
      return;
    }

    const playbook = playbooksData?.playbooks.find(p => p.id === selectedPlaybookId);
    if (!playbook) {
      toast({
        title: "Error",
        description: "Selected playbook not found",
        variant: "destructive",
      });
      return;
    }

    // Map the contract type to document type
    const documentType = mapContractTypeToDocumentType(playbook.contractType);

    const request: AnalyzeContractRequest = {
      contractId: documentId,
      playbookId: selectedPlaybookId,
      documentType: documentType,
      options: analysisOptions,
    };

    try {
      const analysis = await analyzeContract.mutateAsync(request);
      
      toast({
        title: "Analysis started",
        description: "Contract analysis is in progress. You'll be notified when it's complete.",
      });

      setOpen(false);
      onAnalysisComplete?.(analysis);
    } catch (error) {
      const err = error as ContractPlaybookError;
      toast({
        title: "Analysis failed",
        description: err.message || "Failed to start contract analysis. Please try again.",
        variant: "destructive",
      });
    }
  };

  const getContractTypeLabel = (type: string) => {
    return CONTRACT_TYPES.find(ct => ct.value === type)?.label || type;
  };

  const selectedPlaybook = playbooksData?.playbooks.find(p => p.id === selectedPlaybookId);

  return (
    <FeatureGuard featureId="contract_playbooks">
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogTrigger asChild>
          {trigger || (
            <Button variant="outline" className="gap-2">
              <Play className="h-4 w-4" />
              Analyze with Playbook
            </Button>
          )}
        </DialogTrigger>
        <DialogContent className="max-w-2xl">
          <DialogHeader>
            <DialogTitle>Analyze Contract with Playbook</DialogTitle>
            <DialogDescription>
              Run automated contract analysis using predefined rules and criteria.
              {documentName && ` Document: ${documentName}`}
            </DialogDescription>
          </DialogHeader>

          <div className="space-y-6">
            {/* Playbook Selection */}
            <div className="space-y-3">
              <Label htmlFor="playbook">Select Playbook</Label>
              {loadingPlaybooks ? (
                <div className="flex items-center gap-2 p-3 border rounded-lg">
                  <Loader2 className="h-4 w-4 animate-spin" />
                  <span className="text-sm text-muted-foreground">Loading playbooks...</span>
                </div>
              ) : !playbooksData?.playbooks.length ? (
                <div className="p-4 border rounded-lg text-center">
                  <p className="text-sm text-muted-foreground mb-3">
                    No active playbooks found. Create a playbook first.
                  </p>
                  <Button variant="outline" size="sm" onClick={() => window.open('/contract-playbooks/create', '_blank')}>
                    Create Playbook
                  </Button>
                </div>
              ) : (
                <Select value={selectedPlaybookId} onValueChange={setSelectedPlaybookId}>
                  <SelectTrigger>
                    <SelectValue placeholder="Choose a playbook..." />
                  </SelectTrigger>
                  <SelectContent>
                    {playbooksData.playbooks.map((playbook: ContractPlaybook) => (
                      <SelectItem key={playbook.id} value={playbook.id}>
                        <div className="flex items-center gap-2">
                          <span>{playbook.name}</span>
                          <Badge variant="outline" className="text-xs">
                            {getContractTypeLabel(playbook.contractType)}
                          </Badge>
                        </div>
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>

            {/* Selected Playbook Info */}
            {selectedPlaybook && (
              <Card>
                <CardHeader className="pb-3">
                  <CardTitle className="text-lg">{selectedPlaybook.name}</CardTitle>
                  <CardDescription>
                    {selectedPlaybook.description || 'No description provided'}
                  </CardDescription>
                </CardHeader>
                <CardContent className="pt-0">
                  <div className="grid grid-cols-2 gap-4 text-sm">
                    <div>
                      <span className="text-muted-foreground">Contract Type:</span>
                      <div className="font-medium">{getContractTypeLabel(selectedPlaybook.contractType)}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Rules:</span>
                      <div className="font-medium">{selectedPlaybook.rules.length} rules</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Version:</span>
                      <div className="font-medium">v{selectedPlaybook.version}</div>
                    </div>
                    <div>
                      <span className="text-muted-foreground">Risk Profile:</span>
                      <div className="font-medium">{selectedPlaybook.metadata.riskProfile || 'Not specified'}</div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )}

            <Separator />

            {/* Analysis Options */}
            <div className="space-y-4">
              <h4 className="font-medium">Analysis Options</h4>
              
              <div className="space-y-3">
                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="recommendations">Include Recommendations</Label>
                    <p className="text-sm text-muted-foreground">
                      Generate actionable recommendations for identified issues
                    </p>
                  </div>
                  <Switch
                    id="recommendations"
                    checked={analysisOptions.includeRecommendations}
                    onCheckedChange={(checked) => 
                      setAnalysisOptions(prev => ({ ...prev, includeRecommendations: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="aiAnalysis">AI-Enhanced Analysis</Label>
                    <p className="text-sm text-muted-foreground">
                      Use advanced AI for deeper contract understanding
                    </p>
                  </div>
                  <Switch
                    id="aiAnalysis"
                    checked={analysisOptions.aiAnalysis}
                    onCheckedChange={(checked) => 
                      setAnalysisOptions(prev => ({ ...prev, aiAnalysis: checked }))
                    }
                  />
                </div>

                <div className="flex items-center justify-between">
                  <div>
                    <Label htmlFor="detailedReport">Detailed Report</Label>
                    <p className="text-sm text-muted-foreground">
                      Generate comprehensive analysis report
                    </p>
                  </div>
                  <Switch
                    id="detailedReport"
                    checked={analysisOptions.detailedReport}
                    onCheckedChange={(checked) => 
                      setAnalysisOptions(prev => ({ ...prev, detailedReport: checked }))
                    }
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="riskThreshold">Risk Threshold</Label>
                  <Select
                    value={analysisOptions.riskThreshold.toString()}
                    onValueChange={(value) => 
                      setAnalysisOptions(prev => ({ ...prev, riskThreshold: parseInt(value) }))
                    }
                  >
                    <SelectTrigger>
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1 - Very Low</SelectItem>
                      <SelectItem value="2">2 - Low</SelectItem>
                      <SelectItem value="3">3 - Medium</SelectItem>
                      <SelectItem value="4">4 - High</SelectItem>
                      <SelectItem value="5">5 - Very High</SelectItem>
                    </SelectContent>
                  </Select>
                  <p className="text-sm text-muted-foreground">
                    Minimum risk level to flag issues (1 = most sensitive, 5 = least sensitive)
                  </p>
                </div>
              </div>
            </div>

            {/* Analysis Time Estimate */}
            {selectedPlaybook && (
              <div className="p-4 bg-muted/50 rounded-lg">
                <div className="flex items-center gap-2 mb-2">
                  <Clock className="h-4 w-4 text-muted-foreground" />
                  <span className="font-medium text-sm">Estimated Analysis Time</span>
                </div>
                <p className="text-sm text-muted-foreground">
                  Based on {selectedPlaybook.rules.length} rules and selected options: 
                  <span className="font-medium"> 2-5 minutes</span>
                </p>
              </div>
            )}

            {/* Action Buttons */}
            <div className="flex justify-end gap-3">
              <Button variant="outline" onClick={() => setOpen(false)}>
                Cancel
              </Button>
              <Button 
                onClick={handleAnalyze}
                disabled={!selectedPlaybookId || analyzeContract.isPending}
                className="gap-2"
              >
                {analyzeContract.isPending ? (
                  <>
                    <Loader2 className="h-4 w-4 animate-spin" />
                    Starting Analysis...
                  </>
                ) : (
                  <>
                    <Play className="h-4 w-4" />
                    Start Analysis
                  </>
                )}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </FeatureGuard>
  );
}

// Quick analysis status component for showing analysis progress
interface AnalysisStatusProps {
  analysis: ContractAnalysis;
  compact?: boolean;
}

export function AnalysisStatus({ analysis, compact = false }: AnalysisStatusProps) {
  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'PENDING': return <Clock className="h-4 w-4 text-gray-600" />;
      case 'IN_PROGRESS': return <Loader2 className="h-4 w-4 text-blue-600 animate-spin" />;
      case 'COMPLETED': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'FAILED': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <Clock className="h-4 w-4 text-gray-600" />;
    }
  };

  const getRiskIcon = (riskLevel: string) => {
    switch (riskLevel) {
      case 'LOW': return <CheckCircle className="h-4 w-4 text-green-600" />;
      case 'MEDIUM': return <Clock className="h-4 w-4 text-yellow-600" />;
      case 'HIGH': return <AlertTriangle className="h-4 w-4 text-orange-600" />;
      case 'CRITICAL': return <XCircle className="h-4 w-4 text-red-600" />;
      default: return <CheckCircle className="h-4 w-4 text-gray-600" />;
    }
  };

  if (compact) {
    return (
      <div className="flex items-center gap-2">
        {getStatusIcon(analysis.status)}
        <span className="text-sm">{analysis.status}</span>
        {analysis.status === 'COMPLETED' && (
          <>
            <Separator orientation="vertical" className="h-4" />
            {getRiskIcon(analysis.riskLevel)}
            <span className="text-sm">{analysis.riskLevel} Risk</span>
            <Separator orientation="vertical" className="h-4" />
            <span className="text-sm font-medium">{analysis.overallScore}/100</span>
          </>
        )}
      </div>
    );
  }

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="text-lg flex items-center gap-2">
          {getStatusIcon(analysis.status)}
          Contract Analysis
        </CardTitle>
        <CardDescription>
          Playbook: {analysis.playbookName}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-3">
          <div className="flex items-center justify-between">
            <span className="text-sm text-muted-foreground">Status:</span>
            <Badge variant={analysis.status === 'COMPLETED' ? 'default' : 'secondary'}>
              {analysis.status}
            </Badge>
          </div>
          
          {analysis.status === 'COMPLETED' && (
            <>
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Overall Score:</span>
                <span className="font-medium">{analysis.overallScore}/100</span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-muted-foreground">Risk Level:</span>
                <div className="flex items-center gap-2">
                  {getRiskIcon(analysis.riskLevel)}
                  <span className="font-medium">{analysis.riskLevel}</span>
                </div>
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div>
                  <span className="text-muted-foreground">Passed Rules:</span>
                  <div className="font-medium text-green-600">{analysis.summary.passedRules}</div>
                </div>
                <div>
                  <span className="text-muted-foreground">Failed Rules:</span>
                  <div className="font-medium text-red-600">{analysis.summary.failedRules}</div>
                </div>
              </div>
            </>
          )}
          
          <div className="text-xs text-muted-foreground">
            Analyzed {new Date(analysis.analyzedAt).toLocaleString()}
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
