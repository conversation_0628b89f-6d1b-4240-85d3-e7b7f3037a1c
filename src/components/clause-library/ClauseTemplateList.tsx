"use client";

import React, { useEffect, useState, useCallback } from 'react';
import { ClauseTemplate } from '@/lib/types/clause-library';
import { clauseLibraryService } from '@/lib/services/clause-library-service';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';

interface ClauseTemplateListProps {
  onTemplateSelect?: (template: ClauseTemplate) => void;
}

export const ClauseTemplateList: React.FC<ClauseTemplateListProps> = ({
  onTemplateSelect,
}) => {
  const [templates, setTemplates] = useState<ClauseTemplate[]>([]);
  const [filteredTemplates, setFilteredTemplates] = useState<ClauseTemplate[]>([]);
  const [searchTerm, setSearchTerm] = useState('');
  const [categoryQuery, setCategoryQuery] = useState('');
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  const fetchTemplates = useCallback(async (category?: string) => {
    try {
      setLoading(true);
      const data = await clauseLibraryService.getTemplates(category);
      setTemplates(data);
      setFilteredTemplates(data);
      setError(null);
    } catch (err) {
      setError('Failed to fetch templates');
      console.error('Error fetching templates:', err);
    } finally {
      setLoading(false);
    }
  }, []);

  // Initial load
  useEffect(() => {
    fetchTemplates();
  }, [fetchTemplates]);

  // Debounced category search
  useEffect(() => {
    const timeoutId = setTimeout(() => {
      if (categoryQuery) {
        fetchTemplates(categoryQuery);
      } else {
        fetchTemplates();
      }
    }, 300);

    return () => clearTimeout(timeoutId);
  }, [categoryQuery, fetchTemplates]);

  // Filter by name/content
  useEffect(() => {
    if (!searchTerm) {
      setFilteredTemplates(templates);
      return;
    }

    const searchLower = searchTerm.toLowerCase();
    const filtered = templates.filter(
      (template) =>
        template.name.toLowerCase().includes(searchLower) ||
        template.content.toLowerCase().includes(searchLower)
    );
    setFilteredTemplates(filtered);
  }, [searchTerm, templates]);

  if (loading) return <div>Loading templates...</div>;
  if (error) return <div className="text-red-500">{error}</div>;

  return (
    <div className="space-y-4">
      <div className="flex gap-4">
        <Input
          placeholder="Search templates..."
          value={searchTerm}
          onChange={(e) => setSearchTerm(e.target.value)}
          className="flex-1"
        />
        <Input
          placeholder="Search categories..."
          value={categoryQuery}
          onChange={(e) => setCategoryQuery(e.target.value)}
          className="w-[200px]"
        />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
        {filteredTemplates.map((template) => (
          <Card
            key={template.id}
            className="cursor-pointer hover:shadow-md transition-shadow"
            onClick={() => onTemplateSelect?.(template)}
          >
            <CardHeader className="pb-2">
              <h3 className="text-lg font-semibold">{template.name}</h3>
              <Badge variant="secondary">{template.category}</Badge>
            </CardHeader>
            <CardContent>
              <p className="text-sm text-gray-600 line-clamp-3">
                {template.content}
              </p>
              <div className="mt-2 flex flex-wrap gap-1">
                {template.tags.map((tag) => (
                  <Badge key={tag} variant="outline" className="text-xs">
                    {tag}
                  </Badge>
                ))}
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    </div>
  );
};
