"use client";

import React, { useState } from 'react';
import { ClauseMatch } from '@/lib/types/clause-library';
import { clauseLibraryService } from '@/lib/services/clause-library-service';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { Slider } from '@/components/ui/slider';

interface ClauseIdentifierProps {
  documentId: string;
  onMatchesFound?: (matches: ClauseMatch[]) => void;
}

export const ClauseIdentifier: React.FC<ClauseIdentifierProps> = ({
  documentId,
  onMatchesFound,
}) => {
  const [matches, setMatches] = useState<ClauseMatch[]>([]);
  const [selectedCategories, setSelectedCategories] = useState<string[]>([]);
  const [similarityThreshold, setSimilarityThreshold] = useState(0.5);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState<string | null>(null);
  const [matchingPreferences, setMatchingPreferences] = useState({
    ignoreNumbering: true,
    useKeywordMatching: true,
    handleSpecialFormats: true,
    boostSimilarContent: true
  });

  const categories = [
    'Confidentiality',
    'Term',
    'Termination',
    'Intellectual Property',
    'Liability',
    'Payment',
  ];

  const identifyClauses = async () => {
    setLoading(true);
    setError(null);

    try {
      const matches = await clauseLibraryService.identifyClauses(documentId, {
        categories: selectedCategories.length > 0 ? selectedCategories : undefined,
        similarityThreshold,
        matchingPreferences
      });

      setMatches(matches);
      onMatchesFound?.(matches);
    } catch {
      setError('Failed to identify clauses');
    } finally {
      setLoading(false);
    }
  };

  const handleCategoryChange = (category: string) => {
    setSelectedCategories((prev) =>
      prev.includes(category)
        ? prev.filter((c) => c !== category)
        : [...prev, category]
    );
  };

  return (
    <div className="space-y-6">
      <div className="space-y-4">
        <h3 className="text-lg font-medium">Clause Identification Settings</h3>
        
        <div>
          <label className="text-sm font-medium">Categories</label>
          <div className="grid grid-cols-2 md:grid-cols-3 gap-2 mt-2">
            {categories.map((category) => (
              <Button
                key={category}
                variant={selectedCategories.includes(category) ? "default" : "outline"}
                onClick={() => handleCategoryChange(category)}
                className="justify-start"
              >
                {category}
              </Button>
            ))}
          </div>
        </div>

        <div className="space-y-4">
          <div>
            <label className="text-sm font-medium">Similarity Threshold: {similarityThreshold}</label>
            <Slider
              value={[similarityThreshold]}
              onValueChange={(values: number[]) => setSimilarityThreshold(values[0])}
              min={0}
              max={1}
              step={0.1}
              className="mt-2"
            />
          </div>

          <div className="space-y-2">
            <label className="text-sm font-medium">Matching Preferences</label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-2">
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={matchingPreferences.ignoreNumbering}
                  onChange={e => setMatchingPreferences(prev => ({
                    ...prev,
                    ignoreNumbering: e.target.checked
                  }))}
                  className="rounded border-gray-300"
                />
                <span className="text-sm">Ignore Numbering</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={matchingPreferences.useKeywordMatching}
                  onChange={e => setMatchingPreferences(prev => ({
                    ...prev,
                    useKeywordMatching: e.target.checked
                  }))}
                  className="rounded border-gray-300"
                />
                <span className="text-sm">Use Keyword Matching</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={matchingPreferences.handleSpecialFormats}
                  onChange={e => setMatchingPreferences(prev => ({
                    ...prev,
                    handleSpecialFormats: e.target.checked
                  }))}
                  className="rounded border-gray-300"
                />
                <span className="text-sm">Handle Special Formats</span>
              </label>
              <label className="flex items-center space-x-2">
                <input
                  type="checkbox"
                  checked={matchingPreferences.boostSimilarContent}
                  onChange={e => setMatchingPreferences(prev => ({
                    ...prev,
                    boostSimilarContent: e.target.checked
                  }))}
                  className="rounded border-gray-300"
                />
                <span className="text-sm">Boost Similar Content</span>
              </label>
            </div>
          </div>
        </div>

        <Button
          onClick={identifyClauses}
          disabled={loading}
          className="w-full"
        >
          {loading ? 'Identifying Clauses...' : 'Identify Clauses'}
        </Button>

        {error && (
          <p className="text-red-500 text-sm">{error}</p>
        )}
      </div>

      {matches.length > 0 && (
        <div className="space-y-4">
          <h3 className="text-lg font-medium">Matched Clauses</h3>
          <div className="space-y-3">
            {matches.map((match, index) => (
              <Card key={index}>
                <CardContent className="pt-4">
                  <div className="flex justify-between items-start mb-2">
                    <h4 className="font-medium">{match.name}</h4>
                    <span className="text-sm text-gray-500">
                      {Math.round(match.similarity * 100)}% match
                    </span>
                  </div>
                  <p className="text-sm text-gray-600">{match.content}</p>
                  <div className="mt-2 text-sm text-gray-500">
                    Category: {match.category}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      )}
    </div>
  );
};