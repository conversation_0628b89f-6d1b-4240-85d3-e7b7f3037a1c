'use client';

import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';
import { z } from 'zod';
import { Button } from '@/components/ui/button';
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel, FormMessage } from '@/components/ui/form';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Alert, AlertDescription } from '@/components/ui/alert';
import { Loader2, AlertCircle } from 'lucide-react';
import { useEffect } from 'react';
import { useUpdateProfile } from '@/hooks/use-compliance';
import { ComplianceProfile } from '@/lib/types/compliance';

const editProfileSchema = z.object({
  name: z.string().min(1, 'Profile name is required'),
  description: z.string().min(1, 'Description is required'),
  industry: z.enum(['Healthcare', 'Financial', 'Technology', 'Legal', 'Government', 'Other']),
  riskThresholds: z.object({
    low: z.number().min(0).max(1),
    medium: z.number().min(0).max(1),
    high: z.number().min(0).max(1),
  }),
});

type EditProfileFormValues = z.infer<typeof editProfileSchema>;

interface EditProfileFormProps {
  profile: ComplianceProfile;
  onSuccess?: () => void;
}

export function EditProfileForm({ profile, onSuccess }: EditProfileFormProps) {
  const updateProfileMutation = useUpdateProfile();

  const form = useForm<EditProfileFormValues>({
    resolver: zodResolver(editProfileSchema),
    defaultValues: {
      name: profile.name,
      description: profile.description,
      industry: profile.industry,
      riskThresholds: profile.riskThresholds,
    },
  });

  // Update form when profile changes
  useEffect(() => {
    form.reset({
      name: profile.name,
      description: profile.description,
      industry: profile.industry,
      riskThresholds: profile.riskThresholds,
    });
  }, [profile, form]);

  const onSubmit = async (values: EditProfileFormValues) => {
    try {
      await updateProfileMutation.mutateAsync({
        profileId: profile.id,
        updates: values,
      });
      onSuccess?.();
    } catch (error) {
      console.error('Failed to update profile:', error);
    }
  };

  return (
    <Form {...form}>
      <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
        {/* Basic Information */}
        <div className="space-y-4">
          <FormField
            control={form.control}
            name="name"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Profile Name</FormLabel>
                <FormControl>
                  <Input placeholder="e.g., Healthcare Compliance Profile" {...field} />
                </FormControl>
                <FormDescription>
                  A descriptive name for this compliance profile
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="description"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Description</FormLabel>
                <FormControl>
                  <Textarea 
                    placeholder="Describe the purpose and scope of this compliance profile..."
                    {...field}
                  />
                </FormControl>
                <FormDescription>
                  Explain what this profile is designed for and its intended use
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />

          <FormField
            control={form.control}
            name="industry"
            render={({ field }) => (
              <FormItem>
                <FormLabel>Industry</FormLabel>
                <Select onValueChange={field.onChange} defaultValue={field.value}>
                  <FormControl>
                    <SelectTrigger>
                      <SelectValue placeholder="Select industry" />
                    </SelectTrigger>
                  </FormControl>
                  <SelectContent>
                    <SelectItem value="Healthcare">Healthcare</SelectItem>
                    <SelectItem value="Financial">Financial Services</SelectItem>
                    <SelectItem value="Technology">Technology</SelectItem>
                    <SelectItem value="Legal">Legal Services</SelectItem>
                    <SelectItem value="Government">Government</SelectItem>
                    <SelectItem value="Other">Other</SelectItem>
                  </SelectContent>
                </Select>
                <FormDescription>
                  The industry this profile is designed for
                </FormDescription>
                <FormMessage />
              </FormItem>
            )}
          />
        </div>

        {/* Risk Thresholds */}
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium">Risk Thresholds</h3>
            <p className="text-sm text-muted-foreground">
              Define the risk level thresholds for compliance scoring
            </p>
          </div>
          
          <div className="grid grid-cols-3 gap-4">
            <FormField
              control={form.control}
              name="riskThresholds.low"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Low Risk Threshold</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="0.1" 
                      min="0" 
                      max="1"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>0.0 - 1.0</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="riskThresholds.medium"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Medium Risk Threshold</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="0.1" 
                      min="0" 
                      max="1"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>0.0 - 1.0</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="riskThresholds.high"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>High Risk Threshold</FormLabel>
                  <FormControl>
                    <Input 
                      type="number" 
                      step="0.1" 
                      min="0" 
                      max="1"
                      {...field}
                      onChange={(e) => field.onChange(parseFloat(e.target.value))}
                    />
                  </FormControl>
                  <FormDescription>0.0 - 1.0</FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
          </div>
        </div>

        {/* Current Frameworks (Read-only) */}
        <div className="space-y-4">
          <div>
            <h3 className="text-lg font-medium">Current Frameworks</h3>
            <p className="text-sm text-muted-foreground">
              The regulatory frameworks currently configured for this profile
            </p>
          </div>
          
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {profile.frameworks.map((framework, index) => (
              <div key={index} className="p-3 border rounded-lg bg-muted/50">
                <div className="font-medium">{framework.name}</div>
                <div className="text-sm text-muted-foreground">
                  Version: {framework.version} • Weight: {(framework.weight * 100).toFixed(0)}%
                </div>
                <div className="text-sm text-muted-foreground">
                  Status: {framework.enabled ? 'Enabled' : 'Disabled'}
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Current Custom Requirements (Read-only) */}
        {profile.customRequirements.length > 0 && (
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium">Custom Requirements</h3>
              <p className="text-sm text-muted-foreground">
                Organization-specific compliance requirements for this profile
              </p>
            </div>
            
            <div className="space-y-3">
              {profile.customRequirements.map((req, index) => (
                <div key={index} className="p-3 border rounded-lg bg-muted/50">
                  <div className="flex items-center justify-between">
                    <div className="font-medium">{req.category}</div>
                    {req.mandatory && (
                      <span className="text-xs bg-red-100 text-red-800 px-2 py-1 rounded">
                        Mandatory
                      </span>
                    )}
                  </div>
                  <div className="text-sm text-muted-foreground mt-1">
                    {req.requirement}
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Error Display */}
        {updateProfileMutation.error && (
          <Alert variant="destructive">
            <AlertCircle className="h-4 w-4" />
            <AlertDescription>
              {updateProfileMutation.error.message}
            </AlertDescription>
          </Alert>
        )}

        {/* Submit Button */}
        <div className="flex justify-end gap-4">
          <Button type="button" variant="outline" onClick={onSuccess}>
            Cancel
          </Button>
          <Button 
            type="submit" 
            disabled={updateProfileMutation.isPending}
          >
            {updateProfileMutation.isPending ? (
              <>
                <Loader2 className="h-4 w-4 animate-spin mr-2" />
                Updating Profile...
              </>
            ) : (
              'Update Profile'
            )}
          </Button>
        </div>
      </form>
    </Form>
  );
}
