'use client';

import { useState } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { Dialog, DialogContent, DialogDescription, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { AlertDialog, AlertDialogAction, AlertDialogCancel, AlertDialogContent, AlertDialogDescription, AlertDialogFooter, AlertDialogHeader, AlertDialogTitle, AlertDialogTrigger } from '@/components/ui/alert-dialog';
import { 
  Table, 
  TableBody, 
  TableCell, 
  TableHead, 
  TableHeader, 
  TableRow 
} from '@/components/ui/table';
import { 
  Plus, 
  Edit, 
  Trash2, 
  Shield, 
  Settings,
  Star,
  Building,
  Calendar,
  AlertTriangle,
} from 'lucide-react';
import { format } from 'date-fns';
import { useComplianceProfiles, useDeleteProfile } from '@/hooks/use-compliance';
import { CreateProfileForm } from './create-profile-form';
import { EditProfileForm } from './edit-profile-form';
import { ComplianceProfile } from '@/lib/types/compliance';

export function ComplianceProfileManager() {
  const [createDialogOpen, setCreateDialogOpen] = useState(false);
  const [editDialogOpen, setEditDialogOpen] = useState(false);
  const [selectedProfile, setSelectedProfile] = useState<ComplianceProfile | null>(null);
  
  const { data, isLoading, error } = useComplianceProfiles();
  const deleteProfileMutation = useDeleteProfile();

  const profiles = data?.profiles || [];

  const handleEdit = (profile: ComplianceProfile) => {
    setSelectedProfile(profile);
    setEditDialogOpen(true);
  };

  const handleDelete = async (profileId: string) => {
    try {
      await deleteProfileMutation.mutateAsync(profileId);
    } catch (error) {
      console.error('Failed to delete profile:', error);
    }
  };

  const getIndustryIcon = (industry: string) => {
    switch (industry) {
      case 'Healthcare':
        return '🏥';
      case 'Financial':
        return '🏦';
      case 'Technology':
        return '💻';
      case 'Legal':
        return '⚖️';
      case 'Government':
        return '🏛️';
      default:
        return '🏢';
    }
  };

  if (isLoading) {
    return (
      <div className="space-y-4">
        <div className="flex justify-between items-center">
          <Skeleton className="h-8 w-48" />
          <Skeleton className="h-10 w-32" />
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {Array.from({ length: 6 }).map((_, i) => (
            <Card key={i}>
              <CardHeader>
                <Skeleton className="h-6 w-32" />
                <Skeleton className="h-4 w-24" />
              </CardHeader>
              <CardContent>
                <Skeleton className="h-20 w-full" />
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  if (error) {
    return (
      <Card>
        <CardContent className="p-6 text-center">
          <AlertTriangle className="h-8 w-8 text-red-500 mx-auto mb-2" />
          <p className="text-muted-foreground">Failed to load compliance profiles</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h3 className="text-lg font-medium">Compliance Profiles</h3>
          <p className="text-sm text-muted-foreground">
            Manage industry-specific compliance profiles and custom rules
          </p>
        </div>
        <Dialog open={createDialogOpen} onOpenChange={setCreateDialogOpen}>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Create Profile
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
            <DialogHeader>
              <DialogTitle>Create Compliance Profile</DialogTitle>
              <DialogDescription>
                Create a new compliance profile with custom frameworks and rules
              </DialogDescription>
            </DialogHeader>
            <CreateProfileForm onSuccess={() => setCreateDialogOpen(false)} />
          </DialogContent>
        </Dialog>
      </div>

      {/* Profiles Grid */}
      {profiles.length === 0 ? (
        <Card>
          <CardContent className="p-6 text-center">
            <Shield className="h-8 w-8 text-muted-foreground mx-auto mb-2" />
            <p className="text-muted-foreground mb-4">No compliance profiles found</p>
            <Button onClick={() => setCreateDialogOpen(true)}>
              <Plus className="h-4 w-4 mr-2" />
              Create Your First Profile
            </Button>
          </CardContent>
        </Card>
      ) : (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
          {profiles.map((profile) => (
            <Card key={profile.id} className="relative">
              {profile.isDefault && (
                <div className="absolute top-2 right-2">
                  <Badge variant="secondary" className="text-xs">
                    <Star className="h-3 w-3 mr-1" />
                    Default
                  </Badge>
                </div>
              )}
              
              <CardHeader className="pb-3">
                <CardTitle className="flex items-center gap-2 text-base">
                  <span className="text-lg">{getIndustryIcon(profile.industry)}</span>
                  {profile.name}
                </CardTitle>
                <CardDescription className="text-sm">
                  {profile.description}
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-4">
                {/* Industry & Frameworks */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2 text-sm">
                    <Building className="h-4 w-4 text-muted-foreground" />
                    <span>{profile.industry}</span>
                  </div>
                  <div className="flex flex-wrap gap-1">
                    {profile.frameworks.map((framework) => (
                      <Badge key={framework.name} variant="outline" className="text-xs">
                        {framework.name}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Risk Thresholds */}
                <div className="space-y-1">
                  <div className="text-sm font-medium">Risk Thresholds</div>
                  <div className="grid grid-cols-3 gap-1 text-xs">
                    <div className="text-center">
                      <div className="text-green-600 font-medium">
                        {(profile.riskThresholds.low * 100).toFixed(0)}%
                      </div>
                      <div className="text-muted-foreground">Low</div>
                    </div>
                    <div className="text-center">
                      <div className="text-yellow-600 font-medium">
                        {(profile.riskThresholds.medium * 100).toFixed(0)}%
                      </div>
                      <div className="text-muted-foreground">Med</div>
                    </div>
                    <div className="text-center">
                      <div className="text-red-600 font-medium">
                        {(profile.riskThresholds.high * 100).toFixed(0)}%
                      </div>
                      <div className="text-muted-foreground">High</div>
                    </div>
                  </div>
                </div>

                {/* Custom Requirements */}
                {profile.customRequirements.length > 0 && (
                  <div className="space-y-1">
                    <div className="text-sm font-medium">Custom Requirements</div>
                    <div className="text-xs text-muted-foreground">
                      {profile.customRequirements.length} custom rule{profile.customRequirements.length !== 1 ? 's' : ''}
                    </div>
                  </div>
                )}

                {/* Metadata */}
                <div className="flex items-center gap-2 text-xs text-muted-foreground">
                  <Calendar className="h-3 w-3" />
                  Created {format(new Date(profile.createdAt), 'MMM dd, yyyy')}
                </div>

                {/* Actions */}
                <div className="flex items-center gap-2 pt-2">
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => handleEdit(profile)}
                    className="flex-1"
                  >
                    <Edit className="h-3 w-3 mr-1" />
                    Edit
                  </Button>
                  
                  <AlertDialog>
                    <AlertDialogTrigger asChild>
                      <Button
                        variant="outline"
                        size="sm"
                        className="text-red-600 hover:text-red-700"
                        disabled={profile.isDefault}
                      >
                        <Trash2 className="h-3 w-3" />
                      </Button>
                    </AlertDialogTrigger>
                    <AlertDialogContent>
                      <AlertDialogHeader>
                        <AlertDialogTitle>Delete Profile</AlertDialogTitle>
                        <AlertDialogDescription>
                          Are you sure you want to delete "{profile.name}"? This action cannot be undone.
                        </AlertDialogDescription>
                      </AlertDialogHeader>
                      <AlertDialogFooter>
                        <AlertDialogCancel>Cancel</AlertDialogCancel>
                        <AlertDialogAction
                          onClick={() => handleDelete(profile.id)}
                          className="bg-red-600 hover:bg-red-700"
                        >
                          Delete
                        </AlertDialogAction>
                      </AlertDialogFooter>
                    </AlertDialogContent>
                  </AlertDialog>
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      )}

      {/* Edit Profile Dialog */}
      <Dialog open={editDialogOpen} onOpenChange={setEditDialogOpen}>
        <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle>Edit Compliance Profile</DialogTitle>
            <DialogDescription>
              Update the compliance profile settings and rules
            </DialogDescription>
          </DialogHeader>
          {selectedProfile && (
            <EditProfileForm 
              profile={selectedProfile}
              onSuccess={() => {
                setEditDialogOpen(false);
                setSelectedProfile(null);
              }}
            />
          )}
        </DialogContent>
      </Dialog>
    </div>
  );
}
