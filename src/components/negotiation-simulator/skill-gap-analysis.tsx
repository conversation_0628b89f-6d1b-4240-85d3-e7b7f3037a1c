"use client";

import React, { useState } from 'react';
import { 
  Target, 
  TrendingUp, 
  Clock, 
  Brain,
  CheckCircle,
  AlertCircle,
  ArrowR<PERSON>,
  RefreshCw,
  Play
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { useNegotiationIntegration } from '@/hooks/use-negotiation-integration';
import type { 
  SkillGapAnalysisRequest,
  Difficulty 
} from '@/lib/types/negotiation-simulator';

interface SkillGapAnalysisProps {
  userId: string;
}

export function SkillGapAnalysis({ userId }: SkillGapAnalysisProps) {
  const [isAnalyzing, setIsAnalyzing] = useState(false);
  const [targetDifficulty, setTargetDifficulty] = useState<Difficulty>('EXPERT');
  const [focusAreas, setFocusAreas] = useState<string[]>([]);
  
  const { toast } = useToast();
  const router = useRouter();
  const {
    skillGapAnalysis,
    loading,
    error,
    hasAccess,
    generateSkillGapAnalysis,
    clearError
  } = useNegotiationIntegration();

  const handleAnalyze = async () => {
    if (!hasAccess) {
      toast({
        title: "Subscription Required",
        description: "Skill gap analysis requires PRO subscription",
        variant: "destructive",
      });
      return;
    }

    setIsAnalyzing(true);
    try {
      const request: SkillGapAnalysisRequest = {
        targetDifficulty,
        focusAreas: focusAreas.length > 0 ? focusAreas : undefined,
      };

      await generateSkillGapAnalysis(userId, request);
    } catch (error) {
      console.error('Failed to generate skill gap analysis:', error);
    } finally {
      setIsAnalyzing(false);
    }
  };

  const handlePracticeScenario = (scenarioId: string) => {
    router.push(`/negotiation-simulator/scenarios/${scenarioId}`);
  };

  const getGapSeverity = (gap: string) => {
    // Simple heuristic based on common negotiation skills
    const criticalGaps = ['communication', 'strategy', 'preparation'];
    const moderateGaps = ['timing', 'concessions', 'relationship'];
    
    if (criticalGaps.some(critical => gap.toLowerCase().includes(critical))) {
      return { level: 'high', color: 'text-red-600', icon: AlertCircle };
    } else if (moderateGaps.some(moderate => gap.toLowerCase().includes(moderate))) {
      return { level: 'medium', color: 'text-yellow-600', icon: Target };
    } else {
      return { level: 'low', color: 'text-green-600', icon: CheckCircle };
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority.toLowerCase()) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  if (!hasAccess) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Skill Gap Analysis
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground">
              Skill gap analysis requires PRO subscription
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Skill Gap Analysis
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="space-y-4">
            <Skeleton className="h-4 w-32" />
            <Skeleton className="h-10 w-full" />
          </div>
          <div className="space-y-4">
            <Skeleton className="h-4 w-24" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Brain className="h-5 w-5" />
              Skill Gap Analysis
            </div>
            <Button variant="outline" size="sm" onClick={clearError}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground mb-2">
              Failed to load skill gap analysis
            </p>
            <p className="text-xs text-muted-foreground">
              {error}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Brain className="h-5 w-5" />
          Skill Gap Analysis
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Analysis Controls */}
        {!skillGapAnalysis && (
          <div className="space-y-4">
            <div className="space-y-2">
              <Label>Target Skill Level</Label>
              <Select value={targetDifficulty} onValueChange={(value: Difficulty) => setTargetDifficulty(value)}>
                <SelectTrigger>
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="INTERMEDIATE">Intermediate</SelectItem>
                  <SelectItem value="EXPERT">Expert</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <Button onClick={handleAnalyze} disabled={isAnalyzing} className="w-full">
              {isAnalyzing ? (
                <>
                  <RefreshCw className="h-4 w-4 mr-2 animate-spin" />
                  Analyzing...
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4 mr-2" />
                  Analyze Skill Gaps
                </>
              )}
            </Button>
          </div>
        )}

        {/* Analysis Results */}
        {skillGapAnalysis && (
          <div className="space-y-6">
            {/* Current vs Target */}
            <div className="grid grid-cols-2 gap-4">
              <div className="text-center p-4 border rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">Current Level</div>
                <Badge className="text-sm">
                  {skillGapAnalysis.currentSkillLevel}
                </Badge>
              </div>
              <div className="text-center p-4 border rounded-lg">
                <div className="text-sm text-muted-foreground mb-1">Target Level</div>
                <Badge variant="outline" className="text-sm">
                  {skillGapAnalysis.targetSkillLevel}
                </Badge>
              </div>
            </div>

            {/* Identified Gaps */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Identified Skill Gaps</h4>
              <div className="space-y-2">
                {skillGapAnalysis.identifiedGaps.map((gap, index) => {
                  const severity = getGapSeverity(gap);
                  const Icon = severity.icon;
                  
                  return (
                    <div key={index} className="flex items-center gap-3 p-3 border rounded-lg">
                      <Icon className={`h-4 w-4 ${severity.color}`} />
                      <span className="text-sm flex-1">{gap}</span>
                      <Badge className={getPriorityColor(severity.level)}>
                        {severity.level}
                      </Badge>
                    </div>
                  );
                })}
              </div>
            </div>

            {/* Recommended Practice */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Recommended Practice</h4>
              <div className="p-4 bg-muted/50 rounded-lg space-y-3">
                <div className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  <span className="text-sm">
                    Estimated practice time: {skillGapAnalysis.recommendedPractice.estimatedPracticeTime} hours
                  </span>
                </div>
                
                <div className="space-y-2">
                  <span className="text-sm font-medium">Focus Areas:</span>
                  <div className="flex flex-wrap gap-2">
                    {skillGapAnalysis.recommendedPractice.focusAreas.map((area, index) => (
                      <Badge key={index} variant="secondary" className="text-xs">
                        {area}
                      </Badge>
                    ))}
                  </div>
                </div>

                {skillGapAnalysis.recommendedPractice.scenarios.length > 0 && (
                  <div className="space-y-2">
                    <span className="text-sm font-medium">Recommended Scenarios:</span>
                    <div className="space-y-2">
                      {skillGapAnalysis.recommendedPractice.scenarios.slice(0, 3).map((scenarioId, index) => (
                        <div key={index} className="flex items-center justify-between p-2 border rounded">
                          <span className="text-sm">Practice Scenario {index + 1}</span>
                          <Button size="sm" variant="ghost" onClick={() => handlePracticeScenario(scenarioId)}>
                            <Play className="h-3 w-3 mr-1" />
                            Practice
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>

            {/* Improvement Plan */}
            <div className="space-y-4">
              <h4 className="text-sm font-medium">Improvement Plan</h4>
              <div className="space-y-3">
                {skillGapAnalysis.improvementPlan.shortTerm.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">Short Term</Badge>
                      <span className="text-sm text-muted-foreground">Next 2-4 weeks</span>
                    </div>
                    <ul className="text-sm space-y-1 ml-4">
                      {skillGapAnalysis.improvementPlan.shortTerm.map((item, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <ArrowRight className="h-3 w-3 mt-0.5 text-muted-foreground" />
                          {item}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}

                {skillGapAnalysis.improvementPlan.mediumTerm.length > 0 && (
                  <div className="space-y-2">
                    <div className="flex items-center gap-2">
                      <Badge variant="outline" className="text-xs">Medium Term</Badge>
                      <span className="text-sm text-muted-foreground">1-3 months</span>
                    </div>
                    <ul className="text-sm space-y-1 ml-4">
                      {skillGapAnalysis.improvementPlan.mediumTerm.map((item, index) => (
                        <li key={index} className="flex items-start gap-2">
                          <ArrowRight className="h-3 w-3 mt-0.5 text-muted-foreground" />
                          {item}
                        </li>
                      ))}
                    </ul>
                  </div>
                )}
              </div>
            </div>

            {/* Action Button */}
            <div className="flex justify-center pt-4">
              <Button variant="outline" onClick={() => window.location.reload()}>
                <RefreshCw className="h-4 w-4 mr-2" />
                Run New Analysis
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
