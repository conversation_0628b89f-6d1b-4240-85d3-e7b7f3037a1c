"use client";

import React, { useState } from "react";
import {
	Play,
	MoreH<PERSON>zon<PERSON>,
	Trash2,
	<PERSON><PERSON>,
	Edit,
	Clock,
	Users,
	Target,
	AlertCircle,
} from "lucide-react";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
	AlertDialog,
	AlertDialogAction,
	AlertDialogCancel,
	AlertDialogContent,
	AlertDialogDescription,
	AlertDialogFooter,
	AlertDialogHeader,
	AlertDialogTitle,
} from "@/components/ui/alert-dialog";
import { useRouter } from "next/navigation";
import { useNegotiationSession } from "@/hooks/use-negotiation-simulator";
import type { NegotiationScenario } from "@/lib/types/negotiation-simulator";
import { useCreditUsage } from "@/hooks/use-credit-usage";
import { FeatureCostDisplay } from "@/components/subscription/feature-cost-display";

interface ScenarioCardProps {
	scenario: NegotiationScenario;
	onDelete: (scenarioId: string) => Promise<void>;
}

export function ScenarioCard({ scenario, onDelete }: ScenarioCardProps) {
	const [showDeleteDialog, setShowDeleteDialog] = useState(false);
	const [isDeleting, setIsDeleting] = useState(false);
	const router = useRouter();
	const { startSession } = useNegotiationSession();
	const { canAfford } = useCreditUsage();

	const getDifficultyColor = (difficulty: string) => {
		switch (difficulty) {
			case "BEGINNER":
				return "bg-green-100 text-green-800 border-green-200";
			case "INTERMEDIATE":
				return "bg-yellow-100 text-yellow-800 border-yellow-200";
			case "EXPERT":
				return "bg-red-100 text-red-800 border-red-200";
			default:
				return "bg-gray-100 text-gray-800 border-gray-200";
		}
	};

	const formatIndustry = (industry: string) => {
		return industry.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
	};

	const formatContractType = (type: string) => {
		return type.replace(/_/g, " ").replace(/\b\w/g, (l) => l.toUpperCase());
	};

	const handleStartSession = async () => {
		const session = await startSession({
			scenarioId: scenario.id,
			aiPersonality: {
				aggressiveness: 0.6,
				flexibility: 0.7,
				riskTolerance: 0.5,
				communicationStyle: "DIPLOMATIC",
				decisionSpeed: "MODERATE",
				concessionPattern: "GRADUAL",
			},
		});

		if (session) {
			router.push(`/negotiation-simulator/sessions/${session.id}`);
		}
	};

	const handleDelete = async () => {
		setIsDeleting(true);
		try {
			await onDelete(scenario.id);
			setShowDeleteDialog(false);
		} catch (error) {
			// Error handling is done in the hook
		} finally {
			setIsDeleting(false);
		}
	};

	const handleClone = () => {
		// TODO: Implement clone functionality
		router.push(`/negotiation-simulator/scenarios/create?clone=${scenario.id}`);
	};

	const handleEdit = () => {
		router.push(`/negotiation-simulator/scenarios/${scenario.id}/edit`);
	};

	return (
		<>
			<Card className="group hover:shadow-lg transition-all duration-200 border-2 hover:border-primary/20">
				<CardHeader className="pb-3">
					<div className="flex items-start justify-between">
						<div className="flex-1">
							<CardTitle className="text-lg line-clamp-2 group-hover:text-primary transition-colors">
								{scenario.name}
							</CardTitle>
							<div className="flex items-center gap-2 mt-2">
								<Badge
									className={getDifficultyColor(scenario.difficulty)}
									variant="outline"
								>
									{scenario.difficulty.toLowerCase()}
								</Badge>
								<Badge variant="secondary" className="text-xs">
									{formatIndustry(scenario.industry)}
								</Badge>
							</div>
						</div>

						<DropdownMenu>
							<DropdownMenuTrigger asChild>
								<Button
									variant="ghost"
									size="sm"
									className="opacity-0 group-hover:opacity-100 transition-opacity"
								>
									<MoreHorizontal className="h-4 w-4" />
								</Button>
							</DropdownMenuTrigger>
							<DropdownMenuContent align="end">
								<DropdownMenuItem onClick={handleEdit}>
									<Edit className="h-4 w-4 mr-2" />
									Edit
								</DropdownMenuItem>
								<DropdownMenuItem onClick={handleClone}>
									<Copy className="h-4 w-4 mr-2" />
									Clone
								</DropdownMenuItem>
								<DropdownMenuSeparator />
								<DropdownMenuItem
									onClick={() => setShowDeleteDialog(true)}
									className="text-red-600 focus:text-red-600"
								>
									<Trash2 className="h-4 w-4 mr-2" />
									Delete
								</DropdownMenuItem>
							</DropdownMenuContent>
						</DropdownMenu>
					</div>
				</CardHeader>

				<CardContent className="space-y-4">
					{/* Description */}
					<p className="text-sm text-muted-foreground line-clamp-3">
						{scenario.description}
					</p>

					{/* Contract Type */}
					<div className="flex items-center gap-2 text-sm">
						<Target className="h-4 w-4 text-muted-foreground" />
						<span className="text-muted-foreground">Contract:</span>
						<span className="font-medium">
							{formatContractType(scenario.contractType)}
						</span>
					</div>

					{/* Parties */}
					<div className="flex items-center gap-2 text-sm">
						<Users className="h-4 w-4 text-muted-foreground" />
						<span className="text-muted-foreground">Parties:</span>
						<span className="font-medium">{scenario.parties.length}</span>
					</div>

					{/* Timeline */}
					<div className="flex items-center gap-2 text-sm">
						<Clock className="h-4 w-4 text-muted-foreground" />
						<span className="text-muted-foreground">Duration:</span>
						<span className="font-medium">
							{scenario.timeline.expectedDuration}min
						</span>
					</div>

					{/* Constraints Preview */}
					<div className="space-y-2">
						<div className="text-xs text-muted-foreground">Constraints:</div>
						<div className="flex flex-wrap gap-1">
							<Badge variant="outline" className="text-xs">
								Max {scenario.constraints.maxRounds} rounds
							</Badge>
							{scenario.constraints.mustHaveTerms.length > 0 && (
								<Badge variant="outline" className="text-xs">
									{scenario.constraints.mustHaveTerms.length} required terms
								</Badge>
							)}
							{scenario.constraints.dealBreakers.length > 0 && (
								<Badge variant="outline" className="text-xs text-red-600">
									{scenario.constraints.dealBreakers.length} deal breakers
								</Badge>
							)}
						</div>
					</div>

					{/* Tags */}
					{scenario.tags.length > 0 && (
						<div className="flex flex-wrap gap-1">
							{scenario.tags.slice(0, 3).map((tag, index) => (
								<Badge key={index} variant="secondary" className="text-xs">
									{tag}
								</Badge>
							))}
							{scenario.tags.length > 3 && (
								<Badge variant="secondary" className="text-xs">
									+{scenario.tags.length - 3} more
								</Badge>
							)}
						</div>
					)}

					{/* Credit Cost Display */}
					<div className="flex items-center justify-between pt-2 border-t">
						<FeatureCostDisplay
							featureName="negotiation_simulator"
							variant="tooltip"
							showDescription={false}
						/>
					</div>

					{/* Action Buttons */}
					<div className="flex gap-2 pt-2">
						<Button
							onClick={handleStartSession}
							className="flex-1 gap-2"
							size="sm"
							disabled={!canAfford("negotiation_simulator")}
						>
							<Play className="h-4 w-4" />
							Start Session
						</Button>
						<Button
							variant="outline"
							size="sm"
							onClick={() =>
								router.push(`/negotiation-simulator/scenarios/${scenario.id}`)
							}
						>
							View Details
						</Button>
					</div>

					{/* Created Date */}
					<div className="text-xs text-muted-foreground pt-2 border-t">
						Created {new Date(scenario.createdAt).toLocaleDateString()}
					</div>
				</CardContent>
			</Card>

			{/* Delete Confirmation Dialog */}
			<AlertDialog open={showDeleteDialog} onOpenChange={setShowDeleteDialog}>
				<AlertDialogContent>
					<AlertDialogHeader>
						<AlertDialogTitle className="flex items-center gap-2">
							<AlertCircle className="h-5 w-5 text-red-500" />
							Delete Scenario
						</AlertDialogTitle>
						<AlertDialogDescription>
							Are you sure you want to delete "{scenario.name}"? This action
							cannot be undone. All associated sessions will also be removed.
						</AlertDialogDescription>
					</AlertDialogHeader>
					<AlertDialogFooter>
						<AlertDialogCancel disabled={isDeleting}>Cancel</AlertDialogCancel>
						<AlertDialogAction
							onClick={handleDelete}
							disabled={isDeleting}
							className="bg-red-600 hover:bg-red-700"
						>
							{isDeleting ? "Deleting..." : "Delete"}
						</AlertDialogAction>
					</AlertDialogFooter>
				</AlertDialogContent>
			</AlertDialog>
		</>
	);
}
