"use client";

import React, { useState, useEffect } from 'react';
import { 
  DollarSign, 
  Calendar, 
  Clock, 
  Users, 
  Shield, 
  Truck, 
  FileText,
  Plus,
  Minus,
  Info
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Badge } from '@/components/ui/badge';
import { Separator } from '@/components/ui/separator';
import { Toolt<PERSON>, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Dynamic field types
export interface DynamicField {
  id: string;
  type: 'number' | 'text' | 'select' | 'textarea' | 'date' | 'currency' | 'percentage' | 'duration';
  label: string;
  placeholder?: string;
  required?: boolean;
  options?: { value: string; label: string }[];
  min?: number;
  max?: number;
  step?: number;
  tooltip?: string;
  category: 'financial' | 'terms' | 'timeline' | 'conditions' | 'custom';
  icon?: React.ComponentType<{ className?: string }>;
  dependsOn?: string; // Field ID this depends on
  showWhen?: (values: Record<string, any>) => boolean;
}

// Negotiation context types
export interface NegotiationContext {
  type: 'software_licensing' | 'real_estate' | 'salary' | 'service_contract' | 'partnership' | 'acquisition';
  phase: 'opening' | 'counter_offer' | 'concessions' | 'final_terms' | 'closing';
  complexity: 'simple' | 'moderate' | 'complex';
  aiPersonality: {
    detailOriented: boolean;
    riskAverse: boolean;
    timeConstrained: boolean;
    relationshipFocused: boolean;
  };
  previousMoves: number;
  currentTerms?: Record<string, any>;
}

// Move templates for different scenarios
const MOVE_TEMPLATES: Record<string, DynamicField[]> = {
  software_licensing: [
    {
      id: 'license_fee',
      type: 'currency',
      label: 'License Fee',
      placeholder: '50000',
      required: true,
      category: 'financial',
      icon: DollarSign,
      tooltip: 'Annual or one-time license fee'
    },
    {
      id: 'user_count',
      type: 'number',
      label: 'Number of Users',
      placeholder: '100',
      min: 1,
      max: 10000,
      category: 'terms',
      icon: Users,
      tooltip: 'Maximum number of licensed users'
    },
    {
      id: 'contract_duration',
      type: 'select',
      label: 'Contract Duration',
      required: true,
      category: 'timeline',
      icon: Calendar,
      options: [
        { value: '1_year', label: '1 Year' },
        { value: '2_years', label: '2 Years' },
        { value: '3_years', label: '3 Years' },
        { value: '5_years', label: '5 Years' }
      ]
    },
    {
      id: 'support_level',
      type: 'select',
      label: 'Support Level',
      category: 'terms',
      icon: Shield,
      options: [
        { value: 'basic', label: 'Basic (Email)' },
        { value: 'standard', label: 'Standard (Phone + Email)' },
        { value: 'premium', label: 'Premium (24/7 + Dedicated)' }
      ]
    },
    {
      id: 'implementation_fee',
      type: 'currency',
      label: 'Implementation Fee',
      placeholder: '10000',
      category: 'financial',
      icon: DollarSign,
      tooltip: 'One-time setup and implementation cost'
    },
    {
      id: 'payment_terms',
      type: 'select',
      label: 'Payment Terms',
      category: 'financial',
      options: [
        { value: 'annual', label: 'Annual Upfront' },
        { value: 'quarterly', label: 'Quarterly' },
        { value: 'monthly', label: 'Monthly' }
      ]
    }
  ],
  
  real_estate: [
    {
      id: 'purchase_price',
      type: 'currency',
      label: 'Purchase Price',
      placeholder: '500000',
      required: true,
      category: 'financial',
      icon: DollarSign
    },
    {
      id: 'down_payment',
      type: 'percentage',
      label: 'Down Payment',
      placeholder: '20',
      min: 0,
      max: 100,
      category: 'financial',
      icon: DollarSign,
      tooltip: 'Percentage of purchase price'
    },
    {
      id: 'closing_date',
      type: 'date',
      label: 'Closing Date',
      required: true,
      category: 'timeline',
      icon: Calendar
    },
    {
      id: 'inspection_period',
      type: 'duration',
      label: 'Inspection Period',
      placeholder: '14',
      category: 'conditions',
      icon: Clock,
      tooltip: 'Days for property inspection'
    },
    {
      id: 'financing_contingency',
      type: 'select',
      label: 'Financing Contingency',
      category: 'conditions',
      options: [
        { value: 'yes', label: 'Yes' },
        { value: 'no', label: 'No (Cash)' },
        { value: 'pre_approved', label: 'Pre-approved Only' }
      ]
    },
    {
      id: 'included_items',
      type: 'textarea',
      label: 'Included Items',
      placeholder: 'Appliances, fixtures, etc.',
      category: 'conditions'
    }
  ],

  salary: [
    {
      id: 'base_salary',
      type: 'currency',
      label: 'Base Salary',
      placeholder: '120000',
      required: true,
      category: 'financial',
      icon: DollarSign
    },
    {
      id: 'bonus_target',
      type: 'percentage',
      label: 'Bonus Target',
      placeholder: '20',
      min: 0,
      max: 100,
      category: 'financial',
      icon: DollarSign,
      tooltip: 'Percentage of base salary'
    },
    {
      id: 'equity_grant',
      type: 'number',
      label: 'Equity Grant (Shares)',
      placeholder: '1000',
      category: 'financial',
      tooltip: 'Number of stock options or shares'
    },
    {
      id: 'start_date',
      type: 'date',
      label: 'Start Date',
      required: true,
      category: 'timeline',
      icon: Calendar
    },
    {
      id: 'vacation_days',
      type: 'number',
      label: 'Vacation Days',
      placeholder: '20',
      min: 0,
      max: 50,
      category: 'terms',
      tooltip: 'Annual vacation days'
    },
    {
      id: 'remote_work',
      type: 'select',
      label: 'Remote Work',
      category: 'terms',
      options: [
        { value: 'full_remote', label: 'Fully Remote' },
        { value: 'hybrid', label: 'Hybrid (2-3 days)' },
        { value: 'office_only', label: 'Office Only' }
      ]
    },
    {
      id: 'title',
      type: 'text',
      label: 'Job Title',
      placeholder: 'Senior Software Engineer',
      category: 'terms'
    }
  ]
};

interface DynamicMoveFormProps {
  context: NegotiationContext;
  onSubmit: (moveData: any) => void;
  disabled?: boolean;
  loading?: boolean;
}

export function DynamicMoveForm({ context, onSubmit, disabled, loading }: DynamicMoveFormProps) {
  const [formData, setFormData] = useState<Record<string, any>>({});
  const [message, setMessage] = useState('');
  const [strategy, setStrategy] = useState('COLLABORATIVE');
  const [customFields, setCustomFields] = useState<DynamicField[]>([]);

  // Get fields based on negotiation type and context
  const getRelevantFields = (): DynamicField[] => {
    const baseFields = MOVE_TEMPLATES[context.type] || [];
    
    // Filter fields based on context
    let relevantFields = baseFields.filter(field => {
      // Show fewer fields for simple negotiations
      if (context.complexity === 'simple' && field.category === 'conditions') {
        return false;
      }
      
      // Hide advanced fields in opening moves
      if (context.phase === 'opening' && field.id.includes('fee') && !field.required) {
        return false;
      }
      
      // Show dependency-based fields
      if (field.showWhen) {
        return field.showWhen(formData);
      }
      
      return true;
    });

    // Add custom fields
    relevantFields = [...relevantFields, ...customFields];

    return relevantFields;
  };

  const handleFieldChange = (fieldId: string, value: any) => {
    setFormData(prev => ({
      ...prev,
      [fieldId]: value
    }));
  };

  const addCustomField = () => {
    const newField: DynamicField = {
      id: `custom_${Date.now()}`,
      type: 'text',
      label: 'Custom Term',
      category: 'custom',
      placeholder: 'Enter custom term...'
    };
    setCustomFields(prev => [...prev, newField]);
  };

  const removeCustomField = (fieldId: string) => {
    setCustomFields(prev => prev.filter(f => f.id !== fieldId));
    setFormData(prev => {
      const newData = { ...prev };
      delete newData[fieldId];
      return newData;
    });
  };

  const renderField = (field: DynamicField) => {
    const Icon = field.icon;
    
    const fieldElement = (() => {
      switch (field.type) {
        case 'currency':
          return (
            <div className="flex gap-2">
              <div className="relative flex-1">
                <DollarSign className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
                <Input
                  type="number"
                  value={formData[field.id] || ''}
                  onChange={(e) => handleFieldChange(field.id, parseFloat(e.target.value) || 0)}
                  placeholder={field.placeholder}
                  className="pl-10"
                  min={field.min}
                  max={field.max}
                  step={field.step}
                />
              </div>
              <Select 
                value={formData[`${field.id}_currency`] || 'USD'} 
                onValueChange={(value) => handleFieldChange(`${field.id}_currency`, value)}
              >
                <SelectTrigger className="w-20">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="USD">USD</SelectItem>
                  <SelectItem value="EUR">EUR</SelectItem>
                  <SelectItem value="GBP">GBP</SelectItem>
                </SelectContent>
              </Select>
            </div>
          );

        case 'percentage':
          return (
            <div className="relative">
              <Input
                type="number"
                value={formData[field.id] || ''}
                onChange={(e) => handleFieldChange(field.id, parseFloat(e.target.value) || 0)}
                placeholder={field.placeholder}
                className="pr-8"
                min={field.min}
                max={field.max}
                step={field.step || 0.1}
              />
              <span className="absolute right-3 top-1/2 transform -translate-y-1/2 text-muted-foreground">%</span>
            </div>
          );

        case 'select':
          return (
            <Select 
              value={formData[field.id] || ''} 
              onValueChange={(value) => handleFieldChange(field.id, value)}
            >
              <SelectTrigger>
                <SelectValue placeholder={`Select ${field.label.toLowerCase()}`} />
              </SelectTrigger>
              <SelectContent>
                {field.options?.map(option => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          );

        case 'date':
          return (
            <Input
              type="date"
              value={formData[field.id] || ''}
              onChange={(e) => handleFieldChange(field.id, e.target.value)}
            />
          );

        case 'duration':
          return (
            <div className="flex gap-2 items-center">
              <Input
                type="number"
                value={formData[field.id] || ''}
                onChange={(e) => handleFieldChange(field.id, parseInt(e.target.value) || 0)}
                placeholder={field.placeholder}
                className="flex-1"
                min={field.min}
                max={field.max}
              />
              <span className="text-sm text-muted-foreground">days</span>
            </div>
          );

        case 'textarea':
          return (
            <Textarea
              value={formData[field.id] || ''}
              onChange={(e) => handleFieldChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              className="min-h-[80px]"
            />
          );

        default:
          return (
            <Input
              type={field.type}
              value={formData[field.id] || ''}
              onChange={(e) => handleFieldChange(field.id, e.target.value)}
              placeholder={field.placeholder}
              min={field.min}
              max={field.max}
              step={field.step}
            />
          );
      }
    })();

    return (
      <div key={field.id} className="space-y-2">
        <div className="flex items-center gap-2">
          {Icon && <Icon className="h-4 w-4 text-muted-foreground" />}
          <Label className="flex items-center gap-1">
            {field.label}
            {field.required && <span className="text-red-500">*</span>}
            {field.tooltip && (
              <TooltipProvider>
                <Tooltip>
                  <TooltipTrigger>
                    <Info className="h-3 w-3 text-muted-foreground" />
                  </TooltipTrigger>
                  <TooltipContent>
                    <p className="max-w-xs">{field.tooltip}</p>
                  </TooltipContent>
                </Tooltip>
              </TooltipProvider>
            )}
          </Label>
          {field.category === 'custom' && (
            <Button
              type="button"
              variant="ghost"
              size="sm"
              onClick={() => removeCustomField(field.id)}
              className="h-6 w-6 p-0 text-red-500 hover:text-red-700"
            >
              <Minus className="h-3 w-3" />
            </Button>
          )}
        </div>
        {fieldElement}
      </div>
    );
  };

  const handleSubmit = () => {
    const moveData = {
      type: context.type,
      phase: context.phase,
      terms: formData,
      message: message.trim(),
      strategy,
      timestamp: new Date().toISOString()
    };
    
    onSubmit(moveData);
  };

  const relevantFields = getRelevantFields();
  const fieldsByCategory = relevantFields.reduce((acc, field) => {
    if (!acc[field.category]) acc[field.category] = [];
    acc[field.category].push(field);
    return acc;
  }, {} as Record<string, DynamicField[]>);

  const categoryLabels = {
    financial: 'Financial Terms',
    terms: 'Contract Terms',
    timeline: 'Timeline',
    conditions: 'Conditions',
    custom: 'Custom Terms'
  };

  const categoryIcons = {
    financial: DollarSign,
    terms: FileText,
    timeline: Calendar,
    conditions: Shield,
    custom: Plus
  };

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Make Your Move</span>
          <Badge variant="outline" className="capitalize">
            {context.type.replace('_', ' ')} • {context.phase.replace('_', ' ')}
          </Badge>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Dynamic Fields by Category */}
        {Object.entries(fieldsByCategory).map(([category, fields]) => {
          const CategoryIcon = categoryIcons[category as keyof typeof categoryIcons];
          return (
            <div key={category} className="space-y-4">
              <div className="flex items-center gap-2">
                <CategoryIcon className="h-4 w-4 text-muted-foreground" />
                <h4 className="font-medium text-sm">{categoryLabels[category as keyof typeof categoryLabels]}</h4>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4 pl-6">
                {fields.map(renderField)}
              </div>
              {category !== 'custom' && <Separator />}
            </div>
          );
        })}

        {/* Add Custom Field Button */}
        <Button
          type="button"
          variant="outline"
          onClick={addCustomField}
          className="w-full gap-2"
        >
          <Plus className="h-4 w-4" />
          Add Custom Term
        </Button>

        {/* Message */}
        <div className="space-y-2">
          <Label>Your Message</Label>
          <Textarea
            value={message}
            onChange={(e) => setMessage(e.target.value)}
            placeholder="Explain your position and reasoning..."
            className="min-h-[100px]"
          />
        </div>

        {/* Strategy */}
        <div className="space-y-2">
          <Label>Strategy</Label>
          <Select value={strategy} onValueChange={setStrategy}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="COLLABORATIVE">Collaborative</SelectItem>
              <SelectItem value="COMPETITIVE">Competitive</SelectItem>
              <SelectItem value="ACCOMMODATING">Accommodating</SelectItem>
              <SelectItem value="AVOIDING">Avoiding</SelectItem>
              <SelectItem value="COMPROMISING">Compromising</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <Button 
          onClick={handleSubmit}
          disabled={disabled || !message.trim() || loading}
          className="w-full"
        >
          {loading ? 'Sending Move...' : 'Send Move'}
        </Button>
      </CardContent>
    </Card>
  );
}
