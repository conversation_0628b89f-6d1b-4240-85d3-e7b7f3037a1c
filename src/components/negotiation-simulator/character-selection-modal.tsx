"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  <PERSON>, 
  Lock, 
  Heart, 
  Shield, 
  TrendingUp, 
  X, 
  Info,
  CheckCircle,
  Award
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';

interface AICharacter {
  id: string;
  name: string;
  title: string;
  company: string;
  avatar: string;
  difficulty: number;
  specialties: string[];
  backstory: string;
  unlocked: boolean;
  unlockRequirements?: {
    level?: number;
    achievements?: string[];
    sessionsCompleted?: number;
  };
}

interface CharacterRelationship {
  respectLevel: number;
  trustLevel: number;
  status: string;
  totalInteractions: number;
  bonuses: {
    betterStartingTerms: boolean;
    increasedFlexibility: number;
    insiderInformation: boolean;
  };
}

interface CharacterSelectionModalProps {
  isOpen: boolean;
  onClose: () => void;
  onSelectCharacter: (character: <PERSON><PERSON>haracter) => void;
  characters: AICharacter[];
  relationships: Record<string, CharacterRelationship>;
  userLevel: number;
}

const difficultyColors = {
  1: 'text-green-600 bg-green-100',
  2: 'text-blue-600 bg-blue-100',
  3: 'text-yellow-600 bg-yellow-100',
  4: 'text-orange-600 bg-orange-100',
  5: 'text-red-600 bg-red-100'
};

export function CharacterSelectionModal({
  isOpen,
  onClose,
  onSelectCharacter,
  characters,
  relationships,
  userLevel
}: CharacterSelectionModalProps) {
  const [selectedCharacter, setSelectedCharacter] = useState<AICharacter | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const handleSelectCharacter = (character: AICharacter) => {
    if (!character.unlocked) return;
    setSelectedCharacter(character);
    setShowDetails(true);
  };

  const handleConfirmSelection = () => {
    if (selectedCharacter) {
      onSelectCharacter(selectedCharacter);
      onClose();
    }
  };

  const getRelationshipStatus = (characterId: string) => {
    const relationship = relationships[characterId];
    if (!relationship) return null;
    
    return {
      respectLevel: relationship.respectLevel,
      trustLevel: relationship.trustLevel,
      status: relationship.status,
      interactions: relationship.totalInteractions
    };
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2 text-2xl">
            <Award className="h-6 w-6" />
            Choose Your Negotiation Opponent
          </DialogTitle>
          <p className="text-muted-foreground">
            Select an AI character to practice your negotiation skills with. Each character has unique personality traits and difficulty levels.
          </p>
        </DialogHeader>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4 mt-6">
          {characters.map((character) => {
            const relationship = getRelationshipStatus(character.id);
            const isLocked = !character.unlocked;

            return (
              <motion.div
                key={character.id}
                whileHover={{ scale: isLocked ? 1 : 1.02 }}
                whileTap={{ scale: isLocked ? 1 : 0.98 }}
              >
                <Card 
                  className={`cursor-pointer transition-all duration-200 ${
                    isLocked 
                      ? 'opacity-60 cursor-not-allowed' 
                      : 'hover:shadow-lg border-2 hover:border-purple-300'
                  }`}
                  onClick={() => handleSelectCharacter(character)}
                >
                  <CardHeader className="pb-3">
                    <div className="flex items-start justify-between">
                      <div className="flex items-center gap-3">
                        {/* Character Avatar */}
                        <div className="relative">
                          <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                            {character.name.split(' ').map(n => n[0]).join('')}
                          </div>
                          {isLocked && (
                            <div className="absolute inset-0 bg-black/50 rounded-full flex items-center justify-center">
                              <Lock className="h-4 w-4 text-white" />
                            </div>
                          )}
                        </div>
                        
                        <div>
                          <h3 className="font-bold text-lg">{character.name}</h3>
                          <p className="text-sm text-muted-foreground">{character.title}</p>
                        </div>
                      </div>

                      {/* Difficulty Stars */}
                      <div className="flex items-center gap-1">
                        {[...Array(5)].map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${
                              i < character.difficulty
                                ? 'text-yellow-400 fill-yellow-400'
                                : 'text-gray-300'
                            }`}
                          />
                        ))}
                      </div>
                    </div>
                  </CardHeader>

                  <CardContent className="space-y-3">
                    {/* Company */}
                    <p className="text-sm text-muted-foreground">{character.company}</p>

                    {/* Specialties */}
                    <div className="flex flex-wrap gap-1">
                      {character.specialties.slice(0, 2).map((specialty) => (
                        <Badge key={specialty} variant="outline" className="text-xs">
                          {specialty}
                        </Badge>
                      ))}
                      {character.specialties.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{character.specialties.length - 2} more
                        </Badge>
                      )}
                    </div>

                    {/* Difficulty Badge */}
                    <Badge className={difficultyColors[character.difficulty as keyof typeof difficultyColors]}>
                      Difficulty {character.difficulty}/5
                    </Badge>

                    {/* Relationship Status */}
                    {relationship && !isLocked && (
                      <div className="space-y-2 pt-2 border-t">
                        <div className="flex items-center gap-2 text-sm">
                          <Heart className="h-4 w-4 text-red-500" />
                          <span>Respect: {relationship.respectLevel}%</span>
                        </div>
                        <div className="flex items-center gap-2 text-sm">
                          <Shield className="h-4 w-4 text-blue-500" />
                          <span>Trust: {relationship.trustLevel}%</span>
                        </div>
                        <div className="text-xs text-muted-foreground">
                          {relationship.interactions} previous interactions
                        </div>
                      </div>
                    )}

                    {/* Unlock Requirements */}
                    {isLocked && character.unlockRequirements && (
                      <div className="pt-2 border-t">
                        <p className="text-xs text-muted-foreground mb-2">Unlock Requirements:</p>
                        <div className="space-y-1 text-xs">
                          {character.unlockRequirements.level && (
                            <div className={`flex items-center gap-1 ${
                              userLevel >= character.unlockRequirements.level 
                                ? 'text-green-600' 
                                : 'text-red-600'
                            }`}>
                              {userLevel >= character.unlockRequirements.level ? (
                                <CheckCircle className="h-3 w-3" />
                              ) : (
                                <X className="h-3 w-3" />
                              )}
                              Level {character.unlockRequirements.level}
                            </div>
                          )}
                          {character.unlockRequirements.sessionsCompleted && (
                            <div className="text-red-600 flex items-center gap-1">
                              <X className="h-3 w-3" />
                              Complete {character.unlockRequirements.sessionsCompleted} sessions
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </motion.div>
            );
          })}
        </div>

        {/* Character Details Modal */}
        {showDetails && selectedCharacter && (
          <Dialog open={showDetails} onOpenChange={setShowDetails}>
            <DialogContent className="max-w-2xl">
              <DialogHeader>
                <DialogTitle className="flex items-center gap-3">
                  <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                    {selectedCharacter.name.split(' ').map(n => n[0]).join('')}
                  </div>
                  <div>
                    <h3 className="text-xl">{selectedCharacter.name}</h3>
                    <p className="text-muted-foreground font-normal">
                      {selectedCharacter.title} at {selectedCharacter.company}
                    </p>
                  </div>
                </DialogTitle>
              </DialogHeader>

              <div className="space-y-6">
                {/* Backstory */}
                <div>
                  <h4 className="font-semibold mb-2">Background</h4>
                  <p className="text-muted-foreground">{selectedCharacter.backstory}</p>
                </div>

                {/* Specialties */}
                <div>
                  <h4 className="font-semibold mb-2">Specialties</h4>
                  <div className="flex flex-wrap gap-2">
                    {selectedCharacter.specialties.map((specialty) => (
                      <Badge key={specialty} variant="outline">
                        {specialty}
                      </Badge>
                    ))}
                  </div>
                </div>

                {/* Relationship Details */}
                {getRelationshipStatus(selectedCharacter.id) && (
                  <div>
                    <h4 className="font-semibold mb-3">Your Relationship</h4>
                    <div className="space-y-3">
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Respect Level</span>
                          <span>{getRelationshipStatus(selectedCharacter.id)?.respectLevel}%</span>
                        </div>
                        <Progress value={getRelationshipStatus(selectedCharacter.id)?.respectLevel} />
                      </div>
                      <div>
                        <div className="flex justify-between text-sm mb-1">
                          <span>Trust Level</span>
                          <span>{getRelationshipStatus(selectedCharacter.id)?.trustLevel}%</span>
                        </div>
                        <Progress value={getRelationshipStatus(selectedCharacter.id)?.trustLevel} />
                      </div>
                      <p className="text-sm text-muted-foreground">
                        Status: {getRelationshipStatus(selectedCharacter.id)?.status}
                      </p>
                    </div>
                  </div>
                )}

                {/* Action Buttons */}
                <div className="flex gap-3">
                  <Button onClick={handleConfirmSelection} className="flex-1">
                    Start Negotiation
                  </Button>
                  <Button variant="outline" onClick={() => setShowDetails(false)}>
                    Back to Selection
                  </Button>
                </div>
              </div>
            </DialogContent>
          </Dialog>
        )}
      </DialogContent>
    </Dialog>
  );
}
