"use client";

import React, { useState } from 'react';
import { 
  Trophy, 
  Star, 
  Zap, 
  Award, 
  Target, 
  TrendingUp,
  Users,
  Calendar,
  Play,
  Lock,
  Gift
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { <PERSON><PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { CharacterSelectionModal } from './character-selection-modal';

interface GamificationDashboardProps {
  onStartNegotiation: (characterId: string) => void;
}

// Mock data (will be replaced with real API calls)
const mockUserProfile = {
  level: {
    current: 3,
    title: 'Senior Negotiator',
    currentXP: 1100,
    totalXP: 2600,
    xpToNext: 900,
    progress: 55
  },
  statistics: {
    totalSessions: 25,
    completedSessions: 23,
    averageScore: 8.2,
    winRate: 0.87,
    currentStreak: 5,
    bestStreak: 8,
    achievementsCount: 12
  },
  recentAchievements: [
    {
      id: 'speed_demon',
      title: 'Speed Demon',
      description: 'Close a deal in 3 rounds or less',
      badge: '⚡',
      rarity: 'rare' as const,
      unlockedAt: new Date()
    },
    {
      id: 'win_win_master',
      title: 'Win-Win Master',
      description: 'Both parties 85%+ satisfied',
      badge: '🤝',
      rarity: 'epic' as const,
      unlockedAt: new Date()
    }
  ]
};

const mockCharacters = [
  {
    id: 'sarah_chen',
    name: 'Sarah Chen',
    title: 'Senior Sales Director',
    company: 'TechCorp Solutions',
    avatar: '/avatars/sarah-chen.jpg',
    difficulty: 3,
    specialties: ['Cost reduction', 'Data-driven decisions'],
    backstory: '15 years in enterprise sales, known for analytical approach.',
    unlocked: true
  },
  {
    id: 'marcus_rodriguez',
    name: 'Marcus Rodriguez',
    title: 'VP of Sales',
    company: 'Enterprise Corp',
    avatar: '/avatars/marcus-rodriguez.jpg',
    difficulty: 4,
    specialties: ['Aggressive tactics', 'Deadline pressure'],
    backstory: 'Former consultant, bonus-motivated, deadline-focused.',
    unlocked: true
  },
  {
    id: 'jennifer_liu',
    name: 'Jennifer Liu',
    title: 'Partnership Director',
    company: 'Collaborative Inc',
    avatar: '/avatars/jennifer-liu.jpg',
    difficulty: 2,
    specialties: ['Relationship building', 'Win-win solutions'],
    backstory: 'Partnership-focused, long-term thinker, collaborative.',
    unlocked: true
  },
  {
    id: 'the_shark',
    name: 'The Shark',
    title: 'Wall Street Legend',
    company: 'Elite Investments',
    avatar: '/avatars/the-shark.jpg',
    difficulty: 5,
    specialties: ['Ruthless tactics', 'High-pressure deals'],
    backstory: 'Legendary negotiator from Wall Street.',
    unlocked: false,
    unlockRequirements: {
      level: 5,
      achievements: ['master_negotiator'],
      sessionsCompleted: 50
    }
  }
];

const mockRelationships = {
  'sarah_chen': {
    respectLevel: 75,
    trustLevel: 60,
    status: 'Professional Respect',
    totalInteractions: 8,
    bonuses: {
      betterStartingTerms: true,
      increasedFlexibility: 0.1,
      insiderInformation: false
    }
  },
  'marcus_rodriguez': {
    respectLevel: 45,
    trustLevel: 30,
    status: 'Cautious',
    totalInteractions: 3,
    bonuses: {
      betterStartingTerms: false,
      increasedFlexibility: 0,
      insiderInformation: false
    }
  },
  'jennifer_liu': {
    respectLevel: 90,
    trustLevel: 85,
    status: 'Trusted Partner',
    totalInteractions: 12,
    bonuses: {
      betterStartingTerms: true,
      increasedFlexibility: 0.2,
      insiderInformation: true
    }
  }
};

export function GamificationDashboard({ onStartNegotiation }: GamificationDashboardProps) {
  const [showCharacterSelection, setShowCharacterSelection] = useState(false);

  const handleStartPractice = () => {
    setShowCharacterSelection(true);
  };

  const handleCharacterSelect = (character: any) => {
    onStartNegotiation(character.id);
    setShowCharacterSelection(false);
  };

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="text-center space-y-4">
        <h1 className="text-3xl font-bold">Negotiation Practice Arena</h1>
        <p className="text-muted-foreground max-w-2xl mx-auto">
          Master the art of negotiation through gamified practice sessions. 
          Level up your skills, unlock new characters, and compete with colleagues.
        </p>
      </div>

      {/* User Level & Progress */}
      <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
        <CardContent className="p-6">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-4">
              <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center">
                <Trophy className="h-8 w-8 text-white" />
              </div>
              <div>
                <h2 className="text-2xl font-bold">{mockUserProfile.level.title}</h2>
                <p className="text-muted-foreground">Level {mockUserProfile.level.current}</p>
              </div>
            </div>
            
            <div className="text-right space-y-2">
              <div className="flex items-center gap-2">
                <Zap className="h-4 w-4 text-purple-600" />
                <span className="text-sm">
                  {mockUserProfile.level.currentXP.toLocaleString()} / {(mockUserProfile.level.currentXP + mockUserProfile.level.xpToNext).toLocaleString()} XP
                </span>
              </div>
              <Progress value={mockUserProfile.level.progress} className="w-48 h-2" />
              <p className="text-xs text-muted-foreground">
                {mockUserProfile.level.xpToNext.toLocaleString()} XP to next level
              </p>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quick Stats */}
      <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <Target className="h-8 w-8 mx-auto mb-2 text-blue-600" />
            <div className="text-2xl font-bold">{mockUserProfile.statistics.averageScore}</div>
            <div className="text-sm text-muted-foreground">Average Score</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <TrendingUp className="h-8 w-8 mx-auto mb-2 text-green-600" />
            <div className="text-2xl font-bold">{Math.round(mockUserProfile.statistics.winRate * 100)}%</div>
            <div className="text-sm text-muted-foreground">Win Rate</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Zap className="h-8 w-8 mx-auto mb-2 text-yellow-600" />
            <div className="text-2xl font-bold">{mockUserProfile.statistics.currentStreak}</div>
            <div className="text-sm text-muted-foreground">Current Streak</div>
          </CardContent>
        </Card>
        
        <Card>
          <CardContent className="p-4 text-center">
            <Award className="h-8 w-8 mx-auto mb-2 text-purple-600" />
            <div className="text-2xl font-bold">{mockUserProfile.statistics.achievementsCount}</div>
            <div className="text-sm text-muted-foreground">Achievements</div>
          </CardContent>
        </Card>
      </div>

      {/* Recent Achievements */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Gift className="h-5 w-5" />
            Recent Achievements
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {mockUserProfile.recentAchievements.map((achievement) => (
              <div key={achievement.id} className="flex items-center gap-3 p-3 bg-muted/50 rounded-lg">
                <div className="text-2xl">{achievement.badge}</div>
                <div className="flex-1">
                  <div className="font-medium">{achievement.title}</div>
                  <div className="text-sm text-muted-foreground">{achievement.description}</div>
                </div>
                <Badge className={
                  achievement.rarity === 'epic' 
                    ? 'bg-purple-100 text-purple-800' 
                    : 'bg-blue-100 text-blue-800'
                }>
                  {achievement.rarity}
                </Badge>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* Quick Actions */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <Card className="border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-green-800">
              <Play className="h-5 w-5" />
              Start Practice Session
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-green-700">
              Choose an AI opponent and practice your negotiation skills in a realistic business scenario.
            </p>
            <Button onClick={handleStartPractice} className="w-full bg-green-600 hover:bg-green-700">
              <Play className="h-4 w-4 mr-2" />
              Choose Opponent
            </Button>
          </CardContent>
        </Card>

        <Card className="border-blue-200 bg-gradient-to-br from-blue-50 to-cyan-50">
          <CardHeader>
            <CardTitle className="flex items-center gap-2 text-blue-800">
              <Users className="h-5 w-5" />
              Team Challenges
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <p className="text-sm text-blue-700">
              Compete with your colleagues in weekly challenges and climb the leaderboard.
            </p>
            <Button variant="outline" className="w-full border-blue-300 text-blue-700 hover:bg-blue-50">
              <Calendar className="h-4 w-4 mr-2" />
              View Challenges
            </Button>
          </CardContent>
        </Card>
      </div>

      {/* Character Selection Modal */}
      <CharacterSelectionModal
        isOpen={showCharacterSelection}
        onClose={() => setShowCharacterSelection(false)}
        onSelectCharacter={handleCharacterSelect}
        characters={mockCharacters}
        relationships={mockRelationships}
        userLevel={mockUserProfile.level.current}
      />
    </div>
  );
}
