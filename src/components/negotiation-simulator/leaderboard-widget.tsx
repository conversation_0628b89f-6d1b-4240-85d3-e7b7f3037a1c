"use client";

import React from 'react';
import { Trophy, Medal, Award, TrendingUp, Users } from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';

interface LeaderboardEntry {
  rank: number;
  userId: string;
  name: string;
  title: string;
  organization: string;
  score: number;
  totalDeals: number;
  winRate: number;
  avatar?: string;
}

interface LeaderboardWidgetProps {
  userRank?: number;
  timeframe?: 'weekly' | 'monthly' | 'all_time';
  scope?: 'global' | 'organization';
}

const mockLeaderboardData: LeaderboardEntry[] = [
  {
    rank: 1,
    userId: 'user-1',
    name: '<PERSON>',
    title: 'Senior Partner',
    organization: 'BigLaw Firm',
    score: 9.2,
    totalDeals: 28,
    winRate: 0.93,
  },
  {
    rank: 2,
    userId: 'user-2',
    name: '<PERSON>',
    title: 'Procurement Director',
    organization: 'TechCorp',
    score: 8.9,
    totalDeals: 22,
    winRate: 0.86,
  },
  {
    rank: 3,
    userId: 'user-3',
    name: '<PERSON>',
    title: 'Legal Counsel',
    organization: 'StartupCo',
    score: 8.7,
    totalDeals: 19,
    winRate: 0.84,
  },
  {
    rank: 4,
    userId: 'user-4',
    name: 'David Kim',
    title: 'VP Business Dev',
    organization: 'Enterprise Inc',
    score: 8.5,
    totalDeals: 25,
    winRate: 0.80,
  },
  {
    rank: 5,
    userId: 'user-5',
    name: 'Lisa Wang',
    title: 'Contract Manager',
    organization: 'Global Corp',
    score: 8.3,
    totalDeals: 17,
    winRate: 0.82,
  }
];

const getRankIcon = (rank: number) => {
  switch (rank) {
    case 1:
      return <Trophy className="h-4 w-4 text-yellow-600" />;
    case 2:
      return <Medal className="h-4 w-4 text-gray-500" />;
    case 3:
      return <Award className="h-4 w-4 text-amber-600" />;
    default:
      return <span className="text-sm font-bold text-muted-foreground">#{rank}</span>;
  }
};

const getRankBadgeColor = (rank: number) => {
  switch (rank) {
    case 1:
      return 'bg-yellow-100 text-yellow-800 border-yellow-300';
    case 2:
      return 'bg-gray-100 text-gray-800 border-gray-300';
    case 3:
      return 'bg-amber-100 text-amber-800 border-amber-300';
    default:
      return 'bg-blue-100 text-blue-800 border-blue-300';
  }
};

export function LeaderboardWidget({ 
  userRank = 8, 
  timeframe = 'weekly',
  scope = 'organization' 
}: LeaderboardWidgetProps) {
  const topEntries = mockLeaderboardData.slice(0, 5);

  return (
    <Card className="border-yellow-200 bg-gradient-to-br from-yellow-50 to-orange-50">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-yellow-800">
          <Trophy className="h-5 w-5" />
          Leaderboard
        </CardTitle>
        <div className="flex items-center gap-2 text-sm">
          <Badge variant="outline" className="bg-yellow-100 text-yellow-800 border-yellow-300">
            {timeframe === 'weekly' ? 'This Week' : timeframe === 'monthly' ? 'This Month' : 'All Time'}
          </Badge>
          <Badge variant="outline" className="bg-orange-100 text-orange-800 border-orange-300">
            {scope === 'global' ? 'Global' : 'Organization'}
          </Badge>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {/* Top 5 Rankings */}
        <div className="space-y-2">
          {topEntries.map((entry) => (
            <div
              key={entry.userId}
              className="flex items-center gap-3 p-2 rounded-lg bg-white/50 hover:bg-white/80 transition-colors"
            >
              {/* Rank */}
              <div className="flex items-center justify-center w-8">
                {getRankIcon(entry.rank)}
              </div>

              {/* Avatar */}
              <Avatar className="h-8 w-8">
                <AvatarFallback className="text-xs bg-gradient-to-br from-purple-400 to-blue-500 text-white">
                  {entry.name.split(' ').map(n => n[0]).join('')}
                </AvatarFallback>
              </Avatar>

              {/* User Info */}
              <div className="flex-1 min-w-0">
                <div className="font-medium text-sm truncate">{entry.name}</div>
                <div className="text-xs text-muted-foreground truncate">{entry.title}</div>
              </div>

              {/* Score */}
              <div className="text-right">
                <div className="font-bold text-sm">{entry.score}</div>
                <div className="text-xs text-muted-foreground">{entry.totalDeals} deals</div>
              </div>
            </div>
          ))}
        </div>

        {/* User's Rank */}
        {userRank && userRank > 5 && (
          <div className="pt-3 border-t border-yellow-200">
            <div className="flex items-center gap-3 p-2 rounded-lg bg-blue-50 border border-blue-200">
              <div className="flex items-center justify-center w-8">
                <span className="text-sm font-bold text-blue-600">#{userRank}</span>
              </div>
              
              <Avatar className="h-8 w-8">
                <AvatarFallback className="text-xs bg-gradient-to-br from-blue-400 to-purple-500 text-white">
                  YU
                </AvatarFallback>
              </Avatar>

              <div className="flex-1">
                <div className="font-medium text-sm text-blue-900">You</div>
                <div className="text-xs text-blue-600">Senior Negotiator</div>
              </div>

              <div className="text-right">
                <div className="font-bold text-sm text-blue-900">8.2</div>
                <div className="text-xs text-blue-600">12 deals</div>
              </div>
            </div>
          </div>
        )}

        {/* Quick Stats */}
        <div className="pt-3 border-t border-yellow-200">
          <div className="grid grid-cols-2 gap-3 text-center">
            <div className="bg-white/50 rounded-lg p-2">
              <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground mb-1">
                <Users className="h-3 w-3" />
                <span>Participants</span>
              </div>
              <div className="font-bold text-sm">247</div>
            </div>
            <div className="bg-white/50 rounded-lg p-2">
              <div className="flex items-center justify-center gap-1 text-xs text-muted-foreground mb-1">
                <TrendingUp className="h-3 w-3" />
                <span>Your Trend</span>
              </div>
              <div className="font-bold text-sm text-green-600">↗ +2</div>
            </div>
          </div>
        </div>

        {/* View Full Leaderboard Link */}
        <div className="pt-2">
          <button className="w-full text-center text-xs text-yellow-700 hover:text-yellow-800 font-medium">
            View Full Leaderboard →
          </button>
        </div>
      </CardContent>
    </Card>
  );
}

// Quick Achievement Progress Widget
interface AchievementProgressProps {
  achievements: {
    id: string;
    title: string;
    progress: number;
    total: number;
    badge: string;
  }[];
}

export function AchievementProgress({ achievements }: AchievementProgressProps) {
  return (
    <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50">
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-purple-800">
          <Award className="h-5 w-5" />
          Achievement Progress
        </CardTitle>
      </CardHeader>
      
      <CardContent className="space-y-3">
        {achievements.slice(0, 3).map((achievement) => (
          <div key={achievement.id} className="space-y-2">
            <div className="flex items-center gap-2">
              <span className="text-lg">{achievement.badge}</span>
              <div className="flex-1">
                <div className="font-medium text-sm">{achievement.title}</div>
                <div className="text-xs text-muted-foreground">
                  {achievement.progress}/{achievement.total}
                </div>
              </div>
            </div>
            <div className="w-full bg-gray-200 rounded-full h-2">
              <div 
                className="bg-purple-600 h-2 rounded-full transition-all duration-300"
                style={{ width: `${(achievement.progress / achievement.total) * 100}%` }}
              />
            </div>
          </div>
        ))}
        
        <div className="pt-2">
          <button className="w-full text-center text-xs text-purple-700 hover:text-purple-800 font-medium">
            View All Achievements →
          </button>
        </div>
      </CardContent>
    </Card>
  );
}
