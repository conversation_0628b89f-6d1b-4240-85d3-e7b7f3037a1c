"use client";

import React, { useState } from 'react';
import { Plus, X, Users, Clock, Target } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import type {
  CreateScenarioRequest,
  Industry,
  ContractType,
  Difficulty,
  PartyRole,
  NegotiationStyle
} from '@/lib/types/negotiation-simulator';
import {
  INDUSTRY_OPTIONS,
  CONTRACT_TYPE_OPTIONS,
  DIFFICULTY_OPTIONS
} from '@/lib/types/negotiation-simulator';

interface CreateScenarioModalProps {
  isOpen: boolean;
  onClose: () => void;
  onCreateScenario: (data: CreateScenarioRequest) => Promise<any>;
}

const PARTY_ROLE_OPTIONS = [
  { value: 'BUYER', label: 'Buyer' },
  { value: 'SELLER', label: 'Seller' },
  { value: 'VENDOR', label: 'Vendor' },
  { value: 'CLIENT', label: 'Client' },
  { value: 'CONTRACTOR', label: 'Contractor' },
  { value: 'LICENSOR', label: 'Licensor' },
  { value: 'LICENSEE', label: 'Licensee' },
  { value: 'EMPLOYER', label: 'Employer' },
  { value: 'EMPLOYEE', label: 'Employee' }
];

const NEGOTIATION_STYLE_OPTIONS = [
  { value: 'COLLABORATIVE', label: 'Collaborative' },
  { value: 'COMPETITIVE', label: 'Competitive' },
  { value: 'ANALYTICAL', label: 'Analytical' },
  { value: 'AGGRESSIVE', label: 'Aggressive' },
  { value: 'ACCOMMODATING', label: 'Accommodating' },
  { value: 'DIPLOMATIC', label: 'Diplomatic' }
];

export function CreateScenarioModal({ isOpen, onClose, onCreateScenario }: CreateScenarioModalProps) {
  const [loading, setLoading] = useState(false);
  
  // Basic Info
  const [name, setName] = useState('');
  const [description, setDescription] = useState('');
  const [industry, setIndustry] = useState<Industry>('TECHNOLOGY');
  const [contractType, setContractType] = useState<ContractType>('SOFTWARE_LICENSE');
  const [difficulty, setDifficulty] = useState<Difficulty>('INTERMEDIATE');
  const [tags, setTags] = useState<string[]>([]);
  const [newTag, setNewTag] = useState('');

  // Parties
  const [parties, setParties] = useState([
    {
      role: 'BUYER' as PartyRole,
      priorities: [''],
      negotiationStyle: 'COLLABORATIVE' as NegotiationStyle,
      constraints: {},
      budget: { min: 0, max: 100000, currency: 'USD' }
    }
  ]);

  // Initial Offer
  const [initialOffer, setInitialOffer] = useState({
    price: 50000,
    currency: 'USD',
    paymentTerms: 'Monthly',
    customTerms: {}
  });

  // Constraints
  const [maxRounds, setMaxRounds] = useState(10);
  const [mustHaveTerms, setMustHaveTerms] = useState<string[]>([]);
  const [dealBreakers, setDealBreakers] = useState<string[]>([]);
  const [newMustHaveTerm, setNewMustHaveTerm] = useState('');
  const [newDealBreaker, setNewDealBreaker] = useState('');

  // Timeline
  const [expectedDuration, setExpectedDuration] = useState(30);
  const [maxDuration, setMaxDuration] = useState(60);

  const handleSubmit = async () => {
    setLoading(true);
    try {
      const data: CreateScenarioRequest = {
        name,
        description,
        industry,
        contractType,
        difficulty,
        parties: parties.map(party => ({
          role: party.role,
          priorities: party.priorities.filter(p => p.trim()),
          negotiationStyle: party.negotiationStyle,
          constraints: party.constraints,
          budget: party.budget
        })),
        initialOffer,
        constraints: {
          maxRounds,
          mustHaveTerms: mustHaveTerms.filter(t => t.trim()),
          dealBreakers: dealBreakers.filter(t => t.trim())
        },
        timeline: {
          expectedDuration,
          maxDuration
        },
        tags: tags.filter(t => t.trim())
      };

      await onCreateScenario(data);
      resetForm();
    } finally {
      setLoading(false);
    }
  };

  const resetForm = () => {
    setName('');
    setDescription('');
    setIndustry('TECHNOLOGY');
    setContractType('SOFTWARE_LICENSE');
    setDifficulty('INTERMEDIATE');
    setTags([]);
    setNewTag('');
    setParties([{
      role: 'BUYER' as PartyRole,
      priorities: [''],
      negotiationStyle: 'COLLABORATIVE' as NegotiationStyle,
      constraints: {},
      budget: { min: 0, max: 100000, currency: 'USD' }
    }]);
    setInitialOffer({
      price: 50000,
      currency: 'USD',
      paymentTerms: 'Monthly',
      customTerms: {}
    });
    setMaxRounds(10);
    setMustHaveTerms([]);
    setDealBreakers([]);
    setNewMustHaveTerm('');
    setNewDealBreaker('');
    setExpectedDuration(30);
    setMaxDuration(60);
  };

  const addTag = () => {
    if (newTag.trim() && !tags.includes(newTag.trim())) {
      setTags([...tags, newTag.trim()]);
      setNewTag('');
    }
  };

  const removeTag = (tagToRemove: string) => {
    setTags(tags.filter(tag => tag !== tagToRemove));
  };

  const addParty = () => {
    setParties([...parties, {
      role: 'SELLER' as PartyRole,
      priorities: [''],
      negotiationStyle: 'COLLABORATIVE' as NegotiationStyle,
      constraints: {},
      budget: { min: 0, max: 100000, currency: 'USD' }
    }]);
  };

  const updateParty = (index: number, field: string, value: any) => {
    const updatedParties = [...parties];
    if (field.includes('.')) {
      const [parent, child] = field.split('.');
      updatedParties[index] = {
        ...updatedParties[index],
        [parent]: {
          ...updatedParties[index][parent as keyof typeof updatedParties[index]],
          [child]: value
        }
      };
    } else {
      updatedParties[index] = { ...updatedParties[index], [field]: value };
    }
    setParties(updatedParties);
  };

  const addPriority = (partyIndex: number) => {
    const updatedParties = [...parties];
    updatedParties[partyIndex].priorities.push('');
    setParties(updatedParties);
  };

  const updatePriority = (partyIndex: number, priorityIndex: number, value: string) => {
    const updatedParties = [...parties];
    updatedParties[partyIndex].priorities[priorityIndex] = value;
    setParties(updatedParties);
  };

  const removePriority = (partyIndex: number, priorityIndex: number) => {
    const updatedParties = [...parties];
    updatedParties[partyIndex].priorities.splice(priorityIndex, 1);
    setParties(updatedParties);
  };

  const addMustHaveTerm = () => {
    if (newMustHaveTerm.trim() && !mustHaveTerms.includes(newMustHaveTerm.trim())) {
      setMustHaveTerms([...mustHaveTerms, newMustHaveTerm.trim()]);
      setNewMustHaveTerm('');
    }
  };

  const addDealBreaker = () => {
    if (newDealBreaker.trim() && !dealBreakers.includes(newDealBreaker.trim())) {
      setDealBreakers([...dealBreakers, newDealBreaker.trim()]);
      setNewDealBreaker('');
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Create Negotiation Scenario
          </DialogTitle>
          <DialogDescription>
            Design a custom negotiation scenario with parties, constraints, and objectives.
          </DialogDescription>
        </DialogHeader>

        <Tabs defaultValue="basic" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="basic">Basic Info</TabsTrigger>
            <TabsTrigger value="parties">Parties</TabsTrigger>
            <TabsTrigger value="terms">Terms & Offer</TabsTrigger>
            <TabsTrigger value="constraints">Constraints</TabsTrigger>
          </TabsList>

          <TabsContent value="basic" className="space-y-4">
            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="name">Scenario Name</Label>
                <Input
                  id="name"
                  value={name}
                  onChange={(e) => setName(e.target.value)}
                  placeholder="e.g., Software License Negotiation"
                />
              </div>
              <div className="space-y-2">
                <Label htmlFor="industry">Industry</Label>
                <Select value={industry} onValueChange={(value: Industry) => setIndustry(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {INDUSTRY_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label htmlFor="description">Description</Label>
              <Textarea
                id="description"
                value={description}
                onChange={(e) => setDescription(e.target.value)}
                placeholder="Describe the negotiation scenario and context..."
                className="min-h-[100px]"
              />
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="contractType">Contract Type</Label>
                <Select value={contractType} onValueChange={(value: ContractType) => setContractType(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {CONTRACT_TYPE_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              <div className="space-y-2">
                <Label htmlFor="difficulty">Difficulty</Label>
                <Select value={difficulty} onValueChange={(value: Difficulty) => setDifficulty(value)}>
                  <SelectTrigger>
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    {DIFFICULTY_OPTIONS.map(option => (
                      <SelectItem key={option.value} value={option.value}>
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
            </div>

            <div className="space-y-2">
              <Label>Tags</Label>
              <div className="flex gap-2">
                <Input
                  value={newTag}
                  onChange={(e) => setNewTag(e.target.value)}
                  placeholder="Add a tag..."
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addTag())}
                />
                <Button type="button" onClick={addTag} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {tags.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {tags.map((tag, index) => (
                    <Badge key={index} variant="secondary" className="gap-1">
                      {tag}
                      <button onClick={() => removeTag(tag)}>
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="parties" className="space-y-4">
            {parties.map((party, partyIndex) => (
              <Card key={partyIndex}>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Users className="h-4 w-4" />
                    Party {partyIndex + 1}
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="grid grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label>Role</Label>
                      <Select 
                        value={party.role} 
                        onValueChange={(value: PartyRole) => updateParty(partyIndex, 'role', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {PARTY_ROLE_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label>Negotiation Style</Label>
                      <Select 
                        value={party.negotiationStyle} 
                        onValueChange={(value: NegotiationStyle) => updateParty(partyIndex, 'negotiationStyle', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          {NEGOTIATION_STYLE_OPTIONS.map(option => (
                            <SelectItem key={option.value} value={option.value}>
                              {option.label}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Priorities</Label>
                    {party.priorities.map((priority, priorityIndex) => (
                      <div key={priorityIndex} className="flex gap-2">
                        <Input
                          value={priority}
                          onChange={(e) => updatePriority(partyIndex, priorityIndex, e.target.value)}
                          placeholder="Enter priority..."
                        />
                        {party.priorities.length > 1 && (
                          <Button
                            type="button"
                            variant="outline"
                            size="sm"
                            onClick={() => removePriority(partyIndex, priorityIndex)}
                          >
                            <X className="h-4 w-4" />
                          </Button>
                        )}
                      </div>
                    ))}
                    <Button
                      type="button"
                      variant="outline"
                      size="sm"
                      onClick={() => addPriority(partyIndex)}
                    >
                      <Plus className="h-4 w-4 mr-2" />
                      Add Priority
                    </Button>
                  </div>

                  <div className="grid grid-cols-3 gap-4">
                    <div className="space-y-2">
                      <Label>Min Budget</Label>
                      <Input
                        type="number"
                        value={party.budget.min}
                        onChange={(e) => updateParty(partyIndex, 'budget.min', parseInt(e.target.value) || 0)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Max Budget</Label>
                      <Input
                        type="number"
                        value={party.budget.max}
                        onChange={(e) => updateParty(partyIndex, 'budget.max', parseInt(e.target.value) || 0)}
                      />
                    </div>
                    <div className="space-y-2">
                      <Label>Currency</Label>
                      <Select 
                        value={party.budget.currency} 
                        onValueChange={(value) => updateParty(partyIndex, 'budget.currency', value)}
                      >
                        <SelectTrigger>
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="GBP">GBP</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
            
            <Button type="button" onClick={addParty} variant="outline" className="w-full">
              <Plus className="h-4 w-4 mr-2" />
              Add Another Party
            </Button>
          </TabsContent>

          <TabsContent value="terms" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Initial Offer</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Price</Label>
                    <Input
                      type="number"
                      value={initialOffer.price}
                      onChange={(e) => setInitialOffer({...initialOffer, price: parseInt(e.target.value) || 0})}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Currency</Label>
                    <Select 
                      value={initialOffer.currency} 
                      onValueChange={(value) => setInitialOffer({...initialOffer, currency: value})}
                    >
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="USD">USD</SelectItem>
                        <SelectItem value="EUR">EUR</SelectItem>
                        <SelectItem value="GBP">GBP</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label>Payment Terms</Label>
                    <Input
                      value={initialOffer.paymentTerms}
                      onChange={(e) => setInitialOffer({...initialOffer, paymentTerms: e.target.value})}
                      placeholder="e.g., Monthly, Quarterly"
                    />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-4 w-4" />
                  Timeline
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label>Expected Duration (minutes)</Label>
                    <Input
                      type="number"
                      value={expectedDuration}
                      onChange={(e) => setExpectedDuration(parseInt(e.target.value) || 30)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label>Maximum Duration (minutes)</Label>
                    <Input
                      type="number"
                      value={maxDuration}
                      onChange={(e) => setMaxDuration(parseInt(e.target.value) || 60)}
                    />
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="constraints" className="space-y-4">
            <div className="space-y-2">
              <Label>Maximum Rounds</Label>
              <Input
                type="number"
                value={maxRounds}
                onChange={(e) => setMaxRounds(parseInt(e.target.value) || 10)}
                min="1"
                max="20"
              />
            </div>

            <div className="space-y-2">
              <Label>Must-Have Terms</Label>
              <div className="flex gap-2">
                <Input
                  value={newMustHaveTerm}
                  onChange={(e) => setNewMustHaveTerm(e.target.value)}
                  placeholder="Add required term..."
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addMustHaveTerm())}
                />
                <Button type="button" onClick={addMustHaveTerm} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {mustHaveTerms.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {mustHaveTerms.map((term, index) => (
                    <Badge key={index} variant="secondary" className="gap-1">
                      {term}
                      <button onClick={() => setMustHaveTerms(mustHaveTerms.filter((_, i) => i !== index))}>
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>

            <div className="space-y-2">
              <Label>Deal Breakers</Label>
              <div className="flex gap-2">
                <Input
                  value={newDealBreaker}
                  onChange={(e) => setNewDealBreaker(e.target.value)}
                  placeholder="Add deal breaker..."
                  onKeyDown={(e) => e.key === 'Enter' && (e.preventDefault(), addDealBreaker())}
                />
                <Button type="button" onClick={addDealBreaker} size="sm">
                  <Plus className="h-4 w-4" />
                </Button>
              </div>
              {dealBreakers.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-2">
                  {dealBreakers.map((breaker, index) => (
                    <Badge key={index} variant="destructive" className="gap-1">
                      {breaker}
                      <button onClick={() => setDealBreakers(dealBreakers.filter((_, i) => i !== index))}>
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </TabsContent>
        </Tabs>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading || !name.trim()}>
            {loading ? 'Creating...' : 'Create Scenario'}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
