"use client";

import React, { useState, useEffect } from 'react';
import {
  MessageSquare,
  Send,
  Pause,
  Play,
  Clock,
  Target,
  Trophy,
  <PERSON>ert<PERSON>riangle,
  <PERSON><PERSON>,
  User,
  DollarSign,
  Star,
  Zap,
  Award,
  TrendingUp,
  Heart,
  Shield,
  Bell,
  Gift
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Separator } from '@/components/ui/separator';
import { useNegotiationSession } from '@/hooks/use-negotiation-simulator';
import type {
  NegotiationSession,
  MoveStrategy,
  Terms
} from '@/lib/types/negotiation-simulator';
import { STRATEGY_OPTIONS } from '@/lib/types/negotiation-simulator';
import { AchievementNotification, XPAnimation, LevelUpModal } from './achievement-notification';
import { CharacterSelectionModal } from './character-selection-modal';
import { LeaderboardWidget, AchievementProgress } from './leaderboard-widget';
import { DynamicMoveForm } from './dynamic-move-form';
import { ScenarioSelection } from './scenario-selection';
import { useGamification } from '@/hooks/use-gamification';
import { useGamificationSocket, useAchievementNotifications, usePressureEvents } from '@/hooks/use-gamification-socket';
import MoveContextEngine from '@/lib/negotiation/move-context';

// Gamification types
interface GamificationData {
  xpEarned: number;
  currentScore: number;
  achievementsUnlocked: Achievement[];
  levelUpdate?: LevelUpdate;
  pressureEvents: PressureEvent[];
  character: AICharacter;
  relationship: CharacterRelationship;
}

// Import Achievement type from service
import type { Achievement } from '@/lib/services/gamification-service';

interface LevelUpdate {
  previousLevel: number;
  newLevel: number;
  xpGained: number;
  totalXP: number;
  leveledUp: boolean;
  newTitle: string;
  newUnlocks: string[];
}

interface PressureEvent {
  id: string;
  type: 'stakeholder' | 'market' | 'time' | 'competitor';
  title: string;
  message: string;
  intensity: 'low' | 'medium' | 'high';
  timestamp: Date;
}

interface AICharacter {
  id: string;
  name: string;
  title: string;
  company: string;
  avatar: string;
  difficulty: number;
  specialties: string[];
  backstory: string;
}

interface CharacterRelationship {
  respectLevel: number;
  trustLevel: number;
  status: string;
  totalInteractions: number;
  bonuses: {
    betterStartingTerms: boolean;
    increasedFlexibility: number;
    insiderInformation: boolean;
  };
}

interface NegotiationSessionProps {
  sessionId: string;
}

export function NegotiationSession({ sessionId }: NegotiationSessionProps) {
  const {
    session,
    loading,
    makingMove,
    makeMove,
    pauseSession,
    resumeSession,
    abandonSession
  } = useNegotiationSession(sessionId);

  // Gamification hooks
  const {
    profile,
    characters,
    relationships,
    loading: gamificationLoading,
    updateSessionGamification,
    initializeSessionGamification,
    finalizeSessionGamification,
    userLevel,
    userRank
  } = useGamification({ autoFetch: true, enableRealTimeUpdates: true });

  const { joinSession, leaveSession, requestLiveScore } = useGamificationSocket();
  const { achievements: realtimeAchievements, levelUpdates } = useAchievementNotifications();
  const { events: pressureEvents, acknowledgeEvent } = usePressureEvents();

  // Local gamification state
  const [showLevelUpModal, setShowLevelUpModal] = useState(false);
  const [currentLevelUpdate, setCurrentLevelUpdate] = useState<LevelUpdate | null>(null);
  const [xpAnimation, setXpAnimation] = useState<{ amount: number; show: boolean }>({ amount: 0, show: false });
  const [currentScore, setCurrentScore] = useState<number>(5.0);

  // Scenario and dynamic form state
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [showScenarioSelection, setShowScenarioSelection] = useState(true);
  const [negotiationContext, setNegotiationContext] = useState<any>(null);
  const [useDynamicForm, setUseDynamicForm] = useState(false);

  const [message, setMessage] = useState('');
  const [strategy, setStrategy] = useState<MoveStrategy>('COLLABORATIVE');
  const [reasoning, setReasoning] = useState('');
  const [currentOffer, setCurrentOffer] = useState<Terms>({
    price: 0,
    currency: 'USD',
    paymentTerms: '',
    customTerms: {}
  });

  // Mock gamification data (will be replaced with real API calls)
  const mockGamificationData: GamificationData = {
    xpEarned: 150,
    currentScore: 8.2,
    achievementsUnlocked: [],
    pressureEvents: [
      {
        id: 'ceo_urgent',
        type: 'stakeholder',
        title: 'CEO Urgent Message',
        message: 'Board meeting moved up 2 hours. Need this deal closed ASAP!',
        intensity: 'high',
        timestamp: new Date()
      }
    ],
    character: {
      id: 'sarah_chen',
      name: 'Sarah Chen',
      title: 'Senior Sales Director',
      company: 'TechCorp Solutions',
      avatar: '/avatars/sarah-chen.jpg',
      difficulty: 3,
      specialties: ['Cost reduction', 'Data-driven decisions'],
      backstory: '15 years in enterprise sales, known for analytical approach and quarterly pressure.'
    },
    relationship: {
      respectLevel: 75,
      trustLevel: 60,
      status: 'Professional Respect',
      totalInteractions: 8,
      bonuses: {
        betterStartingTerms: true,
        increasedFlexibility: 0.1,
        insiderInformation: false
      }
    }
  };

  const mockUserLevel = {
    current: 3,
    title: 'Senior Negotiator',
    currentXP: 1100,
    totalXP: 2600,
    xpToNext: 900,
    progress: (1100 / 2000) * 100 // Progress within current level
  };

  useEffect(() => {
    if (session?.currentTerms) {
      setCurrentOffer(session.currentTerms);
    }
  }, [session]);

  const handleMakeMove = async () => {
    if (!message.trim() || !session) return;

    const moveData = {
      offer: currentOffer,
      message: message.trim(),
      strategy,
      reasoning: reasoning.trim() || undefined
    };

    const result = await makeMove(moveData);
    if (result) {
      setMessage('');
      setReasoning('');

      // Update gamification
      await handleGamificationUpdate(moveData);
    }
  };

  // Real gamification integration
  const handleGamificationUpdate = async (moveData: any) => {
    if (!session?.id) return;

    try {
      const update = await updateSessionGamification(session.id, moveData);
      if (update) {
        // Show XP animation
        if (update.xpEarned > 0) {
          setXpAnimation({ amount: update.xpEarned, show: true });
        }

        // Update live score
        setCurrentScore(update.liveScore);

        // Handle level up
        if (update.levelUpdate?.leveledUp) {
          setCurrentLevelUpdate(update.levelUpdate);
          setShowLevelUpModal(true);
        }
      }
    } catch (error) {
      console.error('Failed to update session gamification:', error);
    }
  };

  const formatCurrency = (amount: number, currency: string) => {
    return new Intl.NumberFormat('en-US', {
      style: 'currency',
      currency: currency || 'USD'
    }).format(amount);
  };

  const getStrategyColor = (strategy: string) => {
    switch (strategy) {
      case 'COLLABORATIVE': return 'bg-green-100 text-green-800';
      case 'COMPETITIVE': return 'bg-red-100 text-red-800';
      case 'VALUE_BASED': return 'bg-blue-100 text-blue-800';
      case 'CONCESSION': return 'bg-yellow-100 text-yellow-800';
      case 'ANCHORING': return 'bg-purple-100 text-purple-800';
      case 'DEADLINE_PRESSURE': return 'bg-orange-100 text-orange-800';
      default: return 'bg-gray-100 text-gray-800';
    }
  };

  if (loading) {
    return (
      <div className="container mx-auto p-6">
        <div className="animate-pulse space-y-6">
          <div className="h-8 bg-muted rounded w-1/3"></div>
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
            <div className="lg:col-span-2 space-y-4">
              <div className="h-96 bg-muted rounded"></div>
              <div className="h-32 bg-muted rounded"></div>
            </div>
            <div className="space-y-4">
              <div className="h-48 bg-muted rounded"></div>
              <div className="h-32 bg-muted rounded"></div>
            </div>
          </div>
        </div>
      </div>
    );
  }

  if (!session) {
    return (
      <div className="container mx-auto p-6">
        <Card>
          <CardContent className="py-12">
            <div className="text-center space-y-4">
              <AlertTriangle className="h-12 w-12 text-muted-foreground mx-auto" />
              <div>
                <h3 className="text-lg font-medium">Session Not Found</h3>
                <p className="text-muted-foreground">
                  The negotiation session could not be loaded.
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    );
  }

  const currentRound = session.rounds.length;
  const maxRounds = 10; // This should come from scenario constraints
  const progressPercentage = (currentRound / maxRounds) * 100;

  return (
    <div className="container mx-auto p-6 space-y-6">
      {/* Enhanced Session Header with Gamification */}
      <div className="space-y-4">
        {/* Character & Gamification Header */}
        <Card className="bg-gradient-to-r from-purple-50 to-blue-50 border-purple-200">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="flex items-start gap-4">
                {/* Character Avatar */}
                <div className="relative">
                  <div className="w-16 h-16 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold text-xl">
                    SC
                  </div>
                  <div className="absolute -bottom-1 -right-1 bg-yellow-400 rounded-full p-1">
                    <Star className="h-3 w-3 text-yellow-800" />
                  </div>
                </div>

                {/* Character & Session Info */}
                <div className="space-y-2">
                  <div className="flex items-center gap-2">
                    <h2 className="text-lg font-semibold text-purple-900">
                      {mockGamificationData.character.name}
                    </h2>
                    <Badge variant="outline" className="bg-purple-100 text-purple-800 border-purple-300">
                      Difficulty {mockGamificationData.character.difficulty}/5
                    </Badge>
                  </div>
                  <p className="text-sm text-purple-700">
                    {mockGamificationData.character.title} at {mockGamificationData.character.company}
                  </p>
                  <div className="flex items-center gap-4 text-sm text-purple-600">
                    <span className="flex items-center gap-1">
                      <Heart className="h-4 w-4" />
                      Respect: {mockGamificationData.relationship.respectLevel}%
                    </span>
                    <span className="flex items-center gap-1">
                      <Shield className="h-4 w-4" />
                      Trust: {mockGamificationData.relationship.trustLevel}%
                    </span>
                    <span className="flex items-center gap-1">
                      <TrendingUp className="h-4 w-4" />
                      {mockGamificationData.relationship.status}
                    </span>
                  </div>
                </div>
              </div>

              {/* User Level & XP */}
              <div className="text-right space-y-2">
                <div className="flex items-center gap-2">
                  <Trophy className="h-5 w-5 text-yellow-600" />
                  <span className="font-semibold text-purple-900">{mockUserLevel.title}</span>
                  <Badge className="bg-yellow-100 text-yellow-800">Level {mockUserLevel.current}</Badge>
                </div>
                <div className="space-y-1">
                  <div className="flex items-center gap-2 text-sm text-purple-700">
                    <Zap className="h-4 w-4" />
                    <span>{mockUserLevel.currentXP.toLocaleString()} / {(mockUserLevel.currentXP + mockUserLevel.xpToNext).toLocaleString()} XP</span>
                  </div>
                  <Progress value={mockUserLevel.progress} className="w-32 h-2" />
                  <p className="text-xs text-purple-600">{mockUserLevel.xpToNext.toLocaleString()} XP to next level</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Business Scenario Context */}
        <Card className="bg-gradient-to-r from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-4">
            <div className="flex items-start justify-between">
              <div className="space-y-2">
                <h3 className="text-lg font-semibold text-blue-900">
                  Enterprise Software Licensing Negotiation
                </h3>
                <div className="flex items-center gap-4 text-sm text-blue-700">
                  <span className="flex items-center gap-1">
                    <DollarSign className="h-4 w-4" />
                    $2.3M Annual Contract
                  </span>
                  <span className="flex items-center gap-1">
                    <Clock className="h-4 w-4" />
                    Q4 Deadline: 3 weeks
                  </span>
                  <span className="flex items-center gap-1">
                    <Target className="h-4 w-4" />
                    Mission Critical
                  </span>
                </div>
                <p className="text-sm text-blue-600">
                  Your company needs this software platform to launch the new product line.
                  The CEO is counting on this deal to hit Q4 numbers.
                </p>
              </div>
              <Badge className="bg-blue-100 text-blue-800 border-blue-300">
                High Stakes
              </Badge>
            </div>
          </CardContent>
        </Card>

        {/* Pressure Events */}
        {mockGamificationData.pressureEvents.length > 0 && (
          <Card className="border-orange-200 bg-gradient-to-r from-orange-50 to-red-50">
            <CardContent className="p-4">
              <div className="flex items-start gap-3">
                <div className="p-2 bg-orange-100 rounded-full">
                  <Bell className="h-4 w-4 text-orange-600" />
                </div>
                <div className="flex-1">
                  <div className="flex items-center gap-2 mb-2">
                    <h4 className="font-semibold text-orange-900">
                      {mockGamificationData.pressureEvents[0].title}
                    </h4>
                    <Badge className="bg-orange-100 text-orange-800">
                      {mockGamificationData.pressureEvents[0].intensity.toUpperCase()}
                    </Badge>
                  </div>
                  <p className="text-sm text-orange-700">
                    {mockGamificationData.pressureEvents[0].message}
                  </p>
                  <p className="text-xs text-orange-600 mt-1">
                    Just now • This affects your negotiation position
                  </p>
                </div>
                <Button variant="outline" size="sm" className="text-orange-700 border-orange-300">
                  Acknowledge
                </Button>
              </div>
            </CardContent>
          </Card>
        )}

        {/* Session Progress Header with Live Scoring */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h1 className="text-2xl font-bold flex items-center gap-2">
              <MessageSquare className="h-6 w-6" />
              Live Negotiation
            </h1>
            <div className="flex items-center gap-4 mt-2 text-sm text-muted-foreground">
              <span>Round {currentRound} of {maxRounds}</span>
              <Badge className={session.status === 'ACTIVE' ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'}>
                {session.status.toLowerCase()}
              </Badge>
              <span className="flex items-center gap-1">
                <Award className="h-4 w-4" />
                Live Score: {mockGamificationData.currentScore}/10
              </span>
            </div>
          </div>

          {/* Live Score & XP Display */}
          <div className="flex items-center gap-4">
            <div className="text-center">
              <div className="text-2xl font-bold text-green-600">{mockGamificationData.currentScore}</div>
              <div className="text-xs text-muted-foreground">Current Score</div>
            </div>
            <div className="text-center">
              <div className="text-lg font-bold text-purple-600 flex items-center gap-1">
                <Zap className="h-4 w-4" />
                +{mockGamificationData.xpEarned}
              </div>
              <div className="text-xs text-muted-foreground">XP Earned</div>
            </div>
          </div>
        </div>

        <div className="flex items-center gap-2">
          {session.status === 'ACTIVE' ? (
            <Button variant="outline" onClick={pauseSession} className="gap-2">
              <Pause className="h-4 w-4" />
              Pause
            </Button>
          ) : session.status === 'PAUSED' ? (
            <Button onClick={resumeSession} className="gap-2">
              <Play className="h-4 w-4" />
              Resume
            </Button>
          ) : null}
          
          <Button variant="destructive" onClick={abandonSession}>
            End Session
          </Button>
        </div>
      </div>

      {/* Progress Bar */}
      <Card>
        <CardContent className="p-4">
          <div className="space-y-2">
            <div className="flex justify-between text-sm">
              <span>Negotiation Progress</span>
              <span>{currentRound}/{maxRounds} rounds</span>
            </div>
            <Progress value={progressPercentage} className="h-2" />
          </div>
        </CardContent>
      </Card>

      <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
        {/* Main Negotiation Area */}
        <div className="lg:col-span-2 space-y-6">
          {/* Conversation History */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5" />
                Negotiation History
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4 max-h-96 overflow-y-auto">
                {session.rounds.length === 0 ? (
                  <div className="text-center py-8 text-muted-foreground">
                    <MessageSquare className="h-8 w-8 mx-auto mb-2" />
                    <p>No moves yet. Make your opening move to start the negotiation.</p>
                  </div>
                ) : (
                  session.rounds.map((round) => (
                    <div key={round.roundNumber} className="space-y-3">
                      {/* User Move */}
                      <div className="flex gap-3">
                        <div className="p-2 bg-blue-100 rounded-full">
                          <User className="h-4 w-4 text-blue-600" />
                        </div>
                        <div className="flex-1 space-y-2">
                          <div className="flex items-center gap-2">
                            <span className="font-medium">You</span>
                            <Badge className={getStrategyColor(round.userMove.strategy)} variant="outline">
                              {round.userMove.strategy.toLowerCase().replace('_', ' ')}
                            </Badge>
                            <span className="text-xs text-muted-foreground">
                              {new Date(round.userMove.timestamp).toLocaleTimeString()}
                            </span>
                          </div>
                          <div className="bg-blue-50 p-3 rounded-lg">
                            <p className="text-sm">{round.userMove.message}</p>
                            {round.userMove.offer.price && (
                              <div className="mt-2 text-xs text-muted-foreground">
                                Offer: {formatCurrency(round.userMove.offer.price, round.userMove.offer.currency || 'USD')}
                                {round.userMove.offer.paymentTerms && ` • ${round.userMove.offer.paymentTerms}`}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>

                      {/* AI Response */}
                      {round.aiMove && (
                        <div className="flex gap-3">
                          <div className="p-2 bg-gray-100 rounded-full">
                            <Bot className="h-4 w-4 text-gray-600" />
                          </div>
                          <div className="flex-1 space-y-2">
                            <div className="flex items-center gap-2">
                              <span className="font-medium">AI Negotiator</span>
                              <Badge className={getStrategyColor(round.aiMove.strategy)} variant="outline">
                                {round.aiMove.strategy.toLowerCase().replace('_', ' ')}
                              </Badge>
                              <span className="text-xs text-muted-foreground">
                                {new Date(round.aiMove.timestamp).toLocaleTimeString()}
                              </span>
                            </div>
                            <div className="bg-gray-50 p-3 rounded-lg">
                              <p className="text-sm">{round.aiMove.message}</p>
                              {round.aiMove.offer.price && (
                                <div className="mt-2 text-xs text-muted-foreground">
                                  Counter-offer: {formatCurrency(round.aiMove.offer.price, round.aiMove.offer.currency || 'USD')}
                                  {round.aiMove.offer.paymentTerms && ` • ${round.aiMove.offer.paymentTerms}`}
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      )}

                      {round.roundNumber < session.rounds.length && <Separator />}
                    </div>
                  ))
                )}
              </div>
            </CardContent>
          </Card>

          {/* Make Move Interface */}
          {session.status === 'ACTIVE' && (
            <Card>
              <CardHeader>
                <CardTitle>Make Your Move</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                {/* Current Offer */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  <div className="space-y-2">
                    <Label>Price Offer</Label>
                    <div className="flex gap-2">
                      <Input
                        type="number"
                        value={currentOffer.price || ''}
                        onChange={(e) => setCurrentOffer({
                          ...currentOffer,
                          price: parseInt(e.target.value) || 0
                        })}
                        placeholder="0"
                      />
                      <Select 
                        value={currentOffer.currency || 'USD'} 
                        onValueChange={(value) => setCurrentOffer({
                          ...currentOffer,
                          currency: value
                        })}
                      >
                        <SelectTrigger className="w-20">
                          <SelectValue />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="USD">USD</SelectItem>
                          <SelectItem value="EUR">EUR</SelectItem>
                          <SelectItem value="GBP">GBP</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label>Payment Terms</Label>
                    <Input
                      value={currentOffer.paymentTerms || ''}
                      onChange={(e) => setCurrentOffer({
                        ...currentOffer,
                        paymentTerms: e.target.value
                      })}
                      placeholder="e.g., Monthly, Quarterly"
                    />
                  </div>

                  <div className="space-y-2">
                    <Label>Strategy</Label>
                    <Select value={strategy} onValueChange={(value: MoveStrategy) => setStrategy(value)}>
                      <SelectTrigger>
                        <SelectValue />
                      </SelectTrigger>
                      <SelectContent>
                        {STRATEGY_OPTIONS.map(option => (
                          <SelectItem key={option.value} value={option.value}>
                            {option.label}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                {/* Message */}
                <div className="space-y-2">
                  <Label>Your Message</Label>
                  <Textarea
                    value={message}
                    onChange={(e) => setMessage(e.target.value)}
                    placeholder="Explain your position and reasoning..."
                    className="min-h-[100px]"
                  />
                </div>

                {/* Reasoning (Optional) */}
                <div className="space-y-2">
                  <Label>Internal Reasoning (Optional)</Label>
                  <Textarea
                    value={reasoning}
                    onChange={(e) => setReasoning(e.target.value)}
                    placeholder="Your strategic thinking for this move..."
                    className="min-h-[60px]"
                  />
                </div>

                <Button 
                  onClick={handleMakeMove}
                  disabled={!message.trim() || makingMove}
                  className="w-full gap-2"
                >
                  {makingMove ? (
                    <>
                      <Clock className="h-4 w-4 animate-spin" />
                      Making Move...
                    </>
                  ) : (
                    <>
                      <Send className="h-4 w-4" />
                      Send Move
                    </>
                  )}
                </Button>
              </CardContent>
            </Card>
          )}
        </div>

        {/* Enhanced Gamification Sidebar */}
        <div className="space-y-6">
          {/* Live Performance */}
          <Card className="border-green-200 bg-gradient-to-br from-green-50 to-emerald-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-green-800">
                <TrendingUp className="h-5 w-5" />
                Live Performance
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="text-center">
                <div className="text-3xl font-bold text-green-600 mb-1">
                  {mockGamificationData.currentScore}
                </div>
                <div className="text-sm text-green-700">Current Score</div>
                <Progress value={mockGamificationData.currentScore * 10} className="mt-2 h-2" />
              </div>

              <div className="grid grid-cols-2 gap-4 text-sm">
                <div className="text-center">
                  <div className="text-muted-foreground">Rounds</div>
                  <div className="font-bold text-lg">{currentRound}</div>
                </div>
                <div className="text-center">
                  <div className="text-muted-foreground">Duration</div>
                  <div className="font-bold text-lg">
                    {Math.floor((Date.now() - new Date(session.startedAt).getTime()) / (1000 * 60))}m
                  </div>
                </div>
              </div>

              <div className="pt-3 border-t border-green-200">
                <div className="flex items-center justify-between text-sm">
                  <span className="text-green-700">XP This Session</span>
                  <span className="font-bold text-green-800 flex items-center gap-1">
                    <Zap className="h-4 w-4" />
                    +{mockGamificationData.xpEarned}
                  </span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Character Relationship */}
          <Card className="border-purple-200 bg-gradient-to-br from-purple-50 to-pink-50">
            <CardHeader>
              <CardTitle className="flex items-center gap-2 text-purple-800">
                <Heart className="h-5 w-5" />
                Character Relationship
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              <div className="flex items-center gap-3">
                <div className="w-12 h-12 bg-gradient-to-br from-purple-400 to-blue-500 rounded-full flex items-center justify-center text-white font-bold">
                  SC
                </div>
                <div>
                  <div className="font-medium text-purple-900">{mockGamificationData.character.name}</div>
                  <div className="text-sm text-purple-600">{mockGamificationData.relationship.status}</div>
                </div>
              </div>

              <div className="space-y-3">
                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-purple-700">Respect</span>
                    <span className="font-medium">{mockGamificationData.relationship.respectLevel}%</span>
                  </div>
                  <Progress value={mockGamificationData.relationship.respectLevel} className="h-2" />
                </div>

                <div>
                  <div className="flex justify-between text-sm mb-1">
                    <span className="text-purple-700">Trust</span>
                    <span className="font-medium">{mockGamificationData.relationship.trustLevel}%</span>
                  </div>
                  <Progress value={mockGamificationData.relationship.trustLevel} className="h-2" />
                </div>
              </div>

              <div className="pt-3 border-t border-purple-200">
                <div className="text-xs text-purple-600 space-y-1">
                  <div className="flex items-center gap-1">
                    <Gift className="h-3 w-3" />
                    <span>Relationship Bonuses Active:</span>
                  </div>
                  {mockGamificationData.relationship.bonuses.betterStartingTerms && (
                    <div className="ml-4 text-purple-700">• Better starting terms</div>
                  )}
                  {mockGamificationData.relationship.bonuses.increasedFlexibility > 0 && (
                    <div className="ml-4 text-purple-700">• +{Math.round(mockGamificationData.relationship.bonuses.increasedFlexibility * 100)}% AI flexibility</div>
                  )}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Session Stats */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Target className="h-5 w-5" />
                Session Stats
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-4">
              {session.status === 'COMPLETED' && (
                <div className="pt-4 border-t">
                  <div className="flex items-center gap-2 mb-2">
                    <Trophy className="h-4 w-4 text-yellow-600" />
                    <span className="font-medium">Final Score</span>
                  </div>
                  <div className="text-2xl font-bold">{session.metrics.finalScore.toFixed(1)}</div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* AI Personality */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Bot className="h-5 w-5" />
                AI Opponent
              </CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Aggressiveness</span>
                  <span>{Math.round(session.aiPersonality.aggressiveness * 100)}%</span>
                </div>
                <Progress value={session.aiPersonality.aggressiveness * 100} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Flexibility</span>
                  <span>{Math.round(session.aiPersonality.flexibility * 100)}%</span>
                </div>
                <Progress value={session.aiPersonality.flexibility * 100} className="h-2" />
              </div>

              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Risk Tolerance</span>
                  <span>{Math.round(session.aiPersonality.riskTolerance * 100)}%</span>
                </div>
                <Progress value={session.aiPersonality.riskTolerance * 100} className="h-2" />
              </div>

              <div className="pt-2 border-t text-sm">
                <div className="flex justify-between">
                  <span>Style:</span>
                  <span className="font-medium">{session.aiPersonality.communicationStyle.toLowerCase()}</span>
                </div>
                <div className="flex justify-between">
                  <span>Speed:</span>
                  <span className="font-medium">{session.aiPersonality.decisionSpeed.toLowerCase()}</span>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Current Terms */}
          {session.currentTerms && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <DollarSign className="h-5 w-5" />
                  Current Terms
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-2 text-sm">
                {session.currentTerms.price && (
                  <div className="flex justify-between">
                    <span>Price:</span>
                    <span className="font-medium">
                      {formatCurrency(session.currentTerms.price, session.currentTerms.currency || 'USD')}
                    </span>
                  </div>
                )}
                {session.currentTerms.paymentTerms && (
                  <div className="flex justify-between">
                    <span>Payment:</span>
                    <span className="font-medium">{session.currentTerms.paymentTerms}</span>
                  </div>
                )}
              </CardContent>
            </Card>
          )}

          {/* Leaderboard Widget */}
          <LeaderboardWidget
            userRank={8}
            timeframe="weekly"
            scope="organization"
          />

          {/* Achievement Progress Widget */}
          <AchievementProgress
            achievements={[
              {
                id: 'speed_demon',
                title: 'Speed Demon',
                progress: 2,
                total: 3,
                badge: '⚡'
              },
              {
                id: 'win_win_master',
                title: 'Win-Win Master',
                progress: 1,
                total: 5,
                badge: '🤝'
              },
              {
                id: 'cost_crusher',
                title: 'Cost Crusher',
                progress: 3,
                total: 10,
                badge: '💰'
              }
            ]}
          />
        </div>
      </div>

      {/* Gamification Components */}
      {realtimeAchievements.length > 0 && (
        <AchievementNotification
          achievements={realtimeAchievements}
          onClose={() => {
            // Clear achievements handled by the hook
          }}
          onShare={(achievement) => {
            console.log('Sharing achievement:', achievement);
            // TODO: Implement sharing functionality
          }}
        />
      )}

      {currentLevelUpdate && showLevelUpModal && (
        <LevelUpModal
          levelUpdate={currentLevelUpdate}
          show={showLevelUpModal}
          onClose={() => {
            setShowLevelUpModal(false);
            setCurrentLevelUpdate(null);
          }}
        />
      )}

      <XPAnimation
        amount={xpAnimation.amount}
        show={xpAnimation.show}
        onComplete={() => setXpAnimation({ amount: 0, show: false })}
      />
    </div>
  );
}
