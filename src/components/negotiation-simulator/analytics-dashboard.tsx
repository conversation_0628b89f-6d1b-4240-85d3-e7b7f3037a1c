"use client";

import React from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Trophy, 
  Clock, 
  Target,
  Users,
  Calendar,
  Award
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import type { SessionAnalytics } from '@/lib/types/negotiation-simulator';

interface AnalyticsDashboardProps {
  analytics: SessionAnalytics | null;
  loading: boolean;
}

export function AnalyticsDashboard({ analytics, loading }: AnalyticsDashboardProps) {
  if (loading) {
    return (
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {[...Array(6)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-muted rounded w-1/2"></div>
                <div className="h-8 bg-muted rounded w-1/3"></div>
                <div className="h-3 bg-muted rounded w-2/3"></div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  if (!analytics) {
    return (
      <Card>
        <CardContent className="py-12">
          <div className="text-center space-y-4">
            <BarChart3 className="h-12 w-12 text-muted-foreground mx-auto" />
            <div>
              <h3 className="text-lg font-medium">No Analytics Available</h3>
              <p className="text-muted-foreground">
                Complete some negotiation sessions to see your performance analytics
              </p>
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  const getPerformanceLevel = (score: number) => {
    if (score >= 8) return { label: 'Excellent', color: 'text-green-600', bgColor: 'bg-green-100' };
    if (score >= 6) return { label: 'Good', color: 'text-blue-600', bgColor: 'bg-blue-100' };
    if (score >= 4) return { label: 'Fair', color: 'text-yellow-600', bgColor: 'bg-yellow-100' };
    return { label: 'Needs Improvement', color: 'text-red-600', bgColor: 'bg-red-100' };
  };

  const performanceLevel = getPerformanceLevel(analytics.averageScore);

  return (
    <div className="space-y-6">
      {/* Overview Stats */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-blue-100 rounded-lg">
                <Target className="h-6 w-6 text-blue-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Total Sessions</p>
                <p className="text-3xl font-bold">{analytics.totalSessions}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-green-100 rounded-lg">
                <Trophy className="h-6 w-6 text-green-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Completed</p>
                <p className="text-3xl font-bold">{analytics.completedSessions}</p>
                <p className="text-xs text-muted-foreground">
                  {(analytics.completionRate * 100).toFixed(0)}% completion rate
                </p>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className={`p-2 ${performanceLevel.bgColor} rounded-lg`}>
                <Award className={`h-6 w-6 ${performanceLevel.color}`} />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Average Score</p>
                <p className="text-3xl font-bold">{analytics.averageScore.toFixed(1)}</p>
                <Badge className={`${performanceLevel.bgColor} ${performanceLevel.color} border-current`} variant="outline">
                  {performanceLevel.label}
                </Badge>
              </div>
            </div>
          </CardContent>
        </Card>

        <Card>
          <CardContent className="p-6">
            <div className="flex items-center gap-3">
              <div className="p-2 bg-purple-100 rounded-lg">
                <Clock className="h-6 w-6 text-purple-600" />
              </div>
              <div>
                <p className="text-sm text-muted-foreground">Avg Duration</p>
                <p className="text-3xl font-bold">{Math.round(analytics.averageDuration)}m</p>
                <p className="text-xs text-muted-foreground">
                  {analytics.averageRounds.toFixed(1)} rounds avg
                </p>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>

      {/* Performance Trends */}
      {analytics.performanceTrends.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Performance Trends
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.performanceTrends.slice(-5).map((trend, index) => (
                <div key={index} className="flex items-center justify-between p-3 bg-muted/30 rounded-lg">
                  <div className="flex items-center gap-3">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="font-medium">{new Date(trend.date).toLocaleDateString()}</span>
                  </div>
                  <div className="flex items-center gap-6 text-sm">
                    <div className="text-center">
                      <div className="text-muted-foreground">Score</div>
                      <div className="font-bold">{trend.score.toFixed(1)}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-muted-foreground">Rounds</div>
                      <div className="font-bold">{trend.rounds}</div>
                    </div>
                    <div className="text-center">
                      <div className="text-muted-foreground">Duration</div>
                      <div className="font-bold">{Math.round(trend.duration)}m</div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Recent Sessions */}
      {analytics.recentSessions.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <Users className="h-5 w-5" />
              Recent Sessions
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              {analytics.recentSessions.slice(0, 5).map((session) => (
                <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                  <div className="space-y-1">
                    <div className="flex items-center gap-2">
                      <Badge 
                        variant={session.status === 'COMPLETED' ? 'default' : 'secondary'}
                        className="text-xs"
                      >
                        {session.status.toLowerCase()}
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        {new Date(session.startedAt).toLocaleDateString()}
                      </span>
                    </div>
                    <div className="text-sm">
                      <span className="font-medium">{session.rounds.length} rounds</span>
                      {session.status === 'COMPLETED' && (
                        <span className="text-muted-foreground ml-2">
                          • Score: {session.metrics.finalScore.toFixed(1)}
                        </span>
                      )}
                    </div>
                  </div>
                  
                  {session.status === 'COMPLETED' && (
                    <div className="grid grid-cols-2 gap-4 text-xs">
                      <div>
                        <div className="text-muted-foreground">Communication</div>
                        <Progress 
                          value={session.metrics.keyMetrics.communicationEffectiveness * 100} 
                          className="h-2 w-16"
                        />
                      </div>
                      <div>
                        <div className="text-muted-foreground">Strategy</div>
                        <Progress 
                          value={session.metrics.keyMetrics.strategicThinking * 100} 
                          className="h-2 w-16"
                        />
                      </div>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}

      {/* Insights & Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Award className="h-5 w-5" />
            Insights & Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {analytics.completionRate < 0.5 && (
            <div className="p-4 bg-yellow-50 border border-yellow-200 rounded-lg">
              <h4 className="font-medium text-yellow-800">Improve Session Completion</h4>
              <p className="text-sm text-yellow-700 mt-1">
                Your completion rate is {(analytics.completionRate * 100).toFixed(0)}%. 
                Try setting shorter time limits or choosing easier scenarios to build momentum.
              </p>
            </div>
          )}

          {analytics.averageScore < 5 && (
            <div className="p-4 bg-blue-50 border border-blue-200 rounded-lg">
              <h4 className="font-medium text-blue-800">Focus on Strategy</h4>
              <p className="text-sm text-blue-700 mt-1">
                Your average score is {analytics.averageScore.toFixed(1)}. 
                Consider practicing with beginner scenarios and focusing on collaborative approaches.
              </p>
            </div>
          )}

          {analytics.averageRounds > 8 && (
            <div className="p-4 bg-purple-50 border border-purple-200 rounded-lg">
              <h4 className="font-medium text-purple-800">Optimize Efficiency</h4>
              <p className="text-sm text-purple-700 mt-1">
                Your negotiations average {analytics.averageRounds.toFixed(1)} rounds. 
                Try to reach agreements more quickly by making stronger initial offers.
              </p>
            </div>
          )}

          {analytics.averageScore >= 7 && analytics.completionRate >= 0.8 && (
            <div className="p-4 bg-green-50 border border-green-200 rounded-lg">
              <h4 className="font-medium text-green-800">Excellent Performance!</h4>
              <p className="text-sm text-green-700 mt-1">
                You're performing very well! Consider trying expert-level scenarios 
                or more complex contract types to continue improving.
              </p>
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}
