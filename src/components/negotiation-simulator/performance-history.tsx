"use client";

import React, { useEffect, useState } from 'react';
import { 
  TrendingUp, 
  Clock, 
  Target, 
  Calendar,
  Filter,
  ChevronLeft,
  ChevronRight,
  BarChart3,
  Award
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useNegotiationIntegration } from '@/hooks/use-negotiation-integration';
import { format } from 'date-fns';

interface PerformanceHistoryProps {
  userId: string;
}

export function PerformanceHistory({ userId }: PerformanceHistoryProps) {
  const [timeframe, setTimeframe] = useState('30d');
  const [currentPage, setCurrentPage] = useState(1);
  const [limit] = useState(10);
  
  const {
    performanceHistory,
    loading,
    error,
    hasAccess,
    getPerformanceHistory,
    clearError
  } = useNegotiationIntegration();

  useEffect(() => {
    if (hasAccess && userId) {
      getPerformanceHistory(userId, { 
        timeframe, 
        page: currentPage, 
        limit 
      });
    }
  }, [hasAccess, userId, timeframe, currentPage, limit, getPerformanceHistory]);

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    if (score >= 40) return 'text-orange-600';
    return 'text-red-600';
  };

  const getScoreBadge = (score: number) => {
    if (score >= 80) return { variant: 'default' as const, label: 'Excellent' };
    if (score >= 60) return { variant: 'secondary' as const, label: 'Good' };
    if (score >= 40) return { variant: 'outline' as const, label: 'Fair' };
    return { variant: 'destructive' as const, label: 'Needs Work' };
  };

  const calculateTrend = () => {
    if (performanceHistory.length < 2) return null;
    
    const recent = performanceHistory.slice(0, 5);
    const older = performanceHistory.slice(5, 10);
    
    if (older.length === 0) return null;
    
    const recentAvg = recent.reduce((sum, entry) => sum + entry.score, 0) / recent.length;
    const olderAvg = older.reduce((sum, entry) => sum + entry.score, 0) / older.length;
    
    const trend = recentAvg - olderAvg;
    return {
      value: trend,
      isImproving: trend > 0,
      percentage: Math.abs((trend / olderAvg) * 100)
    };
  };

  const trend = calculateTrend();

  if (!hasAccess) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground">
              Performance history requires PRO subscription
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance History
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex gap-4">
            <Skeleton className="h-10 w-32" />
            <Skeleton className="h-10 w-24" />
          </div>
          <div className="space-y-3">
            {[1, 2, 3, 4, 5].map((i) => (
              <div key={i} className="flex items-center gap-4 p-3 border rounded-lg">
                <Skeleton className="h-4 w-20" />
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-12" />
                <Skeleton className="h-4 w-24" />
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance History
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground mb-2">
              Failed to load performance history
            </p>
            <p className="text-xs text-muted-foreground mb-4">
              {error}
            </p>
            <Button variant="outline" onClick={clearError}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Performance History
          </div>
          {trend && (
            <div className="flex items-center gap-2">
              <TrendingUp className={`h-4 w-4 ${trend.isImproving ? 'text-green-600' : 'text-red-600'}`} />
              <span className={`text-sm ${trend.isImproving ? 'text-green-600' : 'text-red-600'}`}>
                {trend.isImproving ? '+' : '-'}{trend.percentage.toFixed(1)}%
              </span>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* Filters */}
        <div className="flex items-center gap-4">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4" />
            <Select value={timeframe} onValueChange={setTimeframe}>
              <SelectTrigger className="w-32">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="7d">Last 7 days</SelectItem>
                <SelectItem value="30d">Last 30 days</SelectItem>
                <SelectItem value="90d">Last 3 months</SelectItem>
                <SelectItem value="1y">Last year</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        {/* Performance Entries */}
        {performanceHistory.length === 0 ? (
          <div className="text-center py-8">
            <Award className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-sm text-muted-foreground mb-2">
              No performance data available
            </p>
            <p className="text-xs text-muted-foreground">
              Complete some negotiation sessions to see your performance history
            </p>
          </div>
        ) : (
          <div className="space-y-3">
            {performanceHistory.map((entry, index) => {
              const scoreBadge = getScoreBadge(entry.score);
              
              return (
                <div key={entry.sessionId} className="flex items-center gap-4 p-3 border rounded-lg hover:bg-accent/50 transition-colors">
                  <div className="flex items-center gap-2 min-w-0 flex-1">
                    <Calendar className="h-4 w-4 text-muted-foreground" />
                    <span className="text-sm">
                      {format(new Date(entry.date), 'MMM dd, yyyy')}
                    </span>
                  </div>
                  
                  <div className="flex items-center gap-2">
                    <Target className="h-4 w-4 text-muted-foreground" />
                    <span className={`text-sm font-medium ${getScoreColor(entry.score)}`}>
                      {entry.score.toFixed(1)}%
                    </span>
                    <Badge variant={scoreBadge.variant} className="text-xs">
                      {scoreBadge.label}
                    </Badge>
                  </div>
                  
                  <div className="flex items-center gap-4 text-xs text-muted-foreground">
                    <div className="flex items-center gap-1">
                      <Clock className="h-3 w-3" />
                      {entry.duration}m
                    </div>
                    <div className="flex items-center gap-1">
                      <Target className="h-3 w-3" />
                      {entry.rounds} rounds
                    </div>
                  </div>
                  
                  {entry.strategiesUsed.length > 0 && (
                    <div className="flex gap-1">
                      {entry.strategiesUsed.slice(0, 2).map((strategy, strategyIndex) => (
                        <Badge key={strategyIndex} variant="outline" className="text-xs">
                          {strategy}
                        </Badge>
                      ))}
                      {entry.strategiesUsed.length > 2 && (
                        <Badge variant="outline" className="text-xs">
                          +{entry.strategiesUsed.length - 2}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
              );
            })}
          </div>
        )}

        {/* Pagination */}
        {performanceHistory.length > 0 && (
          <div className="flex items-center justify-between pt-4">
            <div className="text-sm text-muted-foreground">
              Showing {performanceHistory.length} entries
            </div>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                disabled={currentPage === 1}
              >
                <ChevronLeft className="h-4 w-4" />
                Previous
              </Button>
              <span className="text-sm text-muted-foreground">
                Page {currentPage}
              </span>
              <Button
                variant="outline"
                size="sm"
                onClick={() => setCurrentPage(prev => prev + 1)}
                disabled={performanceHistory.length < limit}
              >
                Next
                <ChevronRight className="h-4 w-4" />
              </Button>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
