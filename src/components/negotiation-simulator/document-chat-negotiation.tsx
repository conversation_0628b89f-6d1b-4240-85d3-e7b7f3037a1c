"use client";

import React, { useState, useEffect } from 'react';
import { ChatNegotiation } from './chat-negotiation';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Button } from '@/components/ui/button';
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible';
import { 
  FileText, 
  ChevronDown, 
  ChevronUp, 
  AlertTriangle, 
  CheckCircle,
  Target,
  Lightbulb,
  MessageSquare
} from 'lucide-react';
import { chatNegotiationService } from '@/lib/services/chat-negotiation-service';
import type { DocumentContext } from '@/lib/services/chat-negotiation-service';

interface DocumentChatNegotiationProps {
  sessionId: string;
  onComplete?: (results: any) => void;
}

export function DocumentChatNegotiation({ sessionId, onComplete }: DocumentChatNegotiationProps) {
  const [documentContext, setDocumentContext] = useState<DocumentContext | null>(null);
  const [contextExpanded, setContextExpanded] = useState(true);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDocumentContext();
  }, [sessionId]);

  const loadDocumentContext = async () => {
    try {
      setLoading(true);
      const context = await chatNegotiationService.getDocumentContext(sessionId);
      if (context.hasDocumentContext) {
        // Parse the context string to get structured data
        // This would be properly structured from the backend
        setDocumentContext({
          contractType: 'Service Agreement', // Would come from backend
          contractTitle: 'Software License Agreement', // Would come from backend
          analysisScore: 65, // Would come from backend
          riskLevel: 'MEDIUM', // Would come from backend
          totalDeviations: 8, // Would come from backend
          keyFindings: [
            'Unlimited liability clause detected',
            'Auto-renewal terms favor vendor',
            'Termination notice period too short',
            'Data protection clauses insufficient'
          ] // Would come from backend
        });
      }
    } catch (error) {
      console.error('Failed to load document context:', error);
    } finally {
      setLoading(false);
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'HIGH': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'LOW': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-32 bg-muted/50 rounded-lg animate-pulse" />
        <div className="h-96 bg-muted/50 rounded-lg animate-pulse" />
      </div>
    );
  }

  return (
    <div className="space-y-4">
      {/* Document Context Panel */}
      {documentContext && (
        <Collapsible open={contextExpanded} onOpenChange={setContextExpanded}>
          <Card className="border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
            <CollapsibleTrigger asChild>
              <CardHeader className="cursor-pointer hover:bg-blue-100/50 dark:hover:bg-blue-900/20 transition-colors">
                <CardTitle className="flex items-center justify-between">
                  <div className="flex items-center gap-2">
                    <FileText className="h-5 w-5 text-blue-600" />
                    Contract Context: {documentContext.contractTitle}
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge className={getRiskColor(documentContext.riskLevel)}>
                      {documentContext.riskLevel} Risk
                    </Badge>
                    {contextExpanded ? (
                      <ChevronUp className="h-4 w-4" />
                    ) : (
                      <ChevronDown className="h-4 w-4" />
                    )}
                  </div>
                </CardTitle>
              </CardHeader>
            </CollapsibleTrigger>
            
            <CollapsibleContent>
              <CardContent className="pt-0 space-y-4">
                {/* Contract Metrics */}
                <div className="grid grid-cols-3 gap-4">
                  <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                    <div className={`text-xl font-bold ${getScoreColor(documentContext.analysisScore)}`}>
                      {documentContext.analysisScore}/100
                    </div>
                    <div className="text-xs text-muted-foreground">Analysis Score</div>
                  </div>
                  <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                    <div className="text-xl font-bold text-orange-600">
                      {documentContext.totalDeviations}
                    </div>
                    <div className="text-xs text-muted-foreground">Issues Found</div>
                  </div>
                  <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                    <div className="text-xl font-bold text-blue-600">
                      {documentContext.contractType}
                    </div>
                    <div className="text-xs text-muted-foreground">Contract Type</div>
                  </div>
                </div>

                {/* Key Issues */}
                <div>
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2">
                    <Target className="h-4 w-4" />
                    Key Issues to Negotiate
                  </h4>
                  <div className="space-y-2">
                    {documentContext.keyFindings.map((finding, index) => (
                      <div key={index} className="flex items-start gap-2 p-2 bg-white/50 dark:bg-gray-900/20 rounded">
                        <AlertTriangle className="h-4 w-4 text-orange-500 mt-0.5 flex-shrink-0" />
                        <span className="text-sm">{finding}</span>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Negotiation Tips */}
                <div className="p-3 bg-blue-100/50 dark:bg-blue-900/20 rounded-lg">
                  <h4 className="font-medium text-blue-900 dark:text-blue-100 mb-2 flex items-center gap-2">
                    <Lightbulb className="h-4 w-4" />
                    Negotiation Strategy Tips
                  </h4>
                  <ul className="text-sm text-blue-800 dark:text-blue-200 space-y-1">
                    <li>• Focus on the liability clause - this is your biggest risk</li>
                    <li>• Negotiate termination notice period to 60-90 days</li>
                    <li>• Request opt-out clause for auto-renewal</li>
                    <li>• Strengthen data protection requirements</li>
                  </ul>
                </div>
              </CardContent>
            </CollapsibleContent>
          </Card>
        </Collapsible>
      )}

      {/* Chat Negotiation Interface */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Practice Negotiation
            {documentContext && (
              <Badge variant="outline" className="ml-2">
                Document-Aware AI
              </Badge>
            )}
          </CardTitle>
        </CardHeader>
        <CardContent className="p-0">
          <ChatNegotiation
            scenarioId="document_based"
            aiCharacter={{
              name: "Sarah Mitchell",
              title: "Contract Specialist",
              personality: ["analytical", "detail-oriented", "collaborative"],
              backstory: `Experienced contract negotiator who has reviewed your ${documentContext?.contractType || 'contract'} and understands the specific issues that need to be addressed.`
            }}
            onComplete={onComplete}
            useBackend={true}
            documentContext={documentContext}
          />
        </CardContent>
      </Card>

      {/* Progress Tracking */}
      {documentContext && (
        <Card className="bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20">
          <CardContent className="p-4">
            <div className="flex items-center justify-between">
              <div>
                <h4 className="font-medium text-green-900 dark:text-green-100">
                  Document-Based Practice Session
                </h4>
                <p className="text-sm text-green-700 dark:text-green-300">
                  AI responses are tailored to your contract's specific issues and risk level
                </p>
              </div>
              <div className="flex items-center gap-2">
                <CheckCircle className="h-5 w-5 text-green-600" />
                <span className="text-sm font-medium text-green-800 dark:text-green-200">
                  Context Loaded
                </span>
              </div>
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
