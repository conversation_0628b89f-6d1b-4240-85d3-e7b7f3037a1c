"use client";

import React, { useState, useEffect } from 'react';
import { 
  Play, 
  Pause, 
  Clock, 
  Trophy, 
  Target,
  MoreHorizontal,
  Eye,
  Trash2,
  Calendar,
  Filter
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { useRouter } from 'next/navigation';
import { negotiationSimulatorService } from '@/lib/services/negotiation-simulator-service';
import type { NegotiationSession } from '@/lib/types/negotiation-simulator';

export function SessionsList() {
  const [sessions, setSessions] = useState<NegotiationSession[]>([]);
  const [loading, setLoading] = useState(true);
  const [filterStatus, setFilterStatus] = useState<string>('all');
  const router = useRouter();

  useEffect(() => {
    fetchSessions();
  }, [filterStatus]);

  const fetchSessions = async () => {
    setLoading(true);
    try {
      const params = filterStatus !== 'all' ? { status: filterStatus } : {};
      const result = await negotiationSimulatorService.getSessions(params);
      setSessions(result.sessions);
    } catch (error) {
      console.error('Failed to fetch sessions:', error);
    } finally {
      setLoading(false);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'ACTIVE': return 'bg-green-100 text-green-800 border-green-200';
      case 'COMPLETED': return 'bg-blue-100 text-blue-800 border-blue-200';
      case 'PAUSED': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'ABANDONED': return 'bg-gray-100 text-gray-800 border-gray-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getStatusIcon = (status: string) => {
    switch (status) {
      case 'ACTIVE': return <Play className="h-3 w-3" />;
      case 'COMPLETED': return <Trophy className="h-3 w-3" />;
      case 'PAUSED': return <Pause className="h-3 w-3" />;
      case 'ABANDONED': return <Target className="h-3 w-3" />;
      default: return <Clock className="h-3 w-3" />;
    }
  };

  const formatDuration = (startedAt: Date, completedAt?: Date) => {
    const start = new Date(startedAt);
    const end = completedAt ? new Date(completedAt) : new Date();
    const diffMs = end.getTime() - start.getTime();
    const diffMins = Math.floor(diffMs / (1000 * 60));
    
    if (diffMins < 60) {
      return `${diffMins}m`;
    }
    const hours = Math.floor(diffMins / 60);
    const mins = diffMins % 60;
    return `${hours}h ${mins}m`;
  };

  const handleResumeSession = (sessionId: string) => {
    router.push(`/negotiation-simulator/sessions/${sessionId}`);
  };

  const handleViewSession = (sessionId: string) => {
    router.push(`/negotiation-simulator/sessions/${sessionId}`);
  };

  const handleDeleteSession = async (sessionId: string) => {
    try {
      await negotiationSimulatorService.abandonSession(sessionId);
      setSessions(sessions.filter(s => s.id !== sessionId));
    } catch (error) {
      console.error('Failed to delete session:', error);
    }
  };

  if (loading) {
    return (
      <div className="space-y-4">
        {[...Array(3)].map((_, i) => (
          <Card key={i}>
            <CardContent className="p-6">
              <div className="animate-pulse space-y-4">
                <div className="h-4 bg-muted rounded w-1/3"></div>
                <div className="h-3 bg-muted rounded w-2/3"></div>
                <div className="flex gap-2">
                  <div className="h-6 bg-muted rounded w-16"></div>
                  <div className="h-6 bg-muted rounded w-20"></div>
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Filter Controls */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Filter className="h-5 w-5" />
            Filter Sessions
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-4">
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="w-[180px]">
                <SelectValue placeholder="Filter by status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sessions</SelectItem>
                <SelectItem value="ACTIVE">Active</SelectItem>
                <SelectItem value="PAUSED">Paused</SelectItem>
                <SelectItem value="COMPLETED">Completed</SelectItem>
                <SelectItem value="ABANDONED">Abandoned</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </CardContent>
      </Card>

      {/* Sessions List */}
      <div className="space-y-4">
        {sessions.length === 0 ? (
          <Card>
            <CardContent className="py-12">
              <div className="text-center space-y-4">
                <Target className="h-12 w-12 text-muted-foreground mx-auto" />
                <div>
                  <h3 className="text-lg font-medium">No sessions found</h3>
                  <p className="text-muted-foreground">
                    {filterStatus !== 'all' 
                      ? `No ${filterStatus.toLowerCase()} sessions found`
                      : 'Start a negotiation scenario to see your sessions here'
                    }
                  </p>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          sessions.map((session) => (
            <Card key={session.id} className="hover:shadow-md transition-shadow">
              <CardContent className="p-6">
                <div className="flex items-start justify-between">
                  <div className="flex-1 space-y-3">
                    {/* Session Header */}
                    <div className="flex items-center gap-3">
                      <Badge className={getStatusColor(session.status)} variant="outline">
                        {getStatusIcon(session.status)}
                        <span className="ml-1">{session.status.toLowerCase()}</span>
                      </Badge>
                      <span className="text-sm text-muted-foreground">
                        Session #{session.id.slice(-8)}
                      </span>
                    </div>

                    {/* Session Metrics */}
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
                      <div className="flex items-center gap-2">
                        <Target className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Rounds:</span>
                        <span className="font-medium">{session.rounds.length}</span>
                      </div>
                      
                      <div className="flex items-center gap-2">
                        <Clock className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Duration:</span>
                        <span className="font-medium">
                          {formatDuration(session.startedAt, session.completedAt)}
                        </span>
                      </div>

                      {session.status === 'COMPLETED' && (
                        <div className="flex items-center gap-2">
                          <Trophy className="h-4 w-4 text-muted-foreground" />
                          <span className="text-muted-foreground">Score:</span>
                          <span className="font-medium">{session.metrics.finalScore.toFixed(1)}</span>
                        </div>
                      )}

                      <div className="flex items-center gap-2">
                        <Calendar className="h-4 w-4 text-muted-foreground" />
                        <span className="text-muted-foreground">Started:</span>
                        <span className="font-medium">
                          {new Date(session.startedAt).toLocaleDateString()}
                        </span>
                      </div>
                    </div>

                    {/* AI Personality Preview */}
                    <div className="text-sm">
                      <span className="text-muted-foreground">AI Style:</span>
                      <span className="ml-2 font-medium">
                        {session.aiPersonality.communicationStyle.toLowerCase()} • 
                        {Math.round(session.aiPersonality.aggressiveness * 100)}% aggressive • 
                        {Math.round(session.aiPersonality.flexibility * 100)}% flexible
                      </span>
                    </div>

                    {/* Performance Metrics for Completed Sessions */}
                    {session.status === 'COMPLETED' && (
                      <div className="grid grid-cols-2 md:grid-cols-4 gap-4 p-3 bg-muted/30 rounded-lg text-sm">
                        <div>
                          <div className="text-muted-foreground">Communication</div>
                          <div className="font-medium">
                            {(session.metrics.keyMetrics.communicationEffectiveness * 100).toFixed(0)}%
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">Strategy</div>
                          <div className="font-medium">
                            {(session.metrics.keyMetrics.strategicThinking * 100).toFixed(0)}%
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">Flexibility</div>
                          <div className="font-medium">
                            {(session.metrics.keyMetrics.flexibilityScore * 100).toFixed(0)}%
                          </div>
                        </div>
                        <div>
                          <div className="text-muted-foreground">Time Mgmt</div>
                          <div className="font-medium">
                            {(session.metrics.keyMetrics.timeManagement * 100).toFixed(0)}%
                          </div>
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Action Buttons */}
                  <div className="flex items-center gap-2">
                    {session.status === 'ACTIVE' || session.status === 'PAUSED' ? (
                      <Button
                        onClick={() => handleResumeSession(session.id)}
                        size="sm"
                        className="gap-2"
                      >
                        <Play className="h-4 w-4" />
                        {session.status === 'PAUSED' ? 'Resume' : 'Continue'}
                      </Button>
                    ) : (
                      <Button
                        variant="outline"
                        onClick={() => handleViewSession(session.id)}
                        size="sm"
                        className="gap-2"
                      >
                        <Eye className="h-4 w-4" />
                        View
                      </Button>
                    )}

                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" size="sm">
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem onClick={() => handleViewSession(session.id)}>
                          <Eye className="h-4 w-4 mr-2" />
                          View Details
                        </DropdownMenuItem>
                        {session.status !== 'COMPLETED' && (
                          <>
                            <DropdownMenuSeparator />
                            <DropdownMenuItem 
                              onClick={() => handleDeleteSession(session.id)}
                              className="text-red-600 focus:text-red-600"
                            >
                              <Trash2 className="h-4 w-4 mr-2" />
                              Abandon Session
                            </DropdownMenuItem>
                          </>
                        )}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
