"use client";

import React, { useEffect, useState } from 'react';
import { 
  User, 
  TrendingUp, 
  Target, 
  Clock, 
  Award,
  Edit,
  Save,
  X,
  RefreshCw
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Skeleton } from '@/components/ui/skeleton';
import { useToast } from '@/hooks/use-toast';
import { useNegotiationIntegration } from '@/hooks/use-negotiation-integration';
import type { 
  UpdateUserNegotiationProfileRequest,
  Difficulty 
} from '@/lib/types/negotiation-simulator';

export function UserProfileManagement() {
  const [isEditing, setIsEditing] = useState(false);
  const [editForm, setEditForm] = useState<UpdateUserNegotiationProfileRequest>({});
  
  const { toast } = useToast();
  const {
    userProfile,
    loading,
    error,
    hasAccess,
    getUserProfile,
    updateUserProfile,
    clearError
  } = useNegotiationIntegration();

  useEffect(() => {
    if (hasAccess) {
      getUserProfile();
    }
  }, [hasAccess, getUserProfile]);

  const handleEdit = () => {
    if (userProfile) {
      setEditForm({
        strongStrategies: userProfile.strongStrategies,
        weakAreas: userProfile.weakAreas,
        preferredStyle: userProfile.preferredStyle,
        skillLevel: userProfile.skillLevel,
      });
      setIsEditing(true);
    }
  };

  const handleSave = async () => {
    if (!userProfile) return;

    const result = await updateUserProfile(editForm);
    if (result) {
      setIsEditing(false);
      setEditForm({});
    }
  };

  const handleCancel = () => {
    setIsEditing(false);
    setEditForm({});
  };

  const getSkillLevelColor = (level: string) => {
    switch (level.toLowerCase()) {
      case 'beginner': return 'bg-green-100 text-green-800 border-green-200';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'expert': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const getPerformanceLevel = (score: number) => {
    if (score >= 80) return { level: 'Excellent', color: 'text-green-600' };
    if (score >= 60) return { level: 'Good', color: 'text-yellow-600' };
    if (score >= 40) return { level: 'Fair', color: 'text-orange-600' };
    return { level: 'Needs Improvement', color: 'text-red-600' };
  };

  if (!hasAccess) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Negotiation Profile
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground">
              User profile management requires PRO subscription
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Negotiation Profile
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            {[1, 2, 3, 4].map((i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-full" />
              </div>
            ))}
          </div>
          <div className="space-y-4">
            <Skeleton className="h-4 w-32" />
            <div className="grid grid-cols-3 gap-4">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <User className="h-5 w-5" />
              Negotiation Profile
            </div>
            <Button variant="outline" size="sm" onClick={clearError}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground mb-2">
              Failed to load profile
            </p>
            <p className="text-xs text-muted-foreground">
              {error}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!userProfile) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Negotiation Profile
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <User className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-sm text-muted-foreground mb-4">
              No profile data available
            </p>
            <p className="text-xs text-muted-foreground">
              Complete some negotiation sessions to build your profile
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const performanceLevel = getPerformanceLevel(userProfile.averageScore);

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <User className="h-5 w-5" />
            Negotiation Profile
          </div>
          {!isEditing ? (
            <Button variant="outline" size="sm" onClick={handleEdit}>
              <Edit className="h-4 w-4 mr-2" />
              Edit Profile
            </Button>
          ) : (
            <div className="flex gap-2">
              <Button variant="outline" size="sm" onClick={handleCancel}>
                <X className="h-4 w-4 mr-2" />
                Cancel
              </Button>
              <Button size="sm" onClick={handleSave}>
                <Save className="h-4 w-4 mr-2" />
                Save
              </Button>
            </div>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Performance Overview */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Award className="h-4 w-4" />
              <span className="text-sm font-medium">Performance</span>
            </div>
            <div className={`text-2xl font-bold ${performanceLevel.color}`}>
              {userProfile.averageScore.toFixed(1)}%
            </div>
            <p className="text-xs text-muted-foreground">
              {performanceLevel.level}
            </p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Target className="h-4 w-4" />
              <span className="text-sm font-medium">Sessions</span>
            </div>
            <div className="text-2xl font-bold">
              {userProfile.completedSessions}
            </div>
            <p className="text-xs text-muted-foreground">
              of {userProfile.totalSessions} total
            </p>
          </div>

          <div className="text-center">
            <div className="flex items-center justify-center gap-2 mb-2">
              <Clock className="h-4 w-4" />
              <span className="text-sm font-medium">Avg Duration</span>
            </div>
            <div className="text-2xl font-bold">
              {Math.round(userProfile.averageDuration)}m
            </div>
            <p className="text-xs text-muted-foreground">
              {userProfile.averageRounds.toFixed(1)} rounds avg
            </p>
          </div>
        </div>

        {/* Skill Level */}
        <div className="space-y-2">
          <Label>Skill Level</Label>
          {isEditing ? (
            <Select
              value={editForm.skillLevel || userProfile.skillLevel}
              onValueChange={(value: Difficulty) => 
                setEditForm(prev => ({ ...prev, skillLevel: value }))
              }
            >
              <SelectTrigger>
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="BEGINNER">Beginner</SelectItem>
                <SelectItem value="INTERMEDIATE">Intermediate</SelectItem>
                <SelectItem value="EXPERT">Expert</SelectItem>
              </SelectContent>
            </Select>
          ) : (
            <Badge className={getSkillLevelColor(userProfile.skillLevel)}>
              {userProfile.skillLevel}
            </Badge>
          )}
        </div>

        {/* Preferred Style */}
        <div className="space-y-2">
          <Label>Preferred Negotiation Style</Label>
          {isEditing ? (
            <Input
              value={editForm.preferredStyle || userProfile.preferredStyle}
              onChange={(e) => 
                setEditForm(prev => ({ ...prev, preferredStyle: e.target.value }))
              }
              placeholder="e.g., Collaborative, Competitive, Accommodating"
            />
          ) : (
            <p className="text-sm">{userProfile.preferredStyle || 'Not specified'}</p>
          )}
        </div>

        {/* Strong Strategies */}
        <div className="space-y-2">
          <Label>Strong Strategies</Label>
          {isEditing ? (
            <Textarea
              value={editForm.strongStrategies?.join(', ') || userProfile.strongStrategies.join(', ')}
              onChange={(e) => 
                setEditForm(prev => ({ 
                  ...prev, 
                  strongStrategies: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                }))
              }
              placeholder="Enter strategies separated by commas"
              rows={2}
            />
          ) : (
            <div className="flex flex-wrap gap-2">
              {userProfile.strongStrategies.map((strategy, index) => (
                <Badge key={index} variant="secondary">
                  {strategy}
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Weak Areas */}
        <div className="space-y-2">
          <Label>Areas for Improvement</Label>
          {isEditing ? (
            <Textarea
              value={editForm.weakAreas?.join(', ') || userProfile.weakAreas.join(', ')}
              onChange={(e) => 
                setEditForm(prev => ({ 
                  ...prev, 
                  weakAreas: e.target.value.split(',').map(s => s.trim()).filter(Boolean)
                }))
              }
              placeholder="Enter areas separated by commas"
              rows={2}
            />
          ) : (
            <div className="flex flex-wrap gap-2">
              {userProfile.weakAreas.map((area, index) => (
                <Badge key={index} variant="outline">
                  {area}
                </Badge>
              ))}
            </div>
          )}
        </div>

        {/* Progress Indicator */}
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Overall Progress</span>
            <span>{userProfile.averageScore.toFixed(1)}%</span>
          </div>
          <Progress value={userProfile.averageScore} className="h-2" />
        </div>

        {/* Last Updated */}
        <div className="text-xs text-muted-foreground text-center pt-4 border-t">
          Last updated: {new Date(userProfile.lastUpdated).toLocaleString()}
        </div>
      </CardContent>
    </Card>
  );
}
