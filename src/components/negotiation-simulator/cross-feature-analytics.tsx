"use client";

import React, { useEffect } from 'react';
import { 
  BarChart3, 
  TrendingUp, 
  Users, 
  Target, 
  ArrowUpRight,
  ArrowDownRight,
  Minus,
  RefreshCw
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Skeleton } from '@/components/ui/skeleton';
import { useNegotiationIntegration } from '@/hooks/use-negotiation-integration';

interface CrossFeatureAnalyticsProps {
  userId?: string;
  organizationId?: string;
  timeframe?: string;
}

export function CrossFeatureAnalytics({ 
  userId, 
  organizationId, 
  timeframe = '30d' 
}: CrossFeatureAnalyticsProps) {
  const {
    crossFeatureUsage,
    loading,
    error,
    hasAccess,
    getCrossFeatureUsage,
    clearError
  } = useNegotiationIntegration();

  useEffect(() => {
    if (hasAccess) {
      getCrossFeatureUsage({ timeframe, userId, organizationId });
    }
  }, [hasAccess, timeframe, userId, organizationId, getCrossFeatureUsage]);

  const getTrendIcon = (value: number, threshold: number = 0.5) => {
    if (value > threshold) {
      return <ArrowUpRight className="h-4 w-4 text-green-600" />;
    } else if (value < threshold * 0.8) {
      return <ArrowDownRight className="h-4 w-4 text-red-600" />;
    } else {
      return <Minus className="h-4 w-4 text-yellow-600" />;
    }
  };

  const getPerformanceColor = (value: number, threshold: number = 0.5) => {
    if (value > threshold) return 'text-green-600';
    if (value < threshold * 0.8) return 'text-red-600';
    return 'text-yellow-600';
  };

  const handleRefresh = () => {
    getCrossFeatureUsage({ timeframe, userId, organizationId });
  };

  if (!hasAccess) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Cross-Feature Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground">
              Cross-feature analytics requires PRO subscription
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Cross-Feature Analytics
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            {[1, 2, 3].map((i) => (
              <div key={i} className="space-y-2">
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-8 w-16" />
                <Skeleton className="h-2 w-full" />
              </div>
            ))}
          </div>
          <div className="space-y-4">
            <Skeleton className="h-4 w-32" />
            <div className="grid grid-cols-2 gap-4">
              <Skeleton className="h-16 w-full" />
              <Skeleton className="h-16 w-full" />
            </div>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Cross-Feature Analytics
            </div>
            <Button variant="outline" size="sm" onClick={clearError}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground mb-2">
              Failed to load analytics data
            </p>
            <p className="text-xs text-muted-foreground">
              {error}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!crossFeatureUsage) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Cross-Feature Analytics
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground">
              No analytics data available
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  const { playbookToSimulatorConversion, simulatorToPlaybookUsage, userEngagement } = crossFeatureUsage;

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <BarChart3 className="h-5 w-5" />
            Cross-Feature Analytics
          </div>
          <Button variant="outline" size="sm" onClick={handleRefresh}>
            <RefreshCw className="h-4 w-4 mr-2" />
            Refresh
          </Button>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-6">
        {/* Key Metrics */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Playbook → Simulator</span>
              {getTrendIcon(playbookToSimulatorConversion.conversionRate)}
            </div>
            <div className="text-2xl font-bold">
              {(playbookToSimulatorConversion.conversionRate * 100).toFixed(1)}%
            </div>
            <Progress 
              value={playbookToSimulatorConversion.conversionRate * 100} 
              className="h-2" 
            />
            <p className="text-xs text-muted-foreground">
              {playbookToSimulatorConversion.playbooksWithSimulation} of {playbookToSimulatorConversion.totalPlaybooks} playbooks
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Simulator → Playbook</span>
              {getTrendIcon(simulatorToPlaybookUsage.contextUsageRate)}
            </div>
            <div className="text-2xl font-bold">
              {(simulatorToPlaybookUsage.contextUsageRate * 100).toFixed(1)}%
            </div>
            <Progress 
              value={simulatorToPlaybookUsage.contextUsageRate * 100} 
              className="h-2" 
            />
            <p className="text-xs text-muted-foreground">
              {simulatorToPlaybookUsage.sessionsWithPlaybookContext} of {simulatorToPlaybookUsage.totalSessions} sessions
            </p>
          </div>

          <div className="space-y-2">
            <div className="flex items-center justify-between">
              <span className="text-sm font-medium">Cross-Feature Retention</span>
              {getTrendIcon(userEngagement.crossFeatureRetention)}
            </div>
            <div className="text-2xl font-bold">
              {(userEngagement.crossFeatureRetention * 100).toFixed(1)}%
            </div>
            <Progress 
              value={userEngagement.crossFeatureRetention * 100} 
              className="h-2" 
            />
            <p className="text-xs text-muted-foreground">
              Users engaging with both features
            </p>
          </div>
        </div>

        {/* Engagement Details */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">User Engagement Patterns</h4>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Target className="h-4 w-4" />
                <span className="text-sm font-medium">Sessions per Playbook</span>
              </div>
              <div className="text-xl font-bold mb-1">
                {userEngagement.averageSessionsPerPlaybook.toFixed(1)}
              </div>
              <p className="text-xs text-muted-foreground">
                Average practice sessions generated per playbook
              </p>
            </div>

            <div className="p-4 border rounded-lg">
              <div className="flex items-center gap-2 mb-2">
                <Users className="h-4 w-4" />
                <span className="text-sm font-medium">Playbooks per User</span>
              </div>
              <div className="text-xl font-bold mb-1">
                {userEngagement.averagePlaybooksPerUser.toFixed(1)}
              </div>
              <p className="text-xs text-muted-foreground">
                Average playbooks created per active user
              </p>
            </div>
          </div>
        </div>

        {/* Performance Indicators */}
        <div className="space-y-4">
          <h4 className="text-sm font-medium">Performance Indicators</h4>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-2">
            <Badge 
              variant={playbookToSimulatorConversion.conversionRate > 0.3 ? "default" : "secondary"}
              className="justify-center"
            >
              <TrendingUp className="h-3 w-3 mr-1" />
              Conversion
            </Badge>
            <Badge 
              variant={simulatorToPlaybookUsage.contextUsageRate > 0.4 ? "default" : "secondary"}
              className="justify-center"
            >
              <Target className="h-3 w-3 mr-1" />
              Context Usage
            </Badge>
            <Badge 
              variant={userEngagement.crossFeatureRetention > 0.5 ? "default" : "secondary"}
              className="justify-center"
            >
              <Users className="h-3 w-3 mr-1" />
              Retention
            </Badge>
            <Badge 
              variant={userEngagement.averageSessionsPerPlaybook > 2 ? "default" : "secondary"}
              className="justify-center"
            >
              <BarChart3 className="h-3 w-3 mr-1" />
              Engagement
            </Badge>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
