"use client";

import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { Trophy, Star, Zap, Award, X, Share2 } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';

import type { Achievement as ServiceAchievement } from '@/lib/services/gamification-service';

interface Achievement extends ServiceAchievement {
  unlockedAt?: Date;
  xpReward?: number;
}

interface AchievementNotificationProps {
  achievements: Achievement[];
  onClose: () => void;
  onShare?: (achievement: Achievement) => void;
}

const rarityColors = {
  common: 'from-gray-400 to-gray-600',
  rare: 'from-blue-400 to-blue-600',
  epic: 'from-purple-400 to-purple-600',
  legendary: 'from-yellow-400 to-orange-500'
};

const rarityBorders = {
  common: 'border-gray-300',
  rare: 'border-blue-300',
  epic: 'border-purple-300',
  legendary: 'border-yellow-300'
};

export function AchievementNotification({ 
  achievements, 
  onClose, 
  onShare 
}: AchievementNotificationProps) {
  const [currentIndex, setCurrentIndex] = useState(0);
  const [isVisible, setIsVisible] = useState(true);

  useEffect(() => {
    if (achievements.length === 0) {
      setIsVisible(false);
      return;
    }

    // Auto-advance through achievements
    if (currentIndex < achievements.length - 1) {
      const timer = setTimeout(() => {
        setCurrentIndex(prev => prev + 1);
      }, 3000);
      return () => clearTimeout(timer);
    }
  }, [currentIndex, achievements.length]);

  if (!isVisible || achievements.length === 0) return null;

  const currentAchievement = achievements[currentIndex];

  return (
    <AnimatePresence>
      <motion.div
        initial={{ opacity: 0, scale: 0.8, y: 50 }}
        animate={{ opacity: 1, scale: 1, y: 0 }}
        exit={{ opacity: 0, scale: 0.8, y: -50 }}
        className="fixed top-4 right-4 z-50 w-96"
      >
        <Card className={`${rarityBorders[currentAchievement.rarity]} border-2 shadow-2xl`}>
          <CardContent className="p-6">
            {/* Header */}
            <div className="flex items-start justify-between mb-4">
              <div className="flex items-center gap-2">
                <div className={`w-8 h-8 bg-gradient-to-r ${rarityColors[currentAchievement.rarity]} rounded-full flex items-center justify-center`}>
                  <Trophy className="h-4 w-4 text-white" />
                </div>
                <div>
                  <h3 className="font-bold text-lg">Achievement Unlocked!</h3>
                  <Badge className={`${rarityColors[currentAchievement.rarity]} text-white border-0`}>
                    {currentAchievement.rarity.toUpperCase()}
                  </Badge>
                </div>
              </div>
              <Button
                variant="ghost"
                size="sm"
                onClick={onClose}
                className="h-6 w-6 p-0"
              >
                <X className="h-4 w-4" />
              </Button>
            </div>

            {/* Achievement Content */}
            <div className="space-y-4">
              <div className="text-center">
                <div className="text-4xl mb-2">{currentAchievement.badge}</div>
                <h4 className="font-bold text-xl text-gray-900">
                  {currentAchievement.title}
                </h4>
                <p className="text-gray-600 text-sm mt-1">
                  {currentAchievement.description}
                </p>
              </div>

              {/* XP Reward */}
              {currentAchievement.xpReward && (
                <div className="flex items-center justify-center gap-2 bg-purple-50 rounded-lg p-3">
                  <Zap className="h-5 w-5 text-purple-600" />
                  <span className="font-bold text-purple-800">
                    +{currentAchievement.xpReward} XP
                  </span>
                </div>
              )}

              {/* Actions */}
              <div className="flex gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  className="flex-1"
                  onClick={() => onShare?.(currentAchievement)}
                >
                  <Share2 className="h-4 w-4 mr-1" />
                  Share
                </Button>
                <Button
                  size="sm"
                  className="flex-1"
                  onClick={onClose}
                >
                  Continue
                </Button>
              </div>

              {/* Progress indicator */}
              {achievements.length > 1 && (
                <div className="flex justify-center gap-1">
                  {achievements.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full ${
                        index === currentIndex ? 'bg-purple-600' : 'bg-gray-300'
                      }`}
                    />
                  ))}
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Celebration particles */}
        <div className="absolute inset-0 pointer-events-none">
          {[...Array(6)].map((_, i) => (
            <motion.div
              key={i}
              initial={{ opacity: 1, scale: 0, rotate: 0 }}
              animate={{ 
                opacity: 0, 
                scale: 1, 
                rotate: 360,
                x: Math.random() * 200 - 100,
                y: Math.random() * 200 - 100
              }}
              transition={{ duration: 2, delay: i * 0.1 }}
              className="absolute top-1/2 left-1/2 w-4 h-4"
            >
              <Star className="w-full h-full text-yellow-400" />
            </motion.div>
          ))}
        </div>
      </motion.div>
    </AnimatePresence>
  );
}

// XP Animation Component
interface XPAnimationProps {
  amount: number;
  show: boolean;
  onComplete: () => void;
}

export function XPAnimation({ amount, show, onComplete }: XPAnimationProps) {
  useEffect(() => {
    if (show) {
      const timer = setTimeout(onComplete, 2000);
      return () => clearTimeout(timer);
    }
  }, [show, onComplete]);

  if (!show) return null;

  return (
    <motion.div
      initial={{ opacity: 0, y: 0, scale: 0.8 }}
      animate={{ opacity: 1, y: -50, scale: 1 }}
      exit={{ opacity: 0, y: -100, scale: 0.8 }}
      className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50"
    >
      <div className="bg-purple-600 text-white px-6 py-3 rounded-full shadow-lg flex items-center gap-2">
        <Zap className="h-5 w-5" />
        <span className="font-bold text-lg">+{amount} XP</span>
      </div>
    </motion.div>
  );
}

// Level Up Modal Component
interface LevelUpdate {
  previousLevel: number;
  newLevel: number;
  xpGained: number;
  totalXP: number;
  leveledUp: boolean;
  newTitle: string;
  newUnlocks: string[];
}

interface LevelUpModalProps {
  levelUpdate: LevelUpdate;
  show: boolean;
  onClose: () => void;
}

export function LevelUpModal({ levelUpdate, show, onClose }: LevelUpModalProps) {
  if (!show) return null;

  return (
    <motion.div
      initial={{ opacity: 0 }}
      animate={{ opacity: 1 }}
      exit={{ opacity: 0 }}
      className="fixed inset-0 bg-black/50 flex items-center justify-center z-50"
    >
      <motion.div
        initial={{ scale: 0.8, opacity: 0 }}
        animate={{ scale: 1, opacity: 1 }}
        exit={{ scale: 0.8, opacity: 0 }}
        className="bg-white rounded-lg p-8 max-w-md w-full mx-4 text-center"
      >
        <div className="space-y-6">
          {/* Level Up Header */}
          <div>
            <motion.div
              initial={{ scale: 0 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.2, type: "spring" }}
              className="w-20 h-20 bg-gradient-to-r from-yellow-400 to-orange-500 rounded-full flex items-center justify-center mx-auto mb-4"
            >
              <Award className="h-10 w-10 text-white" />
            </motion.div>
            <h2 className="text-3xl font-bold text-gray-900 mb-2">Level Up!</h2>
            <p className="text-gray-600">
              You've reached <span className="font-bold text-purple-600">{levelUpdate.newTitle}</span>
            </p>
          </div>

          {/* Level Progress */}
          <div className="bg-gray-50 rounded-lg p-4">
            <div className="flex items-center justify-center gap-4 text-2xl font-bold">
              <span className="text-gray-400">{levelUpdate.previousLevel}</span>
              <span className="text-gray-400">→</span>
              <span className="text-purple-600">{levelUpdate.newLevel}</span>
            </div>
          </div>

          {/* New Unlocks */}
          {levelUpdate.newUnlocks.length > 0 && (
            <div>
              <h3 className="font-bold text-lg mb-3">New Unlocks:</h3>
              <div className="space-y-2">
                {levelUpdate.newUnlocks.map((unlock, index) => (
                  <motion.div
                    key={unlock}
                    initial={{ opacity: 0, x: -20 }}
                    animate={{ opacity: 1, x: 0 }}
                    transition={{ delay: 0.5 + index * 0.1 }}
                    className="bg-green-50 border border-green-200 rounded-lg p-3 text-green-800"
                  >
                    ✨ {unlock.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </motion.div>
                ))}
              </div>
            </div>
          )}

          {/* Continue Button */}
          <Button onClick={onClose} className="w-full">
            Continue Negotiating
          </Button>
        </div>
      </motion.div>
    </motion.div>
  );
}
