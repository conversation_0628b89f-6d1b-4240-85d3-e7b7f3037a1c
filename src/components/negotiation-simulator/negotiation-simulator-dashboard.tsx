"use client";

import React, { useState } from "react";
import {
	Users,
	Plus,
	Play,
	BarC<PERSON>3,
	Clock,
	Trophy,
	Target,
	TrendingUp,
	Filter,
	Search,
	MessageSquare,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Badge } from "@/components/ui/badge";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
	useNegotiationScenarios,
	useNegotiationAnalytics,
} from "@/hooks/use-negotiation-simulator";
import { FeatureGuard } from "@/components/subscription/feature-guard";
import { ScenarioCard } from "./scenario-card";
import { CreateScenarioModal } from "./create-scenario-modal";
import { SessionsList } from "./sessions-list";
import { AnalyticsDashboard } from "./analytics-dashboard";
import { CrossFeatureAnalytics } from "./cross-feature-analytics";
import { PersonalizedRecommendations } from "./personalized-recommendations";
import { UserProfileManagement } from "./user-profile-management";
import { SkillGapAnalysis } from "./skill-gap-analysis";
import { PerformanceHistory } from "./performance-history";
import type {
	Industry,
	Difficulty,
	ContractType,
} from "@/lib/types/negotiation-simulator";
import {
	INDUSTRY_OPTIONS,
	DIFFICULTY_OPTIONS,
	CONTRACT_TYPE_OPTIONS,
} from "@/lib/types/negotiation-simulator";

export function NegotiationSimulatorDashboard() {
	const [showCreateModal, setShowCreateModal] = useState(false);
	const [searchQuery, setSearchQuery] = useState("");
	const [filterIndustry, setFilterIndustry] = useState<string>("all");
	const [filterDifficulty, setFilterDifficulty] = useState<string>("all");
	const [filterContractType, setFilterContractType] = useState<string>("all");

	const {
		scenarios,
		loading: scenariosLoading,
		error: scenariosError,
		hasAccess,
		fetchScenarios,
		createScenario,
		deleteScenario,
		clearError: clearScenariosError,
	} = useNegotiationScenarios();

	const { analytics, loading: analyticsLoading } = useNegotiationAnalytics();

	// Filter scenarios based on search and filters
	const filteredScenarios = scenarios.filter((scenario) => {
		const matchesSearch =
			scenario.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
			scenario.description.toLowerCase().includes(searchQuery.toLowerCase());
		const matchesIndustry =
			filterIndustry === "all" || scenario.industry === filterIndustry;
		const matchesDifficulty =
			filterDifficulty === "all" || scenario.difficulty === filterDifficulty;
		const matchesContractType =
			filterContractType === "all" ||
			scenario.contractType === filterContractType;

		return (
			matchesSearch &&
			matchesIndustry &&
			matchesDifficulty &&
			matchesContractType
		);
	});

	const handleCreateScenario = async (data: any) => {
		const scenario = await createScenario(data);
		if (scenario) {
			setShowCreateModal(false);
		}
		return scenario;
	};

	const clearAllFilters = () => {
		setSearchQuery("");
		setFilterIndustry("all");
		setFilterDifficulty("all");
		setFilterContractType("all");
	};

	return (
		<FeatureGuard featureId="negotiation_simulator">
			<div className="container mx-auto p-6 space-y-6">
				{/* Header */}
				<div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
					<div>
						<h1 className="text-3xl font-bold flex items-center gap-3">
							<Users className="h-8 w-8 text-primary" />
							Negotiation Simulator
						</h1>
						<p className="text-muted-foreground mt-1">
							Practice and improve your negotiation skills with AI-powered
							scenarios
						</p>
					</div>

					<div className="flex items-center gap-2">
						<Button
							variant="outline"
							onClick={() => window.location.href = '/demo/chat-negotiation'}
							className="gap-2"
						>
							<MessageSquare className="h-4 w-4" />
							Chat Practice
						</Button>
						<Button
							onClick={() => setShowCreateModal(true)}
							disabled={!hasAccess}
							className="gap-2"
						>
							<Plus className="h-4 w-4" />
							Create Scenario
						</Button>
					</div>
				</div>

				{/* Quick Stats */}
				{analytics && (
					<div className="grid grid-cols-1 md:grid-cols-4 gap-4">
						<Card>
							<CardContent className="p-4">
								<div className="flex items-center gap-3">
									<div className="p-2 bg-blue-100 rounded-lg">
										<Target className="h-5 w-5 text-blue-600" />
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											Total Sessions
										</p>
										<p className="text-2xl font-bold">
											{analytics.totalSessions}
										</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardContent className="p-4">
								<div className="flex items-center gap-3">
									<div className="p-2 bg-green-100 rounded-lg">
										<Trophy className="h-5 w-5 text-green-600" />
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											Completion Rate
										</p>
										<p className="text-2xl font-bold">
											{(analytics.completionRate * 100).toFixed(0)}%
										</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardContent className="p-4">
								<div className="flex items-center gap-3">
									<div className="p-2 bg-purple-100 rounded-lg">
										<TrendingUp className="h-5 w-5 text-purple-600" />
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											Average Score
										</p>
										<p className="text-2xl font-bold">
											{analytics.averageScore.toFixed(1)}
										</p>
									</div>
								</div>
							</CardContent>
						</Card>

						<Card>
							<CardContent className="p-4">
								<div className="flex items-center gap-3">
									<div className="p-2 bg-orange-100 rounded-lg">
										<Clock className="h-5 w-5 text-orange-600" />
									</div>
									<div>
										<p className="text-sm text-muted-foreground">
											Avg Duration
										</p>
										<p className="text-2xl font-bold">
											{Math.round(analytics.averageDuration)}m
										</p>
									</div>
								</div>
							</CardContent>
						</Card>
					</div>
				)}

				{/* Main Content Tabs */}
				<Tabs defaultValue="scenarios" className="w-full">
					<TabsList className="grid w-full grid-cols-5">
						<TabsTrigger value="scenarios" className="gap-2">
							<Target className="h-4 w-4" />
							Scenarios
						</TabsTrigger>
						<TabsTrigger value="sessions" className="gap-2">
							<Play className="h-4 w-4" />
							Sessions
						</TabsTrigger>
						<TabsTrigger value="analytics" className="gap-2">
							<BarChart3 className="h-4 w-4" />
							Analytics
						</TabsTrigger>
						<TabsTrigger value="integration" className="gap-2">
							<TrendingUp className="h-4 w-4" />
							Integration
						</TabsTrigger>
						<TabsTrigger value="recommendations" className="gap-2">
							<Users className="h-4 w-4" />
							AI Insights
						</TabsTrigger>
					</TabsList>

					{/* Scenarios Tab */}
					<TabsContent value="scenarios" className="space-y-6">
						{/* Search and Filters */}
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<Filter className="h-5 w-5" />
									Search & Filter
								</CardTitle>
							</CardHeader>
							<CardContent className="space-y-4">
								<div className="flex flex-col sm:flex-row gap-4">
									<div className="flex-1">
										<div className="relative">
											<Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
											<Input
												placeholder="Search scenarios..."
												value={searchQuery}
												onChange={(e) => setSearchQuery(e.target.value)}
												className="pl-10"
											/>
										</div>
									</div>

									<div className="flex gap-2">
										<Select
											value={filterIndustry}
											onValueChange={setFilterIndustry}
										>
											<SelectTrigger className="w-[140px]">
												<SelectValue placeholder="Industry" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="all">All Industries</SelectItem>
												{INDUSTRY_OPTIONS.map((option) => (
													<SelectItem key={option.value} value={option.value}>
														{option.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>

										<Select
											value={filterDifficulty}
											onValueChange={setFilterDifficulty}
										>
											<SelectTrigger className="w-[130px]">
												<SelectValue placeholder="Difficulty" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="all">All Levels</SelectItem>
												{DIFFICULTY_OPTIONS.map((option) => (
													<SelectItem key={option.value} value={option.value}>
														{option.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>

										<Select
											value={filterContractType}
											onValueChange={setFilterContractType}
										>
											<SelectTrigger className="w-[150px]">
												<SelectValue placeholder="Contract Type" />
											</SelectTrigger>
											<SelectContent>
												<SelectItem value="all">All Types</SelectItem>
												{CONTRACT_TYPE_OPTIONS.map((option) => (
													<SelectItem key={option.value} value={option.value}>
														{option.label}
													</SelectItem>
												))}
											</SelectContent>
										</Select>

										<Button variant="outline" onClick={clearAllFilters}>
											Clear
										</Button>
									</div>
								</div>

								{/* Active Filters */}
								{(searchQuery ||
									filterIndustry !== "all" ||
									filterDifficulty !== "all" ||
									filterContractType !== "all") && (
									<div className="flex items-center gap-2 flex-wrap">
										<span className="text-sm text-muted-foreground">
											Active filters:
										</span>
										{searchQuery && (
											<Badge variant="secondary">Search: "{searchQuery}"</Badge>
										)}
										{filterIndustry !== "all" && (
											<Badge variant="secondary">
												Industry:{" "}
												{
													INDUSTRY_OPTIONS.find(
														(o) => o.value === filterIndustry
													)?.label
												}
											</Badge>
										)}
										{filterDifficulty !== "all" && (
											<Badge variant="secondary">
												Difficulty:{" "}
												{
													DIFFICULTY_OPTIONS.find(
														(o) => o.value === filterDifficulty
													)?.label
												}
											</Badge>
										)}
										{filterContractType !== "all" && (
											<Badge variant="secondary">
												Type:{" "}
												{
													CONTRACT_TYPE_OPTIONS.find(
														(o) => o.value === filterContractType
													)?.label
												}
											</Badge>
										)}
									</div>
								)}
							</CardContent>
						</Card>

						{/* Scenarios Grid */}
						<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
							{filteredScenarios.map((scenario) => (
								<ScenarioCard
									key={scenario.id}
									scenario={scenario}
									onDelete={deleteScenario}
								/>
							))}
						</div>

						{filteredScenarios.length === 0 && !scenariosLoading && (
							<Card>
								<CardContent className="py-12">
									<div className="text-center space-y-4">
										<Target className="h-12 w-12 text-muted-foreground mx-auto" />
										<div>
											<h3 className="text-lg font-medium">
												No scenarios found
											</h3>
											<p className="text-muted-foreground">
												{searchQuery ||
												filterIndustry !== "all" ||
												filterDifficulty !== "all" ||
												filterContractType !== "all"
													? "Try adjusting your search or filters"
													: "Create your first negotiation scenario to get started"}
											</p>
										</div>
										{!(
											searchQuery ||
											filterIndustry !== "all" ||
											filterDifficulty !== "all" ||
											filterContractType !== "all"
										) && (
											<Button onClick={() => setShowCreateModal(true)}>
												<Plus className="h-4 w-4 mr-2" />
												Create Scenario
											</Button>
										)}
									</div>
								</CardContent>
							</Card>
						)}
					</TabsContent>

					{/* Sessions Tab */}
					<TabsContent value="sessions">
						<SessionsList />
					</TabsContent>

					{/* Analytics Tab */}
					<TabsContent value="analytics">
						<AnalyticsDashboard
							analytics={analytics}
							loading={analyticsLoading}
						/>
					</TabsContent>

					{/* Integration Tab */}
					<TabsContent value="integration" className="space-y-6">
						<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
							<CrossFeatureAnalytics />
							<PerformanceHistory userId="current-user" />
						</div>
					</TabsContent>

					{/* Recommendations Tab */}
					<TabsContent value="recommendations" className="space-y-6">
						<div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
							<div className="space-y-6">
								<UserProfileManagement />
								<SkillGapAnalysis userId="current-user" />
							</div>
							<PersonalizedRecommendations
								userId="current-user" // This should come from auth context
								context="simulator"
							/>
						</div>
					</TabsContent>
				</Tabs>

				{/* Create Scenario Modal */}
				<CreateScenarioModal
					isOpen={showCreateModal}
					onClose={() => setShowCreateModal(false)}
					onCreateScenario={handleCreateScenario}
				/>
			</div>
		</FeatureGuard>
	);
}
