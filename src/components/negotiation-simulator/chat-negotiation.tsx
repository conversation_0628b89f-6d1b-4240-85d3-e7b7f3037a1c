"use client";

import React, { useState, useRef, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Send, 
  Bot, 
  User, 
  Lightbulb, 
  TrendingUp, 
  Clock,
  DollarSign,
  Target,
  Zap,
  MessageSquare,
  Sparkles,
  ThumbsUp,
  ThumbsDown,
  MoreHorizontal
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Separator } from '@/components/ui/separator';
import { Avatar, AvatarFallback } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { chatNegotiationService } from '@/lib/services/chat-negotiation-service';

interface ChatMessage {
  id: string;
  type: 'user' | 'ai' | 'system';
  content: string;
  timestamp: Date;
  extractedData?: {
    offer?: {
      price?: number;
      currency?: string;
      terms?: string[];
    };
    strategy?: string;
    sentiment?: 'positive' | 'neutral' | 'negative';
    confidence?: number;
  };
  suggestions?: string[];
  typing?: boolean;
}

interface NegotiationState {
  currentTerms: Record<string, any>;
  userPosition: Record<string, any>;
  aiPosition: Record<string, any>;
  round: number;
  phase: 'opening' | 'negotiating' | 'closing' | 'completed';
  score: number;
  relationship: {
    trust: number;
    respect: number;
    pressure: number;
  };
}

interface ChatNegotiationProps {
  scenarioId: string;
  aiCharacter: {
    name: string;
    title: string;
    personality: string[];
    avatar?: string;
  };
  onComplete?: (result: any) => void;
  useBackend?: boolean; // Toggle between mock and real backend
}

export function ChatNegotiation({ scenarioId, aiCharacter, onComplete, useBackend = false }: ChatNegotiationProps) {
  const [messages, setMessages] = useState<ChatMessage[]>([]);
  const [inputValue, setInputValue] = useState('');
  const [isTyping, setIsTyping] = useState(false);
  const [negotiationState, setNegotiationState] = useState<NegotiationState>({
    currentTerms: {},
    userPosition: {},
    aiPosition: {},
    round: 1,
    phase: 'opening',
    score: 5.0,
    relationship: {
      trust: 50,
      respect: 50,
      pressure: 20
    }
  });
  const [showSuggestions, setShowSuggestions] = useState(true);
  const [chatNegotiationSessionId, setChatNegotiationSessionId] = useState<string | null>(null);
  const [backendError, setBackendError] = useState<string | null>(null);
  const [creditsConsumed, setCreditsConsumed] = useState(0);
  const messagesEndRef = useRef<HTMLDivElement>(null);

  // Initialize conversation
  useEffect(() => {
    const initializeSession = async () => {
      try {
        if (useBackend) {
          // Initialize backend session
          const session = await chatNegotiationService.startChatNegotiation(scenarioId);
          setChatNegotiationSessionId(session.id);
          setNegotiationState(prev => ({
            ...prev,
            relationshipMetrics: session.relationshipMetrics,
            score: session.score
          }));
        }

        const welcomeMessage: ChatMessage = {
          id: '1',
          type: 'ai',
          content: `Hi! I'm ${aiCharacter.name}, ${aiCharacter.title}. I'm excited to discuss this ${getScenarioName(scenarioId)} with you. What would you like to start with?`,
          timestamp: new Date(),
          suggestions: [
            "Let's discuss the main terms first",
            "I'd like to understand your priorities",
            "Can we start with pricing?",
            "What's your timeline for this deal?"
          ]
        };
        setMessages([welcomeMessage]);
      } catch (error) {
        console.error('Failed to initialize session:', error);
        setBackendError('Failed to start negotiation session. Using offline mode.');

        // Fallback to mock mode
        const welcomeMessage: ChatMessage = {
          id: '1',
          type: 'ai',
          content: `Hi! I'm ${aiCharacter.name}, ${aiCharacter.title}. I'm excited to discuss this ${getScenarioName(scenarioId)} with you. What would you like to start with?`,
          timestamp: new Date(),
          suggestions: [
            "Let's discuss the main terms first",
            "I'd like to understand your priorities",
            "Can we start with pricing?",
            "What's your timeline for this deal?"
          ]
        };
        setMessages([welcomeMessage]);
      }
    };

    initializeSession();
  }, [scenarioId, aiCharacter, useBackend]);

  // Auto-scroll to bottom
  useEffect(() => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  }, [messages]);

  const getScenarioName = (id: string) => {
    const names: Record<string, string> = {
      'software_licensing': 'software licensing agreement',
      'real_estate': 'property purchase',
      'salary': 'compensation package',
      'service_contract': 'service agreement',
      'partnership': 'partnership deal',
      'acquisition': 'acquisition'
    };
    return names[id] || 'negotiation';
  };

  const extractDataFromMessage = (message: string): ChatMessage['extractedData'] => {
    const lowerMessage = message.toLowerCase();

    // Enhanced price extraction
    const pricePatterns = [
      /\$?([\d,]+(?:\.\d{2})?)\s*(?:k|thousand)/i,  // $50k, 50 thousand
      /\$?([\d,]+(?:\.\d{2})?)\s*(?:m|million)/i,   // $2m, 2 million
      /\$?([\d,]+(?:\.\d{2})?)/,                     // $50000, 50000
    ];

    let price: number | undefined;
    let currency = 'USD';

    for (const pattern of pricePatterns) {
      const match = message.match(pattern);
      if (match) {
        let value = parseFloat(match[1].replace(',', ''));
        if (lowerMessage.includes('k') || lowerMessage.includes('thousand')) {
          value *= 1000;
        } else if (lowerMessage.includes('m') || lowerMessage.includes('million')) {
          value *= 1000000;
        }
        price = value;
        break;
      }
    }

    // Enhanced strategy detection
    const strategies = {
      'collaborative': ['work together', 'mutual', 'both', 'win-win', 'partnership', 'collaborate', 'team up'],
      'competitive': ['need', 'must have', 'require', 'demand', 'insist', 'non-negotiable', 'final offer'],
      'accommodating': ['flexible', 'open to', 'willing', 'consider', 'adjust', 'compromise', 'meet you'],
      'analytical': ['data', 'numbers', 'research', 'market rate', 'industry standard', 'benchmark'],
      'relationship': ['long-term', 'future', 'relationship', 'trust', 'partnership', 'ongoing']
    };

    let detectedStrategy = 'collaborative';
    let maxMatches = 0;

    for (const [strategy, keywords] of Object.entries(strategies)) {
      const matches = keywords.filter(keyword => lowerMessage.includes(keyword)).length;
      if (matches > maxMatches) {
        maxMatches = matches;
        detectedStrategy = strategy;
      }
    }

    // Enhanced sentiment analysis
    const positiveWords = ['great', 'perfect', 'excellent', 'love', 'excited', 'fantastic', 'wonderful'];
    const negativeWords = ['concerned', 'worried', 'problem', 'issue', 'difficult', 'challenging', 'tight'];
    const urgentWords = ['urgent', 'asap', 'quickly', 'deadline', 'rush', 'immediately'];

    let sentiment: 'positive' | 'neutral' | 'negative' = 'neutral';
    if (positiveWords.some(word => lowerMessage.includes(word))) {
      sentiment = 'positive';
    } else if (negativeWords.some(word => lowerMessage.includes(word)) || urgentWords.some(word => lowerMessage.includes(word))) {
      sentiment = 'negative';
    }

    // Extract terms
    const terms: string[] = [];
    const termPatterns = [
      /(\d+)\s*(?:years?|months?|weeks?|days?)/gi,  // Time periods
      /(\d+)%/g,                                     // Percentages
      /(?:remote|hybrid|office)/gi,                  // Work arrangements
      /(?:quarterly|monthly|annually|yearly)/gi,     // Payment terms
    ];

    termPatterns.forEach(pattern => {
      const matches = message.match(pattern);
      if (matches) {
        terms.push(...matches);
      }
    });

    return {
      offer: price ? { price, currency, terms } : undefined,
      strategy: detectedStrategy,
      sentiment,
      confidence: maxMatches > 0 ? 0.9 : 0.6
    };
  };

  const generateAIResponse = async (userMessage: string, extractedData: any): Promise<ChatMessage> => {
    setIsTyping(true);
    
    // Simulate AI thinking time
    await new Promise(resolve => setTimeout(resolve, 1000 + Math.random() * 2000));
    
    const responses = getContextualResponses(userMessage, extractedData, negotiationState);
    const response = responses[Math.floor(Math.random() * responses.length)];
    
    // Update negotiation state based on the exchange
    setNegotiationState(prev => ({
      ...prev,
      round: prev.round + 1,
      score: Math.max(1, Math.min(10, prev.score + (Math.random() - 0.5) * 2)),
      relationship: {
        trust: Math.max(0, Math.min(100, prev.relationship.trust + (extractedData.sentiment === 'positive' ? 5 : -2))),
        respect: Math.max(0, Math.min(100, prev.relationship.respect + (extractedData.strategy === 'collaborative' ? 3 : -1))),
        pressure: Math.max(0, Math.min(100, prev.relationship.pressure + (extractedData.sentiment === 'negative' ? 10 : -2)))
      }
    }));

    setIsTyping(false);
    
    return {
      id: Date.now().toString(),
      type: 'ai',
      content: response.content,
      timestamp: new Date(),
      extractedData: response.extractedData,
      suggestions: response.suggestions
    };
  };

  const getContextualResponses = (userMessage: string, userData: any, state: NegotiationState) => {
    const responses = [];

    // Relationship-based response modifiers
    const trustLevel = state.relationship.trust;
    const respectLevel = state.relationship.respect;
    const pressureLevel = state.relationship.pressure;

    // Base responses based on AI character personality
    if (aiCharacter.personality.includes('analytical')) {
      responses.push({
        content: "Let me break down the numbers on this. Based on our market analysis and current pricing models...",
        extractedData: { sentiment: 'neutral', strategy: 'analytical' },
        suggestions: ["Can you share your budget range?", "What's driving your timeline?", "How does this compare to other options?"]
      });
    }

    if (aiCharacter.personality.includes('collaborative')) {
      responses.push({
        content: "I love that we're thinking about this as a partnership. How can we structure this so it works well for both teams?",
        extractedData: { sentiment: 'positive', strategy: 'collaborative' },
        suggestions: ["What would success look like for you?", "Are there other ways we can add value?", "Let's think long-term here"]
      });
    }

    // Scenario-specific responses with enhanced context
    if (scenarioId === 'software_licensing') {
      if (userData.offer?.price) {
        const priceReaction = userData.offer.price > 100000 ?
          "That's a substantial investment. Let's make sure you get maximum value." :
          userData.offer.price < 20000 ?
          "I appreciate you being budget-conscious. Let's see what we can do within that range." :
          "That's in a reasonable range. Let's talk about what's included.";

        responses.push({
          content: `${priceReaction} For ${userData.offer.price.toLocaleString()}, we could look at different tiers. What's your expected user growth over the next 2 years?`,
          extractedData: { sentiment: 'neutral', strategy: 'collaborative' },
          suggestions: ["We expect to double our team size", "Growth will be gradual", "Can you offer scalable pricing?", "What about a pilot program?"]
        });
      }

      if (userMessage.toLowerCase().includes('support')) {
        responses.push({
          content: "Support is crucial for success. We offer 24/7 premium support, but I'm curious - what kind of challenges are you anticipating?",
          extractedData: { sentiment: 'positive', strategy: 'relationship' },
          suggestions: ["We're new to this type of software", "Our team needs training", "What's included in premium support?"]
        });
      }
    }

    if (scenarioId === 'salary') {
      if (userData.offer?.price) {
        const salaryFeedback = userData.offer.price > 150000 ?
          "That's at the top of our range, but for the right candidate, we can make it work." :
          userData.offer.price < 80000 ?
          "That's below our typical range for this role. Let me understand your expectations better." :
          "That's definitely in the ballpark. Let's talk about the full package.";

        responses.push({
          content: `${salaryFeedback} Beyond base salary, we have equity, professional development budget, and flexible work arrangements. What matters most to you?`,
          extractedData: { sentiment: 'positive', strategy: 'collaborative' },
          suggestions: ["Equity is important to me", "I value work-life balance", "Professional growth opportunities", "What's the equity range?"]
        });
      }

      if (userMessage.toLowerCase().includes('remote')) {
        responses.push({
          content: "We're very flexible on remote work. Most of our team is hybrid. What arrangement would work best for you?",
          extractedData: { sentiment: 'positive', strategy: 'accommodating' },
          suggestions: ["Fully remote preferred", "Hybrid sounds good", "What days do people come in?", "Is there a home office stipend?"]
        });
      }
    }

    if (scenarioId === 'real_estate') {
      if (userData.offer?.price) {
        const marketResponse = userData.offer.price > 800000 ?
          "That's a strong offer in this market. The sellers will definitely take notice." :
          "In this competitive market, we might need to be more aggressive to win.";

        responses.push({
          content: `${marketResponse} Are you pre-approved for financing? That could make your offer more attractive.`,
          extractedData: { sentiment: 'neutral', strategy: 'competitive' },
          suggestions: ["Yes, we're pre-approved", "We're paying cash", "What would make our offer stronger?", "How many other offers are there?"]
        });
      }
    }

    // Pressure and relationship adjustments
    if (pressureLevel > 70) {
      responses.forEach(r => {
        r.content += " Time is definitely a factor here, so let's focus on the key terms.";
      });
    }

    if (trustLevel < 30) {
      responses.forEach(r => {
        r.content = r.content.replace(/Let's/, "Perhaps we could").replace(/we can/, "we might be able to");
      });
    }

    // Default fallback responses
    if (responses.length === 0) {
      responses.push({
        content: "That's a good point. Help me understand your priorities better so we can find the right solution.",
        extractedData: { sentiment: 'neutral', strategy: 'collaborative' },
        suggestions: ["What's most important to you?", "Are there any deal-breakers?", "What's your timeline?"]
      });
    }

    return responses;
  };

  const handleSendMessage = async () => {
    if (!inputValue.trim()) return;

    const userMessage: ChatMessage = {
      id: Date.now().toString(),
      type: 'user',
      content: inputValue.trim(),
      timestamp: new Date(),
      extractedData: extractDataFromMessage(inputValue.trim())
    };

    setMessages(prev => [...prev, userMessage]);
    setInputValue('');

    try {
      if (useBackend && chatNegotiationSessionId) {
        // Use backend service
        const result = await chatNegotiationService.sendChatMove(chatNegotiationSessionId, {
          content: userMessage.content,
          extractedData: userMessage.extractedData,
          timestamp: userMessage.timestamp
        });

        // Create chat messages from backend response
        const backendUserMessage: ChatMessage = {
          id: Date.now().toString(),
          type: 'user',
          content: result.userMove.content,
          timestamp: new Date(result.userMove.timestamp),
          extractedData: result.userMove.extractedData
        };

        const backendAiMessage: ChatMessage = {
          id: (Date.now() + 1).toString(),
          type: 'ai',
          content: result.aiResponse.content,
          timestamp: new Date(result.aiResponse.timestamp),
          suggestions: result.aiResponse.suggestions
        };

        // Update messages
        setMessages(prev => [
          ...prev.slice(0, -1), // Remove the user message we added
          backendUserMessage,
          backendAiMessage
        ]);

        // Update negotiation state
        setNegotiationState(prev => ({
          ...prev,
          round: result.sessionUpdate.currentRound,
          relationshipMetrics: result.sessionUpdate.relationshipMetrics,
          score: result.sessionUpdate.score,
          phase: result.sessionUpdate.status === 'completed' ? 'completed' : prev.phase
        }));

        // Track credits consumed
        setCreditsConsumed(prev => prev + result.creditsConsumed);
      } else {
        // Use mock/local logic
        const aiResponse = await generateAIResponse(userMessage.content, userMessage.extractedData);
        setMessages(prev => [...prev, aiResponse]);
      }
    } catch (error) {
      console.error('Failed to send message:', error);
      setBackendError('Failed to send message. Using offline mode.');

      // Fallback to mock response
      const aiResponse = await generateAIResponse(userMessage.content, userMessage.extractedData);
      setMessages(prev => [...prev, aiResponse]);
    }
  };

  const handleSuggestionClick = (suggestion: string) => {
    setInputValue(suggestion);
  };

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      handleSendMessage();
    }
  };

  const getMessageVariant = (message: ChatMessage) => {
    if (message.type === 'user') return 'user';
    if (message.type === 'system') return 'system';
    return 'ai';
  };

  return (
    <div className="flex flex-col h-[600px] bg-card rounded-lg border border-border">
      {/* Header */}
      <div className="flex items-center justify-between p-4 border-b border-border bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20">
        <div className="flex items-center gap-3">
          <Avatar>
            <AvatarFallback className="bg-blue-100 text-blue-600">
              {aiCharacter.name.split(' ').map(n => n[0]).join('')}
            </AvatarFallback>
          </Avatar>
          <div>
            <h3 className="font-semibold">{aiCharacter.name}</h3>
            <p className="text-sm text-muted-foreground">{aiCharacter.title}</p>
          </div>
        </div>
        
        <div className="flex items-center gap-4">
          <div className="text-center">
            <div className="text-lg font-bold text-green-600">{negotiationState.score.toFixed(1)}</div>
            <div className="text-xs text-muted-foreground">Score</div>
          </div>
          <div className="text-center">
            <div className="text-lg font-bold text-purple-600">R{negotiationState.round}</div>
            <div className="text-xs text-muted-foreground">Round</div>
          </div>
          {useBackend && creditsConsumed > 0 && (
            <div className="text-center">
              <div className="text-lg font-bold text-orange-600">{creditsConsumed}</div>
              <div className="text-xs text-muted-foreground">Credits</div>
            </div>
          )}
          {useBackend && (
            <div className="text-center">
              <div className={`text-xs px-2 py-1 rounded-full ${
                chatNegotiationSessionId ? 'bg-green-100 text-green-800' : 'bg-yellow-100 text-yellow-800'
              }`}>
                {chatNegotiationSessionId ? 'Live' : 'Connecting...'}
              </div>
            </div>
          )}
          {backendError && (
            <div className="text-center">
              <div className="text-xs px-2 py-1 rounded-full bg-red-100 text-red-800">
                Offline
              </div>
            </div>
          )}
        </div>
      </div>

      {/* Relationship Status */}
      <div className="p-3 bg-muted/50 border-b border-border">
        <div className="grid grid-cols-3 gap-4 text-sm">
          <div>
            <div className="flex justify-between mb-1">
              <span>Trust</span>
              <span>{negotiationState.relationship.trust}%</span>
            </div>
            <Progress value={negotiationState.relationship.trust} className="h-1" />
          </div>
          <div>
            <div className="flex justify-between mb-1">
              <span>Respect</span>
              <span>{negotiationState.relationship.respect}%</span>
            </div>
            <Progress value={negotiationState.relationship.respect} className="h-1" />
          </div>
          <div>
            <div className="flex justify-between mb-1">
              <span>Pressure</span>
              <span>{negotiationState.relationship.pressure}%</span>
            </div>
            <Progress value={negotiationState.relationship.pressure} className="h-1 bg-red-100" />
          </div>
        </div>
      </div>

      {/* Messages */}
      <div className="flex-1 overflow-y-auto p-4 space-y-4">
        <AnimatePresence>
          {messages.map((message) => (
            <motion.div
              key={message.id}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              className={`flex gap-3 ${message.type === 'user' ? 'justify-end' : 'justify-start'}`}
            >
              {message.type === 'ai' && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="bg-muted">
                    <Bot className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
              )}
              
              <div className={`max-w-[70%] ${message.type === 'user' ? 'order-first' : ''}`}>
                <div className={`rounded-lg p-3 ${
                  message.type === 'user'
                    ? 'bg-primary text-primary-foreground'
                    : 'bg-muted text-muted-foreground'
                }`}>
                  <p className="text-sm">{message.content}</p>
                  
                  {/* Extracted data indicators */}
                  {message.extractedData && (
                    <div className="mt-2 flex gap-2">
                      {message.extractedData.offer?.price && (
                        <Badge variant="secondary" className="text-xs">
                          <DollarSign className="h-3 w-3 mr-1" />
                          ${message.extractedData.offer.price.toLocaleString()}
                        </Badge>
                      )}
                      {message.extractedData.strategy && (
                        <Badge variant="outline" className="text-xs capitalize">
                          {message.extractedData.strategy}
                        </Badge>
                      )}
                    </div>
                  )}
                </div>
                
                <div className="text-xs text-muted-foreground mt-1">
                  {message.timestamp.toLocaleTimeString()}
                </div>

                {/* AI message suggestions */}
                {message.type === 'ai' && message.suggestions && showSuggestions && (
                  <div className="mt-3 space-y-2">
                    <p className="text-xs text-muted-foreground">Suggested responses:</p>
                    {message.suggestions.map((suggestion, index) => (
                      <Button
                        key={index}
                        variant="outline"
                        size="sm"
                        className="text-xs h-auto py-1 px-2 mr-2"
                        onClick={() => handleSuggestionClick(suggestion)}
                      >
                        {suggestion}
                      </Button>
                    ))}
                  </div>
                )}
              </div>

              {message.type === 'user' && (
                <Avatar className="w-8 h-8">
                  <AvatarFallback className="bg-primary/10">
                    <User className="h-4 w-4" />
                  </AvatarFallback>
                </Avatar>
              )}
            </motion.div>
          ))}
        </AnimatePresence>

        {/* Typing indicator */}
        {isTyping && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            className="flex gap-3"
          >
            <Avatar className="w-8 h-8">
              <AvatarFallback className="bg-muted">
                <Bot className="h-4 w-4" />
              </AvatarFallback>
            </Avatar>
            <div className="bg-muted rounded-lg p-3">
              <div className="flex gap-1">
                <div className="w-2 h-2 bg-muted-foreground/60 rounded-full animate-bounce" />
                <div className="w-2 h-2 bg-muted-foreground/60 rounded-full animate-bounce" style={{ animationDelay: '0.1s' }} />
                <div className="w-2 h-2 bg-muted-foreground/60 rounded-full animate-bounce" style={{ animationDelay: '0.2s' }} />
              </div>
            </div>
          </motion.div>
        )}
        
        <div ref={messagesEndRef} />
      </div>

      {/* Input */}
      <div className="p-4 border-t border-border bg-muted/30">
        <div className="flex gap-2">
          <Input
            value={inputValue}
            onChange={(e) => setInputValue(e.target.value)}
            onKeyPress={handleKeyPress}
            placeholder="Type your message..."
            className="flex-1"
            disabled={isTyping}
          />
          <Button 
            onClick={handleSendMessage} 
            disabled={!inputValue.trim() || isTyping}
            className="gap-2"
          >
            <Send className="h-4 w-4" />
          </Button>
        </div>
        
        <div className="flex items-center justify-between mt-2 text-xs text-muted-foreground">
          <span>Press Enter to send, Shift+Enter for new line</span>
          <Button
            variant="ghost"
            size="sm"
            onClick={() => setShowSuggestions(!showSuggestions)}
            className="text-xs"
          >
            {showSuggestions ? 'Hide' : 'Show'} suggestions
          </Button>
        </div>
      </div>
    </div>
  );
}
