"use client";

import React, { useEffect } from 'react';
import { 
  Lightbulb, 
  Target, 
  TrendingUp, 
  FileText, 
  Play,
  Clock,
  ArrowRight,
  RefreshCw,
  Star
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useRouter } from 'next/navigation';
import { useNegotiationIntegration } from '@/hooks/use-negotiation-integration';

interface PersonalizedRecommendationsProps {
  userId: string;
  context: "playbook" | "simulator" | "general";
  targetArea?: string;
  currentDocumentId?: string;
  currentSessionId?: string;
}

export function PersonalizedRecommendations({
  userId,
  context,
  targetArea,
  currentDocumentId,
  currentSessionId
}: PersonalizedRecommendationsProps) {
  const router = useRouter();
  const {
    recommendations,
    loading,
    error,
    hasAccess,
    generateRecommendations,
    clearError
  } = useNegotiationIntegration();

  useEffect(() => {
    if (hasAccess && userId) {
      generateRecommendations({
        userId,
        context,
        targetArea,
        currentDocumentId,
        currentSessionId
      });
    }
  }, [hasAccess, userId, context, targetArea, currentDocumentId, currentSessionId, generateRecommendations]);

  const getRecommendationIcon = (type: string) => {
    switch (type) {
      case 'practice_scenario': return <Play className="h-4 w-4" />;
      case 'skill_focus': return <Target className="h-4 w-4" />;
      case 'strategy_improvement': return <TrendingUp className="h-4 w-4" />;
      case 'document_analysis': return <FileText className="h-4 w-4" />;
      default: return <Lightbulb className="h-4 w-4" />;
    }
  };

  const getPriorityColor = (priority: string) => {
    switch (priority) {
      case 'high': return 'bg-red-100 text-red-800 border-red-200';
      case 'medium': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'low': return 'bg-green-100 text-green-800 border-green-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleRecommendationClick = (recommendation: any) => {
    if (recommendation.actionUrl) {
      router.push(recommendation.actionUrl);
    }
  };

  const handleRefresh = () => {
    generateRecommendations({
      userId,
      context,
      targetArea,
      currentDocumentId,
      currentSessionId
    });
  };

  if (!hasAccess) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Personalized Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground">
              Personalized recommendations require PRO subscription
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Personalized Recommendations
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-4 border rounded-lg space-y-2">
              <div className="flex items-center gap-2">
                <Skeleton className="h-4 w-4" />
                <Skeleton className="h-4 w-24" />
                <Skeleton className="h-5 w-12" />
              </div>
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-full" />
              <div className="flex items-center gap-2">
                <Skeleton className="h-3 w-3" />
                <Skeleton className="h-3 w-16" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Personalized Recommendations
            </div>
            <Button variant="outline" size="sm" onClick={clearError}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Retry
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground mb-2">
              Failed to load recommendations
            </p>
            <p className="text-xs text-muted-foreground">
              {error}
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (!recommendations || recommendations.recommendations.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <Lightbulb className="h-5 w-5" />
              Personalized Recommendations
            </div>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Lightbulb className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-sm text-muted-foreground mb-2">
              No recommendations available at this time
            </p>
            <p className="text-xs text-muted-foreground">
              Complete more activities to get personalized suggestions
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Lightbulb className="h-5 w-5" />
            Personalized Recommendations
          </div>
          <div className="flex items-center gap-2">
            <div className="flex items-center gap-1 text-xs text-muted-foreground">
              <Star className="h-3 w-3" />
              {(recommendations.confidenceScore * 100).toFixed(0)}% confidence
            </div>
            <Button variant="outline" size="sm" onClick={handleRefresh}>
              <RefreshCw className="h-4 w-4 mr-2" />
              Refresh
            </Button>
          </div>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {/* AI Reasoning */}
        {recommendations.reasoning && (
          <div className="p-3 bg-muted/50 rounded-lg">
            <p className="text-sm text-muted-foreground">
              <strong>AI Analysis:</strong> {recommendations.reasoning}
            </p>
          </div>
        )}

        {/* Recommendations List */}
        <div className="space-y-3">
          {recommendations.recommendations.map((rec, index) => (
            <div
              key={index}
              className={`p-4 border rounded-lg transition-colors ${
                rec.actionUrl ? 'hover:bg-accent/50 cursor-pointer' : ''
              }`}
              onClick={() => handleRecommendationClick(rec)}
            >
              <div className="flex items-start justify-between mb-2">
                <div className="flex items-center gap-2">
                  {getRecommendationIcon(rec.type)}
                  <h4 className="font-medium text-sm">{rec.title}</h4>
                </div>
                <div className="flex items-center gap-2">
                  <Badge className={getPriorityColor(rec.priority)}>
                    {rec.priority}
                  </Badge>
                  {rec.actionUrl && (
                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  )}
                </div>
              </div>
              
              <p className="text-sm text-muted-foreground mb-3">
                {rec.description}
              </p>
              
              <div className="flex items-center gap-4 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <Clock className="h-3 w-3" />
                  {rec.estimatedTime} min
                </div>
                
                {rec.metadata?.difficulty && (
                  <Badge variant="outline" className="text-xs">
                    {rec.metadata.difficulty}
                  </Badge>
                )}
                
                {rec.metadata?.category && (
                  <Badge variant="outline" className="text-xs">
                    {rec.metadata.category}
                  </Badge>
                )}
              </div>
            </div>
          ))}
        </div>

        {/* Context Information */}
        <div className="pt-4 border-t">
          <div className="flex items-center justify-between text-xs text-muted-foreground">
            <span>Context: {context}</span>
            <span>
              Based on your recent activity and performance patterns
            </span>
          </div>
        </div>
      </CardContent>
    </Card>
  );
}
