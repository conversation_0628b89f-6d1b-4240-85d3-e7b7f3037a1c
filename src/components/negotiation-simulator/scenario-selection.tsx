"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Building2, 
  Home, 
  DollarSign, 
  FileText, 
  Handshake, 
  TrendingUp,
  Clock,
  Users,
  Star,
  ChevronRight,
  Info,
  Zap
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Progress } from '@/components/ui/progress';
import { SCENARIO_CONFIGS, ScenarioConfig } from '@/lib/negotiation/move-context';

interface ScenarioSelectionProps {
  onSelectScenario: (scenarioId: string) => void;
  userLevel?: number;
}

const SCENARIO_ICONS: Record<string, React.ComponentType<{ className?: string }>> = {
  software_licensing: Building2,
  real_estate_purchase: Home,
  salary_negotiation: DollarSign,
  service_contract: FileText,
  partnership_deal: Handshake,
  acquisition_deal: TrendingUp
};

const DIFFICULTY_COLORS = {
  simple: 'bg-green-100 text-green-800 border-green-300',
  moderate: 'bg-yellow-100 text-yellow-800 border-yellow-300',
  complex: 'bg-red-100 text-red-800 border-red-300'
};

const DIFFICULTY_LABELS = {
  simple: 'Beginner',
  moderate: 'Intermediate',
  complex: 'Advanced'
};

export function ScenarioSelection({ onSelectScenario, userLevel = 1 }: ScenarioSelectionProps) {
  const [selectedScenario, setSelectedScenario] = useState<string | null>(null);
  const [showDetails, setShowDetails] = useState(false);

  const scenarios = Object.values(SCENARIO_CONFIGS);

  const isScenarioUnlocked = (scenario: ScenarioConfig): boolean => {
    // Simple scenarios are always unlocked
    if (scenario.complexity === 'simple') return true;
    
    // Moderate scenarios require level 3+
    if (scenario.complexity === 'moderate') return userLevel >= 3;
    
    // Complex scenarios require level 5+
    if (scenario.complexity === 'complex') return userLevel >= 5;
    
    return true;
  };

  const getScenarioXPReward = (scenario: ScenarioConfig): number => {
    const baseXP = {
      simple: 50,
      moderate: 100,
      complex: 200
    };
    return baseXP[scenario.complexity];
  };

  const ScenarioCard = ({ scenario }: { scenario: ScenarioConfig }) => {
    const Icon = SCENARIO_ICONS[scenario.id] || FileText;
    const isUnlocked = isScenarioUnlocked(scenario);
    const xpReward = getScenarioXPReward(scenario);

    return (
      <motion.div
        whileHover={{ scale: isUnlocked ? 1.02 : 1 }}
        whileTap={{ scale: isUnlocked ? 0.98 : 1 }}
      >
        <Card 
          className={`cursor-pointer transition-all duration-200 ${
            isUnlocked 
              ? 'hover:shadow-lg border-gray-200' 
              : 'opacity-60 cursor-not-allowed border-gray-100'
          } ${selectedScenario === scenario.id ? 'ring-2 ring-blue-500' : ''}`}
          onClick={() => {
            if (isUnlocked) {
              setSelectedScenario(scenario.id);
            }
          }}
        >
          <CardHeader className="pb-3">
            <div className="flex items-start justify-between">
              <div className="flex items-center gap-3">
                <div className={`w-12 h-12 rounded-lg flex items-center justify-center ${
                  isUnlocked ? 'bg-blue-100' : 'bg-gray-100'
                }`}>
                  <Icon className={`h-6 w-6 ${isUnlocked ? 'text-blue-600' : 'text-gray-400'}`} />
                </div>
                <div>
                  <CardTitle className="text-lg">{scenario.title}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">
                    {scenario.description}
                  </p>
                </div>
              </div>
              {!isUnlocked && (
                <Badge variant="outline" className="bg-gray-50">
                  Level {scenario.complexity === 'moderate' ? '3' : '5'}+
                </Badge>
              )}
            </div>
          </CardHeader>
          
          <CardContent className="space-y-4">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-4">
                <Badge className={DIFFICULTY_COLORS[scenario.complexity]}>
                  {DIFFICULTY_LABELS[scenario.complexity]}
                </Badge>
                
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Clock className="h-4 w-4" />
                  <span>{scenario.estimatedRounds} rounds</span>
                </div>
                
                <div className="flex items-center gap-1 text-sm text-muted-foreground">
                  <Zap className="h-4 w-4" />
                  <span>+{xpReward} XP</span>
                </div>
              </div>
              
              <Dialog open={showDetails && selectedScenario === scenario.id} onOpenChange={setShowDetails}>
                <DialogTrigger asChild>
                  <Button 
                    variant="ghost" 
                    size="sm"
                    onClick={(e) => {
                      e.stopPropagation();
                      setSelectedScenario(scenario.id);
                      setShowDetails(true);
                    }}
                    disabled={!isUnlocked}
                  >
                    <Info className="h-4 w-4" />
                  </Button>
                </DialogTrigger>
                <DialogContent className="max-w-2xl">
                  <DialogHeader>
                    <DialogTitle className="flex items-center gap-2">
                      <Icon className="h-5 w-5" />
                      {scenario.title}
                    </DialogTitle>
                  </DialogHeader>
                  
                  <div className="space-y-6">
                    <p className="text-muted-foreground">{scenario.description}</p>
                    
                    <div className="grid grid-cols-2 gap-4">
                      <div>
                        <h4 className="font-medium mb-2">Key Terms</h4>
                        <ul className="text-sm space-y-1">
                          {scenario.keyTerms.map(term => (
                            <li key={term} className="flex items-center gap-2">
                              <ChevronRight className="h-3 w-3" />
                              {term.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase())}
                            </li>
                          ))}
                        </ul>
                      </div>
                      
                      <div>
                        <h4 className="font-medium mb-2">AI Personality</h4>
                        <div className="space-y-2 text-sm">
                          {scenario.aiPersonalities.detailOriented && (
                            <Badge variant="outline" className="mr-1">Detail-Oriented</Badge>
                          )}
                          {scenario.aiPersonalities.riskAverse && (
                            <Badge variant="outline" className="mr-1">Risk-Averse</Badge>
                          )}
                          {scenario.aiPersonalities.timeConstrained && (
                            <Badge variant="outline" className="mr-1">Time-Constrained</Badge>
                          )}
                          {scenario.aiPersonalities.relationshipFocused && (
                            <Badge variant="outline" className="mr-1">Relationship-Focused</Badge>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div>
                      <h4 className="font-medium mb-2">Recommended Strategies</h4>
                      <div className="flex gap-2">
                        {scenario.commonStrategies.map(strategy => (
                          <Badge key={strategy} variant="secondary">
                            {strategy.replace('_', ' ').toLowerCase()}
                          </Badge>
                        ))}
                      </div>
                    </div>
                    
                    <div className="flex items-center justify-between pt-4 border-t">
                      <div className="flex items-center gap-4">
                        <div className="flex items-center gap-1">
                          <Clock className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">~{scenario.estimatedRounds} rounds</span>
                        </div>
                        <div className="flex items-center gap-1">
                          <Zap className="h-4 w-4 text-muted-foreground" />
                          <span className="text-sm">+{xpReward} XP reward</span>
                        </div>
                      </div>
                      
                      <Button onClick={() => {
                        setShowDetails(false);
                        onSelectScenario(scenario.id);
                      }}>
                        Start Negotiation
                      </Button>
                    </div>
                  </div>
                </DialogContent>
              </Dialog>
            </div>
            
            <div className="flex items-center justify-between">
              <div className="text-xs text-muted-foreground">
                Key terms: {scenario.keyTerms.slice(0, 3).map(term => 
                  term.replace('_', ' ')
                ).join(', ')}
                {scenario.keyTerms.length > 3 && '...'}
              </div>
              
              {isUnlocked && (
                <Button 
                  size="sm" 
                  onClick={(e) => {
                    e.stopPropagation();
                    onSelectScenario(scenario.id);
                  }}
                  className="gap-1"
                >
                  Start
                  <ChevronRight className="h-3 w-3" />
                </Button>
              )}
            </div>
          </CardContent>
        </Card>
      </motion.div>
    );
  };

  return (
    <div className="space-y-6">
      <div className="text-center space-y-2">
        <h2 className="text-2xl font-bold">Choose Your Negotiation Scenario</h2>
        <p className="text-muted-foreground">
          Each scenario offers unique challenges and learning opportunities
        </p>
      </div>

      {/* User Progress */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50">
        <CardContent className="p-4">
          <div className="flex items-center justify-between">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-blue-100 rounded-full flex items-center justify-center">
                <Star className="h-5 w-5 text-blue-600" />
              </div>
              <div>
                <div className="font-medium">Level {userLevel} Negotiator</div>
                <div className="text-sm text-muted-foreground">
                  {userLevel < 3 && 'Complete beginner scenarios to unlock intermediate ones'}
                  {userLevel >= 3 && userLevel < 5 && 'Intermediate scenarios unlocked! Master them to access advanced scenarios'}
                  {userLevel >= 5 && 'All scenarios unlocked! You\'re ready for any challenge'}
                </div>
              </div>
            </div>
            <div className="text-right">
              <div className="text-sm text-muted-foreground">Progress</div>
              <Progress value={Math.min(100, (userLevel / 10) * 100)} className="w-24 h-2" />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Scenario Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {scenarios.map(scenario => (
          <ScenarioCard key={scenario.id} scenario={scenario} />
        ))}
      </div>

      {/* Tips */}
      <Card className="bg-yellow-50 border-yellow-200">
        <CardContent className="p-4">
          <div className="flex items-start gap-3">
            <Info className="h-5 w-5 text-yellow-600 mt-0.5" />
            <div>
              <h4 className="font-medium text-yellow-800 mb-1">Pro Tips</h4>
              <ul className="text-sm text-yellow-700 space-y-1">
                <li>• Start with beginner scenarios to learn the basics</li>
                <li>• Each scenario type teaches different negotiation skills</li>
                <li>• Higher difficulty scenarios offer more XP rewards</li>
                <li>• Pay attention to AI personality traits for better strategies</li>
              </ul>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  );
}
