"use client";

import React, { useState, useCallback } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  Upload, 
  FileText, 
  CheckCircle,
  AlertTriangle,
  MessageSquare,
  <PERSON>rkles,
  Clock
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { DocumentChatNegotiation } from '@/components/negotiation-simulator/document-chat-negotiation';
import { useRouter } from 'next/navigation';
import { useDropzone } from 'react-dropzone';

// Mock useDropzone if not available
const mockUseDropzone = (options: any) => ({
  getRootProps: () => ({}),
  getInputProps: () => ({}),
  isDragActive: false
});

interface SmartContractProps {
  onBack?: () => void;
}

export function SmartContract({ onBack }: SmartContractProps) {
  const [uploadState, setUploadState] = useState<'idle' | 'uploading' | 'analyzing' | 'ready' | 'practicing'>('idle');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  const [analysisProgress, setAnalysisProgress] = useState(0);
  const [sessionId, setSessionId] = useState<string | null>(null);
  const [analysisResults, setAnalysisResults] = useState<any>(null);
  const router = useRouter();

  const onDrop = useCallback((acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (file) {
      setUploadedFile(file);
      handleUpload(file);
    }
  }, []);

  const { getRootProps, getInputProps, isDragActive } = (typeof useDropzone === 'function' ? useDropzone : mockUseDropzone)({
    onDrop,
    accept: {
      'application/pdf': ['.pdf'],
      'application/msword': ['.doc'],
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
    },
    maxFiles: 1
  });

  const handleUpload = async (file: File) => {
    setUploadState('uploading');
    
    // Simulate upload progress
    for (let i = 0; i <= 100; i += 10) {
      await new Promise(resolve => setTimeout(resolve, 100));
      setAnalysisProgress(i);
    }
    
    setUploadState('analyzing');
    
    // Simulate analysis (in real implementation, this would call the backend)
    const mockAnalysis = {
      contractType: 'Software License Agreement',
      riskLevel: 'MEDIUM',
      analysisScore: 72,
      totalIssues: 6,
      keyIssues: [
        'Unlimited liability clause detected',
        'Auto-renewal terms favor vendor',
        'Termination notice period too short',
        'Data protection clauses insufficient'
      ]
    };
    
    // Simulate analysis progress
    for (let i = 0; i <= 100; i += 5) {
      await new Promise(resolve => setTimeout(resolve, 150));
      setAnalysisProgress(i);
    }
    
    setAnalysisResults(mockAnalysis);
    setUploadState('ready');
    
    // Auto-start practice after brief delay
    setTimeout(() => {
      handleStartPractice();
    }, 2000);
  };

  const handleStartPractice = () => {
    setUploadState('practicing');
    // In real implementation, create session with document context
    setSessionId('mock-session-id');
  };

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.push('/negotiation');
    }
  };

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'HIGH': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'LOW': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  if (uploadState === 'practicing' && sessionId) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-4">
          <div className="flex items-center gap-4 mb-6">
            <Button variant="ghost" onClick={handleBack} className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-xl font-semibold">Contract Practice</h1>
              <p className="text-sm text-muted-foreground">{uploadedFile?.name}</p>
            </div>
          </div>

          <DocumentChatNegotiation
            sessionId={sessionId}
            onComplete={(results) => {
              console.log('Contract practice completed:', results);
            }}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button variant="ghost" onClick={handleBack} className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Upload Your Contract</h1>
              <p className="text-muted-foreground">We'll analyze it and start practice immediately</p>
            </div>
          </div>

          <div className="space-y-6">
            {uploadState === 'idle' && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardContent className="p-8">
                    <div
                      {...getRootProps()}
                      className={`border-2 border-dashed rounded-lg p-8 text-center cursor-pointer transition-colors ${
                        isDragActive 
                          ? 'border-primary bg-primary/5' 
                          : 'border-muted-foreground/25 hover:border-primary/50'
                      }`}
                    >
                      <input {...getInputProps()} />
                      <div className="space-y-4">
                        <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                          <Upload className="h-8 w-8 text-primary" />
                        </div>
                        
                        <div>
                          <h3 className="text-lg font-semibold">
                            {isDragActive ? 'Drop your contract here' : 'Upload your contract'}
                          </h3>
                          <p className="text-muted-foreground">
                            Drag and drop or click to select • PDF, DOC, DOCX
                          </p>
                        </div>
                        
                        <Button variant="outline">
                          Choose File
                        </Button>
                      </div>
                    </div>
                  </CardContent>
                </Card>

                {/* What Happens Next */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Sparkles className="h-5 w-5" />
                      What Happens Next
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-bold text-blue-600">1</span>
                        </div>
                        <div>
                          <h4 className="font-medium">Instant Analysis</h4>
                          <p className="text-sm text-muted-foreground">AI analyzes your contract for risks and negotiation opportunities</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-bold text-green-600">2</span>
                        </div>
                        <div>
                          <h4 className="font-medium">Auto-Practice Setup</h4>
                          <p className="text-sm text-muted-foreground">Practice session starts automatically with AI that knows your contract</p>
                        </div>
                      </div>
                      
                      <div className="flex items-start gap-3">
                        <div className="w-6 h-6 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                          <span className="text-xs font-bold text-purple-600">3</span>
                        </div>
                        <div>
                          <h4 className="font-medium">Targeted Practice</h4>
                          <p className="text-sm text-muted-foreground">Negotiate specific issues from your contract with expert guidance</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {(uploadState === 'uploading' || uploadState === 'analyzing') && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardContent className="p-8">
                    <div className="text-center space-y-6">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                        <FileText className="h-8 w-8 text-primary" />
                      </div>
                      
                      <div>
                        <h3 className="text-lg font-semibold">
                          {uploadState === 'uploading' ? 'Uploading Contract...' : 'Analyzing Contract...'}
                        </h3>
                        <p className="text-muted-foreground">
                          {uploadedFile?.name}
                        </p>
                      </div>
                      
                      <div className="space-y-2">
                        <Progress value={analysisProgress} className="w-full" />
                        <p className="text-sm text-muted-foreground">
                          {uploadState === 'uploading' 
                            ? `Uploading... ${analysisProgress}%`
                            : `Analyzing contract structure and terms... ${analysisProgress}%`
                          }
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {uploadState === 'ready' && analysisResults && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                      <div>
                        <h3 className="font-semibold text-green-900 dark:text-green-100">
                          Analysis Complete!
                        </h3>
                        <p className="text-sm text-green-700 dark:text-green-300">
                          Starting your practice session...
                        </p>
                      </div>
                    </div>
                    
                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                        <div className="text-lg font-bold text-blue-600">{analysisResults.analysisScore}/100</div>
                        <div className="text-xs text-muted-foreground">Score</div>
                      </div>
                      <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                        <Badge className={getRiskColor(analysisResults.riskLevel)} variant="outline">
                          {analysisResults.riskLevel}
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">Risk</div>
                      </div>
                      <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                        <div className="text-lg font-bold text-orange-600">{analysisResults.totalIssues}</div>
                        <div className="text-xs text-muted-foreground">Issues</div>
                      </div>
                      <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                        <div className="text-lg font-bold text-purple-600">15min</div>
                        <div className="text-xs text-muted-foreground">Est. Time</div>
                      </div>
                    </div>
                    
                    <div className="flex items-center gap-2 text-sm text-green-700 dark:text-green-300">
                      <Clock className="h-4 w-4" />
                      <span>Practice will begin automatically in a few seconds...</span>
                    </div>
                  </CardContent>
                </Card>

                {/* Progress Indicator */}
                <Card className="bg-blue-50 dark:bg-blue-950/20">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 text-sm text-blue-800 dark:text-blue-200">
                      <Clock className="h-4 w-4" />
                      <span>Estimated time: 5 minutes • 3 credits for analysis + 3 credits per chat message</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {(uploadState === 'uploading' || uploadState === 'analyzing') && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card>
                  <CardContent className="p-8">
                    <div className="text-center space-y-6">
                      <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto">
                        <FileText className="h-8 w-8 text-primary" />
                      </div>

                      <div>
                        <h3 className="text-lg font-semibold">
                          {uploadState === 'uploading' ? 'Uploading Contract...' : 'Analyzing Contract...'}
                        </h3>
                        <p className="text-muted-foreground">
                          {uploadedFile?.name}
                        </p>
                      </div>

                      <div className="space-y-2">
                        <Progress value={analysisProgress} className="w-full" />
                        <p className="text-sm text-muted-foreground">
                          {uploadState === 'uploading'
                            ? `Uploading... ${analysisProgress}%`
                            : `Analyzing contract structure and terms... ${analysisProgress}%`
                          }
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}

            {uploadState === 'ready' && analysisResults && (
              <motion.div
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
              >
                <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
                  <CardContent className="p-6">
                    <div className="flex items-center gap-3 mb-4">
                      <CheckCircle className="h-6 w-6 text-green-600" />
                      <div>
                        <h3 className="font-semibold text-green-900 dark:text-green-100">
                          Analysis Complete!
                        </h3>
                        <p className="text-sm text-green-700 dark:text-green-300">
                          Starting your practice session...
                        </p>
                      </div>
                    </div>

                    <div className="grid grid-cols-2 md:grid-cols-4 gap-4 mb-4">
                      <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                        <div className="text-lg font-bold text-blue-600">{analysisResults.analysisScore}/100</div>
                        <div className="text-xs text-muted-foreground">Score</div>
                      </div>
                      <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                        <Badge className={getRiskColor(analysisResults.riskLevel)} variant="outline">
                          {analysisResults.riskLevel}
                        </Badge>
                        <div className="text-xs text-muted-foreground mt-1">Risk</div>
                      </div>
                      <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                        <div className="text-lg font-bold text-orange-600">{analysisResults.totalIssues}</div>
                        <div className="text-xs text-muted-foreground">Issues</div>
                      </div>
                      <div className="text-center p-3 bg-white/50 dark:bg-gray-900/20 rounded-lg">
                        <div className="text-lg font-bold text-purple-600">15min</div>
                        <div className="text-xs text-muted-foreground">Est. Time</div>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 text-sm text-green-700 dark:text-green-300">
                      <Clock className="h-4 w-4" />
                      <span>Practice will begin automatically in a few seconds...</span>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            )}
          </div>
        </div>
      </div>
    </div>
  );
}
