"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { 
  ArrowLeft, 
  <PERSON>rkles, 
  MessageSquare,
  TrendingUp,
  Users,
  Target
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { ChatNegotiation } from '@/components/negotiation-simulator/chat-negotiation';
import { useRouter } from 'next/navigation';

interface InstantPracticeProps {
  onBack?: () => void;
}

export function InstantPractice({ onBack }: InstantPracticeProps) {
  const [isStarted, setIsStarted] = useState(false);
  const [scenario, setScenario] = useState<any>(null);
  const router = useRouter();

  useEffect(() => {
    // Auto-select a smart scenario based on user context or random selection
    const scenarios = [
      {
        id: 'software_licensing',
        title: 'Software License Negotiation',
        description: 'Negotiate terms for enterprise software licensing',
        aiCharacter: {
          name: '<PERSON>',
          title: 'Sales Director',
          personality: ['analytical', 'collaborative', 'results-driven'],
          backstory: 'Experienced in enterprise software sales with a focus on building long-term partnerships.'
        }
      },
      {
        id: 'salary_negotiation',
        title: 'Salary Negotiation',
        description: 'Practice negotiating your compensation package',
        aiCharacter: {
          name: 'Sarah Martinez',
          title: 'HR Director',
          personality: ['diplomatic', 'fair', 'detail-oriented'],
          backstory: 'HR professional focused on finding win-win solutions for both employees and the company.'
        }
      },
      {
        id: 'service_contract',
        title: 'Service Agreement',
        description: 'Negotiate terms for professional services',
        aiCharacter: {
          name: 'Michael Thompson',
          title: 'Business Development',
          personality: ['strategic', 'flexible', 'relationship-focused'],
          backstory: 'Business development expert who values long-term client relationships.'
        }
      }
    ];

    // Smart scenario selection (could be based on user history, time of day, etc.)
    const selectedScenario = scenarios[Math.floor(Math.random() * scenarios.length)];
    setScenario(selectedScenario);
  }, []);

  const handleBack = () => {
    if (onBack) {
      onBack();
    } else {
      router.push('/negotiation');
    }
  };

  const handleStart = () => {
    setIsStarted(true);
  };

  const handleComplete = (results: any) => {
    console.log('Practice session completed:', results);
    // Could show results summary or suggest next steps
  };

  if (!scenario) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center space-y-4">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto" />
          <p className="text-muted-foreground">Preparing your practice session...</p>
        </div>
      </div>
    );
  }

  if (isStarted) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container mx-auto p-4">
          {/* Minimal Header */}
          <div className="flex items-center gap-4 mb-6">
            <Button variant="ghost" onClick={handleBack} className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-xl font-semibold">{scenario.title}</h1>
              <p className="text-sm text-muted-foreground">Practice Session</p>
            </div>
          </div>

          {/* Chat Interface */}
          <ChatNegotiation
            scenarioId={scenario.id}
            aiCharacter={scenario.aiCharacter}
            onComplete={handleComplete}
            useBackend={true}
          />
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-background via-background to-muted/20">
      <div className="container mx-auto px-4 py-8">
        <div className="max-w-3xl mx-auto">
          {/* Header */}
          <div className="flex items-center gap-4 mb-8">
            <Button variant="ghost" onClick={handleBack} className="gap-2">
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
            <div>
              <h1 className="text-2xl font-bold">Ready to Practice!</h1>
              <p className="text-muted-foreground">Your AI negotiation partner is ready</p>
            </div>
          </div>

          {/* Scenario Preview */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className="space-y-6"
          >
            <Card className="border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Sparkles className="h-5 w-5 text-primary" />
                  {scenario.title}
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <p className="text-muted-foreground">{scenario.description}</p>
                
                {/* AI Character Preview */}
                <div className="flex items-center gap-3 p-3 bg-background/50 rounded-lg">
                  <div className="w-10 h-10 bg-gradient-to-br from-blue-400 to-purple-500 rounded-full flex items-center justify-center text-white font-bold">
                    {scenario.aiCharacter.name.split(' ').map((n: string) => n[0]).join('')}
                  </div>
                  <div>
                    <div className="font-medium">{scenario.aiCharacter.name}</div>
                    <div className="text-sm text-muted-foreground">{scenario.aiCharacter.title}</div>
                  </div>
                </div>

                {/* Personality Traits */}
                <div className="flex flex-wrap gap-2">
                  {scenario.aiCharacter.personality.map((trait: string) => (
                    <Badge key={trait} variant="secondary" className="text-xs">
                      {trait}
                    </Badge>
                  ))}
                </div>
              </CardContent>
            </Card>

            {/* What to Expect */}
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <MessageSquare className="h-5 w-5" />
                  What to Expect
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="grid gap-4 md:grid-cols-3">
                  <div className="text-center space-y-2">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center mx-auto">
                      <MessageSquare className="h-4 w-4 text-blue-600" />
                    </div>
                    <h4 className="font-medium">Natural Conversation</h4>
                    <p className="text-xs text-muted-foreground">Type like you're texting a colleague</p>
                  </div>
                  
                  <div className="text-center space-y-2">
                    <div className="w-8 h-8 bg-green-100 dark:bg-green-900/30 rounded-full flex items-center justify-center mx-auto">
                      <TrendingUp className="h-4 w-4 text-green-600" />
                    </div>
                    <h4 className="font-medium">Live Feedback</h4>
                    <p className="text-xs text-muted-foreground">See relationship metrics update in real-time</p>
                  </div>
                  
                  <div className="text-center space-y-2">
                    <div className="w-8 h-8 bg-purple-100 dark:bg-purple-900/30 rounded-full flex items-center justify-center mx-auto">
                      <Target className="h-4 w-4 text-purple-600" />
                    </div>
                    <h4 className="font-medium">Smart Suggestions</h4>
                    <p className="text-xs text-muted-foreground">Get strategic guidance when you need it</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Start Button */}
            <motion.div
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ delay: 0.3 }}
              className="text-center"
            >
              <Button 
                onClick={handleStart}
                size="lg"
                className="gap-2 px-8"
              >
                <MessageSquare className="h-5 w-5" />
                Start Negotiating
              </Button>
              
              <p className="text-xs text-muted-foreground mt-3">
                This will consume 3 credits per message exchange
              </p>
            </motion.div>

            {/* Quick Tips */}
            <Card className="bg-yellow-50 dark:bg-yellow-950/20 border-yellow-200 dark:border-yellow-800">
              <CardContent className="p-4">
                <h4 className="font-medium text-yellow-800 dark:text-yellow-200 mb-2">💡 Quick Tips</h4>
                <ul className="text-sm text-yellow-700 dark:text-yellow-300 space-y-1">
                  <li>• Be specific about numbers and terms</li>
                  <li>• Ask questions to understand their position</li>
                  <li>• Watch the relationship metrics on the right</li>
                  <li>• Use suggested responses or type your own</li>
                </ul>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </div>
    </div>
  );
}
