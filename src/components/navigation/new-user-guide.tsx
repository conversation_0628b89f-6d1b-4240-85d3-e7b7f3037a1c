"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import {
  ArrowRight,
  MessageSquare,
  BookOpen,
  Gamepad2,
  Play,
  Users,
  Clock,
  Star,
  ChevronRight,
  X,
  Lightbulb,
  Target,
  Zap
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';

interface NewUserGuideProps {
  isOpen: boolean;
  onClose: () => void;
  userType?: 'curious' | 'learner' | 'business';
}

export function NewUserGuide({ isOpen, onClose, userType = 'curious' }: NewUserGuideProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedPath, setSelectedPath] = useState<string | null>(null);

  const userPaths = {
    curious: {
      title: "Quick Explorer",
      description: "Just want to see what this is about",
      time: "2 minutes",
      steps: [
        {
          title: "Visit Demo Page",
          description: "Go to the Chat Negotiation demo",
          action: "Go to Demo",
          url: "/demo/chat-negotiation",
          icon: MessageSquare,
          color: "bg-blue-100 text-blue-600"
        },
        {
          title: "Try a Scenario",
          description: "Pick 'Software Licensing' for a quick test",
          action: "Start Chat",
          icon: Play,
          color: "bg-green-100 text-green-600"
        },
        {
          title: "Send a Message",
          description: "Type naturally and see AI respond",
          action: "Type Message",
          icon: MessageSquare,
          color: "bg-purple-100 text-purple-600"
        }
      ]
    },
    learner: {
      title: "Skill Builder",
      description: "Want to improve negotiation skills",
      time: "10 minutes",
      steps: [
        {
          title: "Create Account",
          description: "Sign up for full features and credit tracking",
          action: "Sign Up",
          url: "/auth/signup",
          icon: Users,
          color: "bg-blue-100 text-blue-600"
        },
        {
          title: "Try Live Mode",
          description: "Enable 'Backend: ON' for real AI responses",
          action: "Enable Live Mode",
          icon: Zap,
          color: "bg-yellow-100 text-yellow-600"
        },
        {
          title: "Complete Scenario",
          description: "Finish a full negotiation and review metrics",
          action: "Practice",
          icon: Target,
          color: "bg-green-100 text-green-600"
        },
        {
          title: "Explore More",
          description: "Try different scenarios and difficulty levels",
          action: "Continue Learning",
          icon: Star,
          color: "bg-purple-100 text-purple-600"
        }
      ]
    },
    business: {
      title: "Professional User",
      description: "Need to negotiate real contracts",
      time: "30 minutes",
      steps: [
        {
          title: "Upload Document",
          description: "Upload your contract for analysis",
          action: "Upload Contract",
          url: "/documents",
          icon: BookOpen,
          color: "bg-blue-100 text-blue-600"
        },
        {
          title: "Generate Playbook",
          description: "Get strategic recommendations (3 credits)",
          action: "Analyze Contract",
          icon: Lightbulb,
          color: "bg-yellow-100 text-yellow-600"
        },
        {
          title: "Practice Strategy",
          description: "Use chat to practice your approach",
          action: "Start Practice",
          icon: MessageSquare,
          color: "bg-green-100 text-green-600"
        },
        {
          title: "Advanced Training",
          description: "Move to structured simulator scenarios",
          action: "Enter Simulator",
          url: "/negotiation-simulator",
          icon: Gamepad2,
          color: "bg-purple-100 text-purple-600"
        }
      ]
    }
  };

  const handlePathSelect = (path: string) => {
    setSelectedPath(path);
    setCurrentStep(0);
  };

  const handleStepAction = (step: any) => {
    if (step.url) {
      window.location.href = step.url;
    } else {
      // Move to next step or close if last step
      if (currentStep < userPaths[selectedPath as keyof typeof userPaths].steps.length - 1) {
        setCurrentStep(currentStep + 1);
      } else {
        onClose();
      }
    }
  };

  const currentPath = selectedPath ? userPaths[selectedPath as keyof typeof userPaths] : null;

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Welcome to Chat Negotiation!
          </DialogTitle>
        </DialogHeader>

        <div className="space-y-6">
          {!selectedPath ? (
            // Path Selection
            <div className="space-y-4">
              <div className="text-center">
                <h3 className="text-lg font-semibold mb-2">How would you like to get started?</h3>
                <p className="text-muted-foreground">Choose your experience level to get a personalized guide</p>
              </div>

              <div className="grid gap-4">
                {Object.entries(userPaths).map(([key, path]) => (
                  <Card 
                    key={key}
                    className="cursor-pointer hover:shadow-md transition-shadow"
                    onClick={() => handlePathSelect(key)}
                  >
                    <CardContent className="p-4">
                      <div className="flex items-center justify-between">
                        <div className="space-y-1">
                          <h4 className="font-medium">{path.title}</h4>
                          <p className="text-sm text-muted-foreground">{path.description}</p>
                          <div className="flex items-center gap-2">
                            <Clock className="h-3 w-3" />
                            <span className="text-xs text-muted-foreground">{path.time}</span>
                            <Badge variant="outline" className="text-xs">
                              {path.steps.length} steps
                            </Badge>
                          </div>
                        </div>
                        <ChevronRight className="h-5 w-5 text-muted-foreground" />
                      </div>
                    </CardContent>
                  </Card>
                ))}
              </div>

              <div className="text-center">
                <Button variant="outline" onClick={onClose}>
                  I'll explore on my own
                </Button>
              </div>
            </div>
          ) : (
            // Step-by-Step Guide
            <div className="space-y-6">
              {/* Header */}
              <div className="flex items-center justify-between">
                <div>
                  <h3 className="text-lg font-semibold">{currentPath?.title}</h3>
                  <p className="text-sm text-muted-foreground">
                    Step {currentStep + 1} of {currentPath?.steps.length}
                  </p>
                </div>
                <Button variant="ghost" size="sm" onClick={() => setSelectedPath(null)}>
                  ← Back to options
                </Button>
              </div>

              {/* Progress */}
              <div className="space-y-2">
                <div className="flex justify-between text-sm">
                  <span>Progress</span>
                  <span>{Math.round(((currentStep + 1) / currentPath!.steps.length) * 100)}%</span>
                </div>
                <div className="w-full bg-muted rounded-full h-2">
                  <div 
                    className="bg-primary h-2 rounded-full transition-all duration-300"
                    style={{ width: `${((currentStep + 1) / currentPath!.steps.length) * 100}%` }}
                  />
                </div>
              </div>

              {/* Current Step */}
              <AnimatePresence mode="wait">
                <motion.div
                  key={currentStep}
                  initial={{ opacity: 0, x: 20 }}
                  animate={{ opacity: 1, x: 0 }}
                  exit={{ opacity: 0, x: -20 }}
                  transition={{ duration: 0.3 }}
                >
                  <Card className="border-2 border-primary/20">
                    <CardContent className="p-6">
                      <div className="flex items-start gap-4">
                        <div className={`w-12 h-12 rounded-full flex items-center justify-center ${currentPath!.steps[currentStep].color}`}>
                          {React.createElement(currentPath!.steps[currentStep].icon, { className: "h-6 w-6" })}
                        </div>
                        <div className="flex-1 space-y-3">
                          <div>
                            <h4 className="font-semibold text-lg">{currentPath!.steps[currentStep].title}</h4>
                            <p className="text-muted-foreground">{currentPath!.steps[currentStep].description}</p>
                          </div>
                          
                          <Button 
                            onClick={() => handleStepAction(currentPath!.steps[currentStep])}
                            className="gap-2"
                          >
                            {currentPath!.steps[currentStep].action}
                            <ArrowRight className="h-4 w-4" />
                          </Button>
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                </motion.div>
              </AnimatePresence>

              {/* Step Navigation */}
              <div className="flex items-center justify-between">
                <Button 
                  variant="outline" 
                  onClick={() => setCurrentStep(Math.max(0, currentStep - 1))}
                  disabled={currentStep === 0}
                >
                  Previous
                </Button>
                
                <div className="flex gap-2">
                  {currentPath!.steps.map((_, index) => (
                    <div
                      key={index}
                      className={`w-2 h-2 rounded-full transition-colors ${
                        index <= currentStep ? 'bg-primary' : 'bg-muted'
                      }`}
                    />
                  ))}
                </div>

                <Button 
                  variant="outline"
                  onClick={() => setCurrentStep(Math.min(currentPath!.steps.length - 1, currentStep + 1))}
                  disabled={currentStep === currentPath!.steps.length - 1}
                >
                  Next
                </Button>
              </div>

              {/* Quick Actions */}
              <div className="border-t pt-4">
                <h5 className="font-medium mb-3">Quick Access</h5>
                <div className="grid grid-cols-2 gap-2">
                  <Button variant="outline" size="sm" onClick={() => window.location.href = '/demo/chat-negotiation'}>
                    <MessageSquare className="h-4 w-4 mr-2" />
                    Demo Page
                  </Button>
                  <Button variant="outline" size="sm" onClick={() => window.location.href = '/dashboard'}>
                    <Target className="h-4 w-4 mr-2" />
                    Dashboard
                  </Button>
                </div>
              </div>
            </div>
          )}
        </div>
      </DialogContent>
    </Dialog>
  );
}

// Hook to trigger the guide for new users
export function useNewUserGuide() {
  const [isOpen, setIsOpen] = useState(false);

  const showGuide = () => setIsOpen(true);
  const hideGuide = () => setIsOpen(false);

  // Auto-show for new users (you can add logic to check if user is new)
  React.useEffect(() => {
    const hasSeenGuide = localStorage.getItem('hasSeenChatNegotiationGuide');
    if (!hasSeenGuide) {
      setTimeout(() => {
        setIsOpen(true);
        localStorage.setItem('hasSeenChatNegotiationGuide', 'true');
      }, 2000); // Show after 2 seconds
    }
  }, []);

  return {
    isOpen,
    showGuide,
    hideGuide,
    NewUserGuideComponent: (props: Partial<NewUserGuideProps>) => (
      <NewUserGuide isOpen={isOpen} onClose={hideGuide} {...props} />
    )
  };
}
