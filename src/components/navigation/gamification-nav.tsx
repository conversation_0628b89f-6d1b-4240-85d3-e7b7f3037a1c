"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  Trophy, 
  Star, 
  Zap, 
  Award, 
  Target, 
  TrendingUp,
  Users,
  Calendar,
  Play,
  BookOpen,
  BarChart3,
  Settings,
  Menu,
  X,
  Home,
  Crown,
  Sword,
  Shield,
  Gift
} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Card, CardContent } from '@/components/ui/card';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';
import { useGamification } from '@/hooks/use-gamification';

interface GamificationNavProps {
  currentView: string;
  onNavigate: (view: string) => void;
}

export function GamificationNav({ currentView, onNavigate }: GamificationNavProps) {
  const [isOpen, setIsOpen] = useState(false);
  const { profile, userLevel, userRank, recentAchievements } = useGamification();

  const navigationItems = [
    {
      id: 'dashboard',
      label: 'Dashboard',
      icon: Home,
      description: 'Overview and quick actions',
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200'
    },
    {
      id: 'practice',
      label: 'Quick Practice',
      icon: Play,
      description: 'Start a practice session',
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      id: 'campaign',
      label: 'Campaign Mode',
      icon: BookOpen,
      description: 'Story-driven challenges',
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      badge: 'New'
    },
    {
      id: 'challenges',
      label: 'Team Challenges',
      icon: Users,
      description: 'Compete with colleagues',
      color: 'text-orange-600',
      bgColor: 'bg-orange-50',
      borderColor: 'border-orange-200'
    },
    {
      id: 'leaderboard',
      label: 'Leaderboard',
      icon: Trophy,
      description: 'See rankings and compete',
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200'
    },
    {
      id: 'achievements',
      label: 'Achievements',
      icon: Award,
      description: 'View unlocked achievements',
      color: 'text-pink-600',
      bgColor: 'bg-pink-50',
      borderColor: 'border-pink-200',
      badge: recentAchievements?.length > 0 ? recentAchievements.length.toString() : undefined
    },
    {
      id: 'analytics',
      label: 'Analytics',
      icon: BarChart3,
      description: 'Track your progress',
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      borderColor: 'border-indigo-200'
    },
    {
      id: 'characters',
      label: 'Characters',
      icon: Sword,
      description: 'Manage AI opponents',
      color: 'text-red-600',
      bgColor: 'bg-red-50',
      borderColor: 'border-red-200'
    }
  ];

  const NavItem = ({ item, isMobile = false }: { item: any; isMobile?: boolean }) => (
    <motion.div
      whileHover={{ scale: 1.02 }}
      whileTap={{ scale: 0.98 }}
    >
      <Card 
        className={`cursor-pointer transition-all duration-200 ${
          currentView === item.id 
            ? `${item.borderColor} ${item.bgColor} shadow-md` 
            : 'hover:shadow-lg border-gray-200'
        } ${isMobile ? 'mb-2' : ''}`}
        onClick={() => {
          onNavigate(item.id);
          if (isMobile) setIsOpen(false);
        }}
      >
        <CardContent className={`p-4 ${isMobile ? 'flex items-center space-x-3' : 'text-center'}`}>
          <div className={`${isMobile ? '' : 'mb-3'}`}>
            <item.icon className={`h-6 w-6 ${isMobile ? '' : 'mx-auto'} ${
              currentView === item.id ? item.color : 'text-gray-600'
            }`} />
          </div>
          <div className={`${isMobile ? 'flex-1' : ''}`}>
            <div className="flex items-center gap-2">
              <h3 className={`font-semibold text-sm ${
                currentView === item.id ? item.color : 'text-gray-800'
              }`}>
                {item.label}
              </h3>
              {item.badge && (
                <Badge variant="secondary" className="text-xs">
                  {item.badge}
                </Badge>
              )}
            </div>
            {!isMobile && (
              <p className="text-xs text-gray-600 mt-1">{item.description}</p>
            )}
          </div>
        </CardContent>
      </Card>
    </motion.div>
  );

  return (
    <>
      {/* Desktop Navigation */}
      <div className="hidden lg:block w-64 bg-white border-r border-gray-200 h-screen overflow-y-auto">
        <div className="p-4 space-y-4">
          {/* User Profile Section */}
          <Card className="bg-gradient-to-r from-purple-500 to-blue-600 text-white">
            <CardContent className="p-4">
              <div className="flex items-center space-x-3">
                <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
                  <Crown className="h-6 w-6" />
                </div>
                <div className="flex-1">
                  <h3 className="font-bold text-sm">{userLevel?.title || 'Novice Negotiator'}</h3>
                  <p className="text-xs opacity-90">Level {userLevel?.current || 1}</p>
                  {userRank && (
                    <p className="text-xs opacity-75">Rank #{userRank}</p>
                  )}
                </div>
              </div>
              
              {userLevel && (
                <div className="mt-3">
                  <div className="flex justify-between text-xs mb-1">
                    <span>{userLevel.currentXP?.toLocaleString()} XP</span>
                    <span>{userLevel.xpToNext?.toLocaleString()} to next</span>
                  </div>
                  <div className="w-full bg-white/20 rounded-full h-2">
                    <div 
                      className="bg-white h-2 rounded-full transition-all duration-300"
                      style={{ width: `${userLevel.progress || 0}%` }}
                    />
                  </div>
                </div>
              )}
            </CardContent>
          </Card>

          {/* Navigation Items */}
          <div className="space-y-2">
            {navigationItems.map((item) => (
              <NavItem key={item.id} item={item} />
            ))}
          </div>

          {/* Quick Stats */}
          <Card>
            <CardContent className="p-4">
              <h4 className="font-semibold text-sm mb-3 flex items-center gap-2">
                <Target className="h-4 w-4" />
                Quick Stats
              </h4>
              <div className="space-y-2 text-xs">
                <div className="flex justify-between">
                  <span className="text-gray-600">Sessions:</span>
                  <span className="font-medium">{profile?.statistics.totalSessions || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Win Rate:</span>
                  <span className="font-medium">{Math.round((profile?.statistics.winRate || 0) * 100)}%</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Streak:</span>
                  <span className="font-medium">{profile?.statistics.currentStreak || 0}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-gray-600">Achievements:</span>
                  <span className="font-medium">{profile?.statistics.achievementsCount || 0}</span>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Mobile Navigation */}
      <div className="lg:hidden">
        {/* Mobile Header */}
        <div className="bg-white border-b border-gray-200 p-4 flex items-center justify-between">
          <div className="flex items-center space-x-3">
            <Sheet open={isOpen} onOpenChange={setIsOpen}>
              <SheetTrigger asChild>
                <Button variant="outline" size="sm">
                  <Menu className="h-4 w-4" />
                </Button>
              </SheetTrigger>
              <SheetContent side="left" className="w-80">
                <SheetHeader>
                  <SheetTitle>Navigation</SheetTitle>
                </SheetHeader>
                
                <div className="mt-6 space-y-4">
                  {/* Mobile User Profile */}
                  <Card className="bg-gradient-to-r from-purple-500 to-blue-600 text-white">
                    <CardContent className="p-4">
                      <div className="flex items-center space-x-3">
                        <div className="w-10 h-10 bg-white/20 rounded-full flex items-center justify-center">
                          <Crown className="h-5 w-5" />
                        </div>
                        <div>
                          <h3 className="font-bold text-sm">{userLevel?.title || 'Novice Negotiator'}</h3>
                          <p className="text-xs opacity-90">Level {userLevel?.current || 1}</p>
                        </div>
                      </div>
                    </CardContent>
                  </Card>

                  {/* Mobile Navigation Items */}
                  <div className="space-y-2">
                    {navigationItems.map((item) => (
                      <NavItem key={item.id} item={item} isMobile />
                    ))}
                  </div>
                </div>
              </SheetContent>
            </Sheet>
            
            <h1 className="text-lg font-bold">Negotiation Arena</h1>
          </div>
          
          <div className="flex items-center space-x-2">
            {userLevel && (
              <Badge variant="outline" className="text-xs">
                Level {userLevel.current}
              </Badge>
            )}
            {recentAchievements && recentAchievements.length > 0 && (
              <Badge variant="secondary" className="text-xs">
                <Gift className="h-3 w-3 mr-1" />
                {recentAchievements.length}
              </Badge>
            )}
          </div>
        </div>
      </div>
    </>
  );
}
