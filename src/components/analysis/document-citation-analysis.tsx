"use client";

import { useState, useEffect } from "react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Loader2, FileText, AlertCircle } from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import {
	citationAnalysisService,
	type CitationAnalysisResponse,
} from "@/lib/services/citation-analysis-service";
import { CitationAnalysisPanel } from "./citation-analysis-panel";

interface DocumentCitationAnalysisProps {
	documentText: string;
	onClose?: () => void;
}

export function DocumentCitationAnalysis({
	documentText,
	onClose,
}: DocumentCitationAnalysisProps) {
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [citations, setCitations] = useState<CitationAnalysisResponse[]>([]);
	const [selectedCitation, setSelectedCitation] = useState<string | null>(null);
	const [activeTab, setActiveTab] = useState("list");

	useEffect(() => {
		const analyzeDocument = async () => {
			try {
				setLoading(true);
				setError(null);

				const results = await citationAnalysisService.analyzeDocumentCitations({
					documentText,
					includeRelationships: true,
				});

				setCitations(results);
			} catch (err) {
				setError(
					err instanceof Error
						? err.message
						: "Failed to analyze document citations"
				);
				console.error("Error analyzing document citations:", err);
			} finally {
				setLoading(false);
			}
		};

		if (documentText) {
			analyzeDocument();
		}
	}, [documentText]);

	const handleCitationSelect = (citation: string) => {
		setSelectedCitation(citation);
		setActiveTab("detail");
	};

	if (loading) {
		return (
			<Card className="w-full h-full flex flex-col items-center justify-center p-6">
				<Loader2 className="h-8 w-8 animate-spin text-primary mb-4" />
				<p className="text-center text-muted-foreground">
					Analyzing document citations...
					<br />
					This may take a moment for longer documents.
				</p>
			</Card>
		);
	}

	if (error) {
		return (
			<Card className="w-full h-full">
				<CardHeader>
					<CardTitle className="flex items-center text-destructive">
						<AlertCircle className="h-5 w-5 mr-2" />
						Analysis Error
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p>{error}</p>
					<Button onClick={() => window.location.reload()} className="mt-4">
						Retry
					</Button>
				</CardContent>
			</Card>
		);
	}

	return (
		<Card className="w-full h-full flex flex-col">
			<CardHeader className="pb-2">
				<div className="flex justify-between items-start">
					<div>
						<CardTitle className="flex items-center">
							<FileText className="h-5 w-5 mr-2" />
							Document Citation Analysis
						</CardTitle>
						<CardDescription className="mt-1">
							{citations.length} citation{citations.length !== 1 ? "s" : ""}{" "}
							found
						</CardDescription>
					</div>
					{onClose && (
						<Button
							variant="ghost"
							size="sm"
							onClick={onClose}
							className="h-8 w-8 p-0"
						>
							<span className="sr-only">Close</span>
							<svg
								xmlns="http://www.w3.org/2000/svg"
								viewBox="0 0 24 24"
								fill="none"
								stroke="currentColor"
								strokeWidth="2"
								strokeLinecap="round"
								strokeLinejoin="round"
								className="h-4 w-4"
							>
								<line x1="18" y1="6" x2="6" y2="18" />
								<line x1="6" y1="6" x2="18" y2="18" />
							</svg>
						</Button>
					)}
				</div>
			</CardHeader>

			<Tabs
				value={activeTab}
				onValueChange={setActiveTab}
				className="flex-1 flex flex-col"
			>
				<TabsList className="grid grid-cols-2 mb-2 mx-4">
					<TabsTrigger value="list">Citations List</TabsTrigger>
					<TabsTrigger value="detail" disabled={!selectedCitation}>
						Citation Detail
					</TabsTrigger>
				</TabsList>

				<CardContent className="flex-1 p-0 overflow-hidden">
					<TabsContent value="list" className="m-0 h-full">
						{citations.length > 0 ? (
							<ScrollArea className="h-full">
								<div className="p-4 space-y-3">
									{citations.map((item) => (
										<Card
											key={item.citation.id}
											className="overflow-hidden cursor-pointer hover:bg-accent/50 transition-colors"
											onClick={() =>
												handleCitationSelect(item.citation.citation)
											}
										>
											<CardHeader className="p-3">
												<div className="flex justify-between items-start">
													<div>
														<h3 className="font-medium text-sm">
															{item.citation.title || item.citation.citation}
														</h3>
														<p className="text-xs text-muted-foreground mt-1">
															{item.citation.court &&
																`${item.citation.court}, `}
															{item.citation.year && `${item.citation.year}`}
														</p>
													</div>
													<Badge variant="outline">{item.citation.type}</Badge>
												</div>
											</CardHeader>
											<CardContent className="p-3 pt-0">
												{item.relationships &&
													item.relationships.length > 0 && (
														<div className="mt-2">
															<p className="text-xs text-muted-foreground">
																Related citations:
															</p>
															<div className="flex flex-wrap gap-1 mt-1">
																{item.relationships
																	.slice(0, 3)
																	.map((rel, idx) => (
																		<Badge
																			key={idx}
																			variant="secondary"
																			className="text-xs"
																		>
																			{rel.relationshipType === "cites"
																				? rel.metadata.citedCase?.citation || rel.targetCitationId
																				: rel.metadata.citingCase?.citation || rel.sourceCitationId}
																		</Badge>
																	))}
																{item.relationships.length > 3 && (
																	<Badge
																		variant="secondary"
																		className="text-xs"
																	>
																		+{item.relationships.length - 3} more
																	</Badge>
																)}
															</div>
														</div>
													)}
											</CardContent>
										</Card>
									))}
								</div>
							</ScrollArea>
						) : (
							<div className="h-full flex flex-col items-center justify-center p-6">
								<AlertCircle className="h-8 w-8 text-muted-foreground mb-4" />
								<p className="text-center text-muted-foreground">
									No citations found in this document.
								</p>
							</div>
						)}
					</TabsContent>

					<TabsContent value="detail" className="m-0 h-full">
						{selectedCitation ? (
							<CitationAnalysisPanel
								citation={selectedCitation}
							/>
						) : (
							<div className="h-full flex flex-col items-center justify-center p-6">
								<p className="text-center text-muted-foreground">
									Select a citation to view details
								</p>
							</div>
						)}
					</TabsContent>
				</CardContent>
			</Tabs>
		</Card>
	);
}
