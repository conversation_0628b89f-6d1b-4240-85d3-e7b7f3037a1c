"use client";

import type { DocumentAnalysis } from "@/lib/types/analysis";
import {
	ContractArtifact,
	CourtFilingArtifact,
	GeneralArtifact,
	LegalOpinionArtifact,
	LegislationArtifact,
	PolicyArtifact,
	CorporateDocumentArtifact,
	EstatePlanningArtifact,
	IntellectualPropertyArtifact,
	RealEstateArtifact,
	FinancialDocumentArtifact
} from "./artifact";

interface AnalysisArtifactProps {
	analysisId: string;
	result: DocumentAnalysis["result"];
	documentId?: string;
}

export function AnalysisArtifact({
	analysisId,
	result,
	documentId,
}: AnalysisArtifactProps) {
	// Render the appropriate artifact based on document type;
	switch (result.documentType) {
		case "CONTRACT":
		case "AGREEMENT":
		case "EMPLOYMENT_CONTRACT":
		case "LICENSING_AGREEMENT":
		case "NDA":
		case "CONTRACTOR AGREEMENT":
		case "SERVICE AGREEMENT":
			return <ContractArtifact analysisId={analysisId} result={result} documentId={documentId} />;

		case "LEGAL_OPINION":
			return <LegalOpinionArtifact analysisId={analysisId} result={result} documentId={documentId} />;

		case "POLICY":
		case "PRIVACY_POLICY":
		case "TERMS_OF_SERVICE":
			return <PolicyArtifact analysisId={analysisId} result={result} documentId={documentId} />;

		case "LEGISLATION":
		case "STATUTE":
		case "REGULATION":
		case "AGENCY_RULE":
			return <LegislationArtifact analysisId={analysisId} result={result} documentId={documentId} />;

		case "COURT_FILING":
		case "PLEADING":
		case "BRIEF":
		case "MOTION":
			return <CourtFilingArtifact analysisId={analysisId} result={result} documentId={documentId} />;

		case "BYLAWS":
		case "ARTICLES_OF_INCORPORATION":
		case "BOARD_RESOLUTION":
			return <CorporateDocumentArtifact analysisId={analysisId} result={result} documentId={documentId} />;

		case "WILL":
		case "TRUST":
		case "POWER_OF_ATTORNEY":
			return <EstatePlanningArtifact analysisId={analysisId} result={result} documentId={documentId} />;

		case "PATENT":
		case "TRADEMARK_REGISTRATION":
		case "COPYRIGHT_REGISTRATION":
			return <IntellectualPropertyArtifact analysisId={analysisId} result={result} documentId={documentId} />;

		case "DEED":
		case "MORTGAGE":
		case "LEASE":
			return <RealEstateArtifact analysisId={analysisId} result={result} documentId={documentId} />;

		case "SECURITIES_FILING":
		case "PROSPECTUS":
		case "LOAN_AGREEMENT":
			return <FinancialDocumentArtifact analysisId={analysisId} result={result} documentId={documentId} />;

		case "GENERAL":
		default:
			return <GeneralArtifact analysisId={analysisId} result={result} documentId={documentId} />;
	}
}
