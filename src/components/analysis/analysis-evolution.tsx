"use client"

// Separate the imports to ensure there are no issues with them
import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { format } from "date-fns"
import type { AnalysisEvolution as AnalysisEvolutionType } from "@/lib/types/analysis-evolution"
import { 
  LineChart, 
  Line, 
  XAxis, 
  YAxis, 
  CartesianGrid, 
  Tooltip, 
  ResponsiveContainer, 
  BarChart, 
  Bar, 
  Legend 
} from "recharts"
import { ArrowDownIcon, ArrowUpIcon, CalendarIcon } from "lucide-react"

// Helper function outside the component to format dates
function formatDate(dateString: string) {
  return format(new Date(dateString), "MMM d, yyyy")
}

interface AnalysisEvolutionProps {
  evolution: AnalysisEvolutionType
}

// The main component
export function AnalysisEvolution({ evolution }: AnalysisEvolutionProps) {
  // All hooks must be called at the top level
  const [activeTab, setActiveTab] = useState<"risk" | "clauses" | "changes" | "metrics">("risk")

  // Prepare data outside of the render function
  const riskScoreData = evolution.metrics?.riskScoreEvolution?.map(point => ({
    date: formatDate(point.date),
    score: point.score,
    fullDate: point.date
  })) || []

  const clauseData = evolution.metrics?.clauseCountEvolution?.restrictive.map((restrictive, index) => ({
    index: index + 1,
    restrictive,
    favorable: evolution.metrics.clauseCountEvolution.favorable[index]
  })) || []

  // Prepare additional metrics data
  const metricsData = evolution.analysisDates?.map((date, index) => ({
    date: formatDate(date),
    fullDate: date,
    processingTime: evolution.processingTimes[index],
    contentLength: evolution.contentLengths[index],
    analysisId: evolution.analysisIds[index]
  })) || []

  // Check if we have evolution metrics or just basic metrics
  const hasFullMetrics = evolution.metrics && 
    evolution.metrics.riskScoreEvolution && 
    evolution.metrics.riskScoreEvolution.length > 0

  // If we only have one analysis, show a different view
  if (evolution.totalAnalyses === 1 || !hasFullMetrics) {
    return (
      <Card className="w-full">
        <CardHeader>
          <CardTitle className="flex items-center justify-between">
            <span>Analysis History</span>
            <Badge variant="outline" className="ml-2">
              {evolution.totalAnalyses} analysis
            </Badge>
          </CardTitle>
          <CardDescription>
            First analysis: {formatDate(evolution.analysisDates[0])}
          </CardDescription>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <p className="text-sm text-muted-foreground">
              This document has only been analyzed once. Multiple analyses are required to show evolution metrics.
            </p>
            <div className="border rounded-md p-4 space-y-2">
              <div className="flex justify-between">
                <span className="text-sm font-medium">Analysis ID:</span>
                <span className="text-sm text-muted-foreground">{evolution.analysisIds[0]}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Date:</span>
                <span className="text-sm text-muted-foreground">{formatDate(evolution.analysisDates[0])}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Processing Time:</span>
                <span className="text-sm text-muted-foreground">{evolution.processingTimes[0]}ms</span>
              </div>
              <div className="flex justify-between">
                <span className="text-sm font-medium">Content Length:</span>
                <span className="text-sm text-muted-foreground">{evolution.contentLengths[0]} characters</span>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <span>Analysis Evolution</span>
          <Badge variant="outline" className="ml-2">
            {evolution.totalAnalyses} analyses
          </Badge>
        </CardTitle>
        <CardDescription>
          First analysis: {formatDate(evolution.firstAnalysisDate)} • 
          Latest analysis: {formatDate(evolution.latestAnalysisDate)}
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "risk" | "clauses" | "changes" | "metrics")}>
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="risk">Risk Score</TabsTrigger>
            <TabsTrigger value="clauses">Clauses</TabsTrigger>
            <TabsTrigger value="changes">Key Changes</TabsTrigger>
            <TabsTrigger value="metrics">Metrics</TabsTrigger>
          </TabsList>
          
          <TabsContent value="risk" className="space-y-4">
            <div className="h-[300px] mt-4">
              <ResponsiveContainer width="100%" height="100%">
                <LineChart
                  data={riskScoreData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="date" />
                  <YAxis domain={[0, 100]} />
                  <Tooltip 
                    formatter={(value) => [`${value}`, 'Risk Score']}
                    labelFormatter={(label) => `Date: ${label}`}
                  />
                  <Line 
                    type="monotone" 
                    dataKey="score" 
                    stroke="#ff4d4f" 
                    activeDot={{ r: 8 }} 
                    strokeWidth={2}
                  />
                </LineChart>
              </ResponsiveContainer>
            </div>
            <div className="text-sm text-muted-foreground mt-2">
              <p>Risk score trend over time. Lower scores indicate reduced risk.</p>
              {riskScoreData.length > 1 && (
                <div className="flex items-center mt-2">
                  <span className="font-medium mr-2">Overall trend:</span>
                  {riskScoreData[0].score > riskScoreData[riskScoreData.length - 1].score ? (
                    <span className="text-green-500 flex items-center">
                      <ArrowDownIcon className="h-4 w-4 mr-1" />
                      Improving ({riskScoreData[0].score - riskScoreData[riskScoreData.length - 1].score} points)
                    </span>
                  ) : (
                    <span className="text-red-500 flex items-center">
                      <ArrowUpIcon className="h-4 w-4 mr-1" />
                      Worsening ({riskScoreData[riskScoreData.length - 1].score - riskScoreData[0].score} points)
                    </span>
                  )}
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="clauses" className="space-y-4">
            <div className="h-[300px] mt-4">
              <ResponsiveContainer width="100%" height="100%">
                <BarChart
                  data={clauseData}
                  margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                >
                  <CartesianGrid strokeDasharray="3 3" />
                  <XAxis dataKey="index" label={{ value: 'Analysis #', position: 'insideBottom', offset: -5 }} />
                  <YAxis />
                  <Tooltip />
                  <Legend />
                  <Bar dataKey="restrictive" name="Restrictive Clauses" fill="#ff4d4f" />
                  <Bar dataKey="favorable" name="Favorable Clauses" fill="#52c41a" />
                </BarChart>
              </ResponsiveContainer>
            </div>
            <div className="text-sm text-muted-foreground mt-2">
              <p>Evolution of restrictive vs. favorable clauses across analyses.</p>
              {clauseData.length > 1 && (
                <div className="flex items-center mt-2 space-x-4">
                  <div>
                    <span className="font-medium mr-2">Restrictive:</span>
                    {clauseData[0].restrictive > clauseData[clauseData.length - 1].restrictive ? (
                      <span className="text-green-500 flex items-center">
                        <ArrowDownIcon className="h-4 w-4 mr-1" />
                        Decreased ({clauseData[0].restrictive - clauseData[clauseData.length - 1].restrictive})
                      </span>
                    ) : (
                      <span className="text-red-500 flex items-center">
                        <ArrowUpIcon className="h-4 w-4 mr-1" />
                        Increased ({clauseData[clauseData.length - 1].restrictive - clauseData[0].restrictive})
                      </span>
                    )}
                  </div>
                  <div>
                    <span className="font-medium mr-2">Favorable:</span>
                    {clauseData[0].favorable < clauseData[clauseData.length - 1].favorable ? (
                      <span className="text-green-500 flex items-center">
                        <ArrowUpIcon className="h-4 w-4 mr-1" />
                        Increased ({clauseData[clauseData.length - 1].favorable - clauseData[0].favorable})
                      </span>
                    ) : (
                      <span className="text-red-500 flex items-center">
                        <ArrowDownIcon className="h-4 w-4 mr-1" />
                        Decreased ({clauseData[0].favorable - clauseData[clauseData.length - 1].favorable})
                      </span>
                    )}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="changes" className="space-y-4">
            <div className="mt-4">
              {evolution.metrics.keyChanges.length > 0 ? (
                <div className="space-y-4">
                  {evolution.metrics.keyChanges.map((change, index) => (
                    <Card key={index} className="overflow-hidden">
                      <CardHeader className="p-4 pb-2 flex flex-row items-center">
                        <div>
                          <CardTitle className="text-base">{change.field}</CardTitle>
                          <CardDescription className="flex items-center">
                            <CalendarIcon className="h-3 w-3 mr-1" />
                            {formatDate(change.date)}
                          </CardDescription>
                        </div>
                      </CardHeader>
                      <CardContent className="p-4 pt-0">
                        <div className="grid grid-cols-2 gap-4 mt-2">
                          <div className="p-3 rounded-md bg-red-50 dark:bg-red-900/20">
                            <p className="text-xs text-muted-foreground mb-1">From</p>
                            <p className="text-sm">{change.from}</p>
                          </div>
                          <div className="p-3 rounded-md bg-green-50 dark:bg-green-900/20">
                            <p className="text-xs text-muted-foreground mb-1">To</p>
                            <p className="text-sm">{change.to}</p>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ))}
                </div>
              ) : (
                <div className="flex flex-col items-center justify-center py-8">
                  <p className="text-muted-foreground">No key changes detected</p>
                </div>
              )}
            </div>
          </TabsContent>
          
          <TabsContent value="metrics" className="space-y-4">
            <div className="mt-4">
              <div className="space-y-4">
                <div className="overflow-x-auto">
                  <table className="w-full border-collapse">
                    <thead>
                      <tr className="border-b">
                        <th className="text-left p-2 text-sm font-medium">Date</th>
                        <th className="text-left p-2 text-sm font-medium">Processing Time</th>
                        <th className="text-left p-2 text-sm font-medium">Content Length</th>
                        <th className="text-left p-2 text-sm font-medium">Analysis ID</th>
                      </tr>
                    </thead>
                    <tbody>
                      {metricsData.map((item, index) => (
                        <tr key={index} className="border-b hover:bg-muted/50">
                          <td className="p-2 text-sm">{item.date}</td>
                          <td className="p-2 text-sm">{item.processingTime}ms</td>
                          <td className="p-2 text-sm">{item.contentLength} chars</td>
                          <td className="p-2 text-sm font-mono text-xs">{item.analysisId}</td>
                        </tr>
                      ))}
                    </tbody>
                  </table>
                </div>
                
                <div className="h-[250px] mt-4">
                  <ResponsiveContainer width="100%" height="100%">
                    <LineChart
                      data={metricsData}
                      margin={{ top: 5, right: 30, left: 20, bottom: 5 }}
                    >
                      <CartesianGrid strokeDasharray="3 3" />
                      <XAxis dataKey="date" />
                      <YAxis yAxisId="left" />
                      <YAxis yAxisId="right" orientation="right" />
                      <Tooltip />
                      <Legend />
                      <Line 
                        yAxisId="left"
                        type="monotone" 
                        dataKey="processingTime" 
                        name="Processing Time (ms)"
                        stroke="#8884d8" 
                        activeDot={{ r: 8 }} 
                      />
                      <Line 
                        yAxisId="right"
                        type="monotone" 
                        dataKey="contentLength" 
                        name="Content Length (chars)"
                        stroke="#82ca9d" 
                      />
                    </LineChart>
                  </ResponsiveContainer>
                </div>
              </div>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
