"use client";

import type React from "react";

import { useState } from "react";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON>eader,
	CardTitle,
} from "@/components/ui/card";
import { Input } from "@/components/ui/input";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Skeleton } from "@/components/ui/skeleton";
import { Search, BookOpen, Calendar, Gavel, FileText } from "lucide-react";
import { courtListenerService } from "@/lib/services/court-listener-service";
import type { SearchParams } from "@/lib/services/court-listener-service";
import { CitationPreview } from "./citation-preview";

// Based on the actual API response structure
interface SearchResult {
	absolute_url: string;
	caseName: string;
	dateFiled: string;
	court_id: string;
	court_citation_string: string;
	status: string;
	cluster_id: number;
}

// Generated unique ID for list rendering
const generateKey = (result: SearchResult) => {
	return `${result.cluster_id}-${result.dateFiled}`;
};

export function CitationSearch() {
	const [searchQuery, setSearchQuery] = useState("");
	const [isSearching, setIsSearching] = useState(false);
	const [searchResults, setSearchResults] = useState<SearchResult[]>([]);
	const [selectedCitation, setSelectedCitation] = useState<string | null>(null);
	   type SearchType = "citation" | "case_name" | "court";
	   const [searchType, setSearchType] = useState<SearchType>("citation");
	const [error, setError] = useState<string | null>(null);

	const handleSearch = async (e: React.FormEvent) => {
		e.preventDefault();

		if (!searchQuery.trim()) return;

		try {
			setIsSearching(true);
			setError(null);

			// Create search params based on search type
			const params: SearchParams = {
				[searchType]: searchQuery,
				page_size: 10,
			};

			const response = await courtListenerService.searchCases(params);

			if (response.status === "success") {
				// Extract only the fields we need from the response
				const results: SearchResult[] = response.data.results.map(result => ({
					absolute_url: result.absolute_url,
					caseName: result.caseName,
					dateFiled: result.dateFiled,
					court_id: result.court_id,
					court_citation_string: result.court_citation_string,
					status: result.status || 'unknown',
					cluster_id: result.cluster_id
				}));
				setSearchResults(results);
				if (response.data.results.length === 0) {
					setError(`No results found for "${searchQuery}"`);
				}
			} else {
				setError("Failed to search cases");
			}
		} catch (error) {
			console.error("Error searching cases:", error);
			setError("An error occurred while searching. Please try again.");
		} finally {
			setIsSearching(false);
		}
	};

	const handleResultClick = (result: SearchResult) => {
		// Find the primary citation for this case
		// In a real implementation, you would extract this from the result
		// For now, we'll use a simplified approach

		// Construct a citation using available data
		const year = new Date(result.dateFiled).getFullYear();
		const court = result.court_citation_string.toUpperCase();
		const citation = `${court} ${result.cluster_id} (${year})`;

		setSelectedCitation(citation);
	};

	return (
		<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
			<CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
				<CardTitle className="text-lg font-semibold text-foreground">
					Citation Search
				</CardTitle>
				<CardDescription className="text-muted-foreground">
					Search for legal citations, cases, or courts
				</CardDescription>
			</CardHeader>

			<CardContent className="p-4">
				{selectedCitation ? (
					<CitationPreview
						citation={selectedCitation}
						onClose={() => setSelectedCitation(null)}
					/>
				) : (
					<>
						<Tabs
							value={searchType}
							onValueChange={(value: string) => setSearchType(value as SearchType)}
							className="mb-4"
						>
							<TabsList className="w-full">
								<TabsTrigger value="citation" className="flex-1">
									By Citation
								</TabsTrigger>
								<TabsTrigger value="case_name" className="flex-1">
									By Case Name
								</TabsTrigger>
								<TabsTrigger value="court" className="flex-1">
									By Court
								</TabsTrigger>
							</TabsList>
						</Tabs>

						<form onSubmit={handleSearch} className="mb-4">
							<div className="flex gap-2">
								<div className="relative flex-1">
									<Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
									<Input
										placeholder={
											searchType === "citation"
												? "Enter citation (e.g., 410 U.S. 113)"
												: searchType === "case_name"
												? "Enter case name (e.g., Roe v. Wade)"
												: "Enter court (e.g., scotus, ca9)"
										}
										value={searchQuery}
										onChange={(e) => setSearchQuery(e.target.value)}
										className="pl-8"
									/>
								</div>
								<Button
									type="submit"
									disabled={isSearching || !searchQuery.trim()}
								>
									{isSearching ? "Searching..." : "Search"}
								</Button>
							</div>
						</form>

						<div className="mt-4">
							<h4 className="text-sm font-medium mb-3">Search Results</h4>

							{isSearching ? (
								<div className="space-y-3">
									{[...Array(3)].map((_, i) => (
										<div
											key={i}
											className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md"
										>
											<Skeleton className="h-5 w-3/4 mb-2" />
											<div className="flex gap-2">
												<Skeleton className="h-4 w-20" />
												<Skeleton className="h-4 w-20" />
											</div>
										</div>
									))}
								</div>
							) : searchResults.length > 0 ? (
								<ScrollArea className="max-h-[400px]">
									<div className="space-y-3">
										{searchResults.map((result) => (
											<div
												key={generateKey(result)}
												className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md cursor-pointer hover:bg-secondary/30 dark:hover:bg-[#2a2a2a] transition-colors"
												onClick={() => handleResultClick(result)}
											>
												<h5 className="text-sm font-medium">
													{result.caseName}
												</h5>
												<div className="flex flex-wrap gap-2 mt-2">
													<Badge
														variant="outline"
														className="text-xs flex items-center"
													>
														<Calendar className="h-3 w-3 mr-1" />
														{new Date(result.dateFiled).toLocaleDateString()}
													</Badge>
													<Badge
														variant="outline"
														className="text-xs flex items-center"
													>
														<Gavel className="h-3 w-3 mr-1" />
														{result.court_id.toUpperCase()}
													</Badge>
													<Badge
														variant="outline"
														className="text-xs flex items-center"
													>
														<BookOpen className="h-3 w-3 mr-1" />
														{result.court_citation_string}
													</Badge>
													<Badge
														variant="outline"
														className="text-xs flex items-center"
													>
														<FileText className="h-3 w-3 mr-1" />
														{result.status}
													</Badge>
												</div>
											</div>
										))}
									</div>
								</ScrollArea>
							) : error ? (
								<div className="text-center py-8 text-muted-foreground">
									<p>{error}</p>
									<p className="text-sm mt-1">
										Try a different search term or search type
									</p>
								</div>
							) : searchQuery.trim() ? (
								<div className="text-center py-8 text-muted-foreground">
									<p>No results found for &quot;{searchQuery}&quot;</p>
									<p className="text-sm mt-1">
										Try a different search term or search type
									</p>
								</div>
							) : (
								<div className="text-center py-8 text-muted-foreground">
									<BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
									<p>Enter a search term to find legal citations</p>
									<p className="text-sm mt-1">
										{searchType === "citation"
											? "Example: 410 U.S. 113"
											: searchType === "case_name"
											? "Example: Roe v. Wade"
											: "Example: scotus (Supreme Court)"}
									</p>
								</div>
							)}
						</div>
					</>
				)}
			</CardContent>

			<CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
				<div className="text-xs text-muted-foreground">
					Data from CourtListener
				</div>
			</CardFooter>
		</Card>
	);
}
