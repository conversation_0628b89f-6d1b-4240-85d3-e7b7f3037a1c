"use client";

import { useState } from "react";
import {
  ExternalLink,
  Filter,
  BookOpen,
  Search,
  X,
  Download,
} from "lucide-react";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import type { EnrichedCitation } from "@/lib/types/analysis";
import { CitationExport } from "./citation-export";
import { CitationPreview } from "./citation-preview";

interface CitationsListProps {
  identifiedCitations?: string[];
  enrichedCitations?: EnrichedCitation[];
}

export function CitationsList({
  identifiedCitations,
  enrichedCitations,
}: CitationsListProps) {
  const [searchTerm, setSearchTerm] = useState("");
  const [selectedTypes, setSelectedTypes] = useState<string[]>([]);
  const [selectedCitation, setSelectedCitation] = useState<string | null>(null);
  const [showExport, setShowExport] = useState(false);

  if (
    (!enrichedCitations || enrichedCitations.length === 0) &&
    (!identifiedCitations || identifiedCitations.length === 0)
  ) {
    return null;
  }

  // Get unique citation types for filtering
  const citationTypes = enrichedCitations
    ? [...new Set(enrichedCitations.map((citation) => citation?.type))]
    : [];

  // Filter citations based on search term and selected types
  const filteredCitations = enrichedCitations
    ? enrichedCitations.filter((citation) => {
        const matchesSearch =
          searchTerm === "" ||
          citation?.rawCitation
            .toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          citation?.metadata.title
            .toLowerCase()
            .includes(searchTerm.toLowerCase());

        const matchesType =
          selectedTypes.length === 0 || selectedTypes.includes(citation?.type);

        return matchesSearch && matchesType;
      })
    : [];

  // Filter identified citations based on search term
  const filteredIdentifiedCitations = identifiedCitations
    ? identifiedCitations.filter(
        (citation) =>
          searchTerm === "" ||
          citation.toLowerCase().includes(searchTerm.toLowerCase())
      )
    : [];

  const handleTypeToggle = (type: string) => {
    setSelectedTypes((prev) =>
      prev.includes(type) ? prev.filter((t) => t !== type) : [...prev, type]
    );
  };

  const clearFilters = () => {
    setSearchTerm("");
    setSelectedTypes([]);
  };

  const handleCitationClick = (citation: string) => {
    setSelectedCitation(citation);
  };

  return (
    <div className="space-y-3">
      {showExport ? (
        <CitationExport
          citations={enrichedCitations || identifiedCitations || []}
          onClose={() => setShowExport(false)}
        />
      ) : selectedCitation ? (
        <CitationPreview
          citation={selectedCitation}
          onClose={() => setSelectedCitation(null)}
        />
      ) : (
        <>
          <div className="flex items-center justify-between mb-2">
            <h4 className="text-sm font-medium">Citations</h4>
            <div className="flex items-center gap-2">
              <Button
                variant="outline"
                size="sm"
                className="h-7 text-xs"
                onClick={() => setShowExport(true)}
              >
                <Download className="h-3.5 w-3.5 mr-1.5" />
                Export
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" size="sm" className="h-7 text-xs">
                    <Filter className="h-3.5 w-3.5 mr-1.5" />
                    Filter
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end" className="w-48">
                  {citationTypes.map((type) => (
                    <DropdownMenuCheckboxItem
                      key={type}
                      checked={selectedTypes.includes(type)}
                      onCheckedChange={() => handleTypeToggle(type)}
                    >
                      {type}
                    </DropdownMenuCheckboxItem>
                  ))}
                  {citationTypes.length > 0 && (
                    <Button
                      variant="ghost"
                      size="sm"
                      className="w-full mt-2 h-7 text-xs"
                      onClick={clearFilters}
                    >
                      Clear Filters
                    </Button>
                  )}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>

          <div className="relative mb-3">
            <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
            <Input
              placeholder="Search citations..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="pl-8 h-8 text-sm"
            />
            {searchTerm && (
              <Button
                variant="ghost"
                size="icon"
                className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6"
                onClick={() => setSearchTerm("")}
              >
                <X className="h-3.5 w-3.5" />
                <span className="sr-only">Clear search</span>
              </Button>
            )}
          </div>
          <ScrollArea className="h-[300px] w-full">
            <div className="pr-3">
              {enrichedCitations && enrichedCitations.length > 0 ? (
                <ul className="space-y-2">
                  {filteredCitations.length > 0 ? (
                    filteredCitations.map(
                      (citation, index) =>
                        citation && (
                          <li
                            key={index}
                            className="text-xs bg-secondary/20 dark:bg-[#252525] p-2 rounded-md cursor-pointer hover:bg-secondary/30 dark:hover:bg-[#2a2a2a] transition-colors"
                            onClick={() =>
                              handleCitationClick(citation.rawCitation)
                            }
                          >
                            <div className="flex items-start justify-between">
                              <div>
                                <div className="font-medium">
                                  {citation?.metadata?.title}
                                </div>
                                <div className="text-muted-foreground mt-1">
                                  {citation?.rawCitation}
                                </div>
                                {citation?.metadata?.date && (
                                  <div className="text-muted-foreground mt-0.5">
                                    {citation?.metadata?.date}
                                  </div>
                                )}
                              </div>
                              <div className="flex items-center gap-1">
                                <Badge variant="outline" className="text-xs">
                                  {citation?.type}
                                </Badge>
                                <a
                                  href={
                                    citation?.source === "CourtListener"
                                      ? `https://www.courtlistener.com${citation?.links?.sourceUrl}`
                                      : citation?.links?.sourceUrl
                                      ? `https://www.courtlistener.com${citation?.links?.sourceUrl}`
                                      : citation?.links?.sourceUrl
                                  }
                                  target={
                                    citation?.source === "CourtListener"
                                      ? "_blank"
                                      : ""
                                  }
                                  rel="noopener noreferrer"
                                  className="inline-flex items-center text-primary hover:underline ml-2"
                                  onClick={(e) => e.stopPropagation()}
                                >
                                  <ExternalLink className="h-3 w-3 mr-1" />
                                  View
                                </a>
                              </div>
                            </div>
                          </li>
                        )
                    )
                  ) : searchTerm || selectedTypes.length > 0 ? (
                    <li className="text-center py-4 text-muted-foreground text-sm">
                      No citations match your filters
                    </li>
                  ) : null}
                </ul>
              ) : identifiedCitations && identifiedCitations.length > 0 ? (
                <div className="flex flex-wrap gap-2">
                  {filteredIdentifiedCitations.map((citation, index) => (
                    <Badge
                      key={index}
                      variant="outline"
                      className="text-xs cursor-pointer hover:bg-secondary/30 dark:hover:bg-[#2a2a2a]"
                      onClick={() => handleCitationClick(citation)}
                    >
                      <BookOpen className="h-3 w-3 mr-1" />
                      {citation}
                    </Badge>
                  ))}
                  {filteredIdentifiedCitations.length === 0 && searchTerm && (
                    <div className="w-full text-center py-4 text-muted-foreground text-sm">
                      No citations match your search
                    </div>
                  )}
                </div>
              ) : null}
            </div>
          </ScrollArea>
        </>
      )}
    </div>
  );
}
