"use client"

import { useState, useEffect } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON>, <PERSON>bsContent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { But<PERSON> } from "@/components/ui/button"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Skeleton } from "@/components/ui/skeleton"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Badge } from "@/components/ui/badge"
import { BookOpen, Network, TrendingUp, GitBranch, AlertTriangle, ExternalLink, Download, Info } from "lucide-react"
import { citationAnalysisService, type CitationAnalysisResponse } from "@/lib/services/citation-analysis-service"

interface CitationAnalysisProps {
  citation: string
}

export function CitationAnalysis({ citation }: CitationAnalysisProps) {
  const [activeTab, setActiveTab] = useState<string>("overview")
  const [isLoading, setIsLoading] = useState<boolean>(true)
  const [error, setError] = useState<string | null>(null)
  const [analysisData, setAnalysisData] = useState<CitationAnalysisResponse | null>(null)
  const [subscriptionLimited, setSubscriptionLimited] = useState<boolean>(false)

  useEffect(() => {
    const fetchAnalysis = async () => {
      try {
        setIsLoading(true)
        setError(null)
        setSubscriptionLimited(false)

        const data = await citationAnalysisService.analyzeCitation({
          citation,
          includeRelationships: true,
          includePrecedentChains: true,
          includeImpact: true,
        })

        setAnalysisData(data)
      } catch (error) {
        console.error("Error fetching citation analysis:", error)
        const errorMessage = error instanceof Error ? error.message : "Failed to analyze citation"

        if (errorMessage.includes("subscription")) {
          setSubscriptionLimited(true)
          // Still try to get basic analysis
          const basicData = await citationAnalysisService.analyzeCitation({
            citation,
            includeRelationships: true,
            includePrecedentChains: false,
            includeImpact: false,
          }).catch(() => null)

          if (basicData) {
            setAnalysisData(basicData)
          } else {
            setError("Failed to analyze citation")
          }
        } else {
          setError(errorMessage)
        }
      } finally {
        setIsLoading(false)
      }
    }

    if (citation) {
      fetchAnalysis()
    }
  }, [citation])

  const renderCitationOverview = () => {
    if (!analysisData?.citation) return null

    const { citation: citationData } = analysisData

    return (
      <div className="space-y-4">
        <div>
          <h4 className="text-sm font-medium mb-2">Citation</h4>
          <p className="text-sm">{citationData.citation}</p>
        </div>

        {citationData.title && (
          <div>
            <h4 className="text-sm font-medium mb-2">Title</h4>
            <p className="text-sm">{citationData.title}</p>
          </div>
        )}

        <div className="grid grid-cols-2 gap-4">
          {citationData.court && (
            <div>
              <h4 className="text-sm font-medium mb-2">Court</h4>
              <p className="text-sm">{citationData.court}</p>
            </div>
          )}

          {citationData.year && (
            <div>
              <h4 className="text-sm font-medium mb-2">Year</h4>
              <p className="text-sm">{citationData.year}</p>
            </div>
          )}
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">Type</h4>
          <Badge variant="outline" className="bg-blue-500/20 text-blue-500 border-blue-500/30">
            {citationData.type}
          </Badge>
        </div>

        {citationData.courtListenerId && (
          <div className="pt-2">
            <a
              href={`https://www.courtlistener.com/opinion/${citationData.courtListenerId}/`}
              target="_blank"
              rel="noopener noreferrer"
              className="inline-flex items-center text-primary hover:underline"
            >
              <ExternalLink className="h-4 w-4 mr-1.5" />
              View on CourtListener
            </a>
          </div>
        )}
      </div>
    )
  }

  const renderRelationships = () => {
    if (!analysisData?.relationships || analysisData.relationships.length === 0) {
      return (
        <div className="text-center py-4 text-muted-foreground">
          <p>No relationship data available</p>
        </div>
      )
    }

    return (
      <ScrollArea className="h-[300px]">
        <div className="space-y-4">
          {analysisData.relationships.map((relationship, index) => (
            <Card key={index} className="bg-secondary/20 dark:bg-[#252525] border-border">
              <CardContent className="p-4">
                <div className="flex justify-between items-start mb-2">
                  <Badge variant="outline" className="bg-blue-500/20 text-blue-500 border-blue-500/30">
                    {relationship.relationshipType}
                  </Badge>
                  <Badge variant="outline">Strength: {relationship.strength.toUpperCase()}</Badge>
                </div>

                <div className="mb-2">
                  <h4 className="text-sm font-medium">Citation</h4>
                  <p className="text-sm">{relationship.targetCitationId}</p>
                </div>

                {relationship.metadata.relationshipDescription && (
                  <div>
                    <h4 className="text-sm font-medium">Context</h4>
                    <p className="text-sm text-muted-foreground">{relationship.metadata.relationshipDescription}</p>
                  </div>
                )}
              </CardContent>
            </Card>
          ))}
        </div>
      </ScrollArea>
    )
  }

  const renderImpact = () => {
    if (subscriptionLimited) {
      return (
        <Alert className="bg-amber-500/20 text-amber-500 border-amber-500/30">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Subscription Required</AlertTitle>
          <AlertDescription>
            Citation impact analysis requires an enhanced subscription. Upgrade your plan to access this feature.
          </AlertDescription>
        </Alert>
      )
    }

    if (!analysisData?.impact) {
      return (
        <div className="text-center py-4 text-muted-foreground">
          <p>No impact data available</p>
        </div>
      )
    }

    const { impact } = analysisData

    return (
      <div className="space-y-4">
        <div className="grid grid-cols-3 gap-4">
          <Card className="bg-secondary/20 dark:bg-[#252525] border-border">
            <CardContent className="p-4 text-center">
              <h4 className="text-sm font-medium mb-1">Impact Score</h4>
              <p className="text-2xl font-bold">{impact.impactScore}</p>
              <p className="text-xs text-muted-foreground">out of 100</p>
            </CardContent>
          </Card>

          <Card className="bg-secondary/20 dark:bg-[#252525] border-border">
            <CardContent className="p-4 text-center">
              <h4 className="text-sm font-medium mb-1">Total Citations</h4>
              <p className="text-2xl font-bold">{impact.totalCitations}</p>
            </CardContent>
          </Card>

          <Card className="bg-secondary/20 dark:bg-[#252525] border-border">
            <CardContent className="p-4 text-center">
              <h4 className="text-sm font-medium mb-1">Recent Citations</h4>
              <p className="text-2xl font-bold">{impact.recentCitations}</p>
              <p className="text-xs text-muted-foreground">last 5 years</p>
            </CardContent>
          </Card>
        </div>

        <div>
          <h4 className="text-sm font-medium mb-2">Significant Citations</h4>
          <ScrollArea className="h-[200px]">
            <div className="space-y-2">
              {[
                { label: "Total Citations", value: impact.totalCitations },
                { label: "Recent Citations", value: impact.recentCitations },
                { label: "Influential Citations", value: impact.influentialCitations }
              ].map((item, index) => (
                <Card key={index} className="bg-secondary/20 dark:bg-[#252525] border-border">
                  <CardContent className="p-3">
                    <div className="flex justify-between items-start">
                      <div>
                        <p className="text-sm font-medium">{item.label}</p>
                        <p className="text-xs text-muted-foreground">
                          Count: {item.value}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </ScrollArea>
        </div>
      </div>
    )
  }

  const renderPrecedentChains = () => {
    if (subscriptionLimited) {
      return (
        <Alert className="bg-amber-500/20 text-amber-500 border-amber-500/30">
          <AlertTriangle className="h-4 w-4" />
          <AlertTitle>Subscription Required</AlertTitle>
          <AlertDescription>
            Precedent chain analysis requires an enhanced subscription. Upgrade your plan to access this feature.
          </AlertDescription>
        </Alert>
      )
    }

    if (!analysisData?.precedentChains || analysisData.precedentChains.length === 0) {
      return (
        <div className="text-center py-4 text-muted-foreground">
          <p>No precedent chain data available</p>
        </div>
      )
    }

    return (
      <ScrollArea className="h-[300px]">
        <div className="space-y-6">
          {analysisData.precedentChains.map((chain, chainIndex) => (
            <div key={chainIndex} className="space-y-2">
              <div className="flex justify-between items-center">
                <h4 className="text-sm font-medium">Precedent Chain #{chainIndex + 1}</h4>
                <Badge variant="outline">Chain #{chainIndex + 1}</Badge>
              </div>

              <div className="space-y-1">
                {chain.chain.map((item, itemIndex) => (
                  <div key={itemIndex} className="relative">
                    {itemIndex < chain.chain.length - 1 && (
                      <div className="absolute left-3 top-6 bottom-0 w-0.5 bg-border dark:bg-neutral-700"></div>
                    )}

                    <div className="flex items-start gap-3 relative z-10">
                      <div className="flex items-center justify-center w-6 h-6 rounded-full bg-secondary text-secondary-foreground text-xs">
                        {itemIndex + 1}
                      </div>

                      <Card className="flex-1 bg-secondary/20 dark:bg-[#252525] border-border">
                        <CardContent className="p-3">
                          <p className="text-sm font-medium">Citation ID: {item.citationId}</p>
                          <div className="mt-1">
                            <Badge variant="outline" className="text-xs">
                              {item.relationshipToParent}
                            </Badge>
                            <Badge variant="outline" className="text-xs ml-2">
                              Significance: {item.significance}
                            </Badge>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          ))}
        </div>
      </ScrollArea>
    )
  }

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <CardTitle className="text-lg font-semibold text-foreground">
          {isLoading ? (
            <Skeleton className="h-6 w-48" />
          ) : error ? (
            "Citation Analysis"
          ) : (
            analysisData?.citation?.title || "Citation Analysis"
          )}
        </CardTitle>
        <CardDescription className="text-muted-foreground">
          {isLoading ? <Skeleton className="h-4 w-32 mt-1" /> : error ? citation : citation}
        </CardDescription>
      </CardHeader>

      <CardContent className="p-0">
        {isLoading ? (
          <div className="p-4 space-y-4">
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-full" />
            <Skeleton className="h-4 w-3/4" />
            <div className="grid grid-cols-2 gap-4 mt-6">
              <Skeleton className="h-20 w-full" />
              <Skeleton className="h-20 w-full" />
            </div>
          </div>
        ) : error ? (
          <div className="p-4 text-center">
            <Alert variant="destructive">
              <AlertTriangle className="h-4 w-4" />
              <AlertTitle>Error</AlertTitle>
              <AlertDescription>{error}</AlertDescription>
            </Alert>
          </div>
        ) : (
          <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
            <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
              <TabsTrigger
                value="overview"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                <BookOpen className="h-3.5 w-3.5 mr-1.5" />
                Overview
              </TabsTrigger>
              <TabsTrigger
                value="relationships"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                <Network className="h-3.5 w-3.5 mr-1.5" />
                Relationships
              </TabsTrigger>
              <TabsTrigger
                value="impact"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                <TrendingUp className="h-3.5 w-3.5 mr-1.5" />
                Impact
              </TabsTrigger>
              <TabsTrigger
                value="precedent"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                <GitBranch className="h-3.5 w-3.5 mr-1.5" />
                Precedent
              </TabsTrigger>
            </TabsList>

            <div className="p-4">
              {subscriptionLimited && activeTab === "overview" && (
                <Alert className="mb-4 bg-blue-500/10 text-blue-500 border-blue-500/30">
                  <Info className="h-4 w-4" />
                  <AlertTitle>Basic Analysis Only</AlertTitle>
                  <AlertDescription>
                    You&apos;re viewing basic citation analysis. Upgrade your subscription to access enhanced features like
                    impact analysis and precedent chains.
                  </AlertDescription>
                </Alert>
              )}

              <TabsContent value="overview" className="mt-0">
                {renderCitationOverview()}
              </TabsContent>

              <TabsContent value="relationships" className="mt-0">
                {renderRelationships()}
              </TabsContent>

              <TabsContent value="impact" className="mt-0">
                {renderImpact()}
              </TabsContent>

              <TabsContent value="precedent" className="mt-0">
                {renderPrecedentChains()}
              </TabsContent>
            </div>
          </Tabs>
        )}
      </CardContent>

      <CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
        <div className="text-xs text-muted-foreground">
          {subscriptionLimited ? "Basic Analysis" : "Enhanced Analysis"}
        </div>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="h-7 text-xs bg-secondary/50 dark:bg-[#2a2a2a]"
            onClick={() => {
              // Implementation for exporting citation analysis
              console.log("Export citation analysis", analysisData)
            }}
          >
            <Download className="h-3.5 w-3.5 mr-1.5" />
            Export
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
