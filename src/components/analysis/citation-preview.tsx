"use client";

import { useState, useEffect } from "react";
import {
	<PERSON>,
	Card<PERSON>ontent,
	CardD<PERSON>cription,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON>itle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs";
import { Skeleton } from "@/components/ui/skeleton";
import {
	ExternalLink,
	FileText,
	Calendar,
	Gavel,
	BookOpen,
	Download,
	Copy,
	X,
} from "lucide-react";
import { ScrollArea } from "@/components/ui/scroll-area";
import { courtListenerService } from "@/lib/services/court-listener-service";

interface CourtCitation {
    volume: number;
    reporter: string;
    page: string;
    type: number;
}

interface DocketEntry {
    document_number: string;
    date_filed: string;
    description: string;
}

interface CaseDetails {
    case_name: string;
    case_name_full: string;
    case_name_short: string;
    date_filed: string;
    court: string;
    jurisdiction: string;
    judges: string;
    precedential_status: string;
    absolute_url: string;
    docket?: {
        docket_number: string;
        court_id: string;
        date_filed: string;
        date_terminated: string;
        date_argued?: string;
        date_reargued?: string;
        entries: DocketEntry[];
    };
    citations: CourtCitation[];
}

interface CitationPreviewProps {
    citation: string;
    onClose: () => void;
}

const formatDate = (date: string | undefined) => {
    if (!date) return "N/A";
    try {
        return new Date(date).toLocaleDateString();
    } catch {
        return "Invalid date";
    }
};

export function CitationPreview({ citation, onClose }: CitationPreviewProps) {
	const [isLoading, setIsLoading] = useState(true);
	const [caseDetails, setCaseDetails] = useState<CaseDetails | null>(null);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchCitationDetails = async () => {
			try {
				setIsLoading(true);
				setError(null);

				const response = await courtListenerService.lookupCitation(citation);

				if (response.status === "success" && response.data.cases.length > 0) {
					setCaseDetails(response.data.cases[0]);
				} else {
					setError("No case details found for this citation");
				}
			} catch (err) {
				console.error("Error fetching citation details:", err);
				setError("Failed to load citation details");
			} finally {
				setIsLoading(false);
			}
		};

		fetchCitationDetails();
	}, [citation]);

	const copyToClipboard = (text: string) => {
		navigator.clipboard
			.writeText(text)
			.then(() => {
				// Could show a toast notification here
				console.log("Copied to clipboard:", text);
			})
			.catch((err) => {
				console.error("Failed to copy:", err);
			});
	};

	const downloadCitation = () => {
		if (!caseDetails) return;

		// Create citation text in a common format
		const citationText = `${
			caseDetails.case_name_full
		}, ${citation} (${new Date(caseDetails.date_filed).getFullYear()})`;

		// Create a blob and download it
		const blob = new Blob([citationText], { type: "text/plain" });
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = `${caseDetails.case_name_short.replace(
			/\s+/g,
			"_"
		)}_citation.txt`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	};

	return (
		<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
			<CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3 flex flex-row justify-between items-start">
				<div>
					<CardTitle className="text-lg font-semibold text-foreground">
						{isLoading ? (
							<Skeleton className="h-6 w-48" />
						) : error ? (
							"Citation Details"
						) : (
							caseDetails?.case_name || "Citation Details"
						)}
					</CardTitle>
					<CardDescription className="text-muted-foreground">
						{isLoading ? (
							<Skeleton className="h-4 w-32 mt-1" />
						) : error ? (
							citation
						) : (
							citation
						)}
					</CardDescription>
				</div>
				<Button
					variant="ghost"
					size="icon"
					onClick={onClose}
					className="h-8 w-8"
				>
					<X className="h-4 w-4" />
					<span className="sr-only">Close</span>
				</Button>
			</CardHeader>

			<CardContent className="p-0">
				{isLoading ? (
					<div className="p-4 space-y-4">
						<Skeleton className="h-4 w-full" />
						<Skeleton className="h-4 w-full" />
						<Skeleton className="h-4 w-3/4" />
						<div className="grid grid-cols-2 gap-4 mt-6">
							<Skeleton className="h-20 w-full" />
							<Skeleton className="h-20 w-full" />
						</div>
					</div>
				) : error ? (
					<div className="p-4 text-center text-muted-foreground">
						<p>{error}</p>
					</div>
				) : caseDetails ? (
					<Tabs defaultValue="overview" className="w-full">
						<TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
							<TabsTrigger
								value="overview"
								className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
							>
								Overview
							</TabsTrigger>
							<TabsTrigger
								value="docket"
								className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
							>
								Docket
							</TabsTrigger>
							<TabsTrigger
								value="citations"
								className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
							>
								Citations
							</TabsTrigger>
						</TabsList>

						<TabsContent value="overview" className="p-4 mt-0">
							<div className="space-y-4">
								<div>
									<h4 className="text-sm font-medium mb-2">Case Name</h4>
									<p className="text-sm">{caseDetails.case_name_full}</p>
								</div>

								<div className="grid grid-cols-2 gap-4">
									<div>
										<h4 className="text-sm font-medium mb-2 flex items-center">
											<Calendar className="h-4 w-4 mr-1.5 text-blue-500" />
											Date Filed
										</h4>
										<p className="text-sm">
											{formatDate(caseDetails.date_filed)}
										</p>
									</div>
									<div>
										<h4 className="text-sm font-medium mb-2 flex items-center">
											<Gavel className="h-4 w-4 mr-1.5 text-blue-500" />
											Court
										</h4>
										<p className="text-sm">
											{caseDetails.court} • {caseDetails.jurisdiction}
										</p>
									</div>
								</div>

								<div>
									<h4 className="text-sm font-medium mb-2">Judges</h4>
									<p className="text-sm">{caseDetails.judges}</p>
								</div>

								<div>
									<h4 className="text-sm font-medium mb-2">Status</h4>
									<Badge
										variant="outline"
										className="bg-green-500/20 text-green-500 border-green-500/30"
									>
										{caseDetails.precedential_status}
									</Badge>
								</div>

								<div className="pt-2">
									<a
										href={`https://www.courtlistener.com${caseDetails.absolute_url}`}
										target="_blank"
										rel="noopener noreferrer"
										className="inline-flex items-center text-primary hover:underline"
									>
										<ExternalLink className="h-4 w-4 mr-1.5" />
										View on CourtListener
									</a>
								</div>
							</div>
						</TabsContent>

						<TabsContent value="docket" className="mt-0">
							<div className="p-4 mb-2">
								<div className="grid grid-cols-2 gap-4 mb-4">
									<div>
										<h4 className="text-sm font-medium mb-1">Docket Number</h4>
										<p className="text-sm">
											{caseDetails?.docket?.docket_number}
										</p>
									</div>
									<div>
										<h4 className="text-sm font-medium mb-1">Court</h4>
										<p className="text-sm">
											{caseDetails?.docket?.court_id.toUpperCase()}
										</p>
									</div>
								</div>

								<div className="grid grid-cols-2 gap-4 mb-4">
									<div>
										<h4 className="text-sm font-medium mb-1">Filed</h4>
										<p className="text-sm">
											{formatDate(caseDetails?.docket?.date_filed)}
										</p>
									</div>
									<div>
										<h4 className="text-sm font-medium mb-1">Terminated</h4>
										<p className="text-sm">
											{formatDate(caseDetails?.docket?.date_terminated)}
										</p>
									</div>
								</div>

								{caseDetails?.docket?.date_argued && (
									<div className="mb-4">
										<h4 className="text-sm font-medium mb-1">Argued</h4>
										<p className="text-sm">
											{formatDate(caseDetails?.docket?.date_argued)}
										</p>
									</div>
								)}

								{caseDetails?.docket?.date_reargued && (
									<div className="mb-4">
										<h4 className="text-sm font-medium mb-1">Reargued</h4>
										<p className="text-sm">
											{formatDate(caseDetails?.docket?.date_reargued)}
										</p>
									</div>
								)}
							</div>

							<h4 className="text-sm font-medium px-4 mb-2">Docket Entries</h4>
							<ScrollArea className="h-[200px]">
								<div className="divide-y divide-border dark:divide-neutral-700">
									{caseDetails?.docket?.entries.map(
										(entry: DocketEntry, index: number) => (
											<div
												key={index}
												className="p-3 hover:bg-secondary/20 dark:hover:bg-[#252525]"
											>
												<div className="flex justify-between items-start">
													<div className="text-xs">
														<span className="font-medium">
															#{entry.document_number}
														</span>
														<span className="text-muted-foreground ml-2">
															{formatDate(entry.date_filed)}
														</span>
													</div>
													<Badge variant="outline" className="text-xs">
														<FileText className="h-3 w-3 mr-1" />
														Document
													</Badge>
												</div>
												<p className="text-sm mt-1">{entry.description}</p>
											</div>
										)
									)}
								</div>
							</ScrollArea>
						</TabsContent>

						<TabsContent value="citations" className="p-4 mt-0">
							<h4 className="text-sm font-medium mb-3">
								Alternative Citations
							</h4>
							<div className="space-y-2">
								{caseDetails?.citations?.map((citation: CourtCitation, index: number) => (
									<div
										key={index}
										className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md"
									>
										<div className="flex justify-between items-center">
											<div className="flex items-center">
												<BookOpen className="h-4 w-4 mr-2 text-blue-500" />
												<span className="text-sm font-medium">
													{citation.volume} {citation.reporter} {citation.page}
												</span>
											</div>
											<Button
												variant="ghost"
												size="sm"
												className="h-7 w-7 p-0"
												onClick={() =>
													copyToClipboard(
														`${citation.volume} ${citation.reporter} ${citation.page}`
													)
												}
											>
												<Copy className="h-3.5 w-3.5" />
												<span className="sr-only">Copy citation</span>
											</Button>
										</div>
									</div>
								))}
							</div>
						</TabsContent>
					</Tabs>
				) : null}
			</CardContent>

			{!isLoading && !error && caseDetails && (
				<CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
					<div className="text-xs text-muted-foreground">
						Data from CourtListener
					</div>
					<div className="flex gap-2">
						<Button
							variant="outline"
							size="sm"
							className="h-7 text-xs bg-secondary/50 dark:bg-[#2a2a2a]"
							onClick={() =>
								copyToClipboard(
									`${caseDetails.case_name_full}, ${citation} (${
										caseDetails.date_filed
											? new Date(caseDetails.date_filed).getFullYear()
											: "N/A"
									})`
								)
							}
						>
							<Copy className="h-3.5 w-3.5 mr-1.5" />
							Copy
						</Button>
						<Button
							variant="outline"
							size="sm"
							className="h-7 text-xs bg-secondary/50 dark:bg-[#2a2a2a]"
							onClick={downloadCitation}
						>
							<Download className="h-3.5 w-3.5 mr-1.5" />
							Export
						</Button>
					</div>
				</CardFooter>
			)}
		</Card>
	);
}
