"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { Download, Copy, Check } from "lucide-react"
import type { EnrichedCitation } from "@/lib/types/analysis"

interface CitationExportProps {
  citations: EnrichedCitation[] | string[]
  onClose: () => void
}

export function CitationExport({ citations, onClose }: CitationExportProps) {
  const [format, setFormat] = useState("apa")
  const [selectedCitations, setSelectedCitations] = useState<string[]>([])
  const [copied, setCopied] = useState(false)

  // Format citations based on the selected format
  const formatCitation = (citation: EnrichedCitation | string): string => {
    // For simple string citations, just return them
    if (typeof citation === "string") {
      return citation
    }

    // For enriched citations, format them according to the selected style
    const { metadata } = citation
    const year = metadata.date ? new Date(metadata.date).getFullYear() : "n.d."

    switch (format) {
      case "apa":
        return `${metadata.title}. (${year}). ${citation.rawCitation}.`
      case "mla":
        return `"${metadata.title}." ${citation.rawCitation}. ${year}.`
      case "chicago":
        return `${metadata.title}, ${citation.rawCitation} (${year}).`
      case "bluebook":
        return citation.rawCitation
      default:
        return citation.rawCitation
    }
  }

  const formattedCitations = citations.map((citation) => formatCitation(citation))

  const toggleCitation = (index: number) => {
    const citationText = formattedCitations[index]

    setSelectedCitations((prev) =>
      prev.includes(citationText) ? prev.filter((c) => c !== citationText) : [...prev, citationText],
    )
  }

  const selectAll = () => {
    if (selectedCitations.length === formattedCitations.length) {
      setSelectedCitations([])
    } else {
      setSelectedCitations([...formattedCitations])
    }
  }

  const copyToClipboard = () => {
    const textToCopy = selectedCitations.length > 0 ? selectedCitations.join("\n\n") : formattedCitations.join("\n\n")

    navigator.clipboard
      .writeText(textToCopy)
      .then(() => {
        setCopied(true)
        setTimeout(() => setCopied(false), 2000)
      })
      .catch((err) => {
        console.error("Failed to copy:", err)
      })
  }

  const downloadCitations = () => {
    const textToDownload =
      selectedCitations.length > 0 ? selectedCitations.join("\n\n") : formattedCitations.join("\n\n")

    const blob = new Blob([textToDownload], { type: "text/plain" })
    const url = URL.createObjectURL(blob)
    const a = document.createElement("a")
    a.href = url
    a.download = `citations_${format}.txt`
    document.body.appendChild(a)
    a.click()
    document.body.removeChild(a)
    URL.revokeObjectURL(url)
  }

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <CardTitle className="text-lg font-semibold text-foreground">Export Citations</CardTitle>
        <CardDescription className="text-muted-foreground">Select format and citations to export</CardDescription>
      </CardHeader>

      <CardContent className="p-4">
        <div className="mb-4">
          <Label htmlFor="format-select">Citation Format</Label>
          <Select value={format} onValueChange={setFormat}>
            <SelectTrigger id="format-select" className="mt-1">
              <SelectValue placeholder="Select format" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="apa">APA</SelectItem>
              <SelectItem value="mla">MLA</SelectItem>
              <SelectItem value="chicago">Chicago</SelectItem>
              <SelectItem value="bluebook">Bluebook</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="mb-4">
          <div className="flex items-center justify-between mb-2">
            <Label>Citations</Label>
            <Button variant="ghost" size="sm" className="h-7 text-xs" onClick={selectAll}>
              {selectedCitations.length === formattedCitations.length ? "Deselect All" : "Select All"}
            </Button>
          </div>

          <div className="space-y-2 max-h-[300px] overflow-y-auto border rounded-md p-2">
            {formattedCitations.map((citation, index) => (
              <div key={index} className="flex items-start gap-2">
                <Checkbox
                  id={`citation-${index}`}
                  checked={selectedCitations.includes(citation)}
                  onCheckedChange={() => toggleCitation(index)}
                  className="mt-1"
                />
                <Label htmlFor={`citation-${index}`} className="text-sm cursor-pointer leading-tight">
                  {citation}
                </Label>
              </div>
            ))}
          </div>
        </div>
      </CardContent>

      <CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
        <Button variant="outline" size="sm" onClick={onClose}>
          Cancel
        </Button>
        <div className="flex gap-2">
          <Button
            variant="outline"
            size="sm"
            className="h-8 text-xs bg-secondary/50 dark:bg-[#2a2a2a]"
            onClick={copyToClipboard}
          >
            {copied ? (
              <>
                <Check className="h-3.5 w-3.5 mr-1.5" />
                Copied!
              </>
            ) : (
              <>
                <Copy className="h-3.5 w-3.5 mr-1.5" />
                Copy
              </>
            )}
          </Button>
          <Button variant="default" size="sm" className="h-8 text-xs" onClick={downloadCitations}>
            <Download className="h-3.5 w-3.5 mr-1.5" />
            Download
          </Button>
        </div>
      </CardFooter>
    </Card>
  )
}
