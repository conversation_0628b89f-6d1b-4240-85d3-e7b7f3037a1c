"use client";

import {
	<PERSON>,
	CardContent,
	Card<PERSON>es<PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON>it<PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Tabs, Ta<PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs";
import {
	AlertTriangle,
	AlertCircle,
	CheckCircle,
	BookOpen,
} from "lucide-react";
import { cn } from "@/lib/utils";
import type { LegalOpinionAnalysisResult } from "@/lib/types/analysis";
import { CitationsList } from "../citation-list";
import { FeedbackTrigger, CorrectionForm } from "@/components/feedback";
import { Edit2 } from "lucide-react";
import { Button } from "@/components/ui/button";
import { useState } from "react";

interface LegalOpinionArtifactProps {
	analysisId: string;
	result: LegalOpinionAnalysisResult;
	documentId?: string;
}

export function LegalOpinionArtifact({
	analysisId,
	result,
	documentId,
}: LegalOpinionArtifactProps) {
	const [showCorrectionF<PERSON>, setShowCorrectionForm] = useState(false);
	const [selectedContent, setSelectedContent] = useState<{
		title: string;
		content: string;
	} | null>(null);

	const hasCitations =
		(result.enrichedCitations && result.enrichedCitations.length > 0) ||
		(result.identifiedCitations && result.identifiedCitations.length > 0) ||
		(result.citations && result.citations.length > 0);

	const handleSuggestCorrection = (title: string, content: string) => {
		setSelectedContent({ title, content });
		setShowCorrectionForm(true);
	};

	return (
		<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
			<CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
				<div className="flex justify-between items-start">
					<div>
						<CardTitle className="text-lg font-semibold text-foreground">
							Legal Opinion
						</CardTitle>
						<CardDescription className="text-muted-foreground">
							Analysis ID: {analysisId.substring(0, 8)}
						</CardDescription>
					</div>
					<Badge
						variant="outline"
						className={cn(
							"flex items-center gap-1",
							result.confidence === "high" &&
								"bg-green-500/20 text-green-500 border-green-500/30",
							result.confidence === "medium" &&
								"bg-amber-500/20 text-amber-500 border-amber-500/30",
							result.confidence === "low" &&
								"bg-red-500/20 text-red-500 border-red-500/30"
						)}
					>
						{result.confidence === "high" ? (
							<CheckCircle className="h-3 w-3" />
						) : result.confidence === "medium" ? (
							<AlertCircle className="h-3 w-3" />
						) : (
							<AlertTriangle className="h-3 w-3" />
						)}
						{result.confidence} confidence
					</Badge>
				</div>
			</CardHeader>
			<CardContent className="p-0">
				<Tabs defaultValue="opinion" className="w-full">
					<TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
						<TabsTrigger
							value="opinion"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Opinion
						</TabsTrigger>
						<TabsTrigger
							value="reasoning"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Reasoning
						</TabsTrigger>
						<TabsTrigger
							value="citations"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Citations
						</TabsTrigger>
					</TabsList>

					<TabsContent value="opinion" className="p-4 mt-0">
						<div className="space-y-4">
							<div>
								<h4 className="text-sm font-medium mb-2">Question</h4>
								<p className="text-sm bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
									{result.question}
								</p>
							</div>

							<div>
								<h4 className="text-sm font-medium mb-2 flex items-center">
									Conclusion
									<Badge
										variant="outline"
										className={cn(
											"ml-2 text-xs",
											result.confidence === "high" &&
												"bg-green-500/20 text-green-500 border-green-500/30",
											result.confidence === "medium" &&
												"bg-amber-500/20 text-amber-500 border-amber-500/30",
											result.confidence === "low" &&
												"bg-red-500/20 text-red-500 border-red-500/30"
										)}
									>
										{result.confidence} confidence
									</Badge>
								</h4>
								<div className="text-sm bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
									{result.conclusion}
								</div>
							</div>

							{result.limitations && (
								<div>
									<h4 className="text-sm font-medium mb-2">Limitations</h4>
									<p className="text-sm text-muted-foreground">
										{result.limitations}
									</p>
								</div>
							)}
						</div>
					</TabsContent>

					<TabsContent value="reasoning" className="p-4 mt-0">
						<div className="space-y-4">
							<div className="flex justify-between items-start">
								<h3 className="text-lg font-medium">Legal Reasoning</h3>
								<Button
									variant="outline"
									size="sm"
									className="flex items-center gap-1"
									onClick={() => handleSuggestCorrection("Legal Reasoning", result.reasoning)}
								>
									<Edit2 className="h-3.5 w-3.5" />
									Suggest Correction
								</Button>
							</div>
							<div className="text-muted-foreground whitespace-pre-wrap">
								{result.reasoning}
							</div>
						</div>
					</TabsContent>

					<TabsContent value="citations" className="p-4 mt-0">
						{hasCitations ? (
							<CitationsList
								identifiedCitations={result.identifiedCitations}
								enrichedCitations={result.enrichedCitations}
							/>
						) : result.citations && result.citations.length > 0 ? (
							<div>
								<h4 className="text-sm font-medium mb-3">
									Citations & References
								</h4>
								<ul className="space-y-2">
									{result.citations.map((citation, index) => (
										<li key={index} className="flex items-start gap-2 text-xs">
											<BookOpen className="h-3.5 w-3.5 text-blue-500 mt-0.5 flex-shrink-0" />
											<span>{citation}</span>
										</li>
									))}
								</ul>
							</div>
						) : (
							<p className="text-sm text-muted-foreground">
								No citations available.
							</p>
						)}
					</TabsContent>
				</Tabs>
			</CardContent>
			<CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
				<div className="text-sm text-muted-foreground">
					{new Date().toLocaleDateString()} • Confidence: {result.confidence}
				</div>
				<FeedbackTrigger 
					sourceId={analysisId}
					source="document_analysis"
					contextData={{ documentType: result.documentType }}
					variant="minimal"
				/>
			</CardFooter>
			{/* Correction form */}
			{selectedContent && (
				<CorrectionForm
					sourceId={analysisId}
					sourceType="document"
					contextData={{ 
						documentType: result.documentType,
						section: selectedContent.title
					}}
					originalContent={selectedContent.content}
					open={showCorrectionForm}
					onOpenChange={setShowCorrectionForm}
				/>
			)}
		</Card>
	);
}
