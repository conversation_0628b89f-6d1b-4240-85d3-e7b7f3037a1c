"use client"

import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON>Content, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { DollarSign, AlertTriangle, FileText, Calendar } from "lucide-react"
import type { FinancialDocumentAnalysisResult } from "@/lib/types/analysis"
import { CitationsList } from "../citation-list"

interface FinancialDocumentArtifactProps {
  analysisId: string
  result: FinancialDocumentAnalysisResult
  documentId?: string
}

export function FinancialDocumentArtifact({ analysisId, result, documentId }: FinancialDocumentArtifactProps) {
  const hasCitations =
    (result.enrichedCitations && result.enrichedCitations.length > 0) ||
    (result.identifiedCitations && result.identifiedCitations.length > 0)

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">Financial Document</CardTitle>
            <CardDescription className="text-muted-foreground">
              {result.documentType} • Analysis ID: {analysisId.substring(0, 8)}
            </CardDescription>
          </div>
          <div className="flex gap-1">
            <Badge variant="outline" className="bg-blue-500/20 text-blue-500 border-blue-500/30 flex items-center gap-1">
              <DollarSign className="h-3 w-3 mr-1" />
              {result.documentType}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
            <TabsTrigger
              value="overview"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="obligations"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Obligations
            </TabsTrigger>
            <TabsTrigger
              value="risks"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Risks
            </TabsTrigger>
            <TabsTrigger
              value="disclosures"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Disclosures
            </TabsTrigger>
            {hasCitations && (
              <TabsTrigger
                value="citations"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                Citations
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="overview" className="p-4 mt-0">
            <div className="space-y-4">
              <div>
                <h4 className="text-base font-medium mb-2">Document Information</h4>
                <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                  <dt className="text-muted-foreground">Document Type</dt>
                  <dd>{result.documentType}</dd>
                  <dt className="text-muted-foreground">Issuer</dt>
                  <dd>{result.issuer}</dd>
                  <dt className="text-muted-foreground">Filing Date</dt>
                  <dd>{result.filingDate}</dd>
                  <dt className="text-muted-foreground">Maturity Date</dt>
                  <dd>{result.maturityDate}</dd>
                  <dt className="text-muted-foreground">Governing Law</dt>
                  <dd>{result.governingLaw}</dd>
                </dl>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="obligations" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.financialObligations.map((obligation, index) => (
                <div key={index} className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      <DollarSign className="h-4 w-4 text-green-500" />
                    </div>
                    <div>
                      <h4 className="text-base font-medium">{obligation.description}</h4>
                      <div className="mt-1 flex items-center gap-2">
                        <Badge variant="outline" className="bg-green-500/20 text-green-500 border-green-500/30">
                          {obligation.amount}
                        </Badge>
                        <span className="text-sm text-muted-foreground">
                          <Calendar className="h-3 w-3 inline mr-1" />
                          {obligation.terms}
                        </span>
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="risks" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.risks.map((risk, index) => (
                <div key={index} className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      <AlertTriangle className="h-4 w-4 text-amber-500" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">{risk}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="disclosures" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.disclosures.map((disclosure, index) => (
                <div key={index} className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      <FileText className="h-4 w-4 text-blue-500" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">{disclosure}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          {hasCitations && (
            <TabsContent value="citations" className="mt-0 p-4">
              <CitationsList
                identifiedCitations={result.identifiedCitations}
                enrichedCitations={result.enrichedCitations}
              />
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  )
}
