"use client"

import { <PERSON>, CardContent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, Ta<PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Scale, Gavel } from "lucide-react"
import type { CourtFilingAnalysisResult } from "@/lib/types/analysis"
import { CitationsList } from "../citation-list"

interface CourtFilingArtifactProps {
  analysisId: string
  result: CourtFilingAnalysisResult
  documentId?: string
}

export function CourtFilingArtifact({ analysisId, result, documentId }: CourtFilingArtifactProps) {
  const hasCitations =
    (result.enrichedCitations && result.enrichedCitations.length > 0) ||
    (result.identifiedCitations && result.identifiedCitations.length > 0)
  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">Court Filing Analysis</CardTitle>
            <CardDescription className="text-muted-foreground">
              {result.filingType} • Case #{result.caseNumber} • Analysis ID: {analysisId.substring(0, 8)}
            </CardDescription>
          </div>
          <Badge
            variant="outline"
            className="bg-purple-500/20 text-purple-500 border-purple-500/30 flex items-center gap-1"
          >
            <Gavel className="h-3 w-3" />
            {result.filingType}
          </Badge>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
            <TabsTrigger
              value="overview"
              className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="parties"
              className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Parties
            </TabsTrigger>
            <TabsTrigger
              value="arguments"
              className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Arguments
            </TabsTrigger>
            {hasCitations && (
              <TabsTrigger
                value="citations"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                Citations
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="overview" className="p-4 mt-0">
            <div className="space-y-4">
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Case Number</h4>
                  <p className="text-sm">{result.caseNumber}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-2">Filing Type</h4>
                  <p className="text-sm">{result.filingType}</p>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Court Information</h4>
                <div className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
                  <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-xs">
                    <dt className="text-muted-foreground">Court Name</dt>
                    <dd>{result.courtInfo.courtName}</dd>
                    <dt className="text-muted-foreground">Jurisdiction</dt>
                    <dd>{result.courtInfo.jurisdiction}</dd>
                  </dl>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Relief Sought</h4>
                <p className="text-sm bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">{result.relief}</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="parties" className="p-4 mt-0">
            <h4 className="text-sm font-medium mb-3">Parties to the Case</h4>
            <div className="space-y-3">
              {result.parties.map((party, index) => (
                <div key={index} className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
                  <h5 className="text-sm font-medium flex items-center">
                    {party.name}
                    <Badge className="ml-2 text-xs">{party.role}</Badge>
                  </h5>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="arguments" className="p-4 mt-0">
            <h4 className="text-sm font-medium mb-3">Key Arguments</h4>
            <ul className="space-y-2">
              {result.keyArguments.map((argument, index) => (
                <li key={index} className="flex items-start gap-2 text-xs">
                  <Scale className="h-3.5 w-3.5 text-purple-500 mt-0.5 flex-shrink-0" />
                  <span>{argument}</span>
                </li>
              ))}
            </ul>
          </TabsContent>
          {hasCitations && (
            <TabsContent value="citations" className="p-4 mt-0">
              <CitationsList
                identifiedCitations={result.identifiedCitations}
                enrichedCitations={result.enrichedCitations}
              />
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
        <div className="text-xs text-muted-foreground">{new Date().toLocaleDateString()} • Court Filing Analysis</div>
        <Button variant="outline" size="sm" className="h-7 text-xs bg-secondary/50 dark:bg-[#2a2a2a]">
          Download Report
        </Button>
      </CardFooter>
    </Card>
  )
}
