"use client"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, Info } from "lucide-react"
import { cn } from "@/lib/utils"
import type { CorporateDocumentAnalysisResult } from "@/lib/types/analysis"
import { CitationsList } from "../citation-list"

interface CorporateDocumentArtifactProps {
  analysisId: string
  result: CorporateDocumentAnalysisResult
  documentId?: string
}

export function CorporateDocumentArtifact({ analysisId, result, documentId }: CorporateDocumentArtifactProps) {
  const [expandedSummary, setExpandedSummary] = useState(false)
  const hasCitations =
    (result.enrichedCitations && result.enrichedCitations.length > 0) ||
    (result.identifiedCitations && result.identifiedCitations.length > 0)

  // Count provisions by significance
  const significanceCounts = result.governingProvisions.reduce(
    (acc, provision) => {
      if (provision.significance in acc) {
        acc[provision.significance]++
      }
      return acc
    },
    { high: 0, medium: 0, low: 0 } as Record<string, number>
  )

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">Corporate Document Analysis</CardTitle>
            <CardDescription className="text-muted-foreground">
              {result.documentType} • Analysis ID: {analysisId.substring(0, 8)}
            </CardDescription>
          </div>
          <div className="flex gap-1">
            {significanceCounts.high > 0 && (
              <Badge variant="outline" className="bg-blue-500/20 text-blue-500 border-blue-500/30 flex items-center gap-1">
                <Info className="h-3 w-3" />
                {significanceCounts.high} High Significance
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="summary" className="w-full">
          <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
            <TabsTrigger
              value="summary"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Summary
            </TabsTrigger>
            <TabsTrigger
              value="provisions"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Provisions
            </TabsTrigger>
            <TabsTrigger
              value="stakeholders"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Stakeholders
            </TabsTrigger>
            {hasCitations && (
              <TabsTrigger
                value="citations"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                Citations
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="summary" className="p-4 mt-0">
            <div className="space-y-3">
              <Collapsible open={expandedSummary} onOpenChange={setExpandedSummary}>
                <div className="text-base leading-7 text-foreground whitespace-pre-wrap">
                  {expandedSummary ? result.summary : result.summary.split("\n\n")[0]}
                </div>
                {result.summary.includes("\n\n") && (
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="mt-2 h-7 text-xs">
                      {expandedSummary ? "Show Less" : "Show More"}
                      <ChevronDown
                        className={cn("ml-1 h-4 w-4 transition-transform", expandedSummary ? "rotate-180" : "")}
                      />
                    </Button>
                  </CollapsibleTrigger>
                )}
                <CollapsibleContent className="mt-2">
                  <div className="pt-2 border-t border-border dark:border-neutral-700">
                    <h4 className="text-base font-medium mb-2">Corporate Information</h4>
                    <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                      <dt className="text-muted-foreground">Document Type</dt>
                      <dd>{result.documentType || "Not specified"}</dd>
                      <dt className="text-muted-foreground">Entity Name</dt>
                      <dd>{result.entityName || "Not specified"}</dd>
                      <dt className="text-muted-foreground">Entity Type</dt>
                      <dd>{result.entityType || "Not specified"}</dd>
                      <dt className="text-muted-foreground">Effective Date</dt>
                      <dd>{result.effectiveDate || "Not specified"}</dd>
                      <dt className="text-muted-foreground">Jurisdiction</dt>
                      <dd>{result.jurisdictionOfFormation || "Not specified"}</dd>
                    </dl>
                  </div>
                </CollapsibleContent>
              </Collapsible>
            </div>
          </TabsContent>

          <TabsContent value="provisions" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.governingProvisions
                .sort((a, b) => {
                  const significanceOrder = { high: 0, medium: 1, low: 2 }
                  return significanceOrder[a.significance] - significanceOrder[b.significance]
                })
                .map((provision, index) => (
                  <div key={index} className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">
                        <Info className={cn(
                          "h-4 w-4",
                          provision.significance === "high" ? "text-blue-500" : 
                          provision.significance === "medium" ? "text-amber-500" : "text-muted-foreground"
                        )} />
                      </div>
                      <div>
                        <h4 className="text-base font-medium flex items-center gap-2">
                          {provision.title}
                          <Badge
                            variant="outline"
                            className={cn(
                              "text-sm",
                              provision.significance === "high" && "bg-blue-500/20 text-blue-500 border-blue-500/30",
                              provision.significance === "medium" && "bg-amber-500/20 text-amber-500 border-amber-500/30",
                              provision.significance === "low" && "bg-secondary/50 text-muted-foreground"
                            )}
                          >
                            {provision.significance}
                          </Badge>
                        </h4>
                        <p className="mt-1 text-sm text-muted-foreground whitespace-pre-wrap">{provision.content}</p>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </TabsContent>

          <TabsContent value="stakeholders" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.keyStakeholders.map((stakeholder, index) => (
                <div key={index} className="p-4">
                  <h4 className="text-base font-medium">{stakeholder.name}</h4>
                  <p className="text-sm text-muted-foreground mb-2">Role: {stakeholder.role}</p>
                  
                  {stakeholder.rights.length > 0 && (
                    <div className="mt-2">
                      <h5 className="text-sm font-medium mb-1">Rights</h5>
                      <ul className="space-y-1 text-sm">
                        {stakeholder.rights.map((right, idx) => (
                          <li key={idx} className="text-muted-foreground flex items-start gap-2">
                            <span className="text-primary">•</span>
                            <span>{right}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                  
                  {stakeholder.obligations.length > 0 && (
                    <div className="mt-2">
                      <h5 className="text-sm font-medium mb-1">Obligations</h5>
                      <ul className="space-y-1 text-sm">
                        {stakeholder.obligations.map((obligation, idx) => (
                          <li key={idx} className="text-muted-foreground flex items-start gap-2">
                            <span className="text-primary">•</span>
                            <span>{obligation}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </TabsContent>

          {hasCitations && (
            <TabsContent value="citations" className="mt-0 p-4">
              <CitationsList
                identifiedCitations={result.identifiedCitations}
                enrichedCitations={result.enrichedCitations}
              />
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  )
}
