"use client"

import { useState } from "react"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, User, Briefcase, FileText } from "lucide-react"
import { cn } from "@/lib/utils"
import type { EstatePlanningAnalysisResult } from "@/lib/types/analysis"
import { CitationsList } from "../citation-list"

interface EstatePlanningArtifactProps {
  analysisId: string
  result: EstatePlanningAnalysisResult
  documentId?: string
}

export function EstatePlanningArtifact({ analysisId, result, documentId }: EstatePlanningArtifactProps) {
  const [expandedConditions, setExpandedConditions] = useState(false)
  const hasCitations =
    (result.enrichedCitations && result.enrichedCitations.length > 0) ||
    (result.identifiedCitations && result.identifiedCitations.length > 0)

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">Estate Planning Document</CardTitle>
            <CardDescription className="text-muted-foreground">
              {result.documentType} • Analysis ID: {analysisId.substring(0, 8)}
            </CardDescription>
          </div>
          <div className="flex gap-1">
            <Badge variant="outline" className="bg-blue-500/20 text-blue-500 border-blue-500/30 flex items-center gap-1">
              {result.revocability}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
            <TabsTrigger
              value="overview"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="beneficiaries"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Beneficiaries
            </TabsTrigger>
            <TabsTrigger
              value="assets"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Assets
            </TabsTrigger>
            <TabsTrigger
              value="executors"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Executors
            </TabsTrigger>
            {hasCitations && (
              <TabsTrigger
                value="citations"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                Citations
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="overview" className="p-4 mt-0">
            <div className="space-y-4">
              <div>
                <h4 className="text-base font-medium mb-2">Document Information</h4>
                <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                  <dt className="text-muted-foreground">Document Type</dt>
                  <dd>{result.documentType}</dd>
                  <dt className="text-muted-foreground">Principal</dt>
                  <dd>{result.principal}</dd>
                  <dt className="text-muted-foreground">Effective Date</dt>
                  <dd>{result.effectiveDate}</dd>
                  <dt className="text-muted-foreground">Revocability</dt>
                  <dd>{result.revocability}</dd>
                  <dt className="text-muted-foreground">Governing Law</dt>
                  <dd>{result.governingLaw}</dd>
                </dl>
              </div>

              {result.conditions.length > 0 && (
                <Collapsible open={expandedConditions} onOpenChange={setExpandedConditions} className="mt-4">
                  <div className="border-t border-border dark:border-neutral-700 pt-3">
                    <CollapsibleTrigger asChild>
                      <Button variant="ghost" size="sm" className="flex w-full justify-between p-0 h-auto">
                        <h4 className="text-base font-medium">Conditions</h4>
                        <ChevronDown
                          className={cn("h-4 w-4 transition-transform", expandedConditions ? "rotate-180" : "")}
                        />
                      </Button>
                    </CollapsibleTrigger>
                    <CollapsibleContent className="mt-2">
                      <ul className="space-y-1 text-sm">
                        {result.conditions.map((condition, index) => (
                          <li key={index} className="text-muted-foreground flex items-start gap-2">
                            <span className="text-primary">•</span>
                            <span>{condition}</span>
                          </li>
                        ))}
                      </ul>
                    </CollapsibleContent>
                  </div>
                </Collapsible>
              )}
            </div>
          </TabsContent>

          <TabsContent value="beneficiaries" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.beneficiaries.map((beneficiary, index) => (
                <div key={index} className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      <User className="h-4 w-4 text-blue-500" />
                    </div>
                    <div>
                      <h4 className="text-base font-medium flex items-center gap-2">
                        {beneficiary.name}
                        <Badge variant="outline" className="bg-secondary/50 text-muted-foreground">
                          {beneficiary.relationship}
                        </Badge>
                      </h4>
                      <p className="mt-1 text-sm text-muted-foreground whitespace-pre-wrap">{beneficiary.benefits}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="assets" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.assets.map((asset, index) => (
                <div key={index} className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      <Briefcase className="h-4 w-4 text-amber-500" />
                    </div>
                    <div>
                      <h4 className="text-base font-medium">{asset.description}</h4>
                      <p className="mt-1 text-sm text-muted-foreground whitespace-pre-wrap">{asset.disposition}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="executors" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.executors.map((executor, index) => (
                <div key={index} className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      <FileText className="h-4 w-4 text-green-500" />
                    </div>
                    <div>
                      <h4 className="text-base font-medium">{executor.name}</h4>
                      {executor.powers.length > 0 && (
                        <div className="mt-2">
                          <h5 className="text-sm font-medium mb-1">Powers</h5>
                          <ul className="space-y-1 text-sm">
                            {executor.powers.map((power, idx) => (
                              <li key={idx} className="text-muted-foreground flex items-start gap-2">
                                <span className="text-primary">•</span>
                                <span>{power}</span>
                              </li>
                            ))}
                          </ul>
                        </div>
                      )}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          {hasCitations && (
            <TabsContent value="citations" className="mt-0 p-4">
              <CitationsList
                identifiedCitations={result.identifiedCitations}
                enrichedCitations={result.enrichedCitations}
              />
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  )
}
