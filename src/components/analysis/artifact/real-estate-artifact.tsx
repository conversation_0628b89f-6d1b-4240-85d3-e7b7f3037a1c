"use client"

import { <PERSON>, CardContent, CardDescription, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Home, Users, FileText, AlertCircle } from "lucide-react"
import type { RealEstateAnalysisResult } from "@/lib/types/analysis"
import { CitationsList } from "../citation-list"

interface RealEstateArtifactProps {
  analysisId: string
  result: RealEstateAnalysisResult
  documentId?: string
}

export function RealEstateArtifact({ analysisId, result, documentId }: RealEstateArtifactProps) {
  const hasCitations =
    (result.enrichedCitations && result.enrichedCitations.length > 0) ||
    (result.identifiedCitations && result.identifiedCitations.length > 0)

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">Real Estate Document</CardTitle>
            <CardDescription className="text-muted-foreground">
              {result.documentType} • Analysis ID: {analysisId.substring(0, 8)}
            </CardDescription>
          </div>
          <div className="flex gap-1">
            <Badge variant="outline" className="bg-blue-500/20 text-blue-500 border-blue-500/30 flex items-center gap-1">
              <Home className="h-3 w-3 mr-1" />
              {result.documentType}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
            <TabsTrigger
              value="overview"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="parties"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Parties
            </TabsTrigger>
            <TabsTrigger
              value="encumbrances"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Encumbrances
            </TabsTrigger>
            <TabsTrigger
              value="covenants"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Covenants
            </TabsTrigger>
            {hasCitations && (
              <TabsTrigger
                value="citations"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                Citations
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="overview" className="p-4 mt-0">
            <div className="space-y-4">
              <div>
                <h4 className="text-base font-medium mb-2">Property Information</h4>
                <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                  <dt className="text-muted-foreground">Document Type</dt>
                  <dd>{result.documentType}</dd>
                  <dt className="text-muted-foreground">Property Address</dt>
                  <dd>{result.propertyAddress}</dd>
                  <dt className="text-muted-foreground">Effective Date</dt>
                  <dd>{result.effectiveDate}</dd>
                  <dt className="text-muted-foreground">Consideration</dt>
                  <dd>{result.consideration}</dd>
                  <dt className="text-muted-foreground">Recording Information</dt>
                  <dd>{result.recordingInformation}</dd>
                </dl>
              </div>

              <div className="border-t border-border dark:border-neutral-700 pt-3">
                <h4 className="text-base font-medium mb-2">Legal Description</h4>
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">{result.legalDescription}</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="parties" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.parties.map((party, index) => (
                <div key={index} className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      <Users className="h-4 w-4 text-blue-500" />
                    </div>
                    <div>
                      <h4 className="text-base font-medium flex items-center gap-2">
                        {party.name}
                        <Badge 
                          variant="outline" 
                          className={
                            party.role === "grantor" || party.role === "seller" ? "bg-amber-500/20 text-amber-500 border-amber-500/30" :
                            party.role === "grantee" || party.role === "buyer" ? "bg-green-500/20 text-green-500 border-green-500/30" :
                            party.role === "lender" ? "bg-blue-500/20 text-blue-500 border-blue-500/30" :
                            party.role === "borrower" ? "bg-purple-500/20 text-purple-500 border-purple-500/30" :
                            "bg-secondary/50 text-muted-foreground"
                          }
                        >
                          {party.role}
                        </Badge>
                      </h4>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="encumbrances" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.encumbrances.length > 0 ? (
                result.encumbrances.map((encumbrance, index) => (
                  <div key={index} className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">
                        <AlertCircle className="h-4 w-4 text-amber-500" />
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground whitespace-pre-wrap">{encumbrance}</p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-muted-foreground">
                  No encumbrances specified
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="covenants" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.covenants.length > 0 ? (
                result.covenants.map((covenant, index) => (
                  <div key={index} className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">
                        <FileText className="h-4 w-4 text-blue-500" />
                      </div>
                      <div>
                        <p className="text-sm text-muted-foreground whitespace-pre-wrap">{covenant}</p>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="p-4 text-center text-muted-foreground">
                  No covenants specified
                </div>
              )}
            </div>
          </TabsContent>

          {hasCitations && (
            <TabsContent value="citations" className="mt-0 p-4">
              <CitationsList
                identifiedCitations={result.identifiedCitations}
                enrichedCitations={result.enrichedCitations}
              />
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  )
}
