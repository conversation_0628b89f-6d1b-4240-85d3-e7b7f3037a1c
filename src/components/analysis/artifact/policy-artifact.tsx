"use client"

import { <PERSON>, <PERSON><PERSON>onte<PERSON>, Card<PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>it<PERSON> } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, AlertTriangle, AlertCircle, Info, Edit2 } from "lucide-react"
import { cn } from "@/lib/utils"
import type { PolicyAnalysisResult } from "@/lib/types/analysis"
import { CitationsList } from "../citation-list"
import { FeedbackTrigger, CorrectionForm } from "@/components/feedback"
import { Button } from "@/components/ui/button"
import { useState } from "react";

interface PolicyArtifactProps {
  analysisId: string
  result: PolicyAnalysisResult
  documentId?: string
}

export function PolicyArtifact({ analysisId, result, documentId }: PolicyArtifactProps) {
  // Count sections by importance
  const importanceCounts = result.sections.reduce(
    (acc, section) => {
      if (section.importance in acc) {
        acc[section.importance]++
      }
      return acc
    },
    { high: 0, medium: 0, low: 0 } as Record<string, number>,
  )

  const hasCitations =
  (result.enrichedCitations && result.enrichedCitations.length > 0) ||
  (result.identifiedCitations && result.identifiedCitations.length > 0)

  const [showCorrectionForm, setShowCorrectionForm] = useState(false);
  const [selectedContent, setSelectedContent] = useState<{
    title: string;
    content: string;
  } | null>(null);

  const handleSuggestCorrection = (title: string, content: string) => {
    setSelectedContent({ title, content });
    setShowCorrectionForm(true);
  };

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">Policy Analysis</CardTitle>
            <CardDescription className="text-muted-foreground">
              Analysis ID: {analysisId.substring(0, 8)}
            </CardDescription>
          </div>
          <div className="flex gap-1">
            {importanceCounts.high > 0 && (
              <Badge variant="destructive" className="flex items-center gap-1">
                <AlertTriangle className="h-3 w-3" />
                {importanceCounts.high} High
              </Badge>
            )}
            {importanceCounts.medium > 0 && (
              <Badge
                variant="outline"
                className="bg-amber-500/20 text-amber-500 border-amber-500/30 flex items-center gap-1"
              >
                <AlertCircle className="h-3 w-3" />
                {importanceCounts.medium} Medium
              </Badge>
            )}
            {importanceCounts.low > 0 && (
              <Badge
                variant="outline"
                className="bg-blue-500/20 text-blue-500 border-blue-500/30 flex items-center gap-1"
              >
                <Info className="h-3 w-3" />
                {importanceCounts.low} Low
              </Badge>
            )}
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
            <TabsTrigger
              value="overview"
              className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="sections"
              className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Sections
            </TabsTrigger>
            <TabsTrigger
              value="compliance"
              className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Compliance
            </TabsTrigger>{hasCitations && (
              <TabsTrigger
                value="citations"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                Citations
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="overview" className="p-4 mt-0">
            <div className="space-y-4">
              <div>
                <h4 className="text-sm font-medium mb-2">Policy Scope</h4>
                <p className="text-sm">{result.scope}</p>
              </div>

              <div className="grid grid-cols-2 gap-4">
                <div>
                  <h4 className="text-sm font-medium mb-2">Effective Date</h4>
                  <p className="text-sm">{result.effectiveDate}</p>
                </div>
                <div>
                  <h4 className="text-sm font-medium mb-2">Review Period</h4>
                  <p className="text-sm">{result.reviewPeriod || "Not specified"}</p>
                </div>
              </div>

              <div>
                <h4 className="text-sm font-medium mb-2">Key Sections</h4>
                <div className="space-y-2">
                  {result.sections
                    .filter((section) => section.importance === "high")
                    .map((section, index) => (
                      <div key={index} className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
                        <h5 className="text-sm font-medium flex items-center">
                          {section.title}
                          <Badge variant="destructive" className="ml-2 text-xs">
                            High importance
                          </Badge>
                        </h5>
                      </div>
                    ))}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="sections" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.sections
                .sort((a, b) => {
                  const importanceOrder = { high: 0, medium: 1, low: 2 }
                  return importanceOrder[a.importance] - importanceOrder[b.importance]
                })
                .map((section, index) => (
                  <Collapsible key={index} className="p-4">
                    <CollapsibleTrigger className="flex w-full items-center justify-between">
                      <div className="flex items-center">
                        <h4 className="text-sm font-medium">{section.title}</h4>
                        <Badge
                          variant={
                            section.importance === "high"
                              ? "destructive"
                              : section.importance === "medium"
                                ? "outline"
                                : "secondary"
                          }
                          className={cn(
                            "ml-2 text-xs",
                            section.importance === "medium" && "bg-amber-500/20 text-amber-500 border-amber-500/30",
                            section.importance === "low" && "bg-blue-500/20 text-blue-500 border-blue-500/30",
                          )}
                        >
                          {section.importance}
                        </Badge>
                      </div>
                      <ChevronDown className="h-4 w-4 text-muted-foreground transition-transform ui-open:rotate-180" />
                    </CollapsibleTrigger>
                    <CollapsibleContent className="pt-2 text-xs">
                      <p className="text-muted-foreground whitespace-pre-wrap">{section.content}</p>
                    </CollapsibleContent>
                  </Collapsible>
                ))}
            </div>
          </TabsContent>

          <TabsContent value="compliance" className="p-4 mt-0">
            <div>
              <h4 className="text-sm font-medium mb-2">Compliance Requirements</h4>
              <div className="text-sm whitespace-pre-wrap bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
                {result.complianceRequirements}
              </div>
            </div>
          </TabsContent>
          {hasCitations && (
            <TabsContent value="citations" className="p-4 mt-0">
              <CitationsList
                identifiedCitations={result.identifiedCitations}
                enrichedCitations={result.enrichedCitations}
              />
            </TabsContent>
          )}
          <TabsContent value="provisions" className="p-4 mt-0">
            <div className="space-y-4">
              <div className="flex justify-between items-start">
                <h3 className="text-lg font-medium">Key Sections</h3>
                <Button
                  variant="outline"
                  size="sm"
                  className="flex items-center gap-1"
                  onClick={() => handleSuggestCorrection("Policy Sections", result.sections.map((s: { title: string; content: string }) => `${s.title}: ${s.content}`).join("\n\n"))}
                >
                  <Edit2 className="h-3.5 w-3.5" />
                  Suggest Correction
                </Button>
              </div>
              {result.sections.map((section: { title: string; content: string; importance: string }, index: number) => (
                <div key={index} className="border rounded-md p-4">
                  <h4 className="font-medium">{section.title}</h4>
                  <p className="text-muted-foreground mt-1">{section.content}</p>
                  {section.importance && (
                    <div className="mt-2 p-2 bg-secondary/50 rounded-md">
                      <p className="text-sm font-medium">Importance:</p>
                      <p className="text-sm text-muted-foreground">{section.importance}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
        <div className="text-sm text-muted-foreground">
          {new Date().toLocaleDateString()} • Effective: {result.effectiveDate}
        </div>
        <FeedbackTrigger 
          sourceId={analysisId}
          source="document_analysis"
          contextData={{ documentType: result.documentType }}
          variant="minimal"
        />
      </CardFooter>
      
      {/* Correction form */}
      {selectedContent && (
        <CorrectionForm
          sourceId={analysisId}
          sourceType="document"
          contextData={{ 
            documentType: result.documentType,
            section: selectedContent.title
          }}
          originalContent={selectedContent.content}
          open={showCorrectionForm}
          onOpenChange={setShowCorrectionForm}
        />
      )}
    </Card>
  )
}
