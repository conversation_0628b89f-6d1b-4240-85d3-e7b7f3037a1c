"use client"

import { useState, useCallback } from "react"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import { FileText, CheckCircle, RefreshCw, Loader2 } from "lucide-react"
import type { GeneralAnalysisResult } from "@/lib/types/analysis"
import { CitationsList } from "../citation-list"
import { FeedbackTrigger } from "@/components/feedback"
import { documentService } from "@/lib/services/document-service"
import { useToast } from "@/hooks/use-toast"
import { useChatContext } from "@/lib/chat/chat-context"

interface GeneralArtifactProps {
  analysisId: string
  result: GeneralAnalysisResult
  documentId?: string
}

export function GeneralArtifact({ analysisId, result, documentId }: GeneralArtifactProps) {
  const [isRedoing, setIsRedoing] = useState(false)
  const { toast } = useToast()
  const { documentId: chatDocumentId } = useChatContext()

  // Use documentId from props first, fallback to chat context
  const effectiveDocumentId = documentId || chatDocumentId



  const hasCitations =
    (result.enrichedCitations && result.enrichedCitations.length > 0) ||
    (result.identifiedCitations && result.identifiedCitations.length > 0)

  const handleRedoAnalysis = useCallback(async () => {


    if (!effectiveDocumentId) {
      toast({
        title: "Error",
        description: "Document ID not available for redo analysis. Please ensure you have a document uploaded or selected.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsRedoing(true)

      // TODO: Add proper credit usage check here
      // For now, proceed with the analysis

      // Perform the redo analysis
      await documentService.redoAnalysis(effectiveDocumentId, {
        documentType: "CONTRACT",
        forceNew: true
      })

      toast({
        title: "Analysis Restarted",
        description: "Your document is being re-analyzed. The page will refresh with new results.",
        variant: "default"
      })

      // Refresh the page to show new analysis
      setTimeout(() => {
        window.location.reload()
      }, 2000)

    } catch (error) {
      console.error("Error redoing analysis:", error)
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Failed to restart analysis",
        variant: "destructive"
      })
    } finally {
      setIsRedoing(false)
    }
  }, [effectiveDocumentId, toast])
  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">
              {result.title || "Docgic Analysis"}
            </CardTitle>
            <CardDescription className="text-muted-foreground">
              General Document • Analysis ID: {analysisId.substring(0, 8)}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRedoAnalysis}
              disabled={isRedoing}
              className="gap-1.5"
              title={effectiveDocumentId ? `Redo analysis for document ${effectiveDocumentId}` : "Document ID not available"}
            >
              {isRedoing ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                <RefreshCw className="h-3 w-3" />
              )}
              {isRedoing ? "Redoing..." : "Redo Analysis"}
            </Button>
            <Badge variant="outline" className="bg-gray-500/20 text-gray-500 border-gray-500/30 flex items-center gap-1">
              <FileText className="h-3 w-3" />
              Document
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="summary" className="w-full">
          <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
            <TabsTrigger
              value="summary"
              className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Summary
            </TabsTrigger>
            <TabsTrigger
              value="keypoints"
              className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Key Points
            </TabsTrigger>
            {hasCitations && (
              <TabsTrigger
                value="citations"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                Citations
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="summary" className="p-4 mt-0">
            <div className="text-sm whitespace-pre-wrap">{result.contentSummary}</div>
          </TabsContent>

          <TabsContent value="keypoints" className="p-4 mt-0">
            <h4 className="text-sm font-medium mb-3">Key Points</h4>
            <ul className="space-y-2">
              {result?.keyPoints?.map((point, index) => (
                <li key={index} className="flex items-start gap-2 text-xs">
                  <CheckCircle className="h-3.5 w-3.5 text-green-500 mt-0.5 flex-shrink-0" />
                  <span>{point}</span>
                </li>
              ))}
            </ul>
          </TabsContent>
          
          {hasCitations && (
            <TabsContent value="citations" className="p-4 mt-0">
              <CitationsList
                identifiedCitations={result.identifiedCitations}
                enrichedCitations={result.enrichedCitations}
              />
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
        <div className="text-sm text-muted-foreground">
          {new Date().toLocaleDateString()}
        </div>
        <FeedbackTrigger 
          sourceId={analysisId}
          source="document_analysis"
          contextData={{ documentType: result.documentType }}
          variant="minimal"
        />
      </CardFooter>
    </Card>
  )
}
