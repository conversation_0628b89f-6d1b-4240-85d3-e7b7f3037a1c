"use client"

import { useState, useCallback } from "react"
import { <PERSON>, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { ChevronDown, AlertTriangle, AlertCircle, Info, CheckCircle, Edit2, RefreshCw, Loader2 } from "lucide-react"
import { cn } from "@/lib/utils"
import type { ContractAnalysisResult } from "@/lib/types/analysis"
import { CitationsList } from "../citation-list"
import { FeedbackTrigger, CorrectionForm } from "@/components/feedback"
import { documentService } from "@/lib/services/document-service"
import { useToast } from "@/hooks/use-toast"
import { useChatContext } from "@/lib/chat/chat-context"

interface ContractArtifactProps {
  analysisId: string
  result: ContractAnalysisResult
  documentId?: string
}

export function ContractArtifact({ analysisId, result, documentId }: ContractArtifactProps) {
  const [expandedSummary, setExpandedSummary] = useState(false)
  const [showCorrectionForm, setShowCorrectionForm] = useState(false)
  const [isRedoing, setIsRedoing] = useState(false)
  const { toast } = useToast()
  const { documentId: chatDocumentId } = useChatContext()
  const [selectedClause, setSelectedClause] = useState<{
    title: string;
    content: string;
  } | null>(null)

  // Use documentId from props first, fallback to chat context
  const effectiveDocumentId = documentId || chatDocumentId


  
  const hasCitations =
  (result.enrichedCitations && result.enrichedCitations.length > 0) ||
  (result.identifiedCitations && result.identifiedCitations.length > 0)
  // Count risks by level
  const riskCounts = result.clauses.reduce(
    (acc, clause) => {
      if (clause.riskLevel in acc) {
        acc[clause.riskLevel]++
      }
      return acc
    },
    { high: 0, medium: 0, low: 0, none: 0 } as Record<string, number>,
  )

  // Extract obligations from all clauses
  const allObligations = result.clauses.flatMap((clause) => clause.metadata.obligations)

  const handleRedoAnalysis = useCallback(async () => {


    if (!effectiveDocumentId) {
      toast({
        title: "Error",
        description: "Document ID not available for redo analysis. Please ensure you have a document uploaded or selected.",
        variant: "destructive"
      })
      return
    }

    try {
      setIsRedoing(true)

      // TODO: Add proper credit usage check here
      // For now, proceed with the analysis

      // Perform the redo analysis
      await documentService.redoAnalysis(effectiveDocumentId, {
        documentType: "CONTRACT",
        forceNew: true
      })

      toast({
        title: "Analysis Restarted",
        description: "Your document is being re-analyzed. The page will refresh with new results.",
        variant: "default"
      })

      // Refresh the page to show new analysis
      setTimeout(() => {
        window.location.reload()
      }, 2000)

    } catch (error) {
      console.error("Error redoing analysis:", error)
      toast({
        title: "Analysis Failed",
        description: error instanceof Error ? error.message : "Failed to restart analysis",
        variant: "destructive"
      })
    } finally {
      setIsRedoing(false)
    }
  }, [effectiveDocumentId, toast])

  const handleSuggestCorrection = (clause: { title: string; content: string }) => {
    setSelectedClause(clause)
    setShowCorrectionForm(true)
  }

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">Contract Analysis</CardTitle>
            <CardDescription className="text-muted-foreground">
              {result.documentType} • Analysis ID: {analysisId.substring(0, 8)}
            </CardDescription>
          </div>
          <div className="flex items-center gap-2">
            <Button
              variant="outline"
              size="sm"
              onClick={handleRedoAnalysis}
              disabled={isRedoing}
              className="gap-1.5"
              title={effectiveDocumentId ? `Redo analysis for document ${effectiveDocumentId}` : "Document ID not available"}
            >
              {isRedoing ? (
                <Loader2 className="h-3 w-3 animate-spin" />
              ) : (
                <RefreshCw className="h-3 w-3" />
              )}
              {isRedoing ? "Redoing..." : "Redo Analysis"}
            </Button>
            <div className="flex gap-1">
              {riskCounts.high > 0 && (
                <Badge variant="destructive" className="flex items-center gap-1">
                  <AlertTriangle className="h-3 w-3" />
                  {riskCounts.high} High
                </Badge>
              )}
              {riskCounts.medium > 0 && (
                <Badge
                  variant="outline"
                  className="bg-amber-500/20 text-amber-500 border-amber-500/30 flex items-center gap-1"
                >
                  <AlertCircle className="h-3 w-3" />
                  {riskCounts.medium} Medium
                </Badge>
              )}
              {riskCounts.low > 0 && (
                <Badge
                  variant="outline"
                  className="bg-blue-500/20 text-blue-500 border-blue-500/30 flex items-center gap-1"
                >
                  <Info className="h-3 w-3" />
                  {riskCounts.low} Low
                </Badge>
              )}
            </div>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="summary" className="w-full">
          <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
            <TabsTrigger
              value="summary"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Summary
            </TabsTrigger>
            <TabsTrigger
              value="risks"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Risks
            </TabsTrigger>
            <TabsTrigger
              value="obligations"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Obligations
            </TabsTrigger>
            <TabsTrigger
              value="parties"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Parties
            </TabsTrigger>
            <TabsTrigger
              value="clauses"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Clauses
            </TabsTrigger>
            {hasCitations && (
              <TabsTrigger
                value="citations"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                Citations
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="summary" className="p-4 mt-0">
            <div className="space-y-3">
              <Collapsible open={expandedSummary} onOpenChange={setExpandedSummary}>
                <div className="text-base leading-7 text-foreground whitespace-pre-wrap">
                  {expandedSummary ? result.summary : result.summary.split("\n\n")[0]}
                </div>
                {result.summary.includes("\n\n") && (
                  <CollapsibleTrigger asChild>
                    <Button variant="ghost" size="sm" className="mt-2 h-7 text-xs">
                      {expandedSummary ? "Show Less" : "Show More"}
                      <ChevronDown
                        className={cn("ml-1 h-4 w-4 transition-transform", expandedSummary ? "rotate-180" : "")}
                      />
                    </Button>
                  </CollapsibleTrigger>
                )}
                <CollapsibleContent className="mt-2">
                  <div className="pt-2 border-t border-border dark:border-neutral-700">
                    <h4 className="text-base font-medium mb-2">Contract Information</h4>
                    <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                      <dt className="text-muted-foreground">Document Type</dt>
                      <dd>{result.documentType || "Not specified"}</dd>
                      <dt className="text-muted-foreground">Effective Date</dt>
                      <dd>{result.effectiveDate || "Not specified"}</dd>
                      <dt className="text-muted-foreground">Termination Date</dt>
                      <dd>{result.terminationDate || "Not specified"}</dd>
                      <dt className="text-muted-foreground">Governing Law</dt>
                      <dd>{result.governingLaw || "Not specified"}</dd>
                    </dl>
                  </div>
                  {result.specialTerms && result.specialTerms.length > 0 && (
                    <div className="pt-2 mt-2 border-t border-border dark:border-neutral-700">
                      <h4 className="text-base font-medium mb-2">Special Terms</h4>
                      <ul className="space-y-1 text-sm">
                        {result.specialTerms.map((term, index) => (
                          <li key={index} className="text-muted-foreground flex items-start gap-2">
                            <span className="text-primary">•</span>
                            <span >{term}</span>
                          </li>
                        ))}
                      </ul>
                    </div>
                  )}
                </CollapsibleContent>
              </Collapsible>
            </div>
          </TabsContent>

          <TabsContent value="risks" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.clauses
                .filter((clause) => clause.riskLevel !== "none")
                .sort((a, b) => {
                  const riskOrder = { high: 0, medium: 1, low: 2, none: 3 }
                  return riskOrder[a.riskLevel] - riskOrder[b.riskLevel]
                })
                .map((clause, index) => (
                  <div key={index} className="p-4">
                    <div className="flex items-start gap-3">
                      <div className="mt-0.5">
                        {clause.riskLevel === "high" ? (
                          <AlertTriangle className="h-4 w-4 text-destructive" />
                        ) : clause.riskLevel === "medium" ? (
                          <AlertCircle className="h-4 w-4 text-amber-500" />
                        ) : (
                          <Info className="h-4 w-4 text-blue-500" />
                        )}
                      </div>
                      <div>
                        <h4 className="text-base font-medium flex items-center gap-2">
                          {clause.title}
                          <Badge
                            variant={
                              clause.riskLevel === "high"
                                ? "destructive"
                                : clause.riskLevel === "medium"
                                  ? "outline"
                                  : "secondary"
                            }
                            className={cn(
                              "text-sm",
                              clause.riskLevel === "medium" && "bg-amber-500/20 text-amber-500 border-amber-500/30",
                              clause.riskLevel === "low" && "bg-blue-500/20 text-blue-500 border-blue-500/30",
                            )}
                          >
                            {clause.riskLevel} risk
                          </Badge>
                        </h4>
                        <p className="text-sm text-muted-foreground mt-1">{clause.riskDescription}</p>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </TabsContent>

          <TabsContent value="obligations" className="mt-0">
            <div className="p-4">
              <h4 className="text-base font-medium mb-3">Key Obligations</h4>
              <ul className="space-y-2">
                {allObligations.slice(0, 10).map((obligation, index) => (
                  <li key={index} className="flex items-start gap-2 text-sm">
                    <CheckCircle className="h-3.5 w-3.5 text-green-500 mt-0.5 flex-shrink-0" />
                    <span>{obligation}</span>
                  </li>
                ))}
                {allObligations.length > 10 && (
                  <li className="text-sm text-muted-foreground pl-5">+{allObligations.length - 10} more obligations</li>
                )}
              </ul>
            </div>
          </TabsContent>

          <TabsContent value="parties" className="mt-0">
            <div className="p-4">
              <h4 className="text-base font-medium mb-3">Contract Parties</h4>
              <div className="space-y-4">
                {result.parties.map((party, index) => (
                  <div key={index} className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
                    <h5 className="text-base font-medium mb-2 flex items-center">
                      <span className="bg-primary text-primary-foreground w-5 h-5 rounded-full flex items-center justify-center text-xs mr-2">
                        {index + 1}
                      </span>
                      {party.name}
                      <Badge className="ml-2 text-xs">{party.role}</Badge>
                    </h5>
                    <dl className="grid grid-cols-2 gap-x-4 gap-y-1 text-xs">
                      {party.address && (
                        <>
                          <dt className="text-muted-foreground">Address</dt>
                          <dd>{party.address}</dd>
                        </>
                      )}
                      {party.email && (
                        <>
                          <dt className="text-muted-foreground">Email</dt>
                          <dd>{party.email}</dd>
                        </>
                      )}
                      {party.registrationNumber && (
                        <>
                          <dt className="text-muted-foreground">Registration</dt>
                          <dd>{party.registrationNumber}</dd>
                        </>
                      )}
                    </dl>
                  </div>
                ))}
              </div>
            </div>
          </TabsContent>

          <TabsContent value="clauses" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.clauses.map((clause, index) => (
                <Collapsible key={index} className="px-4 py-3">
                  <div className="flex items-start">
                    <div className="flex-1">
                      <CollapsibleTrigger asChild>
                        <div className="flex items-center justify-between cursor-pointer">
                          <h4 className="text-base font-medium">{clause.title}</h4>
                          <ChevronDown className="h-4 w-4 text-muted-foreground" />
                        </div>
                      </CollapsibleTrigger>
                      <div className="mt-1 text-sm text-muted-foreground line-clamp-2">
                        {clause.content}
                      </div>
                    </div>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="ml-2 h-7 w-7"
                      onClick={(e) => {
                        e.stopPropagation();
                        handleSuggestCorrection({ title: clause.title, content: clause.content });
                      }}
                      title="Suggest correction"
                    >
                      <Edit2 className="h-4 w-4 text-muted-foreground" />
                    </Button>
                  </div>
                  <CollapsibleContent className="mt-2 pt-2 border-t border-border dark:border-neutral-700">
                    <p className="text-muted-foreground">{clause.content}</p>
                    <div className="mt-2 pt-2 border-t border-border dark:border-neutral-700">
                      {/* Section information */}
                      {Array.isArray(clause.metadata.section) && clause.metadata.section.length > 0 && (
                        <div className="mb-2">
                          <h5 className="font-medium mb-1">Section:</h5>
                          <div className="flex flex-wrap gap-1">
                            {clause.metadata.section.map((section, i) => (
                              <Badge key={i} variant="outline" className="text-sm">
                                {section}
                              </Badge>
                            ))}
                          </div>
                        </div>
                      )}

                      {/* Obligations */}
                      {clause.metadata.obligations.length > 0 && (
                        <div className="mb-2">
                          <h5 className="font-medium mb-1">Obligations:</h5>
                          <ul className="space-y-1 pl-4 list-disc">
                            {clause.metadata.obligations.map((obligation, i) => (
                              <li key={i}>{obligation}</li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Rights */}
                      {clause.metadata.rights.length > 0 && (
                        <div className="mb-2">
                          <h5 className="font-medium mb-1">Rights:</h5>
                          <ul className="space-y-1 pl-4 list-disc">
                            {clause.metadata.rights.map((right, i) => (
                              <li key={i}>{right}</li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Restrictions */}
                      {clause.metadata.restrictions && clause.metadata.restrictions.length > 0 && (
                        <div className="mb-2">
                          <h5 className="font-medium mb-1">Restrictions:</h5>
                          <ul className="space-y-1 pl-4 list-disc">
                            {clause.metadata.restrictions.map((restriction, i) => (
                              <li key={i}>{restriction}</li>
                            ))}
                          </ul>
                        </div>
                      )}

                      {/* Definitions */}
                      {clause.metadata.definitions && Object.keys(clause.metadata.definitions).length > 0 && (
                        <div>
                          <h5 className="font-medium mb-1">Definitions:</h5>
                          <dl className="space-y-1">
                            {Object.entries(clause.metadata.definitions).map(([term, definition], i) => (
                              <div key={i} className="grid grid-cols-[1fr,2fr] gap-2">
                                <dt className="font-medium">{term}:</dt>
                                <dd>{definition}</dd>
                              </div>
                            ))}
                          </dl>
                        </div>
                      )}
                    </div>
                  </CollapsibleContent>
                </Collapsible>
              ))}
            </div>
          </TabsContent>
          {hasCitations && (
            <TabsContent value="citations" className="p-4 mt-0">
              <CitationsList
                identifiedCitations={result.identifiedCitations}
                enrichedCitations={result.enrichedCitations}
              />
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
      <CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
        <div className="text-sm text-muted-foreground">
          {new Date().toLocaleDateString()} • {result.clauses.length} clauses analyzed
        </div>
        <FeedbackTrigger 
          sourceId={analysisId}
          source="document_analysis"
          contextData={{ documentType: result.documentType }}
          variant="minimal"
        />
      </CardFooter>
      {/* Correction form */}
      {selectedClause && (
        <CorrectionForm
          sourceId={analysisId}
          sourceType="document"
          contextData={{ 
            documentType: result.documentType,
            clauseTitle: selectedClause.title
          }}
          originalContent={selectedClause.content}
          open={showCorrectionForm}
          onOpenChange={setShowCorrectionForm}
        />
      )}
    </Card>
  )
}
