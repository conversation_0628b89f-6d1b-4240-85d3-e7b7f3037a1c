"use client";

import {
	<PERSON>,
	CardContent,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON><PERSON>er,
	<PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { ChevronDown, Scale } from "lucide-react";
import type { LegislationAnalysisResult } from "@/lib/types/analysis";
import { CitationsList } from "../citation-list";

interface LegislationArtifactProps {
	analysisId: string;
	result: LegislationAnalysisResult;
	documentId?: string;
}

export function LegislationArtifact({
	analysisId,
	result,
	documentId,
}: LegislationArtifactProps) {
	const hasCitations =
		(result.enrichedCitations && result.enrichedCitations.length > 0) ||
		(result.identifiedCitations && result.identifiedCitations.length > 0);
	return (
		<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
			<CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
				<div className="flex justify-between items-start">
					<div>
						<CardTitle className="text-lg font-semibold text-foreground">
							{result.title}
						</CardTitle>
						<CardDescription className="text-muted-foreground">
							Legislation • {result.jurisdiction} • Analysis ID:{" "}
							{analysisId.substring(0, 8)}
						</CardDescription>
					</div>
					<Badge
						variant="outline"
						className="bg-blue-500/20 text-blue-500 border-blue-500/30 flex items-center gap-1"
					>
						<Scale className="h-3 w-3" />
						Legislation
					</Badge>
				</div>
			</CardHeader>
			<CardContent className="p-0">
				<Tabs defaultValue="overview" className="w-full">
					<TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
						<TabsTrigger
							value="overview"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Overview
						</TabsTrigger>
						<TabsTrigger
							value="sections"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Sections
						</TabsTrigger>
						<TabsTrigger
							value="definitions"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Definitions
						</TabsTrigger>
						<TabsTrigger
							value="penalties"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Penalties
						</TabsTrigger>
						{hasCitations && (
							<TabsTrigger
								value="citations"
								className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
							>
								Citations
							</TabsTrigger>
						)}
					</TabsList>

					<TabsContent value="overview" className="p-4 mt-0">
						<div className="space-y-4">
							<div className="grid grid-cols-2 gap-4">
								<div>
									<h4 className="text-sm font-medium mb-2">Jurisdiction</h4>
									<p className="text-sm">{result.jurisdiction}</p>
								</div>
								<div>
									<h4 className="text-sm font-medium mb-2">Effective Date</h4>
									<p className="text-sm">{result.effectiveDate}</p>
								</div>
							</div>

							<div>
								<h4 className="text-sm font-medium mb-2">Key Sections</h4>
								<div className="space-y-2">
									{result.sections.slice(0, 3).map((section, index) => (
										<div
											key={index}
											className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md"
										>
											<h5 className="text-sm font-medium">{section.title}</h5>
										</div>
									))}
									{result.sections.length > 3 && (
										<p className="text-xs text-muted-foreground">
											+{result.sections.length - 3} more sections
										</p>
									)}
								</div>
							</div>
						</div>
					</TabsContent>

					<TabsContent value="sections" className="mt-0">
						<div className="divide-y divide-border dark:divide-neutral-700">
							{result.sections.map((section, index) => (
								<Collapsible key={index} className="p-4">
									<CollapsibleTrigger className="flex w-full items-center justify-between">
										<h4 className="text-sm font-medium">{section.title}</h4>
										<ChevronDown className="h-4 w-4 text-muted-foreground transition-transform ui-open:rotate-180" />
									</CollapsibleTrigger>
									<CollapsibleContent className="pt-2 text-xs">
										<p className="text-muted-foreground whitespace-pre-wrap">
											{section.content}
										</p>
									</CollapsibleContent>
								</Collapsible>
							))}
						</div>
					</TabsContent>

					<TabsContent value="definitions" className="p-4 mt-0">
						<h4 className="text-sm font-medium mb-3">Legal Definitions</h4>
						<div className="space-y-3">
							{Object.entries(result.definitions).map(
								([term, definition], index) => (
									<div
										key={index}
										className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md"
									>
										<h5 className="text-sm font-medium">{term}</h5>
										<p className="text-xs text-muted-foreground mt-1">
											{definition}
										</p>
									</div>
								)
							)}
						</div>
					</TabsContent>

					<TabsContent value="penalties" className="p-4 mt-0">
						<div>
							<h4 className="text-sm font-medium mb-2 flex items-center">
								<Scale className="h-4 w-4 mr-2 text-red-500" />
								Penalties & Enforcement
							</h4>
							<div className="text-sm whitespace-pre-wrap bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
								{result.penalties}
							</div>
						</div>
					</TabsContent>
					{hasCitations && (
						<TabsContent value="citations" className="p-4 mt-0">
							<CitationsList
								identifiedCitations={result.identifiedCitations}
								enrichedCitations={result.enrichedCitations}
							/>
						</TabsContent>
					)}
				</Tabs>
			</CardContent>
			<CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
				<div className="text-xs text-muted-foreground">
					{new Date().toLocaleDateString()} • {result.sections.length} sections
					analyzed
				</div>
				<Button
					variant="outline"
					size="sm"
					className="h-7 text-xs bg-secondary/50 dark:bg-[#2a2a2a]"
				>
					Download Report
				</Button>
			</CardFooter>
		</Card>
	);
}
