"use client"

import { <PERSON>, CardContent, CardDescription, Card<PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { FileText, Globe, Shield } from "lucide-react"
import type { IntellectualPropertyAnalysisResult } from "@/lib/types/analysis"
import { CitationsList } from "../citation-list"

interface IntellectualPropertyArtifactProps {
  analysisId: string
  result: IntellectualPropertyAnalysisResult
  documentId?: string
}

export function IntellectualPropertyArtifact({ analysisId, result, documentId }: IntellectualPropertyArtifactProps) {
  const hasCitations =
    (result.enrichedCitations && result.enrichedCitations.length > 0) ||
    (result.identifiedCitations && result.identifiedCitations.length > 0)

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700 overflow-hidden">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">Intellectual Property Document</CardTitle>
            <CardDescription className="text-muted-foreground">
              {result.documentType} • Analysis ID: {analysisId.substring(0, 8)}
            </CardDescription>
          </div>
          <div className="flex gap-1">
            <Badge variant="outline" className="bg-blue-500/20 text-blue-500 border-blue-500/30 flex items-center gap-1">
              {result.registrationNumber}
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="p-0">
        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
            <TabsTrigger
              value="overview"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Overview
            </TabsTrigger>
            <TabsTrigger
              value="scope"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Scope
            </TabsTrigger>
            <TabsTrigger
              value="restrictions"
              className="text-sm data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Restrictions
            </TabsTrigger>
            {hasCitations && (
              <TabsTrigger
                value="citations"
                className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
              >
                Citations
              </TabsTrigger>
            )}
          </TabsList>

          <TabsContent value="overview" className="p-4 mt-0">
            <div className="space-y-4">
              <div>
                <h4 className="text-base font-medium mb-2">Document Information</h4>
                <dl className="grid grid-cols-2 gap-x-4 gap-y-2 text-sm">
                  <dt className="text-muted-foreground">Document Type</dt>
                  <dd>{result.documentType}</dd>
                  <dt className="text-muted-foreground">Owner</dt>
                  <dd>{result.owner}</dd>
                  <dt className="text-muted-foreground">Filing Date</dt>
                  <dd>{result.filingDate}</dd>
                  <dt className="text-muted-foreground">Registration Number</dt>
                  <dd>{result.registrationNumber}</dd>
                  <dt className="text-muted-foreground">Duration</dt>
                  <dd>{result.duration}</dd>
                </dl>
              </div>

              <div className="border-t border-border dark:border-neutral-700 pt-3">
                <h4 className="text-base font-medium mb-2">Description</h4>
                <p className="text-sm text-muted-foreground whitespace-pre-wrap">{result.description}</p>
              </div>

              {result.territory.length > 0 && (
                <div className="border-t border-border dark:border-neutral-700 pt-3">
                  <h4 className="text-base font-medium mb-2 flex items-center gap-2">
                    <Globe className="h-4 w-4" />
                    Territory
                  </h4>
                  <div className="flex flex-wrap gap-1">
                    {result.territory.map((territory, index) => (
                      <Badge key={index} variant="outline" className="bg-secondary/50">
                        {territory}
                      </Badge>
                    ))}
                  </div>
                </div>
              )}
            </div>
          </TabsContent>

          <TabsContent value="scope" className="p-4 mt-0">
            <div className="flex items-start gap-3">
              <div className="mt-0.5">
                <FileText className="h-4 w-4 text-blue-500" />
              </div>
              <div>
                <h4 className="text-base font-medium">Scope of Protection</h4>
                <p className="mt-2 text-sm text-muted-foreground whitespace-pre-wrap">{result.scope}</p>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="restrictions" className="mt-0">
            <div className="divide-y divide-border dark:divide-neutral-700">
              {result.restrictions.map((restriction, index) => (
                <div key={index} className="p-4">
                  <div className="flex items-start gap-3">
                    <div className="mt-0.5">
                      <Shield className="h-4 w-4 text-amber-500" />
                    </div>
                    <div>
                      <p className="text-sm text-muted-foreground whitespace-pre-wrap">{restriction}</p>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </TabsContent>

          {hasCitations && (
            <TabsContent value="citations" className="mt-0 p-4">
              <CitationsList
                identifiedCitations={result.identifiedCitations}
                enrichedCitations={result.enrichedCitations}
              />
            </TabsContent>
          )}
        </Tabs>
      </CardContent>
    </Card>
  )
}
