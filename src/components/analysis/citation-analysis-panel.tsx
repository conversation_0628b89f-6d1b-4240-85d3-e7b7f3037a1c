"use client";

import { useState, useEffect } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import {
	Loader2,
	Network,
	BookOpen,
	TrendingUp,
	GitBranch,
	ExternalLink,
} from "lucide-react";
import {
	citationAnalysisService,
	type CitationAnalysisResponse,
} from "@/lib/services/citation-analysis-service";
import { ScrollArea } from "@/components/ui/scroll-area";

interface CitationAnalysisPanelProps {
	citation: string;
}

export function CitationAnalysisPanel({
	citation,
}: CitationAnalysisPanelProps) {
	const [loading, setLoading] = useState(true);
	const [error, setError] = useState<string | null>(null);
	const [analysisData, setAnalysisData] =
		useState<CitationAnalysisResponse | null>(null);
	const [activeTab, setActiveTab] = useState("overview");
	const subscriptionFeatures = {
		hasBasicAnalysis: true,
		hasEnhancedAnalysis: true, // Set to true since we're getting impact data
	};

	useEffect(() => {
		const fetchAnalysis = async () => {
			try {
				setLoading(true);
				setError(null);

				const data = await citationAnalysisService.analyzeCitation({
					citation,
					includeRelationships: true,
					includePrecedentChains: subscriptionFeatures.hasEnhancedAnalysis,
					includeImpact: subscriptionFeatures.hasEnhancedAnalysis,
				});

				setAnalysisData(data);
			} catch (err) {
				setError(
					err instanceof Error ? err.message : "Failed to analyze citation"
				);
				console.error("Error fetching citation analysis:", err);
			} finally {
				setLoading(false);
			}
		};

		fetchAnalysis();
	}, [citation, subscriptionFeatures.hasEnhancedAnalysis]);

	if (loading) {
		return (
			<Card className="w-full h-full flex items-center justify-center">
				<Loader2 className="h-8 w-8 animate-spin text-primary" />
				<p className="mt-2 text-sm text-muted-foreground">
					Analyzing citation...
				</p>
			</Card>
		);
	}

	if (error) {
		return (
			<Card className="w-full h-full">
				<CardHeader>
					<CardTitle className="text-destructive">Analysis Error</CardTitle>
				</CardHeader>
				<CardContent>
					<p>{error}</p>
					<Button onClick={() => window.location.reload()} className="mt-4">
						Retry
					</Button>
				</CardContent>
			</Card>
		);
	}

	if (!analysisData || !analysisData.citation) {
		return (
			<Card className="w-full h-full">
				<CardHeader>
					<CardTitle>No Data Available</CardTitle>
				</CardHeader>
				<CardContent>
					<p>No analysis data is available for this citation.</p>
				</CardContent>
			</Card>
		);
	}

	// Get case name from title or citation
	const getCaseName = () => {
		return analysisData.citation.title || analysisData.citation.citation;
	};

	// Format relationship strength
	const formatStrength = (strength: string) => {
		return strength.charAt(0).toUpperCase() + strength.slice(1);
	};

	return (
		<Card className="w-full h-full flex flex-col">
			<CardHeader className="pb-2">
				<div className="flex justify-between items-start">
					<div>
						<CardTitle className="text-xl">{getCaseName()}</CardTitle>
						<CardDescription className="mt-1">
							{analysisData.citation.court &&
								`${analysisData.citation.court}, `}
							{analysisData.citation.year && `${analysisData.citation.year}`}
						</CardDescription>
					</div>
				</div>
				<div className="flex flex-wrap gap-2 mt-2">
					<Badge variant="outline">{analysisData.citation.type}</Badge>
					{analysisData.citation.citation && (
						<Badge variant="secondary">{analysisData.citation.citation}</Badge>
					)}
				</div>
			</CardHeader>

			<Tabs
				value={activeTab}
				onValueChange={setActiveTab}
				className="flex-1 flex flex-col"
			>
				<TabsList className="grid grid-cols-4 mb-2">
					<TabsTrigger value="overview">
						<BookOpen className="h-4 w-4 mr-2" />
						<span className="hidden sm:inline">Overview</span>
					</TabsTrigger>
					<TabsTrigger value="relationships">
						<Network className="h-4 w-4 mr-2" />
						<span className="hidden sm:inline">Relationships</span>
					</TabsTrigger>
					<TabsTrigger
						value="impact"
						disabled={!subscriptionFeatures.hasEnhancedAnalysis}
						title={
							!subscriptionFeatures.hasEnhancedAnalysis
								? "Requires enhanced subscription"
								: ""
						}
					>
						<TrendingUp className="h-4 w-4 mr-2" />
						<span className="hidden sm:inline">Impact</span>
					</TabsTrigger>
					<TabsTrigger
						value="precedent"
						disabled={!subscriptionFeatures.hasEnhancedAnalysis}
						title={
							!subscriptionFeatures.hasEnhancedAnalysis
								? "Requires enhanced subscription"
								: ""
						}
					>
						<GitBranch className="h-4 w-4 mr-2" />
						<span className="hidden sm:inline">Precedent</span>
					</TabsTrigger>
				</TabsList>

				<CardContent className="flex-1 p-0 overflow-hidden">
					<ScrollArea className="h-full">
						<div className="p-4">
							<TabsContent value="overview" className="m-0">
								<div className="space-y-4">
									<div>
										<h3 className="text-sm font-medium text-muted-foreground">
											Citation
										</h3>
										<p className="mt-1">{analysisData.citation.citation}</p>
									</div>

									<div>
										<h3 className="text-sm font-medium text-muted-foreground">
											Type
										</h3>
										<p className="mt-1">{analysisData.citation.type}</p>
									</div>

									{analysisData.citation.court && (
										<div>
											<h3 className="text-sm font-medium text-muted-foreground">
												Court
											</h3>
											<p className="mt-1">{analysisData.citation.court}</p>
										</div>
									)}

									{analysisData.citation.year && (
										<div>
											<h3 className="text-sm font-medium text-muted-foreground">
												Year
											</h3>
											<p className="mt-1">{analysisData.citation.year}</p>
										</div>
									)}

									{analysisData.citation.jurisdiction && (
										<div>
											<h3 className="text-sm font-medium text-muted-foreground">
												Jurisdiction
											</h3>
											<p className="mt-1">
												{analysisData.citation.jurisdiction}
											</p>
										</div>
									)}

									<div className="mt-4">
										<Button variant="outline" size="sm" asChild>
											<a
												href={`https://www.courtlistener.com/?q=${encodeURIComponent(
													analysisData.citation.citation
												)}`}
												target="_blank"
												rel="noopener noreferrer"
											>
												<ExternalLink className="h-4 w-4 mr-2" />
												View on Court Listener
											</a>
										</Button>
									</div>
								</div>
							</TabsContent>

							<TabsContent value="relationships" className="m-0">
								<ScrollArea className="h-[400px]">
									{analysisData.relationships &&
									analysisData.relationships.length > 0 ? (
										<div className="space-y-4">
											{analysisData.relationships.map((rel, idx) => (
												<Card key={idx} className="overflow-hidden">
													<CardHeader className="p-3">
														<div className="flex justify-between items-center">
															<Badge
																variant={
																	rel.relationshipType === "cites"
																		? "default"
																		: "secondary"
																}
															>
																{rel.relationshipType}
															</Badge>
															<span className="text-xs text-muted-foreground">
																Strength: {formatStrength(rel.strength)}
															</span>
														</div>
													</CardHeader>
													<CardContent className="p-3 pt-0">
														{rel.relationshipType === "cites" &&
														rel.metadata.citedCase ? (
															<div>
																<p className="text-sm font-medium">Cites:</p>
																<p className="text-sm mt-1">
																	{rel.metadata.citedCase.name}
																</p>
																<p className="text-xs text-muted-foreground mt-1">
																	{rel.metadata.citedCase.citation} (
																	{rel.metadata.citedCase.court})
																</p>
																{rel.metadata.relationshipDescription && (
																	<p className="text-xs italic mt-2">
																		{rel.metadata.relationshipDescription}
																	</p>
																)}
																{rel.metadata.citedCaseUrl && (
																	<Button
																		variant="link"
																		size="sm"
																		className="p-0 h-auto text-xs mt-2"
																		asChild
																	>
																		<a
																			href={rel.metadata.citedCaseUrl}
																			target="_blank"
																			rel="noopener noreferrer"
																		>
																			<ExternalLink className="h-3 w-3 mr-1" />
																			View Case
																		</a>
																	</Button>
																)}
															</div>
														) : rel.relationshipType === "citedBy" &&
														  rel.metadata.citingCase ? (
															<div>
																<p className="text-sm font-medium">Cited by:</p>
																<p className="text-sm mt-1">
																	{rel.metadata.citingCase.name}
																</p>
																<p className="text-xs text-muted-foreground mt-1">
																	{rel.metadata.citingCase.citation} (
																	{rel.metadata.citingCase.court})
																</p>
																{rel.metadata.relationshipDescription && (
																	<p className="text-xs italic mt-2">
																		{rel.metadata.relationshipDescription}
																	</p>
																)}
																{rel.metadata.citingCaseUrl && (
																	<Button
																		variant="link"
																		size="sm"
																		className="p-0 h-auto text-xs mt-2"
																		asChild
																	>
																		<a
																			href={rel.metadata.citingCaseUrl}
																			target="_blank"
																			rel="noopener noreferrer"
																		>
																			<ExternalLink className="h-3 w-3 mr-1" />
																			View Case
																		</a>
																	</Button>
																)}
															</div>
														) : (
															<p className="text-sm">
																Relationship details not available
															</p>
														)}
													</CardContent>
												</Card>
											))}
										</div>
									) : (
										<div className="text-center py-8">
											<p className="text-muted-foreground">
												No relationship data available
											</p>
										</div>
									)}
								</ScrollArea>
							</TabsContent>

							<TabsContent value="impact" className="m-0">
								{!subscriptionFeatures.hasEnhancedAnalysis ? (
									<div className="text-center py-8 space-y-4">
										<TrendingUp className="h-12 w-12 mx-auto text-muted-foreground" />
										<div>
											<h3 className="text-lg font-medium">Impact Analysis</h3>
											<p className="text-muted-foreground mt-1">
												Upgrade to our enhanced plan to access impact analysis.
											</p>
										</div>
										<Button>Upgrade Now</Button>
									</div>
								) : analysisData.impact ? (
									<div className="space-y-4">
										<div>
											<h3 className="text-sm font-medium text-muted-foreground">
												Impact Score
											</h3>
											<div className="flex items-center mt-1">
												<div className="w-full bg-secondary h-4 rounded-full overflow-hidden">
													<div
														className="bg-primary h-full rounded-full"
														style={{
															width: `${analysisData.impact.impactScore}%`,
														}}
													/>
												</div>
												<span className="ml-2 text-sm font-medium">
													{analysisData.impact.impactScore}/100
												</span>
											</div>
										</div>

										<div className="grid grid-cols-2 gap-4">
											<div>
												<h3 className="text-sm font-medium text-muted-foreground">
													Total Citations
												</h3>
												<p className="mt-1 text-2xl font-bold">
													{analysisData.impact.totalCitations}
												</p>
											</div>
											<div>
												<h3 className="text-sm font-medium text-muted-foreground">
													Recent Citations
												</h3>
												<p className="mt-1 text-2xl font-bold">
													{analysisData.impact.recentCitations}
												</p>
												<p className="text-xs text-muted-foreground">
													(Last 5 years)
												</p>
											</div>
										</div>

										<div className="grid grid-cols-2 gap-4">
											<div>
												<h3 className="text-sm font-medium text-muted-foreground">
													Positive References
												</h3>
												<p className="mt-1 text-2xl font-bold text-green-500">
													{analysisData.impact.positiveReferences}
												</p>
											</div>
											<div>
												<h3 className="text-sm font-medium text-muted-foreground">
													Negative References
												</h3>
												<p className="mt-1 text-2xl font-bold text-red-500">
													{analysisData.impact.negativeReferences}
												</p>
											</div>
										</div>

										<div>
											<h3 className="text-sm font-medium text-muted-foreground">
												Influential Citations
											</h3>
											<p className="mt-1 text-2xl font-bold">
												{analysisData.impact.influentialCitations}
											</p>
										</div>
									</div>
								) : (
									<div className="text-center py-8">
										<p className="text-muted-foreground">
											No impact data available
										</p>
									</div>
								)}
							</TabsContent>

							<TabsContent value="precedent" className="m-0">
								<ScrollArea className="h-[400px]">
									{!subscriptionFeatures.hasEnhancedAnalysis ? (
										<div className="text-center py-8 space-y-4">
											<GitBranch className="h-12 w-12 mx-auto text-muted-foreground" />
											<div>
												<h3 className="text-lg font-medium">
													Precedent Chains
												</h3>
												<p className="text-muted-foreground mt-1">
													Upgrade to our enhanced plan to access precedent chain
													analysis.
												</p>
											</div>
											<Button>Upgrade Now</Button>
										</div>
									) : analysisData.precedentChains &&
									  analysisData.precedentChains.length > 0 ? (
										<div className="space-y-6">
											{analysisData.precedentChains.map((chain, index) => (
												<div key={index} className="space-y-2">
													<div className="flex justify-between items-center">
														<h3 className="text-sm font-medium">
															Precedent Chain #{index + 1}
														</h3>
													</div>

													{chain.chain && chain.chain.length > 0 ? (
														<div className="relative pl-6 border-l-2 border-muted space-y-4 py-2">
															{chain.chain.map((item, itemIndex) => (
																<div key={itemIndex} className="relative">
																	<div className="absolute -left-[22px] top-1/2 -translate-y-1/2 w-4 h-4 rounded-full bg-background border-2 border-muted" />
																	<Card className="p-3">
																		<div className="flex justify-between items-center">
																			<div>
																				<p className="font-medium text-sm">
																					Case ID: {item.citationId}
																				</p>
																				<p className="text-xs text-muted-foreground mt-1">
																					Relationship:{" "}
																					{item.relationshipToParent}
																				</p>
																			</div>
																			<Badge
																				variant="secondary"
																				className="text-xs"
																			>
																				{item.significance}
																			</Badge>
																		</div>
																	</Card>
																</div>
															))}
														</div>
													) : (
														<p className="text-sm text-muted-foreground">
															No chain data available
														</p>
													)}
												</div>
											))}
										</div>
									) : (
										<div className="text-center py-8">
											<p className="text-muted-foreground">
												No precedent chain data available
											</p>
										</div>
									)}
								</ScrollArea>
							</TabsContent>
						</div>
					</ScrollArea>
				</CardContent>
			</Tabs>
		</Card>
	);
}
