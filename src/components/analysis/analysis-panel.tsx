"use client"

import { useEffect, useState } from "react"
import { X, BarChart2 } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Content } from "@/components/ui/tabs"
import { AnalysisArtifact } from "./analysis-artifact"
import { AnalysisEvolution } from "./analysis-evolution"
import type { DocumentAnalysis } from "@/lib/types/analysis"
import type { AnalysisEvolution as AnalysisEvolutionType } from "@/lib/types/analysis-evolution"
import { cn } from "@/lib/utils"

interface AnalysisPanelProps {
  isOpen: boolean
  onClose: () => void
  analysis: DocumentAnalysis | null
  evolution?: AnalysisEvolutionType | null
  onRequestEvolution?: () => void
  isEvolutionLoading?: boolean
  documentId?: string
}

export function AnalysisPanel({
  isOpen,
  onClose,
  analysis,
  evolution,
  onRequestEvolution,
  isEvolutionLoading = false,
  documentId
}: AnalysisPanelProps) {
  const [animateIn, setAnimateIn] = useState(false)
  const [activeTab, setActiveTab] = useState<"analysis" | "evolution">("analysis")

  useEffect(() => {
    if (isOpen) {
      // Small delay to ensure the panel is in the DOM before animating
      const timer = setTimeout(() => setAnimateIn(true), 10)

      return () => {
        clearTimeout(timer)
      }
    } else {
      setAnimateIn(false)
    }
  }, [isOpen])

  // Load evolution data when switching to that tab
  useEffect(() => {
    if (activeTab === "evolution" && !evolution && onRequestEvolution) {
      onRequestEvolution()
    }
  }, [activeTab, evolution, onRequestEvolution])

  if (!isOpen) return null

  // Render different content based on whether evolution data is available
  const renderContent = () => {
    if (onRequestEvolution) {
      return (
        <Tabs 
          value={activeTab} 
          onValueChange={(value) => setActiveTab(value as "analysis" | "evolution")}
          className="flex flex-col h-full"
        >
          <div className="flex items-center justify-between p-4 border-b border-border dark:border-neutral-700">
            <TabsList className="w-full">
              <TabsTrigger value="analysis" className="flex-1">
                Document Analysis
              </TabsTrigger>
              <TabsTrigger value="evolution" className="flex-1">
                <BarChart2 className="h-4 w-4 mr-2" />
                Evolution
              </TabsTrigger>
            </TabsList>
            <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8 ml-2">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>
          
          <div className="overflow-y-auto h-[calc(100%-4rem)]">
            <TabsContent value="analysis" className="mt-0 h-full">
              {analysis ? (
                <div className="p-4">
                  <AnalysisArtifact
                    analysisId={analysis.analysisId}
                    result={analysis.result}
                    documentId={documentId}
                  />
                </div>
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p className="text-muted-foreground">No analysis data available</p>
                </div>
              )}
            </TabsContent>
            
            <TabsContent value="evolution" className="mt-0 h-full">
              {isEvolutionLoading ? (
                <div className="flex items-center justify-center h-64">
                  <div className="flex flex-col items-center">
                    <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mb-2"></div>
                    <p className="text-sm text-muted-foreground">Loading evolution data...</p>
                  </div>
                </div>
              ) : evolution ? (
                <div className="p-4">
                  <AnalysisEvolution evolution={evolution} />
                </div>
              ) : (
                <div className="flex items-center justify-center h-64">
                  <p className="text-muted-foreground">No evolution data available</p>
                </div>
              )}
            </TabsContent>
          </div>
        </Tabs>
      )
    } else {
      return (
        <>
          <div className="flex items-center justify-between p-4 border-b border-border dark:border-neutral-700">
            <h2 className="text-xl font-semibold">Document Analysis</h2>
            <Button variant="ghost" size="icon" onClick={onClose} className="h-8 w-8 ml-2">
              <X className="h-4 w-4" />
              <span className="sr-only">Close</span>
            </Button>
          </div>

          <div className="overflow-y-auto h-[calc(100%-4rem)]">
            {analysis ? (
              <div className="p-4">
                <AnalysisArtifact
                  analysisId={analysis.analysisId}
                  result={analysis.result}
                  documentId={documentId}
                />
              </div>
            ) : (
              <div className="flex items-center justify-center h-full">
                <p className="text-muted-foreground">No analysis data available</p>
              </div>
            )}
          </div>
        </>
      )
    }
  }

  return (
    <div className="fixed inset-0 z-50 bg-black/50 backdrop-blur-sm" onClick={onClose}>
      <div
        className={cn(
          "fixed top-0 right-0 h-full w-full md:w-[40%] bg-card dark:bg-[#1a1a1a] border-l border-border dark:border-neutral-700 shadow-xl overflow-hidden transition-transform duration-300 ease-in-out",
          animateIn ? "translate-x-0" : "translate-x-full",
        )}
        onClick={(e) => e.stopPropagation()}
      >
        {renderContent()}
      </div>
    </div>
  )
}
