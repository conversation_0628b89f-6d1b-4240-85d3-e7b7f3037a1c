"use client";

import React, { useState, useEffect, useCallback } from "react";
import { But<PERSON> } from "@/components/ui/button";
import { <PERSON><PERSON>, DialogContent, <PERSON><PERSON>Header, Di<PERSON>Title, DialogTrigger } from "@/components/ui/dialog";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Loader2, FileText, Check } from "lucide-react";
import { documentService } from "@/lib/services/document-service";
import { useToast } from "@/hooks/use-toast";

interface DocumentSectionSelectorProps {
  documentId: string;
  document?: {
    id: string;
    name: string;
  };
  onSectionSelect: (section: { title: string; content: string }) => void;
}

export function DocumentSectionSelector({
  documentId,
  document,
  onSectionSelect,
}: DocumentSectionSelectorProps) {
  const [isLoading, setIsLoading] = useState(false);
  const [documentContent, setDocumentContent] = useState<string | null>(null);
  const [selectedText, setSelectedText] = useState<string>("");
  const [sectionTitle, setSectionTitle] = useState<string>("");
  const [isOpen, setIsOpen] = useState(false);
  const [suggestedSections, setSuggestedSections] = useState<{title: string, content: string}[]>([]);
  const [error, setError] = useState<string | null>(null);
  const { toast } = useToast();

  // Define fetchDocumentContent with useCallback to use in useEffect
  const fetchDocumentContent = useCallback(async () => {
    if (!documentId) return;

    setIsLoading(true);
    setError(null);
    try {
      // Directly use documentService to fetch content
      const content = await documentService.getDocumentContent(documentId);
      setDocumentContent(content); // Assuming getDocumentContent returns plain text
    } catch (err: unknown) {
      let errorMessage = "Failed to load document content.";
      if (err instanceof Error) {
        errorMessage = err.message;
      }
      setError(errorMessage);
      toast({
        title: "Error",
        description: errorMessage,
        variant: "destructive",
      });
      console.error("Error fetching document content:", err);
    } finally {
      setIsLoading(false);
    }
  }, [documentId, toast]);

  // Fetch document content when dialog opens
  useEffect(() => {
    if (isOpen && documentId) {
      fetchDocumentContent();
    }
  }, [isOpen, documentId, fetchDocumentContent]);

  // Analyze document content to find potential sections
  useEffect(() => {
    if (documentContent) {
      // Find potential section titles in legal documents
      const lines = documentContent.split('\n');
      const potentialSections: {title: string, content: string, lineIndex: number}[] = [];
      
      lines.forEach((line, index) => {
        // Look for common section patterns in legal documents
        // 1. All caps lines are often headings
        const isAllCaps = line.trim() === line.trim().toUpperCase() && line.trim().length > 3;
        // 2. Lines starting with Roman numerals (I., II., etc.)
        const hasRomanNumeral = /^[IVX]+\.\s/.test(line.trim());
        // 3. Lines with "SECTION" or similar keywords
        const isSectionKeyword = /^(SECTION|ARTICLE|BACKGROUND|ANALYSIS|STANDARD|CONCLUSION)/i.test(line.trim());
        
        if ((isAllCaps || hasRomanNumeral || isSectionKeyword) && !line.trim().startsWith('*')) {
          // Find the content that follows this potential section title
          let content = '';
          let nextIndex = index + 1;
          
          // Collect content until the next potential section title or end of document
          while (nextIndex < lines.length) {
            const nextLine = lines[nextIndex];
            const isNextSectionTitle = 
              (nextLine.trim() === nextLine.trim().toUpperCase() && nextLine.trim().length > 3) ||
              /^[IVX]+\.\s/.test(nextLine.trim()) ||
              /^(SECTION|ARTICLE|BACKGROUND|ANALYSIS|STANDARD|CONCLUSION)/i.test(nextLine.trim());
            
            if (isNextSectionTitle && !nextLine.trim().startsWith('*')) {
              break;
            }
            
            content += nextLine + '\n';
            nextIndex++;
          }
          
          if (content.trim()) {
            potentialSections.push({
              title: line.trim(),
              content: content.trim(),
              lineIndex: index
            });
          }
        }
      });
      
      // Sort by line index and take the top 5 sections
      const topSections = potentialSections
        .sort((a, b) => a.lineIndex - b.lineIndex)
        .slice(0, 5)
        .map(({ title, content }) => ({ title, content }));
      
      setSuggestedSections(topSections);
    }
  }, [documentContent]);

  const handleTextSelection = () => {
    const selection = window.getSelection();
    if (selection && selection.toString()) {
      setSelectedText(selection.toString());
      
      // Try to guess a title from the selected text
      const lines = selection.toString().split('\n');
      const firstLine = lines[0].trim();
      
      // If the first line is short, it might be a title
      if (firstLine.length < 100) {
        setSectionTitle(firstLine);
      }
    }
  };

  const handleAddSection = () => {
    if (!selectedText) {
      toast({
        title: "Error",
        description: "Please select some text first",
        variant: "destructive",
      });
      return;
    }

    if (!sectionTitle) {
      toast({
        title: "Error",
        description: "Please provide a title for the section",
        variant: "destructive",
      });
      return;
    }

    onSectionSelect({
      title: sectionTitle,
      content: selectedText,
    });

    // Reset and close
    setSectionTitle("");
    setSelectedText("");
    setIsOpen(false);

    toast({
      title: "Section Added",
      description: "The section has been added to the comparison",
    });
  };

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="w-full text-xs sm:text-sm py-1 h-8 sm:h-9">
          <FileText className="h-3 w-3 sm:h-4 sm:w-4 mr-1 sm:mr-2" />
          Select from Document
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-[95vw] sm:max-w-4xl h-[90vh] sm:h-[80vh] flex flex-col p-3 sm:p-6">
        <DialogHeader>
          <DialogTitle className="text-base sm:text-lg">Select Section from {document?.name}</DialogTitle>
        </DialogHeader>
        
        {/* Main content area with side-by-side layout */}
        <div className="flex flex-col md:flex-row flex-1 gap-3 sm:gap-6 min-h-0 py-2 sm:py-4">
          
          {/* Left Side: Selection Form */}
          <div className="flex flex-col w-full md:w-2/5 space-y-3 sm:space-y-4">
            <div>
              <label className="text-sm font-medium block mb-1">Section Title</label>
              <Input
                placeholder="e.g., 'Confidentiality Clause'"
                value={sectionTitle}
                onChange={(e) => setSectionTitle(e.target.value)}
              />
            </div>
            
            <div className="flex-1 flex flex-col">
              <label className="text-sm font-medium block mb-1">Section Content</label>
              <Textarea
                placeholder="Select text from the document preview below."
                value={selectedText}
                onChange={(e) => setSelectedText(e.target.value)}
                className="min-h-[80px] sm:min-h-[100px] flex-1"
              />
            </div>
            
            {suggestedSections.length > 0 && (
              <div className="overflow-y-auto max-h-[150px] sm:max-h-[250px]">
                <label className="text-sm font-medium block mb-2 text-primary font-semibold">Suggested Sections</label>
                <div className="space-y-2 sm:space-y-3 pr-2">
                  {suggestedSections.map((section, index) => (
                    <div 
                      key={index} 
                      className="border rounded-md p-3 cursor-pointer hover:bg-accent transition-colors border-muted-foreground/20 shadow-sm"
                      onClick={() => {
                        setSectionTitle(section.title);
                        setSelectedText(section.content);
                      }}
                    >
                      <p className="font-medium text-sm mb-1 break-words text-primary">{section.title}</p>
                      <p className="text-xs text-muted-foreground line-clamp-3">{section.content}</p>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </div>
          
          {/* Right Side: Document Preview */}
          <div className="flex-1 w-full md:w-3/5 border rounded-md overflow-y-auto p-2 sm:p-4 mt-3 md:mt-0">
            {isLoading ? (
              <div className="flex items-center justify-center h-full">
                <Loader2 className="h-6 w-6 animate-spin" />
                <span className="ml-2">Loading document content...</span>
              </div>
            ) : documentContent ? (
              <div 
                className="flex-1 border rounded-md p-2 sm:p-4 overflow-y-auto max-h-[300px] sm:max-h-[500px]"
                onMouseUp={handleTextSelection}
                onTouchEnd={handleTextSelection}
              >
                <div className="whitespace-pre-wrap font-sans text-sm leading-relaxed">
                  {documentContent.split('\n').map((line, index) => (
                    <div 
                      key={index} 
                      className={`${
                        // Add styling for headings (all caps lines are often headings in legal docs)
                        line.trim() === line.trim().toUpperCase() && line.trim().length > 3
                          ? 'font-bold text-base my-2'
                          : ''
                      }`}
                    >
                      {line || '\u00A0'}
                    </div>
                  ))}
                </div>
              </div>
            ) : error ? (
              <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                <FileText className="h-12 w-12 mb-2" />
                <p>{error}</p>
              </div>
            ) : (
              <div className="flex flex-col items-center justify-center h-full text-muted-foreground">
                <FileText className="h-12 w-12 mb-2" />
                <p>No document content available</p>
              </div>
            )}
          </div>
        </div>
        
        {/* Footer Buttons */}
        <div className="flex justify-end space-x-2 mt-4">
          <Button variant="outline" onClick={() => setIsOpen(false)}>
            Cancel
          </Button>
          <Button 
            onClick={handleAddSection}
            disabled={!selectedText || !sectionTitle}
          >
            <Check className="h-4 w-4 mr-2" />
            Add Section
          </Button>
        </div>
      </DialogContent>
    </Dialog>
  );
}
