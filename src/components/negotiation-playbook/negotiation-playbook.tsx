"use client";

import React, { useState } from 'react';
import { Brain, RefreshCw, Download, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNegotiationPlaybook } from '@/hooks/use-negotiation-playbook';
import { useFeatureAccess } from '@/hooks/use-feature-access';
import { LoadingState } from './loading-state';
import { ErrorState } from './error-state';
import { PlaybookDisplay } from './playbook-display';
import { GeneratePlaybookModal } from './generate-playbook-modal';
import { FeatureGuard } from '@/components/subscription/feature-guard';
import { SampleNegotiationPlaybooksBrowser } from '@/components/sample-playbooks';
import type { GeneratePlaybookOptions } from '@/lib/types/negotiation-playbook';

interface NegotiationPlaybookProps {
  documentId: string;
}

export function NegotiationPlaybook({ documentId }: NegotiationPlaybookProps) {
  const [showGenerateModal, setShowGenerateModal] = useState(false);
  const { canAccessFeature } = useFeatureAccess();
  
  const {
    playbook,
    loading,
    error,
    hasExisting,
    generate,
    refresh,
    clearError
  } = useNegotiationPlaybook(documentId);

  const hasAccess = canAccessFeature('negotiation_playbook');

  const handleGenerate = async (options: GeneratePlaybookOptions) => {
    await generate(options);
    setShowGenerateModal(false);
  };

  const handleRetry = () => {
    clearError();
    refresh();
  };

  const downloadPlaybook = () => {
    if (!playbook) return;

    // Create a formatted text version of the playbook
    let content = `NEGOTIATION PLAYBOOK\n`;
    content += `Generated: ${new Date(playbook.createdAt).toLocaleString()}\n`;
    content += `Document ID: ${playbook.documentId}\n\n`;
    
    content += `EXECUTIVE SUMMARY\n`;
    content += `${playbook.executiveSummary}\n\n`;
    
    content += `KEY NEGOTIATION POINTS\n`;
    content += `${'='.repeat(50)}\n`;
    playbook.keyNegotiationPoints.forEach((point, index) => {
      content += `${index + 1}. ${point.title} (Priority: ${point.priority})\n`;
      content += `   Description: ${point.description}\n`;
      content += `   Current Position: ${point.currentPosition}\n`;
      content += `   Recommended Position: ${point.recommendedPosition}\n`;
      content += `   Rationale: ${point.rationale}\n\n`;
    });
    
    content += `STRATEGIC RECOMMENDATIONS\n`;
    content += `${'='.repeat(50)}\n`;
    playbook.strategicRecommendations.forEach((rec, index) => {
      content += `${index + 1}. [${rec.category}] ${rec.recommendation}\n`;
      content += `   Reasoning: ${rec.reasoning}\n\n`;
    });
    
    content += `RISK ASSESSMENT\n`;
    content += `${'='.repeat(50)}\n`;
    content += `Overall Risk Level: ${playbook.riskAssessment.overallRiskLevel}\n\n`;
    content += `Risk Factors:\n`;
    playbook.riskAssessment.riskFactors.forEach((factor, index) => {
      content += `${index + 1}. ${factor.factor} (Severity: ${factor.severity})\n`;
      content += `   Mitigation: ${factor.mitigation}\n\n`;
    });
    
    if (playbook.negotiationSimulations.length > 0) {
      content += `NEGOTIATION SIMULATIONS\n`;
      content += `${'='.repeat(50)}\n`;
      playbook.negotiationSimulations.forEach((sim, index) => {
        content += `${index + 1}. Scenario: ${sim.scenario}\n`;
        content += `   Response: ${sim.response}\n`;
        content += `   Expected Outcome: ${sim.expectedOutcome}\n\n`;
      });
    }

    // Create and download the file
    const blob = new Blob([content], { type: 'text/plain' });
    const url = URL.createObjectURL(blob);
    const link = document.createElement('a');
    link.href = url;
    link.download = `negotiation-playbook-${new Date().toISOString().slice(0, 10)}.txt`;
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
    URL.revokeObjectURL(url);
  };

  return (
    <FeatureGuard featureId="negotiation_playbook">
      <div className="space-y-6">
        {/* Header */}
        <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4">
          <div>
            <h2 className="text-2xl font-bold flex items-center gap-2">
              <Brain className="h-6 w-6" />
              Negotiation Playbook
            </h2>
            <p className="text-muted-foreground">
              AI-powered strategic recommendations for document negotiations
            </p>
          </div>
          
          <div className="flex items-center gap-2">
            {playbook && (
              <Button
                variant="outline"
                onClick={downloadPlaybook}
                className="gap-2"
              >
                <Download className="h-4 w-4" />
                Export
              </Button>
            )}
            
            <Button
              onClick={() => setShowGenerateModal(true)}
              disabled={loading || !hasAccess}
              className="gap-2"
            >
              {hasExisting ? (
                <>
                  <RefreshCw className="h-4 w-4" />
                  Regenerate
                </>
              ) : (
                <>
                  <Brain className="h-4 w-4" />
                  Generate Playbook
                </>
              )}
            </Button>
          </div>
        </div>

        {/* Content */}
        {loading ? (
          <LoadingState />
        ) : error ? (
          <ErrorState 
            error={error} 
            onRetry={handleRetry}
            showUpgrade={!hasAccess}
          />
        ) : playbook ? (
          <PlaybookDisplay playbook={playbook} />
        ) : (
          <>
            {/* Sample Playbooks Browser */}
            <SampleNegotiationPlaybooksBrowser
              documentId={documentId}
              onPlaybookCloned={() => {
                // Refresh the playbook when a sample is cloned
                refresh();
              }}
            />

            {/* Empty State */}
          <div className="text-center py-12">
            <div className="space-y-4">
              <div className="mx-auto w-16 h-16 bg-muted rounded-full flex items-center justify-center">
                <Brain className="h-8 w-8 text-muted-foreground" />
              </div>
              <div className="space-y-2">
                <h3 className="text-lg font-medium">No Negotiation Playbook</h3>
                <p className="text-sm text-muted-foreground max-w-md mx-auto">
                  Generate an AI-powered negotiation playbook to get strategic recommendations,
                  risk assessments, and tactical advice for this document.
                </p>
              </div>
              <Button
                onClick={() => setShowGenerateModal(true)}
                disabled={!hasAccess}
                className="gap-2"
              >
                <Brain className="h-4 w-4" />
                Generate Playbook
              </Button>
            </div>
          </div>
          </>
        )}

        {/* Generate Modal */}
        <GeneratePlaybookModal
          isOpen={showGenerateModal}
          onClose={() => setShowGenerateModal(false)}
          onGenerate={handleGenerate}
          loading={loading}
        />
      </div>
    </FeatureGuard>
  );
}
