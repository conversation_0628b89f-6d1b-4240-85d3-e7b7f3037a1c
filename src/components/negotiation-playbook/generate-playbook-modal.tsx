"use client";

import React, { useState } from 'react';
import { Brain, Plus, X } from 'lucide-react';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog';
import { But<PERSON> } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { Checkbox } from '@/components/ui/checkbox';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Badge } from '@/components/ui/badge';
import type {
  GeneratePlaybookOptions,
  DocumentType
} from '@/lib/types/negotiation-playbook';
import { DOCUMENT_TYPE_OPTIONS, COMMON_FOCUS_AREAS } from '@/lib/types/negotiation-playbook';

interface GeneratePlaybookModalProps {
  isOpen: boolean;
  onClose: () => void;
  onGenerate: (options: GeneratePlaybookOptions) => Promise<void>;
  loading?: boolean;
}

export function GeneratePlaybookModal({
  isOpen,
  onClose,
  onGenerate,
  loading = false
}: GeneratePlaybookModalProps) {
  const [documentType, setDocumentType] = useState<DocumentType>('CONTRACT');
  const [focusAreas, setFocusAreas] = useState<string[]>([]);
  const [customFocusArea, setCustomFocusArea] = useState('');
  const [includeSimulations, setIncludeSimulations] = useState(true);
  const [organizationPreferences, setOrganizationPreferences] = useState('');

  const handleSubmit = async () => {
    const options: GeneratePlaybookOptions = {
      documentType,
      focusAreas: focusAreas.length > 0 ? focusAreas : undefined,
      includeSimulations,
      organizationPreferences: organizationPreferences.trim() || undefined,
    };

    await onGenerate(options);
  };

  const addFocusArea = (area: string) => {
    if (area && !focusAreas.includes(area)) {
      setFocusAreas([...focusAreas, area]);
    }
  };

  const removeFocusArea = (area: string) => {
    setFocusAreas(focusAreas.filter(a => a !== area));
  };

  const addCustomFocusArea = () => {
    if (customFocusArea.trim()) {
      addFocusArea(customFocusArea.trim());
      setCustomFocusArea('');
    }
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && customFocusArea.trim()) {
      e.preventDefault();
      addCustomFocusArea();
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="sm:max-w-[600px] max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5" />
            Generate Negotiation Playbook
          </DialogTitle>
          <DialogDescription>
            Configure the AI analysis to generate strategic negotiation recommendations
            tailored to your document and preferences.
          </DialogDescription>
        </DialogHeader>

        <div className="space-y-6 py-4">
          {/* Document Type */}
          <div className="space-y-2">
            <Label htmlFor="document-type">Document Type</Label>
            <Select value={documentType} onValueChange={(value: DocumentType) => setDocumentType(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select document type" />
              </SelectTrigger>
              <SelectContent>
                {DOCUMENT_TYPE_OPTIONS.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Focus Areas */}
          <div className="space-y-3">
            <Label>Focus Areas (Optional)</Label>
            <p className="text-sm text-muted-foreground">
              Select specific areas to focus on during analysis. Leave empty for comprehensive analysis.
            </p>
            
            {/* Common focus areas */}
            <div className="flex flex-wrap gap-2">
              {COMMON_FOCUS_AREAS.map((area) => (
                <Button
                  key={area}
                  type="button"
                  variant={focusAreas.includes(area) ? "default" : "outline"}
                  size="sm"
                  onClick={() => 
                    focusAreas.includes(area) 
                      ? removeFocusArea(area)
                      : addFocusArea(area)
                  }
                  className="text-xs"
                >
                  {area.replace('_', ' ')}
                </Button>
              ))}
            </div>

            {/* Selected focus areas */}
            {focusAreas.length > 0 && (
              <div className="space-y-2">
                <Label className="text-sm">Selected Focus Areas:</Label>
                <div className="flex flex-wrap gap-2">
                  {focusAreas.map((area) => (
                    <Badge key={area} variant="secondary" className="gap-1">
                      {area.replace('_', ' ')}
                      <button
                        type="button"
                        onClick={() => removeFocusArea(area)}
                        className="ml-1 hover:text-destructive"
                      >
                        <X className="h-3 w-3" />
                      </button>
                    </Badge>
                  ))}
                </div>
              </div>
            )}

            {/* Custom focus area input */}
            <div className="flex gap-2">
              <Input
                placeholder="Add custom focus area"
                value={customFocusArea}
                onChange={(e) => setCustomFocusArea(e.target.value)}
                onKeyDown={handleKeyDown}
                className="flex-1"
              />
              <Button
                type="button"
                variant="outline"
                size="sm"
                onClick={addCustomFocusArea}
                disabled={!customFocusArea.trim()}
              >
                <Plus className="h-4 w-4" />
              </Button>
            </div>
          </div>

          {/* Include Simulations */}
          <div className="flex items-center space-x-2">
            <Checkbox
              id="include-simulations"
              checked={includeSimulations}
              onCheckedChange={(checked) => setIncludeSimulations(checked === true)}
            />
            <Label htmlFor="include-simulations" className="text-sm">
              Include negotiation simulations and scenario planning
            </Label>
          </div>

          {/* Organization Preferences */}
          <div className="space-y-2">
            <Label htmlFor="preferences">Organization Preferences (Optional)</Label>
            <Textarea
              id="preferences"
              placeholder="e.g., Prefer shorter contract terms, lower liability exposure, specific negotiation style..."
              value={organizationPreferences}
              onChange={(e) => setOrganizationPreferences(e.target.value)}
              className="min-h-[80px]"
            />
          </div>
        </div>

        <DialogFooter>
          <Button variant="outline" onClick={onClose} disabled={loading}>
            Cancel
          </Button>
          <Button onClick={handleSubmit} disabled={loading}>
            {loading ? (
              <>
                <Brain className="mr-2 h-4 w-4 animate-pulse" />
                Generating...
              </>
            ) : (
              <>
                <Brain className="mr-2 h-4 w-4" />
                Generate Playbook
              </>
            )}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
