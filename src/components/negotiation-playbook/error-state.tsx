"use client";

import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON>fresh<PERSON><PERSON>, ArrowUpCircle } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Alert, AlertDescription } from '@/components/ui/alert';
import Link from 'next/link';

interface ErrorStateProps {
  error: string;
  onRetry?: () => void;
  showUpgrade?: boolean;
}

export function ErrorState({ error, onRetry, showUpgrade }: ErrorStateProps) {
  const isSubscriptionError = error.includes('subscription') || error.includes('PRO');

  return (
    <Card className="w-full">
      <CardContent className="py-8">
        <Alert variant="destructive" className="mb-6">
          <AlertTriangle className="h-4 w-4" />
          <AlertDescription>{error}</AlertDescription>
        </Alert>

        <div className="flex flex-col items-center space-y-4">
          {isSubscriptionError || showUpgrade ? (
            <div className="text-center space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Upgrade Required</h3>
                <p className="text-sm text-muted-foreground">
                  The Negotiation Playbook feature is available with a PRO subscription.
                </p>
              </div>
              <Link href="/subscription/upgrade">
                <Button className="gap-2">
                  <ArrowUpCircle className="h-4 w-4" />
                  Upgrade to PRO
                </Button>
              </Link>
            </div>
          ) : (
            <div className="text-center space-y-4">
              <div className="space-y-2">
                <h3 className="text-lg font-medium">Something went wrong</h3>
                <p className="text-sm text-muted-foreground">
                  We couldn't generate your negotiation playbook. Please try again.
                </p>
              </div>
              {onRetry && (
                <Button onClick={onRetry} variant="outline" className="gap-2">
                  <RefreshCw className="h-4 w-4" />
                  Try Again
                </Button>
              )}
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  );
}
