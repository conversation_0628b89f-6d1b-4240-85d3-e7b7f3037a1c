"use client";

import React, { useState } from 'react';
import { Play, Settings, Loader2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Checkbox } from '@/components/ui/checkbox';
import { Input } from '@/components/ui/input';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { negotiationSimulatorService } from '@/lib/services/negotiation-simulator-service';
import { useFeatureAccess } from '@/hooks/use-feature-access';
import type { 
  NegotiationPlaybook, 
  CreateScenarioFromPlaybookRequest,
  Difficulty 
} from '@/lib/types/negotiation-simulator';

interface PracticeScenarioButtonProps {
  playbook: NegotiationPlaybook;
  className?: string;
}

export function PracticeScenarioButton({ playbook, className }: PracticeScenarioButtonProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [difficulty, setDifficulty] = useState<Difficulty>('INTERMEDIATE');
  const [focusAreas, setFocusAreas] = useState<string[]>([]);
  const [maxRounds, setMaxRounds] = useState(10);
  const [timeLimit, setTimeLimit] = useState(30);
  
  const { toast } = useToast();
  const router = useRouter();
  const { canAccessFeature } = useFeatureAccess();

  const hasSimulatorAccess = canAccessFeature('negotiation_simulator');

  // Extract potential focus areas from playbook
  const availableFocusAreas = [
    ...playbook.keyNegotiationPoints.map(point => point.title),
    ...playbook.strategicRecommendations.map(rec => rec.category.toLowerCase()),
    'risk_mitigation',
    'strategic_positioning',
    'concession_planning'
  ].slice(0, 8); // Limit to 8 options

  const handleFocusAreaChange = (area: string, checked: boolean) => {
    if (checked) {
      setFocusAreas(prev => [...prev, area]);
    } else {
      setFocusAreas(prev => prev.filter(a => a !== area));
    }
  };

  const handleCreateScenario = async () => {
    if (!hasSimulatorAccess) {
      toast({
        title: "Subscription Required",
        description: "Negotiation simulator feature requires PRO subscription",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const request: CreateScenarioFromPlaybookRequest = {
        difficulty,
        focusAreas: focusAreas.length > 0 ? focusAreas : undefined,
        customizations: {
          maxRounds,
          timeLimit,
        }
      };

      const scenario = await negotiationSimulatorService.createScenarioFromPlaybook(
        playbook.documentId,
        request
      );

      toast({
        title: "Practice Scenario Created",
        description: `"${scenario.name}" is ready for practice!`,
      });

      setIsOpen(false);
      
      // Navigate to the negotiation simulator dashboard
      router.push('/negotiation-simulator');
      
    } catch (error) {
      console.error('Failed to create practice scenario:', error);
      toast({
        title: "Creation Failed",
        description: "Failed to create practice scenario. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  if (!hasSimulatorAccess) {
    return (
      <Button variant="outline" className={className} disabled>
        <Play className="h-4 w-4 mr-2" />
        Practice This Scenario
        <Badge variant="secondary" className="ml-2">PRO</Badge>
      </Button>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className={className}>
          <Play className="h-4 w-4 mr-2" />
          Practice This Scenario
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create Practice Scenario</DialogTitle>
          <DialogDescription>
            Generate an interactive negotiation scenario based on this playbook analysis.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Difficulty Selection */}
          <div className="space-y-2">
            <Label htmlFor="difficulty">Difficulty Level</Label>
            <Select value={difficulty} onValueChange={(value: Difficulty) => setDifficulty(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select difficulty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="BEGINNER">
                  <div>
                    <div className="font-medium">Beginner</div>
                    <div className="text-sm text-muted-foreground">Cooperative AI, clear objectives</div>
                  </div>
                </SelectItem>
                <SelectItem value="INTERMEDIATE">
                  <div>
                    <div className="font-medium">Intermediate</div>
                    <div className="text-sm text-muted-foreground">Moderate complexity, some conflicts</div>
                  </div>
                </SelectItem>
                <SelectItem value="EXPERT">
                  <div>
                    <div className="font-medium">Expert</div>
                    <div className="text-sm text-muted-foreground">Aggressive AI, tight constraints</div>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Focus Areas */}
          <div className="space-y-2">
            <Label>Focus Areas (Optional)</Label>
            <div className="grid grid-cols-2 gap-2">
              {availableFocusAreas.map((area) => (
                <div key={area} className="flex items-center space-x-2">
                  <Checkbox
                    id={area}
                    checked={focusAreas.includes(area)}
                    onCheckedChange={(checked) => handleFocusAreaChange(area, checked as boolean)}
                  />
                  <Label
                    htmlFor={area}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {area.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase())}
                  </Label>
                </div>
              ))}
            </div>
          </div>

          {/* Customizations */}
          <div className="grid grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="maxRounds">Max Rounds</Label>
              <Input
                id="maxRounds"
                type="number"
                min="5"
                max="20"
                value={maxRounds}
                onChange={(e) => setMaxRounds(parseInt(e.target.value) || 10)}
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="timeLimit">Time Limit (minutes)</Label>
              <Input
                id="timeLimit"
                type="number"
                min="15"
                max="90"
                value={timeLimit}
                onChange={(e) => setTimeLimit(parseInt(e.target.value) || 30)}
              />
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateScenario} disabled={isCreating}>
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Settings className="h-4 w-4 mr-2" />
                  Create Scenario
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
