"use client";

import React, { useEffect } from 'react';
import { Play, Clock, Target, Users, ArrowRight } from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Skeleton } from '@/components/ui/skeleton';
import { useRouter } from 'next/navigation';
import { useNegotiationIntegration } from '@/hooks/use-negotiation-integration';
import type { NegotiationPlaybook } from '@/lib/types/negotiation-playbook';

interface RelatedScenariosProps {
  playbook: NegotiationPlaybook;
}

export function RelatedScenarios({ playbook }: RelatedScenariosProps) {
  const router = useRouter();
  const {
    relatedScenarios: rawRelatedScenarios,
    loading,
    error,
    hasAccess,
    getScenariosFromPlaybook,
    clearError
  } = useNegotiationIntegration();

  // Ensure relatedScenarios is always an array
  const relatedScenarios = Array.isArray(rawRelatedScenarios) ? rawRelatedScenarios : [];

  useEffect(() => {
    if (hasAccess && playbook.id) {
      getScenariosFromPlaybook(playbook.id, { limit: 3 });
    }
  }, [playbook.id, hasAccess, getScenariosFromPlaybook]);

  const getDifficultyColor = (difficulty: string) => {
    switch (difficulty.toLowerCase()) {
      case 'beginner': return 'bg-green-100 text-green-800 border-green-200';
      case 'intermediate': return 'bg-yellow-100 text-yellow-800 border-yellow-200';
      case 'expert': return 'bg-red-100 text-red-800 border-red-200';
      default: return 'bg-gray-100 text-gray-800 border-gray-200';
    }
  };

  const handleScenarioClick = (scenarioId: string) => {
    router.push(`/negotiation-simulator/scenarios/${scenarioId}`);
  };

  const handleViewAll = () => {
    router.push(`/negotiation-simulator?playbook=${playbook.id}`);
  };

  if (!hasAccess) {
    return null;
  }

  if (loading) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Related Practice Scenarios
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          {[1, 2, 3].map((i) => (
            <div key={i} className="p-4 border rounded-lg space-y-2">
              <Skeleton className="h-4 w-3/4" />
              <Skeleton className="h-3 w-full" />
              <div className="flex gap-2">
                <Skeleton className="h-6 w-16" />
                <Skeleton className="h-6 w-20" />
              </div>
            </div>
          ))}
        </CardContent>
      </Card>
    );
  }

  if (error) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Related Practice Scenarios
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <p className="text-sm text-muted-foreground mb-4">
              Failed to load related scenarios
            </p>
            <Button variant="outline" onClick={clearError}>
              Try Again
            </Button>
          </div>
        </CardContent>
      </Card>
    );
  }

  if (relatedScenarios.length === 0) {
    return (
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Related Practice Scenarios
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="text-center py-6">
            <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-sm text-muted-foreground mb-4">
              No practice scenarios created yet for this playbook
            </p>
            <p className="text-xs text-muted-foreground">
              Use the "Practice This Scenario" button above to create your first practice scenario
            </p>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Related Practice Scenarios
          </div>
          {relatedScenarios.length > 0 && (
            <Button variant="ghost" size="sm" onClick={handleViewAll}>
              View All
              <ArrowRight className="h-4 w-4 ml-1" />
            </Button>
          )}
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {relatedScenarios.map((scenario) => (
          <div
            key={scenario.id}
            className="p-4 border rounded-lg hover:bg-accent/50 transition-colors cursor-pointer"
            onClick={() => handleScenarioClick(scenario.id)}
          >
            <div className="flex items-start justify-between mb-2">
              <h4 className="font-medium text-sm line-clamp-1">{scenario.name}</h4>
              <Button size="sm" variant="ghost" className="h-8 w-8 p-0">
                <Play className="h-4 w-4" />
              </Button>
            </div>
            
            <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
              {scenario.description}
            </p>
            
            <div className="flex items-center gap-2 flex-wrap">
              <Badge className={getDifficultyColor(scenario.difficulty)}>
                {scenario.difficulty}
              </Badge>
              
              <Badge variant="outline" className="text-xs">
                {scenario.industry}
              </Badge>
              
              {scenario.constraints?.maxRounds && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Clock className="h-3 w-3" />
                  {scenario.constraints.maxRounds} rounds
                </div>
              )}
              
              {scenario.parties && scenario.parties.length > 0 && (
                <div className="flex items-center gap-1 text-xs text-muted-foreground">
                  <Users className="h-3 w-3" />
                  {scenario.parties.length} parties
                </div>
              )}
            </div>
            
            {scenario.tags && scenario.tags.length > 0 && (
              <div className="flex gap-1 mt-2 flex-wrap">
                {scenario.tags.slice(0, 3).map((tag) => (
                  <Badge key={tag} variant="secondary" className="text-xs">
                    {tag}
                  </Badge>
                ))}
                {scenario.tags.length > 3 && (
                  <Badge variant="secondary" className="text-xs">
                    +{scenario.tags.length - 3} more
                  </Badge>
                )}
              </div>
            )}
          </div>
        ))}
        
        {relatedScenarios.length === 0 && (
          <div className="text-center py-6">
            <Target className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-sm text-muted-foreground mb-4">
              No practice scenarios found for this playbook
            </p>
            <p className="text-xs text-muted-foreground">
              Create your first scenario using the button above
            </p>
          </div>
        )}
      </CardContent>
    </Card>
  );
}
