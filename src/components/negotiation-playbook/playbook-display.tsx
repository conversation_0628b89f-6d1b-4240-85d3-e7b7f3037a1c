"use client";

import React from "react";
import {
	FileText,
	Target,
	TrendingUp,
	AlertTriangle,
	Users,
	Clock,
	ChevronRight,
} from "lucide-react";
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import {
	Collapsible,
	CollapsibleContent,
	CollapsibleTrigger,
} from "@/components/ui/collapsible";
import { PracticeScenarioButton } from "./practice-scenario-button";
import { RelatedScenarios } from "./related-scenarios";
import type {
	NegotiationPlaybook,
	Priority,
	RiskLevel,
	StrategicCategory,
} from "@/lib/types/negotiation-playbook";

interface PlaybookDisplayProps {
	playbook: NegotiationPlaybook;
}

export function PlaybookDisplay({ playbook }: PlaybookDisplayProps) {
	const getPriorityColor = (priority: Priority) => {
		switch (priority) {
			case "CRITICAL":
				return "bg-red-100 text-red-800 border-red-200";
			case "HIGH":
				return "bg-orange-100 text-orange-800 border-orange-200";
			case "MEDIUM":
				return "bg-yellow-100 text-yellow-800 border-yellow-200";
			case "LOW":
				return "bg-green-100 text-green-800 border-green-200";
			default:
				return "bg-gray-100 text-gray-800 border-gray-200";
		}
	};

	const getRiskColor = (risk: RiskLevel) => {
		switch (risk) {
			case "CRITICAL":
				return "bg-red-100 text-red-800 border-red-200";
			case "HIGH":
				return "bg-orange-100 text-orange-800 border-orange-200";
			case "MEDIUM":
				return "bg-yellow-100 text-yellow-800 border-yellow-200";
			case "LOW":
				return "bg-green-100 text-green-800 border-green-200";
			default:
				return "bg-gray-100 text-gray-800 border-gray-200";
		}
	};

	const getCategoryIcon = (category: StrategicCategory) => {
		switch (category) {
			case "TIMING":
				return <Clock className="h-4 w-4" />;
			case "APPROACH":
				return <Target className="h-4 w-4" />;
			case "LEVERAGE":
				return <TrendingUp className="h-4 w-4" />;
			case "FALLBACK":
				return <ChevronRight className="h-4 w-4" />;
			case "PREPARATION":
				return <FileText className="h-4 w-4" />;
			default:
				return <Target className="h-4 w-4" />;
		}
	};

	return (
		<div className="space-y-6">
			{/* Executive Summary */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center justify-between">
						<div className="flex items-center gap-2">
							<FileText className="h-5 w-5" />
							Executive Summary
						</div>
						<PracticeScenarioButton playbook={playbook} />
					</CardTitle>
				</CardHeader>
				<CardContent>
					<p className="text-sm leading-relaxed">{playbook.executiveSummary}</p>
				</CardContent>
			</Card>

			{/* Key Negotiation Points */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<Target className="h-5 w-5" />
						Key Negotiation Points
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{playbook.keyNegotiationPoints.map((point, index) => (
						<Collapsible key={index}>
							<CollapsibleTrigger className="w-full">
								<div className="flex items-start justify-between p-4 border rounded-lg hover:bg-accent/50 transition-colors">
									<div className="flex-1 text-left">
										<div className="flex items-center gap-2 mb-2">
											<h4 className="font-medium">{point.title}</h4>
											<Badge className={getPriorityColor(point.priority)}>
												{point.priority}
											</Badge>
										</div>
										<p className="text-sm text-muted-foreground line-clamp-2">
											{point.description}
										</p>
									</div>
									<ChevronRight className="h-4 w-4 text-muted-foreground ml-2 flex-shrink-0" />
								</div>
							</CollapsibleTrigger>
							<CollapsibleContent>
								<div className="px-4 pb-4 space-y-3">
									<Separator />
									<div className="grid gap-3 sm:grid-cols-2">
										<div>
											<h5 className="text-sm font-medium mb-1">
												Current Position
											</h5>
											<p className="text-sm text-muted-foreground">
												{point.currentPosition}
											</p>
										</div>
										<div>
											<h5 className="text-sm font-medium mb-1">
												Recommended Position
											</h5>
											<p className="text-sm text-muted-foreground">
												{point.recommendedPosition}
											</p>
										</div>
									</div>
									<div>
										<h5 className="text-sm font-medium mb-1">Rationale</h5>
										<p className="text-sm text-muted-foreground">
											{point.rationale}
										</p>
									</div>
								</div>
							</CollapsibleContent>
						</Collapsible>
					))}
				</CardContent>
			</Card>

			{/* Strategic Recommendations */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<TrendingUp className="h-5 w-5" />
						Strategic Recommendations
					</CardTitle>
				</CardHeader>
				<CardContent className="space-y-4">
					{playbook.strategicRecommendations.map((rec, index) => (
						<div key={index} className="flex gap-3 p-3 border rounded-lg">
							<div className="flex-shrink-0 mt-0.5">
								{getCategoryIcon(rec.category)}
							</div>
							<div className="flex-1">
								<div className="flex items-center gap-2 mb-1">
									<Badge variant="outline" className="text-xs">
										{rec.category}
									</Badge>
								</div>
								<h4 className="font-medium text-sm mb-1">
									{rec.recommendation}
								</h4>
								<p className="text-sm text-muted-foreground">{rec.reasoning}</p>
							</div>
						</div>
					))}
				</CardContent>
			</Card>

			{/* Risk Assessment */}
			<Card>
				<CardHeader>
					<CardTitle className="flex items-center gap-2">
						<AlertTriangle className="h-5 w-5" />
						Risk Assessment
					</CardTitle>
				</CardHeader>
				<CardContent>
					<div className="space-y-4">
						<div className="flex items-center gap-2">
							<span className="text-sm font-medium">Overall Risk Level:</span>
							<Badge
								className={getRiskColor(
									playbook.riskAssessment.overallRiskLevel
								)}
							>
								{playbook.riskAssessment.overallRiskLevel}
							</Badge>
						</div>

						<div className="space-y-3">
							<h4 className="text-sm font-medium">Risk Factors</h4>
							{playbook.riskAssessment.riskFactors.map((factor, index) => (
								<div key={index} className="flex gap-3 p-3 border rounded-lg">
									<div className="flex-1">
										<div className="flex items-center gap-2 mb-1">
											<span className="font-medium text-sm">
												{factor.factor}
											</span>
											<Badge className={getRiskColor(factor.severity)}>
												{factor.severity}
											</Badge>
										</div>
										<p className="text-sm text-muted-foreground">
											<strong>Mitigation:</strong> {factor.mitigation}
										</p>
									</div>
								</div>
							))}
						</div>
					</div>
				</CardContent>
			</Card>

			{/* Negotiation Simulations */}
			{playbook.negotiationSimulations.length > 0 && (
				<Card>
					<CardHeader>
						<CardTitle className="flex items-center gap-2">
							<Users className="h-5 w-5" />
							Negotiation Simulations
						</CardTitle>
					</CardHeader>
					<CardContent className="space-y-4">
						{playbook.negotiationSimulations.map((sim, index) => (
							<div key={index} className="p-4 border rounded-lg space-y-3">
								<div>
									<h4 className="font-medium text-sm mb-1">Scenario</h4>
									<p className="text-sm text-muted-foreground">
										{sim.scenario}
									</p>
								</div>
								<div>
									<h4 className="font-medium text-sm mb-1">
										Recommended Response
									</h4>
									<p className="text-sm text-muted-foreground">
										{sim.response}
									</p>
								</div>
								<div>
									<h4 className="font-medium text-sm mb-1">Expected Outcome</h4>
									<p className="text-sm text-muted-foreground">
										{sim.expectedOutcome}
									</p>
								</div>
							</div>
						))}
					</CardContent>
				</Card>
			)}

			{/* Related Practice Scenarios */}
			<RelatedScenarios playbook={playbook} />

			{/* Metadata */}
			<Card>
				<CardContent className="pt-6">
					<div className="flex justify-between text-xs text-muted-foreground">
						<span>
							Created: {new Date(playbook.createdAt).toLocaleString()}
						</span>
						<span>
							Updated: {new Date(playbook.updatedAt).toLocaleString()}
						</span>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
