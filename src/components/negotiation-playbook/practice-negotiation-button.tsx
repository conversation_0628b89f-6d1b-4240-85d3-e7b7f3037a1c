"use client";

import React, { useState } from 'react';
import { motion } from 'framer-motion';
import { 
  MessageSquare, 
  Play, 
  Settings, 
  AlertTriangle,
  CheckCircle,
  Clock,
  Target,
  Zap,
  Users,
  ArrowRight
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Slider } from '@/components/ui/slider';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { chatNegotiationService } from '@/lib/services/chat-negotiation-service';
import type { DocumentNegotiationScenario } from '@/lib/services/chat-negotiation-service';
import { useRouter } from 'next/navigation';
import { useToast } from '@/hooks/use-toast';

interface PracticeNegotiationButtonProps {
  analysisId: string;
  documentTitle: string;
  analysisScore: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  totalDeviations: number;
  disabled?: boolean;
}

export function PracticeNegotiationButton({
  analysisId,
  documentTitle,
  analysisScore,
  riskLevel,
  totalDeviations,
  disabled = false
}: PracticeNegotiationButtonProps) {
  const [showSetup, setShowSetup] = useState(false);
  const [loading, setLoading] = useState(false);
  const [scenario, setScenario] = useState<DocumentNegotiationScenario | null>(null);
  const [aiPersonality, setAiPersonality] = useState({
    aggressiveness: 0.5,
    flexibility: 0.5,
    riskTolerance: 0.5,
    communicationStyle: 'DIPLOMATIC' as const
  });

  const router = useRouter();
  const { toast } = useToast();

  const getRiskColor = (risk: string) => {
    switch (risk) {
      case 'CRITICAL': return 'bg-red-100 text-red-800 dark:bg-red-900/30 dark:text-red-300';
      case 'HIGH': return 'bg-orange-100 text-orange-800 dark:bg-orange-900/30 dark:text-orange-300';
      case 'MEDIUM': return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900/30 dark:text-yellow-300';
      case 'LOW': return 'bg-green-100 text-green-800 dark:bg-green-900/30 dark:text-green-300';
      default: return 'bg-gray-100 text-gray-800 dark:bg-gray-900/30 dark:text-gray-300';
    }
  };

  const getScoreColor = (score: number) => {
    if (score >= 80) return 'text-green-600';
    if (score >= 60) return 'text-yellow-600';
    return 'text-red-600';
  };

  const handleGenerateScenario = async () => {
    try {
      setLoading(true);
      const generatedScenario = await chatNegotiationService.createScenarioFromAnalysis(analysisId);
      setScenario(generatedScenario);
      
      // Set recommended AI personality
      setAiPersonality({
        aggressiveness: generatedScenario.aiPersonalityRecommendation.aggressiveness,
        flexibility: generatedScenario.aiPersonalityRecommendation.flexibility,
        riskTolerance: generatedScenario.aiPersonalityRecommendation.riskTolerance,
        communicationStyle: generatedScenario.aiPersonalityRecommendation.communicationStyle
      });

      toast({
        title: "Scenario Generated",
        description: `Created negotiation practice scenario with ${generatedScenario.negotiationPoints.length} key issues to discuss.`
      });
    } catch (error) {
      console.error('Failed to generate scenario:', error);
      toast({
        title: "Generation Failed",
        description: "Could not create negotiation scenario. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  const handleStartNegotiation = async () => {
    if (!scenario) return;

    try {
      setLoading(true);
      const session = await chatNegotiationService.startChatNegotiationFromDocument(
        analysisId,
        aiPersonality
      );

      toast({
        title: "Negotiation Started",
        description: "Your practice session has begun. Good luck!"
      });

      // Navigate to the chat negotiation interface
      router.push(`/negotiation-simulator/sessions/${session.id}?mode=chat&source=document`);
    } catch (error) {
      console.error('Failed to start negotiation:', error);
      toast({
        title: "Start Failed",
        description: "Could not start negotiation session. Please try again.",
        variant: "destructive"
      });
    } finally {
      setLoading(false);
    }
  };

  return (
    <>
      <Button
        onClick={() => setShowSetup(true)}
        disabled={disabled || totalDeviations === 0}
        className="gap-2"
        size="lg"
      >
        <MessageSquare className="h-4 w-4" />
        Practice This Negotiation
      </Button>

      <Dialog open={showSetup} onOpenChange={setShowSetup}>
        <DialogContent className="max-w-2xl max-h-[80vh] overflow-y-auto">
          <DialogHeader>
            <DialogTitle className="flex items-center gap-2">
              <MessageSquare className="h-5 w-5" />
              Practice Contract Negotiation
            </DialogTitle>
          </DialogHeader>

          <div className="space-y-6">
            {/* Document Overview */}
            <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20">
              <CardContent className="p-4">
                <div className="space-y-3">
                  <div className="flex items-center justify-between">
                    <h3 className="font-semibold">{documentTitle}</h3>
                    <Badge className={getRiskColor(riskLevel)}>
                      {riskLevel} Risk
                    </Badge>
                  </div>
                  
                  <div className="grid grid-cols-3 gap-4 text-sm">
                    <div className="text-center">
                      <div className={`text-lg font-bold ${getScoreColor(analysisScore)}`}>
                        {analysisScore}/100
                      </div>
                      <div className="text-muted-foreground">Analysis Score</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-orange-600">
                        {totalDeviations}
                      </div>
                      <div className="text-muted-foreground">Issues Found</div>
                    </div>
                    <div className="text-center">
                      <div className="text-lg font-bold text-purple-600">
                        {scenario?.estimatedDuration || '15-20'}min
                      </div>
                      <div className="text-muted-foreground">Est. Duration</div>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>

            {!scenario ? (
              /* Generate Scenario Step */
              <Card>
                <CardHeader>
                  <CardTitle className="flex items-center gap-2">
                    <Target className="h-5 w-5" />
                    Step 1: Generate Practice Scenario
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <p className="text-muted-foreground">
                    We'll analyze your contract issues and create a tailored negotiation practice scenario 
                    with an AI opponent that understands the specific problems in your document.
                  </p>
                  
                  <div className="flex items-center gap-2 p-3 bg-yellow-50 dark:bg-yellow-950/20 rounded-lg">
                    <Zap className="h-4 w-4 text-yellow-600" />
                    <span className="text-sm text-yellow-800 dark:text-yellow-200">
                      This will consume 3 credits to generate the scenario
                    </span>
                  </div>

                  <Button 
                    onClick={handleGenerateScenario}
                    disabled={loading}
                    className="w-full gap-2"
                  >
                    {loading ? (
                      <>
                        <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                        Generating Scenario...
                      </>
                    ) : (
                      <>
                        <Play className="h-4 w-4" />
                        Generate Practice Scenario
                      </>
                    )}
                  </Button>
                </CardContent>
              </Card>
            ) : (
              /* Scenario Generated - Configure AI */
              <div className="space-y-4">
                <Card className="border-green-200 bg-green-50 dark:bg-green-950/20">
                  <CardContent className="p-4">
                    <div className="flex items-center gap-2 mb-2">
                      <CheckCircle className="h-4 w-4 text-green-600" />
                      <span className="font-medium text-green-800 dark:text-green-200">
                        Scenario Generated Successfully
                      </span>
                    </div>
                    <p className="text-sm text-green-700 dark:text-green-300">
                      {scenario.description}
                    </p>
                  </CardContent>
                </Card>

                {/* Negotiation Points Preview */}
                <Card>
                  <CardHeader>
                    <CardTitle className="text-base">Key Issues to Practice</CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-2">
                      {scenario.negotiationPoints.slice(0, 3).map((point, index) => (
                        <div key={point.id} className="flex items-center gap-3 p-2 bg-muted/50 rounded">
                          <Badge variant="outline" className="text-xs">
                            {point.riskLevel}
                          </Badge>
                          <span className="text-sm font-medium">{point.issue}</span>
                        </div>
                      ))}
                      {scenario.negotiationPoints.length > 3 && (
                        <div className="text-xs text-muted-foreground text-center">
                          +{scenario.negotiationPoints.length - 3} more issues
                        </div>
                      )}
                    </div>
                  </CardContent>
                </Card>

                {/* AI Personality Configuration */}
                <Card>
                  <CardHeader>
                    <CardTitle className="flex items-center gap-2">
                      <Settings className="h-5 w-5" />
                      Step 2: Configure AI Opponent
                    </CardTitle>
                  </CardHeader>
                  <CardContent className="space-y-4">
                    <div className="space-y-3">
                      <div>
                        <Label>Aggressiveness: {(aiPersonality.aggressiveness * 100).toFixed(0)}%</Label>
                        <Slider
                          value={[aiPersonality.aggressiveness]}
                          onValueChange={([value]) => setAiPersonality(prev => ({ ...prev, aggressiveness: value }))}
                          max={1}
                          step={0.1}
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Flexibility: {(aiPersonality.flexibility * 100).toFixed(0)}%</Label>
                        <Slider
                          value={[aiPersonality.flexibility]}
                          onValueChange={([value]) => setAiPersonality(prev => ({ ...prev, flexibility: value }))}
                          max={1}
                          step={0.1}
                          className="mt-2"
                        />
                      </div>

                      <div>
                        <Label>Communication Style</Label>
                        <Select 
                          value={aiPersonality.communicationStyle} 
                          onValueChange={(value: any) => setAiPersonality(prev => ({ ...prev, communicationStyle: value }))}
                        >
                          <SelectTrigger className="mt-2">
                            <SelectValue />
                          </SelectTrigger>
                          <SelectContent>
                            <SelectItem value="DIRECT">Direct & Assertive</SelectItem>
                            <SelectItem value="DIPLOMATIC">Diplomatic & Polite</SelectItem>
                            <SelectItem value="ANALYTICAL">Analytical & Data-Driven</SelectItem>
                            <SelectItem value="EMOTIONAL">Emotional & Persuasive</SelectItem>
                          </SelectContent>
                        </Select>
                      </div>
                    </div>

                    <div className="flex items-center gap-2 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                      <Users className="h-4 w-4 text-blue-600" />
                      <span className="text-sm text-blue-800 dark:text-blue-200">
                        AI will adapt its responses based on your contract's risk level and complexity
                      </span>
                    </div>

                    <Button 
                      onClick={handleStartNegotiation}
                      disabled={loading}
                      className="w-full gap-2"
                      size="lg"
                    >
                      {loading ? (
                        <>
                          <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-white" />
                          Starting Session...
                        </>
                      ) : (
                        <>
                          <MessageSquare className="h-4 w-4" />
                          Start Negotiation Practice
                          <ArrowRight className="h-4 w-4" />
                        </>
                      )}
                    </Button>
                  </CardContent>
                </Card>
              </div>
            )}
          </div>
        </DialogContent>
      </Dialog>
    </>
  );
}
