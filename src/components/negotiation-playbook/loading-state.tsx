"use client";

import React from 'react';
import { Loader<PERSON>, <PERSON> } from 'lucide-react';
import { Card, CardContent } from '@/components/ui/card';

interface LoadingStateProps {
  message?: string;
}

export function LoadingState({ 
  message = "Analyzing document for negotiation opportunities..." 
}: LoadingStateProps) {
  return (
    <Card className="w-full">
      <CardContent className="flex flex-col items-center justify-center py-12 space-y-4">
        <div className="relative">
          <Brain className="h-12 w-12 text-primary/20" />
          <Loader2 className="h-6 w-6 text-primary animate-spin absolute top-3 left-3" />
        </div>
        <div className="text-center space-y-2">
          <h3 className="text-lg font-medium">Generating Negotiation Playbook</h3>
          <p className="text-sm text-muted-foreground max-w-md">
            {message}
          </p>
          <p className="text-xs text-muted-foreground">
            This may take 15-30 seconds depending on document complexity.
          </p>
        </div>
      </CardContent>
    </Card>
  );
}
