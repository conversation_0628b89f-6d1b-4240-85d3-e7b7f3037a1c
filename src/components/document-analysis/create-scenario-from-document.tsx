"use client";

import React, { useState } from 'react';
import { Play, FileText, Settings, Loader2, Target } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { useToast } from '@/hooks/use-toast';
import { useRouter } from 'next/navigation';
import { negotiationSimulatorService } from '@/lib/services/negotiation-simulator-service';
import { useFeatureAccess } from '@/hooks/use-feature-access';
import type { 
  CreateScenarioFromDocumentRequest,
  Difficulty 
} from '@/lib/types/negotiation-simulator';

interface CreateScenarioFromDocumentProps {
  documentId: string;
  documentTitle: string;
  className?: string;
}

export function CreateScenarioFromDocument({ 
  documentId, 
  documentTitle, 
  className 
}: CreateScenarioFromDocumentProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [isCreating, setIsCreating] = useState(false);
  const [scenarioName, setScenarioName] = useState('');
  const [description, setDescription] = useState('');
  const [difficulty, setDifficulty] = useState<Difficulty>('INTERMEDIATE');
  const [targetRole, setTargetRole] = useState<'buyer' | 'seller' | 'vendor' | 'client'>('buyer');
  const [focusOnRisks, setFocusOnRisks] = useState(true);
  const [includeRecommendations, setIncludeRecommendations] = useState(true);
  
  const { toast } = useToast();
  const router = useRouter();
  const { canAccessFeature } = useFeatureAccess();

  const hasSimulatorAccess = canAccessFeature('negotiation_simulator');

  const handleCreateScenario = async () => {
    if (!hasSimulatorAccess) {
      toast({
        title: "Subscription Required",
        description: "Negotiation simulator feature requires PRO subscription",
        variant: "destructive",
      });
      return;
    }

    if (!scenarioName.trim()) {
      toast({
        title: "Name Required",
        description: "Please enter a name for the scenario",
        variant: "destructive",
      });
      return;
    }

    setIsCreating(true);
    try {
      const request: CreateScenarioFromDocumentRequest = {
        name: scenarioName.trim(),
        description: description.trim() || undefined,
        difficulty,
        extractionOptions: {
          focusOnRisks,
          includeRecommendations,
          targetRole,
        }
      };

      const scenario = await negotiationSimulatorService.createScenarioFromDocument(
        documentId,
        request
      );

      toast({
        title: "Scenario Created",
        description: `"${scenario.name}" has been created from your document analysis!`,
      });

      setIsOpen(false);
      
      // Reset form
      setScenarioName('');
      setDescription('');
      setDifficulty('INTERMEDIATE');
      setTargetRole('buyer');
      setFocusOnRisks(true);
      setIncludeRecommendations(true);
      
      // Navigate to the new scenario
      router.push(`/negotiation-simulator/scenarios/${scenario.id}`);
      
    } catch (error) {
      console.error('Failed to create scenario from document:', error);
      toast({
        title: "Creation Failed",
        description: "Failed to create scenario from document. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsCreating(false);
    }
  };

  if (!hasSimulatorAccess) {
    return (
      <Button variant="outline" className={className} disabled>
        <Play className="h-4 w-4 mr-2" />
        Create Practice Scenario
        <Badge variant="secondary" className="ml-2">PRO</Badge>
      </Button>
    );
  }

  return (
    <Dialog open={isOpen} onOpenChange={setIsOpen}>
      <DialogTrigger asChild>
        <Button className={className}>
          <Target className="h-4 w-4 mr-2" />
          Create Practice Scenario
        </Button>
      </DialogTrigger>
      <DialogContent className="sm:max-w-[500px]">
        <DialogHeader>
          <DialogTitle>Create Scenario from Document</DialogTitle>
          <DialogDescription>
            Generate an interactive negotiation scenario based on this document analysis.
          </DialogDescription>
        </DialogHeader>
        
        <div className="space-y-6">
          {/* Document Info */}
          <div className="p-3 bg-muted/50 rounded-lg">
            <div className="flex items-center gap-2 mb-1">
              <FileText className="h-4 w-4" />
              <span className="text-sm font-medium">Source Document</span>
            </div>
            <p className="text-sm text-muted-foreground line-clamp-1">
              {documentTitle}
            </p>
          </div>

          {/* Scenario Name */}
          <div className="space-y-2">
            <Label htmlFor="scenarioName">Scenario Name *</Label>
            <Input
              id="scenarioName"
              value={scenarioName}
              onChange={(e) => setScenarioName(e.target.value)}
              placeholder="e.g., Software License Negotiation"
            />
          </div>

          {/* Description */}
          <div className="space-y-2">
            <Label htmlFor="description">Description (Optional)</Label>
            <Textarea
              id="description"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Brief description of the scenario..."
              rows={3}
            />
          </div>

          {/* Difficulty Selection */}
          <div className="space-y-2">
            <Label htmlFor="difficulty">Difficulty Level</Label>
            <Select value={difficulty} onValueChange={(value: Difficulty) => setDifficulty(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select difficulty" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="BEGINNER">
                  <div>
                    <div className="font-medium">Beginner</div>
                    <div className="text-sm text-muted-foreground">Cooperative AI, clear objectives</div>
                  </div>
                </SelectItem>
                <SelectItem value="INTERMEDIATE">
                  <div>
                    <div className="font-medium">Intermediate</div>
                    <div className="text-sm text-muted-foreground">Moderate complexity, some conflicts</div>
                  </div>
                </SelectItem>
                <SelectItem value="EXPERT">
                  <div>
                    <div className="font-medium">Expert</div>
                    <div className="text-sm text-muted-foreground">Aggressive AI, tight constraints</div>
                  </div>
                </SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Target Role */}
          <div className="space-y-2">
            <Label htmlFor="targetRole">Your Role in Negotiation</Label>
            <Select value={targetRole} onValueChange={(value: any) => setTargetRole(value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select your role" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="buyer">Buyer</SelectItem>
                <SelectItem value="seller">Seller</SelectItem>
                <SelectItem value="vendor">Vendor</SelectItem>
                <SelectItem value="client">Client</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Extraction Options */}
          <div className="space-y-4">
            <Label>Scenario Focus</Label>
            <div className="space-y-3">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="focusOnRisks"
                  checked={focusOnRisks}
                  onCheckedChange={(checked) => setFocusOnRisks(checked as boolean)}
                />
                <Label
                  htmlFor="focusOnRisks"
                  className="text-sm font-normal cursor-pointer"
                >
                  Focus on risk mitigation strategies
                </Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="includeRecommendations"
                  checked={includeRecommendations}
                  onCheckedChange={(checked) => setIncludeRecommendations(checked as boolean)}
                />
                <Label
                  htmlFor="includeRecommendations"
                  className="text-sm font-normal cursor-pointer"
                >
                  Include strategic recommendations
                </Label>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-2">
            <Button variant="outline" onClick={() => setIsOpen(false)}>
              Cancel
            </Button>
            <Button onClick={handleCreateScenario} disabled={isCreating || !scenarioName.trim()}>
              {isCreating ? (
                <>
                  <Loader2 className="h-4 w-4 mr-2 animate-spin" />
                  Creating...
                </>
              ) : (
                <>
                  <Settings className="h-4 w-4 mr-2" />
                  Create Scenario
                </>
              )}
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}
