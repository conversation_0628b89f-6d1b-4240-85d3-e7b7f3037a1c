"use client";

import { useState } from "react";
import {
	<PERSON><PERSON>,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { Loader2, Shield, Zap } from "lucide-react";

interface PlanDetails {
	id: string;
	name: string;
	price: number;
	limit: number;
	interval: "monthly" | "yearly";
}

interface UpgradeModalProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	currentPlan: string;
	newPlan: PlanDetails;
	onConfirm: () => Promise<void>;
}

export function UpgradeModal({
	open,
	onOpenChange,
	currentPlan,
	newPlan,
	onConfirm,
}: UpgradeModalProps) {
	const [loading, setLoading] = useState(false);

	const handleConfirm = async () => {
		try {
			setLoading(true);
			await onConfirm();
			onOpenChange(false);
		} catch (error) {
			console.error("Failed to upgrade plan:", error);
		} finally {
			setLoading(false);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Upgrade to {newPlan.name}</DialogTitle>
					<DialogDescription>
						Review your plan upgrade details
					</DialogDescription>
				</DialogHeader>

				<div className="space-y-6">
					<div className="space-y-2">
						<div className="flex items-center justify-between">
							<span className="text-sm text-muted-foreground">
								Current Plan
							</span>
							<span className="font-medium">{currentPlan}</span>
						</div>
						<div className="flex items-center justify-between">
							<span className="text-sm text-muted-foreground">New Plan</span>
							<span className="font-medium text-primary">{newPlan.name}</span>
						</div>
					</div>

					<div className="rounded-lg border p-4">
						<div className="space-y-3">
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Monthly Price</span>
								<div className="flex items-center gap-2">
									<span className="text-2xl font-bold">${newPlan.price}</span>
									<span className="text-sm text-muted-foreground">
										/{newPlan.interval}
									</span>
								</div>
							</div>
							<div className="flex items-center justify-between">
								<span className="text-sm font-medium">Document Limit</span>
								<Badge variant="secondary">
									{newPlan.limit.toLocaleString()} per month
								</Badge>
							</div>
						</div>
					</div>

					<div className="rounded-lg border border-green-200 bg-green-50 p-4">
						<div className="flex gap-2">
							<Shield className="h-5 w-5 text-green-600" />
							<div className="flex-1">
								<p className="text-sm font-medium text-green-900">
									Risk-free Upgrade
								</p>
								<p className="text-sm text-green-800">
									You can downgrade or cancel anytime. Pro-rated refunds
									available.
								</p>
							</div>
						</div>
					</div>
				</div>

				<DialogFooter>
					<Button
						variant="outline"
						onClick={() => onOpenChange(false)}
						disabled={loading}
					>
						Cancel
					</Button>
					<Button onClick={handleConfirm} disabled={loading}>
						{loading ? (
							<>
								<Loader2 className="mr-2 h-4 w-4 animate-spin" />
								Processing
							</>
						) : (
							<>
								<Zap className="mr-2 h-4 w-4" />
								Confirm Upgrade
							</>
						)}
					</Button>
				</DialogFooter>
			</DialogContent>
		</Dialog>
	);
}
