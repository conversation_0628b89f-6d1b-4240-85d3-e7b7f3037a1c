"use client";

import {
	<PERSON>,
	CardContent,
	CardDescription,
	<PERSON><PERSON><PERSON><PERSON>,
	<PERSON><PERSON>eader,
	CardTitle,
} from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/ui/table";
import { ArrowRight, Calculator, Zap } from "lucide-react";

interface OverageDetailsProps {
	currentPlan: "free" | "pro" | "business" | "enterprise";
	documentsUsed: number;
	documentsLimit: number;
	projectedOverage: number;
	daysUntilLimit: number;
}

export function OverageDetails({
	currentPlan,
	documentsUsed,
	documentsLimit,
	projectedOverage,
	daysUntilLimit,
}: OverageDetailsProps) {
	const overageCost = projectedOverage * 0.5; // $0.50 per document
	const nextPlan = {
		free: {
			name: "Pro",
			price: 99,
			limit: 50,
		},
		pro: {
			name: "Business",
			price: 499,
			limit: 500,
		},
		business: {
			name: "Enterprise",
			price: null,
			limit: null,
		},
		enterprise: null,
	}[currentPlan];

	const annualSavings =
		nextPlan && nextPlan.price
			? Math.max(overageCost - nextPlan.price, 0)
			: null;

	return (
		<Card>
			<CardHeader>
				<CardTitle className="flex items-center gap-2">
					<Calculator className="h-5 w-5" />
					Overage Cost Analysis
				</CardTitle>
				<CardDescription>
					Compare overage costs with plan upgrades
				</CardDescription>
			</CardHeader>
			<CardContent className="space-y-6">
				<Table>
					<TableHeader>
						<TableRow>
							<TableHead>Description</TableHead>
							<TableHead>Details</TableHead>
							<TableHead className="text-right">Cost</TableHead>
						</TableRow>
					</TableHeader>
					<TableBody>
						<TableRow>
							<TableCell>Current Usage</TableCell>
							<TableCell>
								{documentsUsed.toLocaleString()} /{" "}
								{documentsLimit.toLocaleString()} documents
							</TableCell>
							<TableCell className="text-right">-</TableCell>
						</TableRow>
						<TableRow>
							<TableCell>Projected Overage</TableCell>
							<TableCell>
								{projectedOverage.toLocaleString()} documents
								<Badge variant="secondary" className="ml-2">
									in {daysUntilLimit} days
								</Badge>
							</TableCell>
							<TableCell className="text-right">
								${overageCost.toFixed(2)}
							</TableCell>
						</TableRow>
						{nextPlan && (
							<TableRow>
								<TableCell>Upgrade to {nextPlan.name}</TableCell>
								<TableCell>
									{nextPlan.limit?.toLocaleString() ?? "Unlimited"}{" "}
									documents/month
								</TableCell>
								<TableCell className="text-right">
									${nextPlan.price ?? "Custom"}/month
								</TableCell>
							</TableRow>
						)}
					</TableBody>
				</Table>

				{nextPlan && annualSavings && annualSavings > 0 && (
					<div className="rounded-lg border border-green-200 bg-green-50 p-4">
						<div className="flex items-center justify-between">
							<div>
								<p className="font-medium text-green-900">
									Recommended: Upgrade to {nextPlan.name}
								</p>
								<p className="text-sm text-green-800">
									Save up to ${annualSavings.toFixed(2)} per month compared to
									overage costs
								</p>
							</div>
							<ArrowRight className="h-5 w-5 text-green-600" />
						</div>
					</div>
				)}
			</CardContent>
			{nextPlan && (
				<CardFooter>
					<Button className="w-full" size="lg">
						<Zap className="mr-2 h-4 w-4" />
						Upgrade to {nextPlan.name}
					</Button>
				</CardFooter>
			)}
		</Card>
	);
}
