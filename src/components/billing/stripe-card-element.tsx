"use client";

import { forwardRef, useState } from "react";
import { CardElement } from "@stripe/react-stripe-js";
import { FormControl } from "@/components/ui/form";
import { cn } from "@/lib/utils";
import { CreditCard } from "lucide-react";

import type { StripeCardElementChangeEvent } from '@stripe/stripe-js';

interface StripeCardElementProps {
	onChange?: (event: StripeCardElementChangeEvent) => void;
	onReady?: () => void;
	error?: string;
}

// All possible card brands from Stripe
type CardBrand =
  | "visa"
  | "mastercard"
  | "amex"
  | "discover"
  | "diners"
  | "jcb"
  | "unionpay"
  | "unknown";

const CARD_ELEMENT_OPTIONS = {
	style: {
		base: {
			fontSize: "16px",
			color: "hsl(var(--foreground))",
			"::placeholder": {
				color: "hsl(var(--muted-foreground))",
			},
		},
		invalid: {
			color: "hsl(var(--destructive))",
			iconColor: "hsl(var(--destructive))",
		},
	},
};

const CardBrandIcon = ({ brand }: { brand: CardBrand }) => {
	const iconProps = {
		className: "h-5 w-5",
	};

	switch (brand) {
		default:
			return <CreditCard {...iconProps} />;
	}
};

export const StripeCardElement = forwardRef<
	HTMLDivElement,
	StripeCardElementProps
>(({ onChange, onReady, error }, ref) => {
	const [cardBrand, setCardBrand] = useState<CardBrand>("unknown");
	const [focused, setFocused] = useState(false);

	const handleChange = (event: StripeCardElementChangeEvent) => {
		setCardBrand(event.brand || "unknown");
		onChange?.(event);
	};

	return (
		<FormControl>
			<div
				ref={ref}
				className={cn(
					"relative rounded-md border border-input bg-background px-3 py-2",
					focused && "ring-2 ring-ring ring-offset-2",
					error && "border-destructive"
				)}
			>
				<div className="flex items-center gap-2">
					<div className="flex-1">
						<CardElement
							options={CARD_ELEMENT_OPTIONS}
							onChange={handleChange}
							onReady={onReady}
							onFocus={() => setFocused(true)}
							onBlur={() => setFocused(false)}
						/>
					</div>
					<div
						className={cn(
							"text-muted-foreground transition-opacity",
							cardBrand === "unknown" && "opacity-50"
						)}
					>
						<CardBrandIcon brand={cardBrand} />
					</div>
				</div>
			</div>
			{error && <p className="mt-2 text-sm text-destructive">{error}</p>}
		</FormControl>
	);
});

StripeCardElement.displayName = "StripeCardElement";
