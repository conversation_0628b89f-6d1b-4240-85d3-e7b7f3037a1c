"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Elements } from "@stripe/react-stripe-js";
import { loadStripe } from "@stripe/stripe-js";
import { StripeCardElement } from "./stripe-card-element";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogFooter,
	DialogHeader,
	DialogTitle,
} from "@/components/ui/dialog";
import {
	Form,
	FormControl,
	FormField,
	FormItem,
	FormLabel,
	FormMessage,
} from "@/components/ui/form";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Loader2 } from "lucide-react";
import { toast } from "sonner";

const stripePromise = loadStripe(
	process.env.NEXT_PUBLIC_STRIPE_PUBLISHABLE_KEY!
);

interface PaymentMethodFormProps {
	open: boolean;
	onOpenChange: (open: boolean) => void;
	onSubmit: (data: FormData) => Promise<void>;
}

const cardSchema = z.object({
	type: z.literal("card"),
	stripeToken: z.string().min(1, "Please enter card details"),
});

const paypalSchema = z.object({
	type: z.literal("paypal"),
	email: z.string().email("Invalid email address"),
});

const formSchema = z.discriminatedUnion("type", [cardSchema, paypalSchema]);

type FormData = z.infer<typeof formSchema>;

function PaymentMethodForm({
	open,
	onOpenChange,
	onSubmit,
}: PaymentMethodFormProps) {
	const [type, setType] = useState<"card" | "paypal">("card");
	const form = useForm<FormData>({
		resolver: zodResolver(formSchema),
		defaultValues: {
			type: "card",
		},
	});

	const handleTypeChange = (value: "card" | "paypal") => {
		setType(value);
		form.reset(
			value === "card"
				? { type: "card", stripeToken: "" }
				: { type: "paypal", email: "" }
		);
	};

	const handleSubmit = async (data: FormData) => {
		try {
			await onSubmit(data);
			form.reset();
			onOpenChange(false);
			toast.success("Payment method added successfully");
		} catch (error) {
			toast.error("Failed to save payment method. Please try again.");
			console.error("Failed to save payment method:", error);
		}
	};

	return (
		<Dialog open={open} onOpenChange={onOpenChange}>
			<DialogContent className="sm:max-w-[425px]">
				<DialogHeader>
					<DialogTitle>Add Payment Method</DialogTitle>
					<DialogDescription>
						Add a new payment method to your account
					</DialogDescription>
				</DialogHeader>

				<Form {...form}>
					<form
						onSubmit={form.handleSubmit(handleSubmit)}
						className="space-y-6"
					>
						<FormField
							control={form.control}
							name="type"
							render={({ field }) => (
								<FormItem>
									<FormLabel>Payment Type</FormLabel>
									<Select
										value={field.value}
										onValueChange={(value: string) => handleTypeChange(value as "card" | "paypal")}
									>
										<SelectTrigger>
											<SelectValue />
										</SelectTrigger>
										<SelectContent>
											<SelectItem value="card">Credit Card</SelectItem>
											<SelectItem value="paypal">PayPal</SelectItem>
										</SelectContent>
									</Select>
								</FormItem>
							)}
						/>

						{type === "card" ? (
							<>
								<FormField
									control={form.control}
									name="stripeToken"
									render={({ field }) => (
										<FormItem>
											<FormLabel>Card Details</FormLabel>
											<FormControl>
												<StripeCardElement {...field} />
											</FormControl>
											<FormMessage />
										</FormItem>
									)}
								/>
							</>
						) : (
							<FormField
								control={form.control}
								name="email"
								render={({ field }) => (
									<FormItem>
										<FormLabel>PayPal Email</FormLabel>
										<FormControl>
											<Input
												{...field}
												type="email"
												placeholder="<EMAIL>"
												autoComplete="email"
											/>
										</FormControl>
										<FormMessage />
									</FormItem>
								)}
							/>
						)}

						<DialogFooter>
							<Button
								type="button"
								variant="outline"
								onClick={() => onOpenChange(false)}
								disabled={form.formState.isSubmitting}
							>
								Cancel
							</Button>
							<Button type="submit" disabled={form.formState.isSubmitting}>
								{form.formState.isSubmitting ? (
									<>
										<Loader2 className="mr-2 h-4 w-4 animate-spin" />
										Saving
									</>
								) : (
									"Save Payment Method"
								)}
							</Button>
						</DialogFooter>
					</form>
				</Form>
			</DialogContent>
		</Dialog>
	);
}

export function PaymentMethodFormWithStripe(props: PaymentMethodFormProps) {
	return (
		<Elements stripe={stripePromise}>
			<PaymentMethodForm {...props} />
		</Elements>
	);
}
