"use client";

import React, { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import {
  BookOpen,
  MessageSquare,
  Gamepad2,
  TrendingUp,
  ArrowRight,
  Clock,
  Target,
  Award,
  ChevronRight,
  Zap,
  Users,
  BarChart3
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { negotiationIntegrationService } from '@/lib/services/negotiation-integration-service';

interface IntegrationDashboardProps {
  userId: string;
  documentId?: string;
}

export function IntegrationDashboard({ userId, documentId }: IntegrationDashboardProps) {
  const [learningPath, setLearningPath] = useState<any>(null);
  const [loading, setLoading] = useState(true);
  const [activeSystem, setActiveSystem] = useState<'playbook' | 'chat' | 'simulator'>('playbook');

  useEffect(() => {
    loadLearningPath();
  }, [userId, documentId]);

  const loadLearningPath = async () => {
    try {
      setLoading(true);
      const path = await negotiationIntegrationService.getIntegratedLearningPath(userId, documentId);
      setLearningPath(path);
    } catch (error) {
      console.error('Failed to load learning path:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleActionClick = async (action: any) => {
    try {
      switch (action.type) {
        case 'generate_playbook':
          // Navigate to playbook generation
          window.location.href = `/documents/${action.params.documentId}/negotiation-playbook`;
          break;
        case 'start_chat_negotiation':
          // Navigate to chat negotiation
          window.location.href = `/demo/chat-negotiation`;
          break;
        case 'start_simulator_session':
          // Navigate to simulator
          window.location.href = `/negotiation-simulator`;
          break;
      }
    } catch (error) {
      console.error('Failed to execute action:', error);
    }
  };

  const getSystemIcon = (system: string) => {
    switch (system) {
      case 'playbook': return BookOpen;
      case 'chat': return MessageSquare;
      case 'simulator': return Gamepad2;
      default: return Target;
    }
  };

  const getSystemColor = (system: string) => {
    switch (system) {
      case 'playbook': return 'bg-blue-100 text-blue-600';
      case 'chat': return 'bg-green-100 text-green-600';
      case 'simulator': return 'bg-purple-100 text-purple-600';
      default: return 'bg-gray-100 text-gray-600';
    }
  };

  const getCurrentStepProgress = () => {
    const steps = ['playbook', 'chat', 'simulator', 'advanced'];
    const currentIndex = steps.indexOf(learningPath?.currentStep || 'playbook');
    return ((currentIndex + 1) / steps.length) * 100;
  };

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="h-32 bg-gray-100 rounded-lg animate-pulse" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          {[1, 2, 3].map(i => (
            <div key={i} className="h-48 bg-gray-100 rounded-lg animate-pulse" />
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Learning Progress Overview */}
      <Card className="bg-gradient-to-r from-blue-50 to-purple-50 dark:from-blue-950/20 dark:to-purple-950/20">
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <TrendingUp className="h-5 w-5" />
            Your Negotiation Learning Journey
          </CardTitle>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="flex items-center justify-between">
            <div>
              <h3 className="font-semibold capitalize">
                Current Stage: {learningPath?.currentStep?.replace('_', ' ')}
              </h3>
              <p className="text-sm text-muted-foreground">
                Progress through our integrated learning system
              </p>
            </div>
            <div className="text-right">
              <div className="text-2xl font-bold text-purple-600">
                {Math.round(getCurrentStepProgress())}%
              </div>
              <div className="text-xs text-muted-foreground">Complete</div>
            </div>
          </div>
          
          <Progress value={getCurrentStepProgress()} className="h-2" />
          
          <div className="grid grid-cols-4 gap-2 text-xs">
            {['Playbook', 'Chat', 'Simulator', 'Advanced'].map((step, index) => (
              <div 
                key={step}
                className={`text-center p-2 rounded ${
                  index <= ['playbook', 'chat', 'simulator', 'advanced'].indexOf(learningPath?.currentStep || 'playbook')
                    ? 'bg-purple-100 text-purple-700 dark:bg-purple-900/30 dark:text-purple-300'
                    : 'bg-muted text-muted-foreground'
                }`}
              >
                {step}
              </div>
            ))}
          </div>
        </CardContent>
      </Card>

      {/* System Integration Overview */}
      <Tabs value={activeSystem} onValueChange={(value: any) => setActiveSystem(value)}>
        <TabsList className="grid w-full grid-cols-3">
          <TabsTrigger value="playbook" className="gap-2">
            <BookOpen className="h-4 w-4" />
            Playbook
          </TabsTrigger>
          <TabsTrigger value="chat" className="gap-2">
            <MessageSquare className="h-4 w-4" />
            Chat Practice
          </TabsTrigger>
          <TabsTrigger value="simulator" className="gap-2">
            <Gamepad2 className="h-4 w-4" />
            Simulator
          </TabsTrigger>
        </TabsList>

        <TabsContent value="playbook" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BookOpen className="h-5 w-5 text-blue-600" />
                Strategic Analysis
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Generate comprehensive negotiation strategies from your documents. 
                  Understand what to negotiate, why it matters, and how to approach each term.
                </p>
                
                <div className="flex items-center justify-between p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center">
                      <Zap className="h-4 w-4 text-blue-600" />
                    </div>
                    <div>
                      <div className="font-medium">3 Credits per Analysis</div>
                      <div className="text-xs text-muted-foreground">Strategic recommendations included</div>
                    </div>
                  </div>
                  <ArrowRight className="h-4 w-4 text-blue-600" />
                </div>

                {documentId && (
                  <Button 
                    onClick={() => handleActionClick({ type: 'generate_playbook', params: { documentId } })}
                    className="w-full"
                  >
                    Generate Playbook for This Document
                  </Button>
                )}
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="chat" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <MessageSquare className="h-5 w-5 text-green-600" />
                Interactive Practice
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Practice your negotiation strategies through natural conversation. 
                  Build relationship skills and test approaches in a safe environment.
                </p>
                
                <div className="grid grid-cols-2 gap-3">
                  <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Users className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Relationship Focus</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Build trust, manage pressure
                    </div>
                  </div>

                  <div className="p-3 bg-green-50 dark:bg-green-950/20 rounded-lg">
                    <div className="flex items-center gap-2 mb-1">
                      <Target className="h-4 w-4 text-green-600" />
                      <span className="text-sm font-medium">Strategy Application</span>
                    </div>
                    <div className="text-xs text-muted-foreground">
                      Apply playbook insights
                    </div>
                  </div>
                </div>

                <Button 
                  onClick={() => handleActionClick({ type: 'start_chat_negotiation', params: {} })}
                  className="w-full"
                  variant="outline"
                >
                  Start Chat Practice Session
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>

        <TabsContent value="simulator" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <Gamepad2 className="h-5 w-5 text-purple-600" />
                Structured Training
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <p className="text-sm text-muted-foreground">
                  Master complex negotiation scenarios with structured rounds, 
                  time pressure, and detailed performance evaluation.
                </p>
                
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Difficulty Progression</span>
                    <Badge variant="outline">Intermediate</Badge>
                  </div>
                  <Progress value={65} className="h-2" />
                  <div className="text-xs text-muted-foreground">
                    Complete chat practice to unlock advanced scenarios
                  </div>
                </div>

                <Button 
                  onClick={() => handleActionClick({ type: 'start_simulator_session', params: {} })}
                  className="w-full"
                  variant="outline"
                >
                  Enter Negotiation Simulator
                </Button>
              </div>
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>

      {/* Personalized Recommendations */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Target className="h-5 w-5" />
            Recommended Next Steps
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {learningPath?.recommendations?.map((rec: any, index: number) => {
              const Icon = getSystemIcon(rec.type);
              return (
                <motion.div
                  key={index}
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ delay: index * 0.1 }}
                  className="flex items-center justify-between p-4 border border-border rounded-lg hover:bg-muted/50 transition-colors"
                >
                  <div className="flex items-center gap-3">
                    <div className={`w-10 h-10 rounded-full flex items-center justify-center ${getSystemColor(rec.type)}`}>
                      <Icon className="h-5 w-5" />
                    </div>
                    <div>
                      <h4 className="font-medium">{rec.title}</h4>
                      <p className="text-sm text-muted-foreground">{rec.description}</p>
                      <div className="flex items-center gap-4 mt-1">
                        <div className="flex items-center gap-1 text-xs text-muted-foreground">
                          <Clock className="h-3 w-3" />
                          {rec.estimatedTime}
                        </div>
                        <Badge variant="outline" className="text-xs">
                          {rec.difficulty}
                        </Badge>
                      </div>
                    </div>
                  </div>
                  <Button
                    onClick={() => handleActionClick(rec.action)}
                    size="sm"
                    className="gap-1"
                  >
                    Start
                    <ChevronRight className="h-3 w-3" />
                  </Button>
                </motion.div>
              );
            })}
          </div>
        </CardContent>
      </Card>

      {/* Skill Development Progress */}
      {learningPath?.skillGaps?.length > 0 && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <BarChart3 className="h-5 w-5" />
              Skill Development Focus
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {learningPath.skillGaps.map((skill: string, index: number) => (
                <div key={skill} className="flex items-center justify-between">
                  <span className="text-sm capitalize">{skill.replace('_', ' ')}</span>
                  <div className="flex items-center gap-2">
                    <Progress value={30 + (index * 15)} className="w-24 h-2" />
                    <span className="text-xs text-muted-foreground">
                      {30 + (index * 15)}%
                    </span>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
