"use client";

import { useState } from "react";
import {
	Card,
	CardContent,
	CardDescription,
	CardHeader,
	CardTitle,
} from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON><PERSON>ist, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { Badge } from "@/components/ui/badge";
import {
	<PERSON>rkles,
	FileText,
	Brain,
	Database,
	Link as LinkIcon,
	TrendingUp,
	Clock,
	CheckCircle,
	AlertCircle,
	Info,
} from "lucide-react";
import { AIAssistedDraftingForm } from "./ai-assisted-drafting-form";
import { ClauseIntelligencePanel } from "./clause-intelligence-panel";
import { RelatedDocumentsGenerator } from "./related-documents-generator";
import { ClauseLibraryBuilder } from "./clause-library-builder";
import { DocumentAutomationFeatureGuard } from "./document-automation-feature-guard";
import { useDocumentAutomationFeatures } from "@/hooks/use-document-automation";

interface DocumentAutomationDashboardProps {
	primaryDocumentId?: string;
	documentContent?: string;
	documentType?: string;
}

export function DocumentAutomationDashboard({
	primaryDocumentId,
	documentContent,
	documentType,
}: DocumentAutomationDashboardProps) {
	const [activeTab, setActiveTab] = useState("ai-drafting");
	const [generatedContent, setGeneratedContent] = useState<string>("");

	const features = useDocumentAutomationFeatures();

	const handleDraftingSuccess = (result: any) => {
		setGeneratedContent(result.content);
		// Could also navigate to a document editor or save the document
	};

	const handleClauseInsert = (clause: string) => {
		// This would integrate with a document editor
		console.log("Inserting clause:", clause);
	};

	const handleSuggestionApply = (suggestion: any) => {
		// This would integrate with a document editor
		console.log("Applying suggestion:", suggestion);
	};

	const featureCards = [
		{
			id: "ai-drafting",
			title: "AI-Assisted Drafting",
			description: "Generate new legal documents using AI",
			icon: Sparkles,
			available: features.hasAIDrafting,
			color: "text-purple-500",
			bgColor: "bg-purple-50 dark:bg-purple-950/20",
		},
		{
			id: "clause-intelligence",
			title: "Clause Intelligence",
			description: "Get intelligent clause suggestions",
			icon: Brain,
			available: features.hasClauseIntelligence,
			color: "text-blue-500",
			bgColor: "bg-blue-50 dark:bg-blue-950/20",
		},
		{
			id: "related-documents",
			title: "Related Documents",
			description: "Generate schedules, exhibits, and addendums",
			icon: LinkIcon,
			available: features.hasRelatedDocuments,
			color: "text-green-500",
			bgColor: "bg-green-50 dark:bg-green-950/20",
		},
		{
			id: "clause-library",
			title: "Clause Library Builder",
			description: "Auto-extract clauses from your documents",
			icon: Database,
			available: features.hasClauseLibraryBuilder,
			color: "text-orange-500",
			bgColor: "bg-orange-50 dark:bg-orange-950/20",
		},
	];

	return (
		<div className="space-y-6">
			{/* Header */}
			<div className="space-y-2">
				<h1 className="text-3xl font-bold tracking-tight">
					Document Automation
				</h1>
				<p className="text-muted-foreground">
					Leverage AI to assist in drafting and generating legal documents,
					moving beyond static templates.
				</p>
			</div>

			{/* Feature Overview Cards */}
			<div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
				{featureCards.map((feature) => {
					const Icon = feature.icon;
					return (
						<Card
							key={feature.id}
							className={`cursor-pointer transition-all duration-200 hover:shadow-md ${
								activeTab === feature.id ? "ring-2 ring-primary" : ""
							} ${feature.available ? "" : "opacity-60"}`}
							onClick={() => feature.available && setActiveTab(feature.id)}
						>
							<CardContent className="p-4">
								<div
									className={`w-12 h-12 rounded-lg ${feature.bgColor} flex items-center justify-center mb-3`}
								>
									<Icon className={`h-6 w-6 ${feature.color}`} />
								</div>
								<h3 className="font-semibold mb-1">{feature.title}</h3>
								<p className="text-sm text-muted-foreground mb-2">
									{feature.description}
								</p>
								<div className="flex items-center justify-between">
									<Badge variant={feature.available ? "default" : "secondary"}>
										{feature.available ? "Available" : "Upgrade Required"}
									</Badge>
									{feature.available ? (
										<CheckCircle className="h-4 w-4 text-green-500" />
									) : (
										<AlertCircle className="h-4 w-4 text-orange-500" />
									)}
								</div>
							</CardContent>
						</Card>
					);
				})}
			</div>

			{/* Main Content Tabs */}
			<Tabs
				value={activeTab}
				onValueChange={setActiveTab}
				className="space-y-6"
			>
				<TabsList className="grid w-full grid-cols-4">
					<TabsTrigger value="ai-drafting" disabled={!features.hasAIDrafting}>
						<Sparkles className="h-4 w-4 mr-2" />
						AI Drafting
					</TabsTrigger>
					<TabsTrigger
						value="clause-intelligence"
						disabled={!features.hasClauseIntelligence}
					>
						<Brain className="h-4 w-4 mr-2" />
						Clause Intelligence
					</TabsTrigger>
					<TabsTrigger
						value="related-documents"
						disabled={!features.hasRelatedDocuments}
					>
						<LinkIcon className="h-4 w-4 mr-2" />
						Related Docs
					</TabsTrigger>
					<TabsTrigger
						value="clause-library"
						disabled={!features.hasClauseLibraryBuilder}
					>
						<Database className="h-4 w-4 mr-2" />
						Library Builder
					</TabsTrigger>
				</TabsList>

				<TabsContent value="ai-drafting" className="space-y-6">
					<DocumentAutomationFeatureGuard feature="ai-drafting">
						<AIAssistedDraftingForm
							onSuccess={handleDraftingSuccess}
							onError={(error) => console.error("Drafting error:", error)}
						/>
					</DocumentAutomationFeatureGuard>

					{/* Generated Content Preview */}
					{generatedContent && (
						<Card>
							<CardHeader>
								<CardTitle className="flex items-center gap-2">
									<FileText className="h-5 w-5 text-green-500" />
									Generated Document
								</CardTitle>
								<CardDescription>
									Your AI-generated document is ready for review and editing.
								</CardDescription>
							</CardHeader>
							<CardContent>
								<div className="bg-muted p-4 rounded-lg max-h-96 overflow-y-auto">
									<pre className="whitespace-pre-wrap text-sm">
										{generatedContent}
									</pre>
								</div>
								<div className="flex gap-2 mt-4">
									<Button>Edit Document</Button>
									<Button variant="outline">Download</Button>
									<Button variant="outline">Save to Library</Button>
								</div>
							</CardContent>
						</Card>
					)}
				</TabsContent>

				<TabsContent value="clause-intelligence" className="space-y-6">
					<DocumentAutomationFeatureGuard feature="clause-intelligence">
						<ClauseIntelligencePanel
							documentType={documentType}
							currentContent={documentContent}
							onClauseInsert={handleClauseInsert}
							onSuggestionApply={handleSuggestionApply}
						/>
					</DocumentAutomationFeatureGuard>
				</TabsContent>

				<TabsContent value="related-documents" className="space-y-6">
					<DocumentAutomationFeatureGuard feature="related-documents">
						<RelatedDocumentsGenerator
							primaryDocumentId={primaryDocumentId}
							onSuccess={(result) =>
								console.log("Related documents generated:", result)
							}
							onError={(error) =>
								console.error("Related documents error:", error)
							}
						/>
					</DocumentAutomationFeatureGuard>
				</TabsContent>

				<TabsContent value="clause-library" className="space-y-6">
					<DocumentAutomationFeatureGuard feature="clause-library">
						<ClauseLibraryBuilder
							onSuccess={(result) =>
								console.log("Clause library built:", result)
							}
							onError={(error) => console.error("Library build error:", error)}
						/>
					</DocumentAutomationFeatureGuard>
				</TabsContent>
			</Tabs>

			{/* Help Section */}
			<Card className="border-dashed">
				<CardContent className="p-6">
					<div className="flex items-start gap-3">
						<Info className="h-5 w-5 text-blue-500 mt-0.5" />
						<div className="space-y-2">
							<h3 className="font-semibold">
								Getting Started with Document Automation
							</h3>
							<div className="text-sm text-muted-foreground space-y-1">
								<p>
									• <strong>AI Drafting:</strong> Start with a clear prompt
									describing your document requirements
								</p>
								<p>
									• <strong>Clause Intelligence:</strong> Paste existing content
									to get intelligent suggestions
								</p>
								<p>
									• <strong>Related Documents:</strong> Generate supporting
									documents from your main agreement
								</p>
								<p>
									• <strong>Library Builder:</strong> Analyze your document
									corpus to build a reusable clause library
								</p>
							</div>
						</div>
					</div>
				</CardContent>
			</Card>
		</div>
	);
}
