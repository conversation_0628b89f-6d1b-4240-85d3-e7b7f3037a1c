"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Progress } from "@/components/ui/progress";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { Slider } from "@/components/ui/slider";
import { 
  Database, 
  FileText, 
  Clock, 
  TrendingUp, 
  Copy,
  Loader2,
  CheckCircle,
  AlertCircle,
  BarChart3
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useBuildClauseLibrary } from "@/hooks/use-document-automation";
import type { 
  ClauseLibraryBuildResponse,
  ExtractedClause 
} from "@/lib/types/document-automation";

const libraryBuildSchema = z.object({
  includeExistingClauses: z.boolean().default(true),
  minimumConfidence: z.number().min(0).max(1).default(0.7),
  maxClausesPerCategory: z.number().min(1).max(50).default(10),
  documentTypes: z.array(z.string()).optional(),
  analysisDepth: z.enum(["basic", "detailed", "comprehensive"]).default("detailed"),
});

type LibraryBuildFormData = z.infer<typeof libraryBuildSchema>;

const documentTypes = [
  { value: "contract", label: "Contracts" },
  { value: "nda", label: "NDAs" },
  { value: "msa", label: "Master Service Agreements" },
  { value: "sow", label: "Statements of Work" },
  { value: "employment_agreement", label: "Employment Agreements" },
  { value: "lease_agreement", label: "Lease Agreements" },
];

const analysisDepthOptions = [
  { value: "basic", label: "Basic", description: "Quick extraction of common clauses" },
  { value: "detailed", label: "Detailed", description: "Comprehensive analysis with categorization" },
  { value: "comprehensive", label: "Comprehensive", description: "Deep analysis with pattern recognition" },
];

interface ClauseLibraryBuilderProps {
  onSuccess?: (result: ClauseLibraryBuildResponse) => void;
  onError?: (error: Error) => void;
}

export function ClauseLibraryBuilder({ onSuccess, onError }: ClauseLibraryBuilderProps) {
  const [selectedDocTypes, setSelectedDocTypes] = useState<string[]>([]);
  const [buildResult, setBuildResult] = useState<ClauseLibraryBuildResponse | null>(null);
  const [confidenceThreshold, setConfidenceThreshold] = useState([0.7]);
  const [maxClauses, setMaxClauses] = useState([10]);

  const { toast } = useToast();
  const buildLibrary = useBuildClauseLibrary();

  const {
    handleSubmit,
    setValue,
    watch,
  } = useForm<LibraryBuildFormData>({
    resolver: zodResolver(libraryBuildSchema),
    defaultValues: {
      includeExistingClauses: true,
      minimumConfidence: 0.7,
      maxClausesPerCategory: 10,
      analysisDepth: "detailed",
    },
  });

  const watchedValues = watch();

  const handleDocTypeChange = (type: string, checked: boolean) => {
    let updatedTypes: string[];
    if (checked) {
      updatedTypes = [...selectedDocTypes, type];
    } else {
      updatedTypes = selectedDocTypes.filter((t) => t !== type);
    }
    setSelectedDocTypes(updatedTypes);
    setValue("documentTypes", updatedTypes.length > 0 ? updatedTypes : undefined);
  };

  const onSubmit = async (data: LibraryBuildFormData) => {
    try {
      const result = await buildLibrary.mutateAsync({
        includeExistingClauses: data.includeExistingClauses,
        minimumConfidence: confidenceThreshold[0],
        maxClausesPerCategory: maxClauses[0],
        documentTypes: selectedDocTypes.length > 0 ? selectedDocTypes : undefined,
        analysisDepth: data.analysisDepth,
      });

      setBuildResult(result);
      toast({
        title: "Clause Library Built Successfully",
        description: `Extracted ${result.totalClausesExtracted} clauses from ${result.totalDocumentsAnalyzed} documents.`,
      });

      onSuccess?.(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to build clause library";
      toast({
        title: "Library Build Failed",
        description: errorMessage,
        variant: "destructive",
      });
      onError?.(error as Error);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied to Clipboard",
        description: "Clause content has been copied to your clipboard.",
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard.",
        variant: "destructive",
      });
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return "text-green-600";
    if (confidence >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.round(ms / 1000);
    if (seconds < 60) return `${seconds}s`;
    const minutes = Math.round(seconds / 60);
    return `${minutes}m`;
  };

  return (
    <div className="space-y-6">
      {/* Configuration Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Database className="h-5 w-5 text-primary" />
            Auto-Build Clause Library
          </CardTitle>
          <CardDescription>
            Automatically extract and categorize clauses from your organization's existing documents to build a comprehensive clause library.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Analysis Depth */}
            <div className="space-y-3">
              <Label>Analysis Depth</Label>
              <div className="grid grid-cols-1 md:grid-cols-3 gap-3">
                {analysisDepthOptions.map((option) => (
                  <div
                    key={option.value}
                    className={`p-3 border rounded-lg cursor-pointer transition-colors ${
                      watchedValues.analysisDepth === option.value
                        ? "border-primary bg-primary/5"
                        : "border-muted hover:border-primary/50"
                    }`}
                    onClick={() => setValue("analysisDepth", option.value as any)}
                  >
                    <div className="font-medium">{option.label}</div>
                    <div className="text-sm text-muted-foreground">{option.description}</div>
                  </div>
                ))}
              </div>
            </div>

            {/* Document Types Filter */}
            <div className="space-y-3">
              <Label>Document Types (Optional)</Label>
              <div className="grid grid-cols-2 md:grid-cols-3 gap-3">
                {documentTypes.map((type) => (
                  <div key={type.value} className="flex items-center space-x-2">
                    <input
                      type="checkbox"
                      checked={selectedDocTypes.includes(type.value)}
                      onChange={(e) => handleDocTypeChange(type.value, e.target.checked)}
                      className="rounded"
                    />
                    <Label className="text-sm">{type.label}</Label>
                  </div>
                ))}
              </div>
              <p className="text-xs text-muted-foreground">
                Leave empty to analyze all document types
              </p>
            </div>

            {/* Confidence Threshold */}
            <div className="space-y-3">
              <Label>Minimum Confidence Threshold: {Math.round(confidenceThreshold[0] * 100)}%</Label>
              <Slider
                value={confidenceThreshold}
                onValueChange={setConfidenceThreshold}
                max={1}
                min={0.1}
                step={0.1}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">
                Only clauses with confidence above this threshold will be extracted
              </p>
            </div>

            {/* Max Clauses Per Category */}
            <div className="space-y-3">
              <Label>Max Clauses Per Category: {maxClauses[0]}</Label>
              <Slider
                value={maxClauses}
                onValueChange={setMaxClauses}
                max={50}
                min={1}
                step={1}
                className="w-full"
              />
              <p className="text-xs text-muted-foreground">
                Limit the number of clauses extracted per category to avoid overwhelming results
              </p>
            </div>

            {/* Include Existing Clauses */}
            <div className="flex items-center space-x-2">
              <Switch
                checked={watchedValues.includeExistingClauses}
                onCheckedChange={(checked) => setValue("includeExistingClauses", checked)}
              />
              <Label>Include existing clauses in the analysis</Label>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={buildLibrary.isPending}
              className="w-full"
              size="lg"
            >
              {buildLibrary.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Building Library...
                </>
              ) : (
                <>
                  <Database className="mr-2 h-4 w-4" />
                  Build Clause Library
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Build Results */}
      {buildResult && (
        <div className="space-y-6">
          {/* Summary Statistics */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <BarChart3 className="h-5 w-5 text-green-500" />
                Build Summary
              </CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                <div className="text-center">
                  <div className="text-2xl font-bold text-primary">{buildResult.totalDocumentsAnalyzed}</div>
                  <div className="text-sm text-muted-foreground">Documents Analyzed</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-green-600">{buildResult.totalClausesExtracted}</div>
                  <div className="text-sm text-muted-foreground">Clauses Extracted</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-blue-600">{buildResult.categoriesFound.length}</div>
                  <div className="text-sm text-muted-foreground">Categories Found</div>
                </div>
                <div className="text-center">
                  <div className="text-2xl font-bold text-purple-600">
                    {formatDuration(buildResult.processingDurationMs)}
                  </div>
                  <div className="text-sm text-muted-foreground">Processing Time</div>
                </div>
              </div>

              <Separator className="my-4" />

              <div className="space-y-2">
                <Label className="text-sm font-medium">Categories Found:</Label>
                <div className="flex flex-wrap gap-2">
                  {buildResult.categoriesFound.map((category) => (
                    <Badge key={category} variant="secondary">
                      {category}
                    </Badge>
                  ))}
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Extracted Clauses */}
          <Card>
            <CardHeader>
              <CardTitle className="flex items-center gap-2">
                <FileText className="h-5 w-5 text-blue-500" />
                Extracted Clauses ({buildResult.extractedClauses.length})
              </CardTitle>
            </CardHeader>
            <CardContent>
              <ScrollArea className="h-[600px]">
                <div className="space-y-4">
                  {buildResult.extractedClauses.map((clause, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{clause.category}</Badge>
                          <Badge variant="secondary">{clause.patternType}</Badge>
                          <span className={`text-sm font-medium ${getConfidenceColor(clause.confidence)}`}>
                            {Math.round(clause.confidence * 100)}% confidence
                          </span>
                        </div>
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(clause.content)}
                        >
                          <Copy className="h-4 w-4" />
                        </Button>
                      </div>
                      
                      <div className="bg-muted p-3 rounded text-sm">
                        {clause.content}
                      </div>
                      
                      <div className="text-xs text-muted-foreground">
                        Source: {clause.sourceDocument}
                      </div>
                    </div>
                  ))}
                </div>
              </ScrollArea>
            </CardContent>
          </Card>
        </div>
      )}
    </div>
  );
}
