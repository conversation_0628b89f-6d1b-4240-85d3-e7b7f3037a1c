"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Checkbox } from "@/components/ui/checkbox";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { ScrollArea } from "@/components/ui/scroll-area";
import { Separator } from "@/components/ui/separator";
import { 
  FileText, 
  Plus, 
  Download, 
  Copy, 
  Loader2,
  Link as LinkIcon,
  Clock
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useGenerateRelatedDocuments } from "@/hooks/use-document-automation";
import type { 
  RelatedDocumentType, 
  RelatedDocumentsResponse,
  GeneratedDocument 
} from "@/lib/types/document-automation";

const relatedDocumentsSchema = z.object({
  primaryDocumentId: z.string().min(1, "Primary document ID is required"),
  documentTypes: z.array(z.string()).min(1, "At least one document type must be selected"),
  additionalContent: z.string().optional(),
  autoPopulate: z.boolean().default(true),
});

type RelatedDocumentsFormData = z.infer<typeof relatedDocumentsSchema>;

const relatedDocumentTypes: { value: RelatedDocumentType; label: string; description: string }[] = [
  { 
    value: "schedule", 
    label: "Schedule", 
    description: "Detailed specifications, timelines, or deliverables" 
  },
  { 
    value: "exhibit", 
    label: "Exhibit", 
    description: "Supporting documents, forms, or reference materials" 
  },
  { 
    value: "addendum", 
    label: "Addendum", 
    description: "Additional terms or modifications to the main agreement" 
  },
  { 
    value: "amendment", 
    label: "Amendment", 
    description: "Formal changes to existing contract terms" 
  },
  { 
    value: "appendix", 
    label: "Appendix", 
    description: "Supplementary information or data" 
  },
];

interface RelatedDocumentsGeneratorProps {
  primaryDocumentId?: string;
  onSuccess?: (result: RelatedDocumentsResponse) => void;
  onError?: (error: Error) => void;
}

export function RelatedDocumentsGenerator({
  primaryDocumentId = "",
  onSuccess,
  onError,
}: RelatedDocumentsGeneratorProps) {
  const [selectedTypes, setSelectedTypes] = useState<RelatedDocumentType[]>([]);
  const [generatedDocuments, setGeneratedDocuments] = useState<RelatedDocumentsResponse | null>(null);

  const { toast } = useToast();
  const generateRelated = useGenerateRelatedDocuments();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<RelatedDocumentsFormData>({
    resolver: zodResolver(relatedDocumentsSchema),
    defaultValues: {
      primaryDocumentId,
      autoPopulate: true,
    },
  });

  const handleTypeChange = (type: RelatedDocumentType, checked: boolean) => {
    let updatedTypes: RelatedDocumentType[];
    if (checked) {
      updatedTypes = [...selectedTypes, type];
    } else {
      updatedTypes = selectedTypes.filter((t) => t !== type);
    }
    setSelectedTypes(updatedTypes);
    setValue("documentTypes", updatedTypes);
  };

  const onSubmit = async (data: RelatedDocumentsFormData) => {
    try {
      const result = await generateRelated.mutateAsync({
        primaryDocumentId: data.primaryDocumentId,
        documentTypes: selectedTypes,
        additionalContent: data.additionalContent || undefined,
        autoPopulate: data.autoPopulate,
      });

      setGeneratedDocuments(result);
      toast({
        title: "Documents Generated Successfully",
        description: `Generated ${result.documents.length} related documents.`,
      });

      onSuccess?.(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to generate related documents";
      toast({
        title: "Generation Failed",
        description: errorMessage,
        variant: "destructive",
      });
      onError?.(error as Error);
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied to Clipboard",
        description: "Document content has been copied to your clipboard.",
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard.",
        variant: "destructive",
      });
    }
  };

  const formatDuration = (ms: number) => {
    const seconds = Math.round(ms / 1000);
    return `${seconds}s`;
  };

  return (
    <div className="space-y-6">
      {/* Generation Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <LinkIcon className="h-5 w-5 text-primary" />
            Generate Related Documents
          </CardTitle>
          <CardDescription>
            Automatically generate ancillary documents (schedules, exhibits, etc.) based on your primary agreement.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
            {/* Primary Document ID */}
            <div className="space-y-2">
              <Label htmlFor="primaryDocumentId">Primary Document ID *</Label>
              <Input
                {...register("primaryDocumentId")}
                placeholder="Enter the ID of your primary document"
              />
              {errors.primaryDocumentId && (
                <p className="text-sm text-destructive">{errors.primaryDocumentId.message}</p>
              )}
            </div>

            {/* Document Types Selection */}
            <div className="space-y-3">
              <Label>Document Types to Generate *</Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-3">
                {relatedDocumentTypes.map((type) => (
                  <div key={type.value} className="flex items-start space-x-3 p-3 border rounded-lg">
                    <Checkbox
                      checked={selectedTypes.includes(type.value)}
                      onCheckedChange={(checked) => handleTypeChange(type.value, checked as boolean)}
                    />
                    <div className="flex-1 space-y-1">
                      <div className="font-medium">{type.label}</div>
                      <div className="text-sm text-muted-foreground">{type.description}</div>
                    </div>
                  </div>
                ))}
              </div>
              {errors.documentTypes && (
                <p className="text-sm text-destructive">{errors.documentTypes.message}</p>
              )}
            </div>

            {/* Additional Content */}
            <div className="space-y-2">
              <Label htmlFor="additionalContent">Additional Content</Label>
              <Textarea
                {...register("additionalContent")}
                placeholder="Specify any additional content or data that should be included in the related documents..."
                className="min-h-[100px]"
              />
            </div>

            {/* Auto-populate Option */}
            <div className="flex items-center space-x-2">
              <Switch
                {...register("autoPopulate")}
                defaultChecked
                onCheckedChange={(checked) => setValue("autoPopulate", checked)}
              />
              <Label>Auto-populate from primary document content</Label>
            </div>

            {/* Submit Button */}
            <Button
              type="submit"
              disabled={generateRelated.isPending || selectedTypes.length === 0}
              className="w-full"
              size="lg"
            >
              {generateRelated.isPending ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Generating Documents...
                </>
              ) : (
                <>
                  <Plus className="mr-2 h-4 w-4" />
                  Generate Related Documents
                </>
              )}
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Generated Documents */}
      {generatedDocuments && (
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <FileText className="h-5 w-5 text-green-500" />
              Generated Documents ({generatedDocuments.documents.length})
            </CardTitle>
            <div className="flex items-center gap-4 text-sm text-muted-foreground">
              <div className="flex items-center gap-1">
                <Clock className="h-4 w-4" />
                Generated in {formatDuration(generatedDocuments.metadata.generationDurationMs)}
              </div>
              <div>
                Primary Document: {generatedDocuments.metadata.primaryDocumentId}
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <ScrollArea className="h-[600px]">
              <div className="space-y-6">
                {generatedDocuments.documents.map((document, index) => (
                  <div key={index} className="border rounded-lg p-4 space-y-4">
                    <div className="flex items-center justify-between">
                      <div className="space-y-1">
                        <h3 className="font-semibold text-lg">{document.title}</h3>
                        <div className="flex items-center gap-2">
                          <Badge variant="outline">{document.type}</Badge>
                          {document.metadata.autoPopulated && (
                            <Badge variant="secondary">Auto-populated</Badge>
                          )}
                          {document.metadata.hasAdditionalContent && (
                            <Badge variant="secondary">Additional Content</Badge>
                          )}
                        </div>
                      </div>
                      <div className="flex gap-2">
                        <Button
                          size="sm"
                          variant="outline"
                          onClick={() => copyToClipboard(document.content)}
                        >
                          <Copy className="h-4 w-4" />
                          Copy
                        </Button>
                        <Button size="sm" variant="outline">
                          <Download className="h-4 w-4" />
                          Download
                        </Button>
                      </div>
                    </div>
                    
                    <Separator />
                    
                    <div className="space-y-2">
                      <Label className="text-sm font-medium">Document Content:</Label>
                      <div className="bg-muted p-4 rounded-lg">
                        <pre className="whitespace-pre-wrap text-sm font-mono">
                          {document.content}
                        </pre>
                      </div>
                    </div>

                    <div className="text-xs text-muted-foreground">
                      Generated from: {document.metadata.generatedFrom}
                    </div>
                  </div>
                ))}
              </div>
            </ScrollArea>
          </CardContent>
        </Card>
      )}
    </div>
  );
}
