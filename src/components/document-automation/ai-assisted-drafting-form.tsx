"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Loader2, Plus, X, FileText, Sparkles } from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useAIAssistedDrafting } from "@/hooks/use-document-automation";
import type { DocumentType } from "@/lib/types/document-automation";

const draftingSchema = z.object({
  documentType: z.string().min(1, "Document type is required"),
  prompt: z.string().min(10, "Prompt must be at least 10 characters"),
  keyTerms: z.array(z.string()).optional(),
  requiredClauses: z.array(z.string()).optional(),
  context: z.string().optional(),
  organizationPreferences: z.string().optional(),
  useClauseLibrary: z.boolean().default(true),
  includeDisclaimers: z.boolean().default(true),
  jurisdiction: z.string().optional(),
});

type DraftingFormData = z.infer<typeof draftingSchema>;

const documentTypes: { value: DocumentType; label: string }[] = [
  { value: "contract", label: "Contract" },
  { value: "nda", label: "Non-Disclosure Agreement" },
  { value: "msa", label: "Master Service Agreement" },
  { value: "sow", label: "Statement of Work" },
  { value: "amendment", label: "Amendment" },
  { value: "addendum", label: "Addendum" },
  { value: "schedule", label: "Schedule" },
  { value: "exhibit", label: "Exhibit" },
  { value: "privacy_policy", label: "Privacy Policy" },
  { value: "terms_of_service", label: "Terms of Service" },
  { value: "employment_agreement", label: "Employment Agreement" },
  { value: "lease_agreement", label: "Lease Agreement" },
  { value: "other", label: "Other" },
];

interface AIAssistedDraftingFormProps {
  onSuccess?: (result: any) => void;
  onError?: (error: Error) => void;
}

export function AIAssistedDraftingForm({ onSuccess, onError }: AIAssistedDraftingFormProps) {
  const [keyTerms, setKeyTerms] = useState<string[]>([]);
  const [requiredClauses, setRequiredClauses] = useState<string[]>([]);
  const [newKeyTerm, setNewKeyTerm] = useState("");
  const [newClause, setNewClause] = useState("");

  const { toast } = useToast();
  const aiDrafting = useAIAssistedDrafting();

  const {
    register,
    handleSubmit,
    formState: { errors },
    setValue,
    watch,
  } = useForm<DraftingFormData>({
    resolver: zodResolver(draftingSchema),
    defaultValues: {
      useClauseLibrary: true,
      includeDisclaimers: true,
    },
  });

  const selectedDocumentType = watch("documentType");

  const addKeyTerm = () => {
    if (newKeyTerm.trim() && !keyTerms.includes(newKeyTerm.trim())) {
      const updatedTerms = [...keyTerms, newKeyTerm.trim()];
      setKeyTerms(updatedTerms);
      setValue("keyTerms", updatedTerms);
      setNewKeyTerm("");
    }
  };

  const removeKeyTerm = (term: string) => {
    const updatedTerms = keyTerms.filter((t) => t !== term);
    setKeyTerms(updatedTerms);
    setValue("keyTerms", updatedTerms);
  };

  const addClause = () => {
    if (newClause.trim() && !requiredClauses.includes(newClause.trim())) {
      const updatedClauses = [...requiredClauses, newClause.trim()];
      setRequiredClauses(updatedClauses);
      setValue("requiredClauses", updatedClauses);
      setNewClause("");
    }
  };

  const removeClause = (clause: string) => {
    const updatedClauses = requiredClauses.filter((c) => c !== clause);
    setRequiredClauses(updatedClauses);
    setValue("requiredClauses", updatedClauses);
  };

  const onSubmit = async (data: DraftingFormData) => {
    try {
      const result = await aiDrafting.mutateAsync({
        documentType: data.documentType as DocumentType,
        draftingPrompt: {
          prompt: data.prompt,
          keyTerms: keyTerms.length > 0 ? keyTerms : undefined,
          requiredClauses: requiredClauses.length > 0 ? requiredClauses : undefined,
          context: data.context || undefined,
        },
        organizationPreferences: data.organizationPreferences || undefined,
        useClauseLibrary: data.useClauseLibrary,
        includeDisclaimers: data.includeDisclaimers,
        jurisdiction: data.jurisdiction || undefined,
      });

      toast({
        title: "Document Generated Successfully",
        description: `Your ${data.documentType} has been generated using AI assistance.`,
      });

      onSuccess?.(result);
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to generate document";
      toast({
        title: "Generation Failed",
        description: errorMessage,
        variant: "destructive",
      });
      onError?.(error as Error);
    }
  };

  return (
    <Card className="w-full max-w-4xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Sparkles className="h-5 w-5 text-primary" />
          AI-Assisted Document Drafting
        </CardTitle>
        <CardDescription>
          Generate legal documents using AI with intelligent clause suggestions and organization preferences.
        </CardDescription>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onSubmit)} className="space-y-6">
          {/* Document Type Selection */}
          <div className="space-y-2">
            <Label htmlFor="documentType">Document Type *</Label>
            <Select onValueChange={(value) => setValue("documentType", value)}>
              <SelectTrigger>
                <SelectValue placeholder="Select document type" />
              </SelectTrigger>
              <SelectContent>
                {documentTypes.map((type) => (
                  <SelectItem key={type.value} value={type.value}>
                    {type.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.documentType && (
              <p className="text-sm text-destructive">{errors.documentType.message}</p>
            )}
          </div>

          {/* Main Prompt */}
          <div className="space-y-2">
            <Label htmlFor="prompt">Drafting Instructions *</Label>
            <Textarea
              {...register("prompt")}
              placeholder="Describe what you want to create. Be specific about the purpose, parties involved, and key requirements..."
              className="min-h-[100px]"
            />
            {errors.prompt && (
              <p className="text-sm text-destructive">{errors.prompt.message}</p>
            )}
          </div>

          {/* Key Terms */}
          <div className="space-y-2">
            <Label>Key Terms</Label>
            <div className="flex gap-2">
              <Input
                value={newKeyTerm}
                onChange={(e) => setNewKeyTerm(e.target.value)}
                placeholder="Add key terms..."
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addKeyTerm())}
              />
              <Button type="button" onClick={addKeyTerm} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {keyTerms.map((term) => (
                <Badge key={term} variant="secondary" className="flex items-center gap-1">
                  {term}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removeKeyTerm(term)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Required Clauses */}
          <div className="space-y-2">
            <Label>Required Clauses</Label>
            <div className="flex gap-2">
              <Input
                value={newClause}
                onChange={(e) => setNewClause(e.target.value)}
                placeholder="Add required clauses..."
                onKeyPress={(e) => e.key === "Enter" && (e.preventDefault(), addClause())}
              />
              <Button type="button" onClick={addClause} size="sm">
                <Plus className="h-4 w-4" />
              </Button>
            </div>
            <div className="flex flex-wrap gap-2">
              {requiredClauses.map((clause) => (
                <Badge key={clause} variant="outline" className="flex items-center gap-1">
                  {clause}
                  <X
                    className="h-3 w-3 cursor-pointer"
                    onClick={() => removeClause(clause)}
                  />
                </Badge>
              ))}
            </div>
          </div>

          {/* Additional Context */}
          <div className="space-y-2">
            <Label htmlFor="context">Additional Context</Label>
            <Textarea
              {...register("context")}
              placeholder="Provide any additional context or specific requirements..."
              className="min-h-[80px]"
            />
          </div>

          {/* Organization Preferences */}
          <div className="space-y-2">
            <Label htmlFor="organizationPreferences">Organization Preferences</Label>
            <Textarea
              {...register("organizationPreferences")}
              placeholder="Specify your organization's preferred language style, standard clauses, or other preferences..."
              className="min-h-[80px]"
            />
          </div>

          {/* Options */}
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="flex items-center space-x-2">
              <Switch
                {...register("useClauseLibrary")}
                defaultChecked
                onCheckedChange={(checked) => setValue("useClauseLibrary", checked)}
              />
              <Label>Use Organization Clause Library</Label>
            </div>
            <div className="flex items-center space-x-2">
              <Switch
                {...register("includeDisclaimers")}
                defaultChecked
                onCheckedChange={(checked) => setValue("includeDisclaimers", checked)}
              />
              <Label>Include Standard Disclaimers</Label>
            </div>
          </div>

          {/* Jurisdiction */}
          <div className="space-y-2">
            <Label htmlFor="jurisdiction">Jurisdiction</Label>
            <Input
              {...register("jurisdiction")}
              placeholder="e.g., California, New York, Delaware..."
            />
          </div>

          {/* Submit Button */}
          <Button
            type="submit"
            disabled={aiDrafting.isPending}
            className="w-full"
            size="lg"
          >
            {aiDrafting.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Generating Document...
              </>
            ) : (
              <>
                <FileText className="mr-2 h-4 w-4" />
                Generate Document
              </>
            )}
          </Button>
        </form>
      </CardContent>
    </Card>
  );
}
