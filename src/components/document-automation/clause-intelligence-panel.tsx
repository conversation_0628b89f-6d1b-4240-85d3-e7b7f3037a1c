"use client";

import { useState, useEffect } from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Label } from "@/components/ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Switch } from "@/components/ui/switch";
import { Badge } from "@/components/ui/badge";
import { Separator } from "@/components/ui/separator";
import { ScrollArea } from "@/components/ui/scroll-area";
import { 
  Brain, 
  Lightbulb, 
  AlertTriangle, 
  CheckCircle, 
  Copy, 
  Plus,
  Loader2,
  FileText
} from "lucide-react";
import { useToast } from "@/hooks/use-toast";
import { useClauseIntelligence } from "@/hooks/use-document-automation";
import type { 
  ClauseIntelligenceResponse, 
  SuggestedClause, 
  AutoPopulationSuggestion,
  MissingClause 
} from "@/lib/types/document-automation";

interface ClauseIntelligencePanelProps {
  documentType?: string;
  currentContent?: string;
  onClauseInsert?: (clause: string) => void;
  onSuggestionApply?: (suggestion: AutoPopulationSuggestion) => void;
}

export function ClauseIntelligencePanel({
  documentType: initialDocumentType = "",
  currentContent: initialContent = "",
  onClauseInsert,
  onSuggestionApply,
}: ClauseIntelligencePanelProps) {
  const [documentType, setDocumentType] = useState(initialDocumentType);
  const [currentContent, setCurrentContent] = useState(initialContent);
  const [sectionType, setSectionType] = useState("");
  const [context, setContext] = useState("");
  const [includeOrgClauses, setIncludeOrgClauses] = useState(true);
  const [intelligence, setIntelligence] = useState<ClauseIntelligenceResponse | null>(null);

  const { toast } = useToast();
  const clauseIntelligence = useClauseIntelligence();

  // Update state when props change
  useEffect(() => {
    setDocumentType(initialDocumentType);
  }, [initialDocumentType]);

  useEffect(() => {
    setCurrentContent(initialContent);
  }, [initialContent]);

  const handleAnalyze = async () => {
    if (!documentType || !currentContent) {
      toast({
        title: "Missing Information",
        description: "Please provide document type and content to analyze.",
        variant: "destructive",
      });
      return;
    }

    try {
      const result = await clauseIntelligence.mutateAsync({
        documentType,
        currentContent,
        sectionType: sectionType || undefined,
        context: context || undefined,
        includeOrgClauses,
      });

      setIntelligence(result);
      toast({
        title: "Analysis Complete",
        description: `Found ${result.suggestedClauses.length} clause suggestions and ${result.missingClauses.length} missing clauses.`,
      });
    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : "Failed to analyze clauses";
      toast({
        title: "Analysis Failed",
        description: errorMessage,
        variant: "destructive",
      });
    }
  };

  const copyToClipboard = async (text: string) => {
    try {
      await navigator.clipboard.writeText(text);
      toast({
        title: "Copied to Clipboard",
        description: "Clause content has been copied to your clipboard.",
      });
    } catch (error) {
      toast({
        title: "Copy Failed",
        description: "Failed to copy to clipboard.",
        variant: "destructive",
      });
    }
  };

  const getConfidenceColor = (confidence: number) => {
    if (confidence >= 0.8) return "text-green-600";
    if (confidence >= 0.6) return "text-yellow-600";
    return "text-red-600";
  };

  const getImportanceColor = (importance: string) => {
    switch (importance) {
      case "critical": return "text-red-600";
      case "important": return "text-yellow-600";
      default: return "text-blue-600";
    }
  };

  return (
    <div className="space-y-6">
      {/* Analysis Form */}
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Brain className="h-5 w-5 text-primary" />
            Clause Intelligence
          </CardTitle>
          <CardDescription>
            Get intelligent clause suggestions and identify missing clauses for your document.
          </CardDescription>
        </CardHeader>
        <CardContent className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="space-y-2">
              <Label htmlFor="documentType">Document Type</Label>
              <Input
                value={documentType}
                onChange={(e) => setDocumentType(e.target.value)}
                placeholder="e.g., contract, nda, msa..."
              />
            </div>
            <div className="space-y-2">
              <Label htmlFor="sectionType">Section Type (Optional)</Label>
              <Input
                value={sectionType}
                onChange={(e) => setSectionType(e.target.value)}
                placeholder="e.g., payment, termination, liability..."
              />
            </div>
          </div>

          <div className="space-y-2">
            <Label htmlFor="currentContent">Current Document Content</Label>
            <Textarea
              value={currentContent}
              onChange={(e) => setCurrentContent(e.target.value)}
              placeholder="Paste your current document content here..."
              className="min-h-[120px]"
            />
          </div>

          <div className="space-y-2">
            <Label htmlFor="context">Context (Optional)</Label>
            <Input
              value={context}
              onChange={(e) => setContext(e.target.value)}
              placeholder="Additional context about the document..."
            />
          </div>

          <div className="flex items-center space-x-2">
            <Switch
              checked={includeOrgClauses}
              onCheckedChange={setIncludeOrgClauses}
            />
            <Label>Include Organization-Specific Clauses</Label>
          </div>

          <Button
            onClick={handleAnalyze}
            disabled={clauseIntelligence.isPending || !documentType || !currentContent}
            className="w-full"
          >
            {clauseIntelligence.isPending ? (
              <>
                <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                Analyzing...
              </>
            ) : (
              <>
                <Brain className="mr-2 h-4 w-4" />
                Analyze Clauses
              </>
            )}
          </Button>
        </CardContent>
      </Card>

      {/* Results */}
      {intelligence && (
        <div className="space-y-6">
          {/* Suggested Clauses */}
          {intelligence.suggestedClauses.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Lightbulb className="h-5 w-5 text-yellow-500" />
                  Suggested Clauses ({intelligence.suggestedClauses.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <ScrollArea className="h-[400px]">
                  <div className="space-y-4">
                    {intelligence.suggestedClauses.map((clause, index) => (
                      <div key={index} className="border rounded-lg p-4 space-y-3">
                        <div className="flex items-center justify-between">
                          <div className="flex items-center gap-2">
                            <Badge variant="outline">{clause.type}</Badge>
                            <Badge variant="secondary">{clause.source}</Badge>
                            {clause.confidence && (
                              <span className={`text-sm font-medium ${getConfidenceColor(clause.confidence)}`}>
                                {Math.round(clause.confidence * 100)}% confidence
                              </span>
                            )}
                          </div>
                          <div className="flex gap-2">
                            <Button
                              size="sm"
                              variant="outline"
                              onClick={() => copyToClipboard(clause.content)}
                            >
                              <Copy className="h-4 w-4" />
                            </Button>
                            {onClauseInsert && (
                              <Button
                                size="sm"
                                onClick={() => onClauseInsert(clause.content)}
                              >
                                <Plus className="h-4 w-4" />
                                Insert
                              </Button>
                            )}
                          </div>
                        </div>
                        <p className="text-sm text-muted-foreground bg-muted p-3 rounded">
                          {clause.content}
                        </p>
                        {clause.explanation && (
                          <p className="text-xs text-muted-foreground italic">
                            {clause.explanation}
                          </p>
                        )}
                      </div>
                    ))}
                  </div>
                </ScrollArea>
              </CardContent>
            </Card>
          )}

          {/* Auto-Population Suggestions */}
          {intelligence.autoPopulationSuggestions.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <CheckCircle className="h-5 w-5 text-green-500" />
                  Auto-Population Suggestions ({intelligence.autoPopulationSuggestions.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {intelligence.autoPopulationSuggestions.map((suggestion, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <span className="font-medium">{suggestion.sectionName}</span>
                          <span className={`text-sm ${getConfidenceColor(suggestion.confidence)}`}>
                            {Math.round(suggestion.confidence * 100)}% confidence
                          </span>
                        </div>
                        <div className="flex gap-2">
                          <Button
                            size="sm"
                            variant="outline"
                            onClick={() => copyToClipboard(suggestion.suggestedContent)}
                          >
                            <Copy className="h-4 w-4" />
                          </Button>
                          {onSuggestionApply && (
                            <Button
                              size="sm"
                              onClick={() => onSuggestionApply(suggestion)}
                            >
                              Apply
                            </Button>
                          )}
                        </div>
                      </div>
                      <p className="text-sm text-muted-foreground bg-muted p-3 rounded">
                        {suggestion.suggestedContent}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}

          {/* Missing Clauses */}
          {intelligence.missingClauses.length > 0 && (
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <AlertTriangle className="h-5 w-5 text-orange-500" />
                  Missing Clauses ({intelligence.missingClauses.length})
                </CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-3">
                  {intelligence.missingClauses.map((missing, index) => (
                    <div key={index} className="border rounded-lg p-4 space-y-2">
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{missing.type}</Badge>
                        <span className={`text-sm font-medium ${getImportanceColor(missing.importance)}`}>
                          {missing.importance}
                        </span>
                      </div>
                      <p className="text-sm text-muted-foreground">
                        {missing.description}
                      </p>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          )}
        </div>
      )}
    </div>
  );
}
