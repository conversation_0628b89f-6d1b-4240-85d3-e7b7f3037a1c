"use client";

import { ReactNode } from "react";
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import { <PERSON>, Spark<PERSON>, Zap, Crown } from "lucide-react";
import { useDocumentAutomationFeatures } from "@/hooks/use-document-automation";

interface DocumentAutomationFeatureGuardProps {
  children: ReactNode;
  feature: "ai-drafting" | "clause-intelligence" | "related-documents" | "clause-library";
  fallback?: ReactNode;
}

export function DocumentAutomationFeatureGuard({
  children,
  feature,
  fallback,
}: DocumentAutomationFeatureGuardProps) {
  const features = useDocumentAutomationFeatures();

  const featureConfig = {
    "ai-drafting": {
      available: features.hasAIDrafting,
      title: "AI-Assisted Document Drafting",
      description: "Generate legal documents using advanced AI technology",
      icon: Sparkles,
    },
    "clause-intelligence": {
      available: features.hasClauseIntelligence,
      title: "Clause Intelligence",
      description: "Get intelligent clause suggestions and recommendations",
      icon: Zap,
    },
    "related-documents": {
      available: features.hasRelatedDocuments,
      title: "Related Document Generation",
      description: "Automatically generate schedules, exhibits, and addendums",
      icon: Sparkles,
    },
    "clause-library": {
      available: features.hasClauseLibraryBuilder,
      title: "Clause Library Builder",
      description: "Auto-extract and categorize clauses from your document corpus",
      icon: Zap,
    },
  };

  const config = featureConfig[feature];

  if (config.available) {
    return <>{children}</>;
  }

  if (fallback) {
    return <>{fallback}</>;
  }

  const Icon = config.icon;

  return (
    <Card className="border-dashed border-2">
      <CardContent className="flex flex-col items-center justify-center p-8 text-center">
        <div className="w-16 h-16 rounded-full bg-primary/10 flex items-center justify-center mb-4">
          <Lock className="h-8 w-8 text-primary/60" />
        </div>
        
        <div className="space-y-2 mb-4">
          <h3 className="text-lg font-semibold flex items-center gap-2 justify-center">
            <Icon className="h-5 w-5" />
            {config.title}
          </h3>
          <p className="text-muted-foreground max-w-md">
            {config.description}
          </p>
        </div>

        <div className="flex items-center gap-2 mb-4">
          <Badge variant="secondary" className="flex items-center gap-1">
            <Crown className="h-3 w-3" />
            Pro Feature
          </Badge>
        </div>

        <div className="space-y-2">
          <p className="text-sm text-muted-foreground">
            Upgrade to Pro to unlock this powerful document automation feature.
          </p>
          <Button className="mt-4">
            Upgrade to Pro
          </Button>
        </div>
      </CardContent>
    </Card>
  );
}
