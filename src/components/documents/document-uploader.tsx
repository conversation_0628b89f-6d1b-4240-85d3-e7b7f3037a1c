"use client";

import { useCallback, useState } from "react";
import { useDropzone } from "react-dropzone";
import { Progress } from "@/components/ui/progress";
import { Button } from "@/components/ui/button";
import { cn } from "@/lib/utils";
import { useToast } from "@/hooks/use-toast";
import { Upload } from "lucide-react";
import { getStoredAuth } from "@/lib/auth/auth-service";
import { useAuth } from "@/lib/auth/auth-context";

interface UploadResult {
  documentId: string;
}

interface DocumentUploaderProps {
  onUploadComplete?: (result: UploadResult) => void;
  onError?: (error: Error) => void;
  accept?: Record<string, string[]>;
  maxSize?: number;
}

export function DocumentUploader({ 
  onUploadComplete,
  onError,
  accept = {
    'application/pdf': ['.pdf'],
    'text/plain': ['.txt'],
    'application/msword': ['.doc'],
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document': ['.docx']
  },
  maxSize = 10 * 1024 * 1024 // 10MB
}: DocumentUploaderProps) {
  const [uploading, setUploading] = useState(false);
  const [progress, setProgress] = useState(0);
  const { toast } = useToast();
  const { user } = useAuth();

  const onDrop = useCallback(async (acceptedFiles: File[]) => {
    const file = acceptedFiles[0];
    if (!file) return;

    try {
      setUploading(true);
      setProgress(0);

      const formData = new FormData();
      formData.append("file", file);

      const xhr = new XMLHttpRequest();
      
      // Track upload progress
      xhr.upload.onprogress = (event) => {
        if (event.lengthComputable) {
          const percentComplete = Math.round((event.loaded / event.total) * 100);
          setProgress(percentComplete);
        }
      };

      // Get auth credentials
      const { accessToken, organizationId } = getStoredAuth();

      if (!accessToken) {
        throw new Error("Authentication required");
      }

      const response: UploadResult = await new Promise((resolve, reject) => {
        xhr.open("POST", "/api/documents/upload");
        
        // Add authentication headers
        xhr.setRequestHeader("Authorization", `Bearer ${accessToken}`);
        
        // Set organization ID header
        if (organizationId) {
          xhr.setRequestHeader("X-Organization-Id", organizationId);
        } else if (user?.organizationId) {
          xhr.setRequestHeader("X-Organization-Id", user.organizationId);
        } else {
          reject(new Error("Organization ID not found"));
          return;
        }

        xhr.onload = () => {
          if (xhr.status >= 200 && xhr.status < 300) {
            try {
              const response = JSON.parse(xhr.responseText);
              resolve(response);
            } catch {
              reject(new Error("Invalid response format"));
            }
          } else {
            reject(new Error(`Upload failed with status ${xhr.status}`));
          }
        };

        xhr.onerror = () => {
          reject(new Error("Upload failed"));
        };

        xhr.onabort = () => {
          reject(new Error("Upload cancelled"));
        };

        xhr.send(formData);
      });

      toast({
        title: "Upload Complete",
        description: "Your document has been uploaded successfully."
      });

      onUploadComplete?.(response);
    } catch (err) {
      const error = err as Error;
      toast({
        title: "Upload Failed",
        description: error.message,
        variant: "destructive"
      });
      onError?.(error);
    } finally {
      setUploading(false);
      setProgress(0);
    }
  }, [onUploadComplete, onError, toast, user]);

  const { getRootProps, getInputProps, isDragActive, open } = useDropzone({
    onDrop,
    accept,
    maxSize,
    multiple: false,
    noClick: true,
    disabled: uploading
  });

  return (
    <div className="space-y-4">
      <div
        {...getRootProps()}
        className={cn(
          "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
          isDragActive ? "border-primary bg-accent" : "border-muted-foreground/25",
          uploading && "pointer-events-none opacity-50"
        )}
      >
        <input {...getInputProps()} />
        <div className="flex flex-col items-center gap-4">
          <Upload className={cn(
            "h-8 w-8",
            uploading ? "text-muted-foreground/50" : "text-muted-foreground"
          )} />
          {isDragActive ? (
            <p>Drop the file here...</p>
          ) : (
            <>
              <div className="space-y-2">
                <p className="text-sm">
                  {uploading 
                    ? "Uploading document..."
                    : "Drag & drop a file here, or"
                  }
                </p>
                <Button 
                  type="button" 
                  variant="outline"
                  onClick={open}
                  disabled={uploading}
                >
                  Select File
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Supported formats: PDF, DOC, DOCX, TXT (max {maxSize / (1024 * 1024)}MB)
              </p>
            </>
          )}
        </div>
      </div>

      {uploading && progress > 0 && (
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>Uploading document...</span>
            <span>{progress}%</span>
          </div>
          <Progress value={progress} className="h-2" />
        </div>
      )}
    </div>
  );
}
