import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON>alogDescription,
  <PERSON><PERSON><PERSON><PERSON>er,
  DialogTitle,
} from "@/components/ui/dialog";
import { Button } from "@/components/ui/button";
import { FileText, Download, Clock, Tag } from "lucide-react";
import { formatBytes } from "@/lib/utils";
import { DocumentMetadata } from "@/lib/services/document-service";
import { getBaseUrl } from "@/lib/config"; // Import getBaseUrl
import { DocumentPreview } from "./document-preview";

interface DocumentDetailsModalProps {
  open: boolean;
  onOpenChange: (open: boolean) => void;
  document: DocumentMetadata | null;
}

export function DocumentDetailsModal({
  open,
  onOpenChange,
  document,
}: DocumentDetailsModalProps) {
  if (!document) return null;

  // Construct download URL directly
  const downloadUrl = `${getBaseUrl()}/api/documents/${document.id}/download`;

  const metadata = [
    {
      label: "File Name",
      value: document.filename, // Use filename from DocumentMetadata
      icon: FileText,
    },
    {
      label: "File Size",
      value: formatBytes(document.size ?? 0), // Handle potentially undefined size
      icon: FileText,
    },
    {
      label: "File Type",
      value: document.type ?? "Unknown", // Use type from DocumentMetadata
      icon: Tag,
    },
    {
      label: "Created",
      value: new Date(document.createdAt).toLocaleString(), // createdAt exists
      icon: Clock,
    },
    // Removed Version item as versionId is not available
  ];

  // Add optional metadata if available
  // pageCount doesn't exist on DocumentMetadata, remove or find alternative
  // Removing for now

  // language doesn't exist on DocumentMetadata, remove or find alternative
  // Removing for now

  // confidence doesn't exist on DocumentMetadata, remove or find alternative
  // Removing for now

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-2xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle>Document Details</DialogTitle>
          <DialogDescription>
            View and download document information
          </DialogDescription>
        </DialogHeader>

        <div className="grid gap-6 py-4">
          {/* Document Preview */}
          <div className="aspect-[16/10] max-h-[400px] w-full overflow-hidden rounded-lg border">
            <DocumentPreview document={document} />
          </div>

          {/* Metadata Grid */}
          <div className="grid gap-4 sm:grid-cols-2">
            {metadata.map(({ label, value, icon: Icon }) => (
              <div
                key={label}
                className="flex items-center gap-2 rounded-lg border p-3"
              >
                <Icon className="h-4 w-4 text-muted-foreground shrink-0" />
                <div className="min-w-0">
                  <p className="text-sm text-muted-foreground">{label}</p>
                  <p className="font-medium truncate">{value ?? 'N/A'}</p>
                </div>
              </div>
            ))}
          </div>

          {/* Download Button */}
          <div className="flex justify-end">
            <Button asChild>
              <a href={downloadUrl} download={document.filename}>
                <Download className="mr-2 h-4 w-4" />
                Download
              </a>
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
}