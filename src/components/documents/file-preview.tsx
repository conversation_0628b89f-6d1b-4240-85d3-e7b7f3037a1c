"use client";

import { useState } from "react";
import { Di<PERSON>, DialogContent, DialogTrigger } from "@/components/ui/dialog";
import { But<PERSON> } from "@/components/ui/button";
import { Eye, FileText, Image as ImageIcon } from "lucide-react";
import Image from "next/image";

interface FilePreviewProps {
	file: File;
	className?: string;
}

export function FilePreview({ file, className }: FilePreviewProps) {
	const [preview, setPreview] = useState<string | null>(null);

	// Generate preview URL when file changes
	useState(() => {
		if (!file) return;

		if (file.type.startsWith("image/")) {
			const url = URL.createObjectURL(file);
			setPreview(url);
			return () => URL.revokeObjectURL(url);
		}
	});

	const isImage = file.type.startsWith("image/");

	return (
		<Dialog>
			<DialogTrigger asChild>
				<Button
					variant="ghost"
					size="sm"
					className={`flex items-center gap-2 ${className}`}
				>
					<Eye className="h-4 w-4" />
					Preview
				</Button>
			</DialogTrigger>
			<DialogContent className="max-w-3xl">
				<div className="aspect-video w-full overflow-hidden rounded-lg border bg-muted">
					{isImage && preview ? (
						<div className="relative h-full w-full">
							<Image
								src={preview}
								alt={file.name}
								fill
								className="object-contain"
								sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
								priority={true}
							/>
						</div>
					) : (
						<div className="flex h-full flex-col items-center justify-center">
							{isImage ? (
								<ImageIcon className="h-16 w-16 text-muted-foreground" />
							) : (
								<FileText className="h-16 w-16 text-muted-foreground" />
							)}
							<p className="mt-4 text-sm text-muted-foreground">
								{isImage
									? "Unable to generate preview"
									: "Preview not available for this file type"}
							</p>
						</div>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
