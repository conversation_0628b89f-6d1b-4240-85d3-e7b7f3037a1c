"use client";

import { useState, useCallback } from "react";
import { Card } from "@/components/ui/card";
import { Button } from "@/components/ui/button";
import { useToast } from "@/hooks/use-toast";
import { useCreditUsage } from "@/hooks/use-credit-usage";
import { FeatureCostDisplay } from "@/components/subscription/feature-cost-display";
import {
	useAnalyzeDocument,
	useAnalysisStatus,
	type ProcessingStatus,
} from "@/lib/services/document-analysis-service";
import { StatusDisplay } from "./status-display";
import { AnalysisOptions } from "./analysis-options";
import { AnalysisResult } from "./analysis-result";

interface DocumentProcessorProps {
	documentId: string;
	onComplete?: (result: ProcessingStatus) => void;
	onError?: (error: Error) => void;
}

export function DocumentProcessor({
	documentId,
	onComplete,
	onError,
}: DocumentProcessorProps) {
	const [isStarted, setIsStarted] = useState(false);
	const [options, setOptions] = useState({
		includeAnnotations: true,
		generateReport: true,
	});
	const { toast } = useToast();
	const { getCost } = useCreditUsage();

	// Mutations and queries
	const analyze = useAnalyzeDocument();
	const { data: status, error: statusError } = useAnalysisStatus(
		documentId,
		isStarted ? 3000 : undefined
	);

	// Start analysis with credit usage
	const handleStartAnalysis = useCallback(async () => {
		try {
			setIsStarted(true);

			// Determine feature based on options
			const featureName = options.generateReport
				? "advanced_analysis"
				: "basic_analysis";

			// TODO: Add credit usage at component level

			// Proceed with analysis
			await analyze.mutateAsync({
				documentId,
				options,
			});

			toast({
				title: "Analysis Started",
				description: `${
					getCost(featureName)?.credits || 0
				} credits used for ${featureName.replace("_", " ")}`,
			});
		} catch (err) {
			const error = err as Error;
			toast({
				title: "Analysis Failed",
				description: error.message,
				variant: "destructive",
			});
			onError?.(error);
			setIsStarted(false);
		}
	}, [options, analyze, documentId, toast, getCost, onError]);

	// Handle completion
	if (status?.status === "completed") {
		onComplete?.(status);
	}

	// Handle cancellation
	const handleCancel = () => {
		setIsStarted(false);
		toast({
			title: "Analysis Cancelled",
			description: "The document analysis has been cancelled.",
			variant: "default",
		});
	};

	// Error state
	if (statusError || (status?.error && status.status === "failed")) {
		const errorMessage =
			statusError?.message || status?.error || "Analysis failed";
		return (
			<Card className="p-6">
				<div className="space-y-4">
					<div className="text-destructive font-medium">
						Error: {errorMessage}
					</div>
					<Button
						variant="outline"
						onClick={handleStartAnalysis}
						disabled={analyze.isPending}
					>
						Retry Analysis
					</Button>
				</div>
			</Card>
		);
	}

	// Loading states
	const isProcessing =
		status?.status === "processing" || status?.status === "queued";
	const showProgress = isStarted && isProcessing;

	return (
		<div className="space-y-6">
			{!isStarted && (
				<AnalysisOptions
					options={options}
					onChange={setOptions}
					disabled={analyze.isPending}
				/>
			)}

			<Card className="p-6">
				<div className="space-y-6">
					{status && (
						<StatusDisplay status={status} showProgress={showProgress} />
					)}

					{!isStarted && (
						<div className="flex items-center justify-between">
							<FeatureCostDisplay
								featureName={
									options.generateReport
										? "advanced_analysis"
										: "basic_analysis"
								}
								variant="tooltip"
								showDescription={true}
							/>
							<div className="flex gap-2">
								<Button
									onClick={handleStartAnalysis}
									disabled={
										analyze.isPending ||
										!canAfford(
											options.generateReport
												? "advanced_analysis"
												: "basic_analysis"
										)
									}
								>
									Start Analysis
								</Button>
							</div>
						</div>
					)}

					{isStarted && (
						<div className="flex justify-end gap-2">
							<Button
								variant="outline"
								disabled={!isProcessing}
								onClick={handleCancel}
							>
								Cancel
							</Button>
						</div>
					)}
				</div>
			</Card>

			{status?.status === "completed" && status.result && (
				<AnalysisResult
					result={status.result}
					documentId={documentId}
					documentTitle={documentTitle}
					onDownload={async () => {
						// Implement download logic here
						toast({
							title: "Download Started",
							description: "Your report is being downloaded.",
							variant: "default",
						});
					}}
				/>
			)}
		</div>
	);
}
