"use client";

import { Progress } from "@/components/ui/progress";
import { type ProcessingStatus } from "@/lib/services/document-analysis-service";

interface StatusDisplayProps {
  status: ProcessingStatus;
  showProgress?: boolean;
}

export function StatusDisplay({ status, showProgress = true }: StatusDisplayProps) {
  const getStatusMessage = () => {
    switch (status.currentStage) {
      case 'upload':
        return 'Preparing document...';
      case 'preprocessing':
        return 'Processing document...';
      case 'analysis':
        return 'Analyzing content...';
      case 'annotation':
        return 'Adding annotations...';
      case 'report':
        return 'Generating report...';
      default:
        return status.status === 'queued' ? 'Waiting in queue...' : 'Processing...';
    }
  };

  const getQueueMessage = () => {
    if (status.status === 'queued') {
      return 'Waiting in queue...';
    }
    return null;
  };

  return (
    <div className="space-y-4">
      <div className="space-y-2">
        <div className="flex justify-between items-center">
          <span className="text-sm font-medium">
            {status.currentStage?.charAt(0).toUpperCase() + status.currentStage?.slice(1)}
          </span>
          <span className="text-sm text-muted-foreground">
            {status.status === 'queued' ? 'Queued' : `${status.progress}%`}
          </span>
        </div>
        {showProgress && (
          <Progress 
            value={status.progress} 
            className="h-2"
          />
        )}
      </div>

      <div className="text-sm text-muted-foreground">
        <p>{getStatusMessage()}</p>
        {getQueueMessage() && (
          <p className="mt-1">{getQueueMessage()}</p>
        )}
      </div>

      {status.error && (
        <div className="mt-2 text-sm text-destructive">
          Error: {status.error}
        </div>
      )}
    </div>
  );
}