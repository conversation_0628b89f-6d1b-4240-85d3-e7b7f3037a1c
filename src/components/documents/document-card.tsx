"use client";

import { useState } from "react";
import type { DocumentMetadata } from "@/lib/services/document-service";
import { Card, CardContent, CardFooter } from "@/components/ui/card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { DocumentPreview } from "./document-preview";
import { DocumentDetailsModal } from "./document-details-modal";
import { DeleteDialog } from "./delete-dialog";
import {
	FileText,
	Trash,
	Calendar,
	MessageSquare,
	Eye,
	MoreHorizontal,
	Clock,
	FileType,
	Info,
	Brain,
	Shield,
} from "lucide-react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import { useDocumentAnalysis } from "@/hooks/use-document-analysis";
import { AnalysisPanel } from "@/components/analysis/analysis-panel";
import { useToast } from "@/hooks/use-toast";
import { useDocumentQuery } from "@/hooks/use-document-query";
import Link from "next/link";
import {
	DropdownMenu,
	DropdownMenuContent,
	DropdownMenuItem,
	DropdownMenuSeparator,
	DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import { cn } from "@/lib/utils";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";

interface DocumentCardProps {
	document: DocumentMetadata;
	className?: string;
}

export function DocumentCard({ document, className }: DocumentCardProps) {
	const [detailsOpen, setDetailsOpen] = useState(false);
	const [deleteDialogOpen, setDeleteDialogOpen] = useState(false);
	const [isDropdownOpen, setIsDropdownOpen] = useState(false);
	const {
		isAnalysisPanelOpen,
		currentAnalysis,
		analysisEvolution,
		isLoading,
		isEvolutionLoading,
		analyzeDocument,
		closeAnalysisPanel,
		getAnalysisEvolution,
	} = useDocumentAnalysis();
	const { toast } = useToast();
	const { deleteDocument, isDeletingDocument } = useDocumentQuery();

	const formattedDate = document.uploadDate
		? format(new Date(document.uploadDate), "MMM d, yyyy")
		: "Unknown date";

	const formattedTime = document.uploadDate
		? format(new Date(document.uploadDate), "h:mm a")
		: "";

	const getDocumentTypeLabel = (type?: string) => {
		if (!type) return "Unknown";
		if (type.includes("pdf")) return "PDF";
		if (type.startsWith("image/")) return "Image";
		if (type.includes("text") || type.includes("document")) return "Text";
		return "Document";
	};

	const getDocumentTypeColor = (type?: string) => {
		if (!type) return "bg-gray-100 text-gray-800";
		if (type.includes("pdf")) return "bg-red-50 text-red-700 border-red-200";
		if (type.startsWith("image/"))
			return "bg-blue-50 text-blue-700 border-blue-200";
		if (type.includes("text") || type.includes("document"))
			return "bg-green-50 text-green-700 border-green-200";
		return "bg-gray-100 text-gray-800";
	};

	const handleFetchEvolution = async () => {
		try {
			await getAnalysisEvolution(document.id);
		} catch {
			// Error handling is already done in the hook
		}
	};

	const handleDeleteDocument = async () => {
		try {
			await deleteDocument(document.id);
			// The DocumentCard (and thus this dialog) will unmount after deletion and list refresh,
			// so explicitly closing it here might interfere with focus management as it unmounts.
			// setDeleteDialogOpen(false); 
		} catch (error) { 
			console.error("Delete failed:", error);
		}
	};

	const handleAnalyzeDocument = async () => {
		try {
			await analyzeDocument(document.id);
			toast({
				title: "Analysis Started",
				description: "Your document is being analyzed",
			});
		} catch (error) {
			toast({
				title: "Analysis Failed",
				description:
					error instanceof Error ? error.message : "Failed to analyze document",
				variant: "destructive",
			});
		}
	};

	// Mock tags - in a real app, these would come from the document metadata
	// const documentTags = document.tags || []

	return (
		<>
			<Card
				className={cn(
					"overflow-hidden flex flex-col h-full transition-all duration-200 group border-muted/60",
					"hover:shadow-lg hover:border-primary/20 hover:scale-[1.01]",
					className
				)}
			>
				<div className="relative h-36 sm:h-44 bg-muted/30 overflow-hidden">
					<Link href={`/documents/${document.id}`} className="block h-full">
						<DocumentPreview
							document={document}
							className="h-full object-cover w-full transition-transform duration-500 group-hover:scale-105"
						/>
						<div className="absolute inset-0 bg-gradient-to-t from-black/40 to-transparent opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
					</Link>

					<div className="absolute top-1.5 sm:top-2 left-1.5 sm:left-2 flex flex-wrap gap-1 sm:gap-1.5 max-w-[calc(100%-60px)] sm:max-w-[calc(100%-80px)]">
						<Badge
							variant="outline"
							className={cn(
								"font-medium border backdrop-blur-sm",
								getDocumentTypeColor(document.fileType)
							)}
						>
							<FileType className="h-3 w-3 mr-1" />
							{getDocumentTypeLabel(document.fileType)}
						</Badge>

						{document.status && (
							<Badge
								variant={
									document.status === "processed" ? "default" : "secondary"
								}
								className="backdrop-blur-sm"
							>
								{document.status === "processed" ? "Processed" : "Processing"}
							</Badge>
						)}
					</div>

					<TooltipProvider>
						<Tooltip>
							<TooltipTrigger asChild>
								<Button
									variant="ghost"
									size="icon"
									className="absolute top-1.5 sm:top-2 right-1.5 sm:right-2 h-7 w-7 sm:h-8 sm:w-8 bg-background/80 backdrop-blur-sm opacity-70 group-hover:opacity-100 transition-opacity"
									onClick={() => setDetailsOpen(true)}
								>
									<Info className="h-3.5 sm:h-4 w-3.5 sm:w-4" />
								</Button>
							</TooltipTrigger>
							<TooltipContent>View details</TooltipContent>
						</Tooltip>
					</TooltipProvider>
				</div>

				<CardContent className="p-3 sm:p-4 flex-grow space-y-2 sm:space-y-3">
					<div className="space-y-1.5">
						<Link href={`/documents/${document.id}`} className="group/title">
							<h3 className="font-medium text-sm sm:text-base line-clamp-2 group-hover/title:text-primary transition-colors">
								{document.name || document.filename}
							</h3>
						</Link>
						{document.name && (
							<p
								className="text-xs text-muted-foreground line-clamp-1 truncate"
								title={document.filename}
							>
								{document.filename}
							</p>
						)}
					</div>

					{/* {documentTags.length > 0 && (
            <div className="flex flex-wrap gap-1.5">
              {documentTags.slice(0, 3).map((tag, index) => (
                <Badge key={index} variant="outline" className="text-xs px-1.5 py-0.5">
                  <Tag className="h-2.5 w-2.5 mr-1" />
                  {tag}
                </Badge>
              ))}
              {documentTags.length > 3 && (
                <Badge variant="outline" className="text-xs px-1.5 py-0.5">
                  +{documentTags.length - 3}
                </Badge>
              )}
            </div>
          )} */}

					<div className="flex items-center justify-between text-xs text-muted-foreground">
						<div className="flex items-center">
							<Calendar className="h-3 w-3 mr-1.5" />
							<span>{formattedDate}</span>
						</div>
						{formattedTime && (
							<div className="flex items-center">
								<Clock className="h-3 w-3 mr-1.5" />
								<span>{formattedTime}</span>
							</div>
						)}
					</div>
				</CardContent>

				<CardFooter className="p-4 pt-0 flex items-center gap-2">
					<Button
						variant="default"
						size="sm"
						className="flex-1 min-w-0"
						onClick={handleAnalyzeDocument}
						disabled={isLoading}
					>
						<FileText className="h-3.5 w-3.5 mr-1.5" />
						{isLoading ? "Analyzing..." : "Analyze"}
					</Button>

					<Button
						variant="secondary"
						size="sm"
						className="flex-1 min-w-0"
						asChild
					>
						<Link href={`/chat?document=${document.id}`}>
							<MessageSquare className="h-3.5 w-3.5 mr-1.5" />
							Chat
						</Link>
					</Button>

					<DropdownMenu open={isDropdownOpen} onOpenChange={setIsDropdownOpen}>
						<DropdownMenuTrigger asChild>
							<Button variant="outline" size="icon" className="h-8 w-8">
								<MoreHorizontal className="h-4 w-4" />
							</Button>
						</DropdownMenuTrigger>
						<DropdownMenuContent align="end" className="w-48">
							<DropdownMenuItem className="cursor-pointer" asChild>
								<Link
									href={`/documents/${document.id}`}
									className="flex w-full"
								>
									<Eye className="h-4 w-4 mr-2" />
									View Document
								</Link>
							</DropdownMenuItem>
							<DropdownMenuItem
								onClick={() => setDetailsOpen(true)}
								className="cursor-pointer"
							>
								<Info className="h-4 w-4 mr-2" />
								View Details
							</DropdownMenuItem>
							<DropdownMenuItem className="cursor-pointer" asChild>
								<Link
									href={`/documents/${document.id}?tab=playbook`}
									className="flex w-full"
								>
									<Brain className="h-4 w-4 mr-2" />
									Negotiation Playbook
								</Link>
							</DropdownMenuItem>
							<DropdownMenuItem className="cursor-pointer" asChild>
								<Link
									href={`/documents/${document.id}?tab=privilege`}
									className="flex w-full"
								>
									<Shield className="h-4 w-4 mr-2" />
									Privilege Log
								</Link>
							</DropdownMenuItem>
							<DropdownMenuSeparator />
							<DropdownMenuItem
								onClick={() => {
									setIsDropdownOpen(false); // Explicitly close dropdown
									setDeleteDialogOpen(true);
								}}
								className="text-red-600 focus:text-red-600 cursor-pointer"
							>
								<Trash className="h-4 w-4 mr-2" />
								Delete Document
							</DropdownMenuItem>
						</DropdownMenuContent>
					</DropdownMenu>
				</CardFooter>
			</Card>

			<DocumentDetailsModal
				open={detailsOpen}
				onOpenChange={setDetailsOpen}
				document={document}
			/>

			<DeleteDialog
				open={deleteDialogOpen}
				onOpenChange={setDeleteDialogOpen}
				documentName={document.name || document.filename}
				loading={isDeletingDocument}
				onConfirm={handleDeleteDocument}
			/>

			{isAnalysisPanelOpen && (
				<AnalysisPanel
					isOpen={isAnalysisPanelOpen}
					onClose={closeAnalysisPanel}
					analysis={currentAnalysis}
					evolution={analysisEvolution}
					onRequestEvolution={handleFetchEvolution}
					isEvolutionLoading={isEvolutionLoading}
					documentId={document.id}
				/>
			)}
		</>
	);
}
