"use client";

import { useState, useCallback, useMemo } from "react";
import { DocumentMetadata } from "@/lib/services/document-service";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
} from "@/components/ui/select";
import { DocumentCard } from "./document-card";
import { Spinner } from "@/components/ui/spinner";
import { Search, Filter, SortAsc, SortDesc, Grid, List, X } from "lucide-react";
import { Badge } from "@/components/ui/badge";
import Link from "next/link";
import { useDocumentQuery } from "@/hooks/use-document-query";
import { Pagination } from "@/components/ui/pagination";

export function DocumentsView() {
  const {
    documents,
    pagination,
    isLoading: loading,
    error,
    refetch,
    isDeletingDocument,
  } = useDocumentQuery();
  const [searchTerm, setSearchTerm] = useState("");
  const [sortBy, setSortBy] = useState<"date" | "name" | "size">("date");
  const [sortOrder, setSortOrder] = useState<"asc" | "desc">("desc");
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid");
  const [documentType, setDocumentType] = useState<string>("all");

  const filteredDocuments = useMemo(() => {
    if (!documents) return [];

    let processedDocuments = [...documents];

    // Apply search filter
    if (searchTerm) {
      processedDocuments = processedDocuments.filter(
        (doc) =>
          doc.name?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          false ||
          doc.filename?.toLowerCase().includes(searchTerm.toLowerCase()) ||
          false ||
          doc.metadata?.title
            ?.toLowerCase()
            .includes(searchTerm.toLowerCase()) ||
          false
      );
    }

    // Apply document type filter
    if (documentType !== "all") {
      processedDocuments = processedDocuments.filter(
        (doc) => doc.fileType === documentType
      );
    }

    // Apply sorting
    processedDocuments.sort((a, b) => {
      if (sortBy === "date") {
        return sortOrder === "asc"
          ? a.uploadDate.localeCompare(b.uploadDate)
          : b.uploadDate.localeCompare(a.uploadDate);
      } else if (sortBy === "name") {
        return sortOrder === "asc"
          ? a.filename.localeCompare(b.filename)
          : b.filename.localeCompare(a.filename);
      } else if (sortBy === "size") {
        const sizeA = a.size || 0;
        const sizeB = b.size || 0;
        return sortOrder === "asc" ? sizeA - sizeB : sizeB - sizeA;
      }
      return 0;
    });

    return processedDocuments;
  }, [documents, searchTerm, documentType, sortBy, sortOrder]);

  const handleSearch = (e: React.ChangeEvent<HTMLInputElement>) => {
    setSearchTerm(e.target.value);
  };

  const clearSearch = () => {
    setSearchTerm("");
  };

  const handlePageChange = useCallback(
    (pageNumber: number) => {
      pagination.goToPage(pageNumber);
    },
    [pagination]
  );

  const handleSortByChange = useCallback(
    (value: string) => {
      setSortBy(value as "date" | "name" | "size");
    },
    [setSortBy]
  );

  const handleDocumentTypeChange = useCallback(
    (value: string) => {
      setDocumentType(value);
    },
    [setDocumentType]
  );

  if (error) {
    return (
      <div className="text-center py-10">
        <p className="text-red-500 mb-2">Failed to load documents</p>
        <Button onClick={() => refetch()} variant="outline">
          Try Again
        </Button>
      </div>
    );
  }

  return (
    <div className="w-full overflow-hidden">
      {/* Global deletion loading overlay */}
      {isDeletingDocument && (
        <div className="fixed inset-0 bg-background/80 backdrop-blur-sm z-50 flex items-center justify-center">
          <div className="bg-card border rounded-lg p-6 shadow-lg flex items-center gap-3">
            <Spinner size="sm" />
            <span className="text-sm font-medium">Deleting document...</span>
          </div>
        </div>
      )}

      <div className="flex flex-col gap-3 mb-4 sm:mb-6 w-full">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-muted-foreground h-4 w-4" />
          <Input
            placeholder="Search documents..."
            value={searchTerm}
            onChange={handleSearch}
            className="pl-10 pr-10"
            disabled={isDeletingDocument}
          />
          {searchTerm && (
            <Button
              variant="ghost"
              size="sm"
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-7 w-7 p-0"
              onClick={clearSearch}
              disabled={isDeletingDocument}
            >
              <span className="sr-only">Clear search</span>
              <X className="h-4 w-4" />
            </Button>
          )}
        </div>
        
        <div className="flex flex-wrap gap-2 justify-between">
          <div className="flex gap-2">
            <Select value={documentType} onValueChange={handleDocumentTypeChange} disabled={isDeletingDocument}>
              <SelectTrigger className="h-9">
                <Filter className="h-4 w-4" />
                <span className="ml-2 text-xs sm:text-sm">Filter</span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All</SelectItem>
                <SelectItem value="application/pdf">PDF</SelectItem>
                <SelectItem value="image/jpeg">Image</SelectItem>
                <SelectItem value="application/msword">Word (.doc)</SelectItem>
                <SelectItem value="application/vnd.openxmlformats-officedocument.wordprocessingml.document">
                  Word (.docx)
                </SelectItem>
              </SelectContent>
            </Select>
            <Select value={sortBy} onValueChange={handleSortByChange} disabled={isDeletingDocument}>
              <SelectTrigger className="h-9">
                {sortOrder === "asc" ? (
                  <SortAsc className="h-4 w-4" />
                ) : (
                  <SortDesc className="h-4 w-4" />
                )}
                <span className="ml-2 text-xs sm:text-sm">Sort</span>
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="date">Date</SelectItem>
                <SelectItem value="name">Name</SelectItem>
                <SelectItem value="size">Size</SelectItem>
              </SelectContent>
            </Select>
            <Button
              variant="ghost"
              size="sm"
              className="h-9 px-2"
              onClick={() => setSortOrder(sortOrder === "asc" ? "desc" : "asc")}
              disabled={isDeletingDocument}
            >
              {sortOrder === "asc" ? (
                <SortDesc className="h-4 w-4" />
              ) : (
                <SortAsc className="h-4 w-4" />
              )}
            </Button>
          </div>
          
          <div className="flex gap-2">
            <Button
              variant={viewMode === "grid" ? "default" : "outline"}
              size="sm"
              className="h-9 px-2"
              onClick={() => setViewMode("grid")}
              title="Grid View"
              disabled={isDeletingDocument}
            >
              <Grid className="h-4 w-4" />
            </Button>
            <Button
              variant={viewMode === "list" ? "default" : "outline"}
              size="sm"
              className="h-9 px-2"
              onClick={() => setViewMode("list")}
              title="List View"
              disabled={isDeletingDocument}
            >
              <List className="h-4 w-4" />
            </Button>
          </div>
        </div>
      </div>

      {loading ? (
        <div className="flex justify-center py-12">
          <Spinner size="lg" />
        </div>
      ) : filteredDocuments.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground mb-4">No documents found</p>
          <Link href="/documents/upload">
            <Button>Upload Document</Button>
          </Link>
        </div>
      ) : viewMode === "grid" ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 md:gap-6">
          {filteredDocuments.map((document) => (
            <DocumentCard key={document.id} document={document} />
          ))}
        </div>
      ) : (
        <div className="space-y-2">
          {filteredDocuments.map((document) => (
            <DocumentListItem key={document.id} document={document} />
          ))}
        </div>
      )}

      {!loading && documents.length > 0 && (
        <div className="mt-6">
          <Pagination
            currentPage={pagination.page}
            totalPages={pagination.totalPages}
            onPageChange={handlePageChange}
          />

          <div className="text-center mt-2 text-sm text-muted-foreground">
            Showing {(pagination.page - 1) * pagination.limit + 1} to{" "}
            {Math.min(pagination.page * pagination.limit, pagination.total)} of{" "}
            {pagination.total} documents
          </div>
        </div>
      )}
    </div>
  );
}

function DocumentListItem({ document }: { document: DocumentMetadata }) {
  const docType = document.fileType || "unknown";
  const formatFileSize = (bytes?: number) => {
    if (!bytes) return "";
    if (bytes < 1024) return `${bytes} B`;
    if (bytes < 1024 * 1024) return `${(bytes / 1024).toFixed(1)} KB`;
    return `${(bytes / (1024 * 1024)).toFixed(1)} MB`;
  };

  // Get document type label for badge
  const getDocTypeLabel = () => {
    if (docType.includes("pdf")) return "PDF";
    if (docType.startsWith("image/")) return "Image";
    return "Doc";
  };

  // Get badge color based on document type
  const getBadgeClass = () => {
    if (docType.includes("pdf")) {
      return "bg-red-100 dark:bg-red-900/20 text-red-800 dark:text-red-300 border-red-200";
    }
    if (docType.startsWith("image/")) {
      return "bg-blue-100 dark:bg-blue-900/20 text-blue-800 dark:text-blue-300 border-blue-200";
    }
    return "bg-gray-100 dark:bg-gray-800 text-gray-800 dark:text-gray-300 border-gray-200";
  };

  return (
    <Link href={`/documents/${document.id}`}>
      <div className="flex items-center p-3 rounded-md hover:bg-accent/50 transition-colors cursor-pointer border border-border w-full overflow-hidden max-w-full">
        <div className="mr-3">
          <Badge 
            variant="outline"
            className={`${getBadgeClass()} text-xs font-medium px-2 py-0.5`}
          >
            {getDocTypeLabel()}
          </Badge>
        </div>
        <div className="flex-1 min-w-0 mr-2 max-w-[65%]">
          <p className="font-medium text-sm truncate">
            {document.name || document.filename}
          </p>
          <p className="text-xs text-muted-foreground truncate">
            {document.filename}
          </p>
        </div>
        <div className="text-xs text-muted-foreground text-right whitespace-nowrap">
          {formatFileSize(document.size)}
        </div>
      </div>
    </Link>
  );
}
