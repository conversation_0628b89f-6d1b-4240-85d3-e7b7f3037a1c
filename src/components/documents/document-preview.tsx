import { useEffect, useState } from "react";
import { FileText, Image as ImageIcon, FileQuestion } from "lucide-react";
import Image from "next/image";
import { DocumentMetadata } from "@/lib/services/document-service";
import { cn } from "@/lib/utils";

interface DocumentPreviewProps {
  document: DocumentMetadata | null; // Allow null and use correct type
  className?: string;
}

export function DocumentPreview({ document, className }: DocumentPreviewProps) {
  const [imageUrl, setImageUrl] = useState<string | null>(null);

  useEffect(() => {
    // Reset image URL when document changes
    setImageUrl(null);

    if (
      document?.fileType?.startsWith("image/") && // Use fileType property and optional chaining
      document?.filename // Use filename property
    ) {
      // For images, create an object URL for preview
      fetch(`/api/documents/${document.id}/file`)
        .then((response) => response.blob())
        .then((blob) => {
          const url = URL.createObjectURL(blob);
          setImageUrl(url);
        })
        .catch((error) => {
          console.error("Error loading image preview:", error);
        });
    }

    // Cleanup function for the effect
    return () => {
      if (imageUrl) {
        URL.revokeObjectURL(imageUrl);
      }
    };
  }, [document, imageUrl]); // Add imageUrl to dependency array

  // Handle null document case after hooks
  if (!document) {
    return (
      <div className={cn("relative flex h-full w-full items-center justify-center bg-muted/50", className)}>
        <FileQuestion className="h-12 w-12 text-muted-foreground" />
      </div>
    );
  }

  // Define getPreviewContent once, after hooks and null check
  const getPreviewContent = () => {
    // Handle image files
    if (document.fileType?.startsWith("image/")) { // Use fileType property
      if (imageUrl) {
        return (
          <Image
            src={imageUrl}
            alt={document.filename || 'Document preview'}
            fill
            className="object-cover"
            sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
            priority={true}
          />
        );
      }
      return (
        <div className="flex h-full w-full items-center justify-center">
          <ImageIcon className="h-12 w-12 text-muted-foreground" />
        </div>
      );
    }

    // Handle PDF files
    if (document.fileType === "pdf") { // Use fileType property
      return (
        <div className="flex h-full w-full flex-col items-center justify-center gap-2">
          <FileText className="h-12 w-12 text-red-500" />
          <span className="text-sm text-muted-foreground">PDF Document</span>
        </div>
      );
    }

    // Handle Word documents
    if (
      document.fileType === "msword" || // Use fileType property
      document.fileType ===
        "application/vnd.openxmlformats-officedocument.wordprocessingml.document"
    ) {
      return (
        <div className="flex h-full w-full flex-col items-center justify-center gap-2">
          <FileText className="h-12 w-12 text-blue-500" />
          <span className="text-sm text-muted-foreground">Word Document</span>
        </div>
      );
    }

    // Default preview for other file types
    return (
      <div className="flex h-full w-full flex-col items-center justify-center gap-2">
        <FileQuestion className="h-12 w-12 text-muted-foreground" />
        <span className="text-sm text-muted-foreground">
          {/* Use filename, provide default */}
          {document.filename || 'Unknown File'}
        </span>
      </div>
    );
  };

  return (
    <div
      className={cn(
        "relative flex h-full w-full items-center justify-center bg-muted/50",
        className
      )}
    >
      {getPreviewContent()}
    </div>
   );
}