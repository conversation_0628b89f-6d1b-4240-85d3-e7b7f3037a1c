"use client";

import { But<PERSON> } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import { ScrollArea } from "@/components/ui/scroll-area";
import { useToast } from "@/hooks/use-toast";
import { CreateScenarioFromDocument } from "@/components/document-analysis";
import { type ProcessingStatus } from "@/lib/services/document-analysis-service";

interface AnalysisResultProps {
	result: NonNullable<ProcessingStatus["result"]>;
	documentId?: string;
	documentTitle?: string;
	onDownload?: () => Promise<void>;
}

export function AnalysisResult({
	result,
	documentId,
	documentTitle,
	onDownload,
}: AnalysisResultProps) {
	const { toast } = useToast();

	const handleDownload = async () => {
		try {
			await onDownload?.();
			toast({
				title: "Download Started",
				description: "Your report is being downloaded.",
				variant: "default",
			});
		} catch (error) {
			toast({
				title: "Download Failed",
				description:
					error instanceof Error ? error.message : "Failed to download report",
				variant: "destructive",
			});
		}
	};

	return (
		<Card className="p-6">
			<div className="space-y-6">
				<div className="flex items-center justify-between">
					<h3 className="text-lg font-semibold">Analysis Results</h3>
					<div className="flex items-center gap-2">
						{documentId && documentTitle && (
							<CreateScenarioFromDocument
								documentId={documentId}
								documentTitle={documentTitle}
							/>
						)}
						{result.reportUrl && (
							<Button variant="outline" onClick={handleDownload}>
								Download Report
							</Button>
						)}
					</div>
				</div>

				{result.analysis && (
					<div className="space-y-4">
						{result.analysis.segments &&
							result.analysis.segments.length > 0 && (
								<div className="space-y-2">
									<h4 className="text-sm font-medium">Document Segments</h4>
									<ScrollArea className="h-[200px] rounded-md border p-4">
										<div className="space-y-2">
											{result.analysis.segments.map((segment, index) => (
												<div
													key={index}
													className="text-sm text-muted-foreground"
												>
													{segment}
												</div>
											))}
										</div>
									</ScrollArea>
								</div>
							)}

						{result.analysis.metrics &&
							Object.keys(result.analysis.metrics).length > 0 && (
								<div className="space-y-2">
									<h4 className="text-sm font-medium">Analysis Metrics</h4>
									<div className="rounded-md border p-4">
										<dl className="space-y-2">
											{Object.entries(result.analysis.metrics).map(
												([key, value]) => (
													<div
														key={key}
														className="flex justify-between text-sm"
													>
														<dt className="font-medium">{key}</dt>
														<dd className="text-muted-foreground">
															{String(value)}
														</dd>
													</div>
												)
											)}
										</dl>
									</div>
								</div>
							)}
					</div>
				)}
			</div>
		</Card>
	);
}
