"use client";

import { Card } from "@/components/ui/card";
import { Label } from "@/components/ui/label";
import { Switch } from "@/components/ui/switch";

interface AnalysisOptions {
  includeAnnotations: boolean;
  generateReport: boolean;
}

interface AnalysisOptionsProps {
  options: AnalysisOptions;
  onChange: (options: AnalysisOptions) => void;
  disabled?: boolean;
}

export function AnalysisOptions({ 
  options, 
  onChange,
  disabled = false 
}: AnalysisOptionsProps) {
  const handleOptionChange = (key: keyof AnalysisOptions) => {
    onChange({
      ...options,
      [key]: !options[key]
    });
  };

  return (
    <Card className="p-6">
      <div className="space-y-6">
        <h3 className="text-lg font-semibold">Analysis Options</h3>
        
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="annotations">Include Annotations</Label>
              <p className="text-sm text-muted-foreground">
                Add detailed annotations to the document analysis
              </p>
            </div>
            <Switch
              id="annotations"
              checked={options.includeAnnotations}
              onCheckedChange={() => handleOptionChange('includeAnnotations')}
              disabled={disabled}
            />
          </div>

          <div className="flex items-center justify-between">
            <div className="space-y-0.5">
              <Label htmlFor="report">Generate Report</Label>
              <p className="text-sm text-muted-foreground">
                Create a comprehensive analysis report
              </p>
            </div>
            <Switch
              id="report"
              checked={options.generateReport}
              onCheckedChange={() => handleOptionChange('generateReport')}
              disabled={disabled}
            />
          </div>
        </div>
      </div>
    </Card>
  );
}