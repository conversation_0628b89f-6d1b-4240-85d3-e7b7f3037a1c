"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  HelpCircle, 
  Play, 
  Trophy, 
  Users, 
  BookOpen, 
  Award,
  BarChart3,
  Sword,
  Crown,
  Target,
  Zap,
  Gift,
  ChevronRight,
  X,
  ArrowRight,
  MousePointer,
  Smartphone,
  Monitor
} from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Badge } from '@/components/ui/badge';
import { Dialog, DialogContent, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog';
import { Ta<PERSON>, Ta<PERSON>Content, TabsList, TabsTrigger } from '@/components/ui/tabs';

interface UIGuideProps {
  onClose?: () => void;
}

export function UIGuide({ onClose }: UIGuideProps) {
  const [currentStep, setCurrentStep] = useState(0);
  const [selectedFeature, setSelectedFeature] = useState<string | null>(null);

  const navigationSteps = [
    {
      title: "Welcome to the Negotiation Arena!",
      description: "Let's take a quick tour of the gamification features",
      icon: Crown,
      content: "This is your command center for mastering negotiation skills through engaging, game-like experiences."
    },
    {
      title: "Dashboard Overview",
      description: "Your central hub for all activities",
      icon: Target,
      content: "The dashboard shows your level, recent achievements, quick stats, and provides easy access to all features."
    },
    {
      title: "Quick Practice",
      description: "Start negotiating immediately",
      icon: Play,
      content: "Choose an AI opponent and jump into a practice session. Perfect for quick skill building."
    },
    {
      title: "Campaign Mode",
      description: "Story-driven progression",
      icon: BookOpen,
      content: "Follow narrative campaigns that teach specific negotiation skills through engaging storylines."
    },
    {
      title: "Team Challenges",
      description: "Compete with colleagues",
      icon: Users,
      content: "Join or create teams, participate in competitions, and collaborate on negotiation challenges."
    },
    {
      title: "Track Your Progress",
      description: "Monitor your improvement",
      icon: BarChart3,
      content: "View detailed analytics, leaderboards, and achievement progress to see how you're improving."
    }
  ];

  const features = [
    {
      id: 'levels',
      title: 'Levels & XP',
      icon: Crown,
      description: 'Earn experience points and level up',
      details: [
        'Complete negotiations to earn XP',
        'Level up to unlock new titles and features',
        'Each level requires more XP than the last',
        'Higher levels unlock advanced scenarios'
      ]
    },
    {
      id: 'achievements',
      title: 'Achievements',
      icon: Award,
      description: 'Unlock badges for accomplishments',
      details: [
        'Over 50 achievements to unlock',
        'Categories: Performance, Milestones, Relationships',
        'Rare achievements give more XP',
        'Share achievements with your team'
      ]
    },
    {
      id: 'characters',
      title: 'AI Characters',
      icon: Sword,
      description: 'Negotiate with diverse AI opponents',
      details: [
        'Each character has unique personality traits',
        'Build relationships through repeated interactions',
        'Unlock new characters by leveling up',
        'Higher difficulty characters give more XP'
      ]
    },
    {
      id: 'leaderboards',
      title: 'Leaderboards',
      icon: Trophy,
      description: 'Compete with others',
      details: [
        'Global, organization, and team rankings',
        'Weekly, monthly, and all-time leaderboards',
        'Track your rank and improvement',
        'See top performers in your organization'
      ]
    },
    {
      id: 'campaigns',
      title: 'Campaign Mode',
      icon: BookOpen,
      description: 'Story-driven learning',
      details: [
        'Multi-chapter storylines',
        'Progressive difficulty',
        'Specific learning objectives',
        'Unlock requirements and rewards'
      ]
    },
    {
      id: 'teams',
      title: 'Team Features',
      icon: Users,
      description: 'Collaborate and compete',
      details: [
        'Create or join teams',
        'Team challenges and tournaments',
        'Collaborative negotiation sessions',
        'Team performance analytics'
      ]
    }
  ];

  const mobileFeatures = [
    {
      title: 'Touch-Friendly Interface',
      description: 'Optimized for mobile devices with gesture support',
      icon: Smartphone
    },
    {
      title: 'Swipe Notifications',
      description: 'Achievement notifications you can swipe to dismiss',
      icon: Gift
    },
    {
      title: 'Responsive Design',
      description: 'Adapts perfectly to any screen size',
      icon: Monitor
    }
  ];

  return (
    <Dialog>
      <DialogTrigger asChild>
        <Button variant="outline" size="sm" className="flex items-center gap-2">
          <HelpCircle className="h-4 w-4" />
          UI Guide
        </Button>
      </DialogTrigger>
      <DialogContent className="max-w-4xl max-h-[90vh] overflow-y-auto">
        <DialogHeader>
          <DialogTitle className="flex items-center gap-2">
            <HelpCircle className="h-5 w-5" />
            Negotiation Arena UI Guide
          </DialogTitle>
        </DialogHeader>

        <Tabs defaultValue="overview" className="w-full">
          <TabsList className="grid w-full grid-cols-4">
            <TabsTrigger value="overview">Overview</TabsTrigger>
            <TabsTrigger value="features">Features</TabsTrigger>
            <TabsTrigger value="navigation">Navigation</TabsTrigger>
            <TabsTrigger value="mobile">Mobile</TabsTrigger>
          </TabsList>

          <TabsContent value="overview" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Target className="h-5 w-5" />
                  Getting Started
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-3">
                    <h4 className="font-semibold flex items-center gap-2">
                      <Play className="h-4 w-4 text-green-600" />
                      Quick Start
                    </h4>
                    <ol className="text-sm space-y-2 list-decimal list-inside">
                      <li>Click "Quick Practice" to start immediately</li>
                      <li>Choose an AI character to negotiate with</li>
                      <li>Follow the guided negotiation process</li>
                      <li>Earn XP and unlock achievements</li>
                    </ol>
                  </div>
                  
                  <div className="space-y-3">
                    <h4 className="font-semibold flex items-center gap-2">
                      <BookOpen className="h-4 w-4 text-purple-600" />
                      Structured Learning
                    </h4>
                    <ol className="text-sm space-y-2 list-decimal list-inside">
                      <li>Start with Campaign Mode for guided learning</li>
                      <li>Complete chapters to unlock new content</li>
                      <li>Join team challenges for collaboration</li>
                      <li>Track progress in Analytics</li>
                    </ol>
                  </div>
                </div>

                <div className="bg-blue-50 p-4 rounded-lg">
                  <h4 className="font-semibold text-blue-800 mb-2">💡 Pro Tips</h4>
                  <ul className="text-sm text-blue-700 space-y-1">
                    <li>• Start with easier characters to build confidence</li>
                    <li>• Check achievements regularly for goals to work toward</li>
                    <li>• Use the leaderboard to see how you compare</li>
                    <li>• Join a team to access collaborative features</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="features" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              {features.map((feature) => (
                <Card 
                  key={feature.id}
                  className={`cursor-pointer transition-all ${
                    selectedFeature === feature.id ? 'ring-2 ring-blue-500' : 'hover:shadow-md'
                  }`}
                  onClick={() => setSelectedFeature(selectedFeature === feature.id ? null : feature.id)}
                >
                  <CardContent className="p-4">
                    <div className="flex items-start gap-3">
                      <feature.icon className="h-6 w-6 text-blue-600 mt-1" />
                      <div className="flex-1">
                        <h3 className="font-semibold">{feature.title}</h3>
                        <p className="text-sm text-gray-600 mb-2">{feature.description}</p>
                        
                        <AnimatePresence>
                          {selectedFeature === feature.id && (
                            <motion.div
                              initial={{ height: 0, opacity: 0 }}
                              animate={{ height: 'auto', opacity: 1 }}
                              exit={{ height: 0, opacity: 0 }}
                              className="overflow-hidden"
                            >
                              <ul className="text-xs space-y-1 mt-2">
                                {feature.details.map((detail, index) => (
                                  <li key={index} className="flex items-start gap-2">
                                    <ChevronRight className="h-3 w-3 text-gray-400 mt-0.5 flex-shrink-0" />
                                    <span>{detail}</span>
                                  </li>
                                ))}
                              </ul>
                            </motion.div>
                          )}
                        </AnimatePresence>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </TabsContent>

          <TabsContent value="navigation" className="space-y-4">
            <Card>
              <CardHeader>
                <CardTitle>Navigation Guide</CardTitle>
              </CardHeader>
              <CardContent>
                <div className="space-y-6">
                  {navigationSteps.map((step, index) => (
                    <div key={index} className="flex items-start gap-4">
                      <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center flex-shrink-0">
                        <step.icon className="h-4 w-4 text-blue-600" />
                      </div>
                      <div className="flex-1">
                        <h4 className="font-semibold">{step.title}</h4>
                        <p className="text-sm text-gray-600 mb-1">{step.description}</p>
                        <p className="text-xs text-gray-500">{step.content}</p>
                      </div>
                    </div>
                  ))}
                </div>

                <div className="mt-6 p-4 bg-gray-50 rounded-lg">
                  <h4 className="font-semibold mb-2 flex items-center gap-2">
                    <MousePointer className="h-4 w-4" />
                    Desktop Navigation
                  </h4>
                  <ul className="text-sm space-y-1">
                    <li>• Left sidebar contains all main navigation</li>
                    <li>• User profile shows level and XP progress</li>
                    <li>• Quick stats panel for at-a-glance info</li>
                    <li>• Hover over items for additional details</li>
                  </ul>
                </div>
              </CardContent>
            </Card>
          </TabsContent>

          <TabsContent value="mobile" className="space-y-4">
            <div className="grid grid-cols-1 gap-4">
              {mobileFeatures.map((feature, index) => (
                <Card key={index}>
                  <CardContent className="p-4">
                    <div className="flex items-center gap-3">
                      <feature.icon className="h-6 w-6 text-blue-600" />
                      <div>
                        <h3 className="font-semibold">{feature.title}</h3>
                        <p className="text-sm text-gray-600">{feature.description}</p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>

            <Card>
              <CardHeader>
                <CardTitle>Mobile Gestures</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-green-100 rounded-full flex items-center justify-center">
                      <span className="text-green-600 text-xs">👆</span>
                    </div>
                    <div>
                      <p className="font-medium">Tap</p>
                      <p className="text-sm text-gray-600">Navigate and select items</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-blue-100 rounded-full flex items-center justify-center">
                      <span className="text-blue-600 text-xs">👈</span>
                    </div>
                    <div>
                      <p className="font-medium">Swipe</p>
                      <p className="text-sm text-gray-600">Dismiss notifications and navigate</p>
                    </div>
                  </div>
                  
                  <div className="flex items-center gap-3">
                    <div className="w-8 h-8 bg-purple-100 rounded-full flex items-center justify-center">
                      <span className="text-purple-600 text-xs">📱</span>
                    </div>
                    <div>
                      <p className="font-medium">Menu Button</p>
                      <p className="text-sm text-gray-600">Access full navigation on mobile</p>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </TabsContent>
        </Tabs>
      </DialogContent>
    </Dialog>
  );
}
