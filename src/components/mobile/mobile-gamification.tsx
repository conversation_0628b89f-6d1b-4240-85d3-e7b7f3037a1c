"use client";

import React, { useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  Trophy, 
  Star, 
  Zap, 
  Award, 
  Target, 
  TrendingUp,
  Users,
  Calendar,
  Play,
  ChevronRight,
  X,
  Share2,
  Gift
} from 'lucide-react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Progress } from '@/components/ui/progress';
import { Sheet, SheetContent, SheetHeader, SheetTitle, SheetTrigger } from '@/components/ui/sheet';

// Mobile Achievement Notification
interface MobileAchievementNotificationProps {
  achievement: {
    id: string;
    title: string;
    description: string;
    badge: string;
    rarity: 'common' | 'rare' | 'epic' | 'legendary';
  };
  show: boolean;
  onClose: () => void;
  onShare?: () => void;
}

export function MobileAchievementNotification({ 
  achievement, 
  show, 
  onClose, 
  onShare 
}: MobileAchievementNotificationProps) {
  const getRarityColor = (rarity: string) => {
    switch (rarity) {
      case 'legendary': return 'from-yellow-400 to-orange-500';
      case 'epic': return 'from-purple-400 to-pink-500';
      case 'rare': return 'from-blue-400 to-cyan-500';
      default: return 'from-gray-400 to-gray-600';
    }
  };

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ y: -100, opacity: 0 }}
          animate={{ y: 0, opacity: 1 }}
          exit={{ y: -100, opacity: 0 }}
          className="fixed top-4 left-4 right-4 z-50"
        >
          <Card className={`bg-gradient-to-r ${getRarityColor(achievement.rarity)} text-white shadow-2xl`}>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-3">
                  <div className="text-3xl">{achievement.badge}</div>
                  <div className="flex-1">
                    <h3 className="font-bold text-lg">{achievement.title}</h3>
                    <p className="text-sm opacity-90">{achievement.description}</p>
                    <Badge className="mt-1 bg-white/20 text-white border-white/30">
                      {achievement.rarity}
                    </Badge>
                  </div>
                </div>
                <div className="flex flex-col space-y-2">
                  {onShare && (
                    <Button
                      size="sm"
                      variant="ghost"
                      className="text-white hover:bg-white/20 p-2"
                      onClick={onShare}
                    >
                      <Share2 className="h-4 w-4" />
                    </Button>
                  )}
                  <Button
                    size="sm"
                    variant="ghost"
                    className="text-white hover:bg-white/20 p-2"
                    onClick={onClose}
                  >
                    <X className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Mobile XP Animation
interface MobileXPAnimationProps {
  amount: number;
  show: boolean;
  onComplete: () => void;
}

export function MobileXPAnimation({ amount, show, onComplete }: MobileXPAnimationProps) {
  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ scale: 0, y: 0, opacity: 1 }}
          animate={{ scale: 1, y: -50, opacity: 1 }}
          exit={{ scale: 0, y: -100, opacity: 0 }}
          transition={{ duration: 2, ease: "easeOut" }}
          onAnimationComplete={onComplete}
          className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 z-50 pointer-events-none"
        >
          <div className="bg-gradient-to-r from-yellow-400 to-orange-500 text-white px-4 py-2 rounded-full shadow-lg">
            <div className="flex items-center space-x-2">
              <Zap className="h-5 w-5" />
              <span className="font-bold">+{amount} XP</span>
            </div>
          </div>
        </motion.div>
      )}
    </AnimatePresence>
  );
}

// Mobile Level Progress Card
interface MobileLevelProgressProps {
  level: {
    current: number;
    title: string;
    currentXP: number;
    xpToNext: number;
    progress: number;
  };
}

export function MobileLevelProgress({ level }: MobileLevelProgressProps) {
  return (
    <Card className="bg-gradient-to-r from-purple-500 to-blue-600 text-white">
      <CardContent className="p-4">
        <div className="flex items-center justify-between mb-3">
          <div>
            <h3 className="font-bold text-lg">{level.title}</h3>
            <p className="text-sm opacity-90">Level {level.current}</p>
          </div>
          <div className="w-12 h-12 bg-white/20 rounded-full flex items-center justify-center">
            <Trophy className="h-6 w-6" />
          </div>
        </div>
        
        <div className="space-y-2">
          <div className="flex justify-between text-sm">
            <span>{level.currentXP.toLocaleString()} XP</span>
            <span>{level.xpToNext.toLocaleString()} to next</span>
          </div>
          <Progress value={level.progress} className="h-2 bg-white/20" />
        </div>
      </CardContent>
    </Card>
  );
}

// Mobile Quick Stats Grid
interface MobileQuickStatsProps {
  stats: {
    averageScore: number;
    winRate: number;
    currentStreak: number;
    achievementsCount: number;
  };
}

export function MobileQuickStats({ stats }: MobileQuickStatsProps) {
  const statItems = [
    { icon: Target, label: 'Avg Score', value: stats.averageScore.toFixed(1), color: 'text-blue-500' },
    { icon: TrendingUp, label: 'Win Rate', value: `${Math.round(stats.winRate * 100)}%`, color: 'text-green-500' },
    { icon: Zap, label: 'Streak', value: stats.currentStreak.toString(), color: 'text-yellow-500' },
    { icon: Award, label: 'Achievements', value: stats.achievementsCount.toString(), color: 'text-purple-500' },
  ];

  return (
    <div className="grid grid-cols-2 gap-3">
      {statItems.map((stat, index) => (
        <Card key={index} className="bg-white">
          <CardContent className="p-3 text-center">
            <stat.icon className={`h-6 w-6 mx-auto mb-2 ${stat.color}`} />
            <div className="text-xl font-bold">{stat.value}</div>
            <div className="text-xs text-muted-foreground">{stat.label}</div>
          </CardContent>
        </Card>
      ))}
    </div>
  );
}

// Mobile Achievement Progress List
interface MobileAchievementProgressProps {
  achievements: Array<{
    id: string;
    title: string;
    progress: number;
    total: number;
    badge: string;
  }>;
}

export function MobileAchievementProgress({ achievements }: MobileAchievementProgressProps) {
  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center gap-2 text-base">
          <Gift className="h-5 w-5" />
          Achievement Progress
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-4">
        {achievements.slice(0, 3).map((achievement) => (
          <div key={achievement.id} className="space-y-2">
            <div className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <span className="text-lg">{achievement.badge}</span>
                <div className="flex-1">
                  <div className="font-medium text-sm">{achievement.title}</div>
                  <div className="text-xs text-muted-foreground">
                    {achievement.progress}/{achievement.total}
                  </div>
                </div>
              </div>
              <div className="text-xs text-muted-foreground">
                {Math.round((achievement.progress / achievement.total) * 100)}%
              </div>
            </div>
            <Progress 
              value={(achievement.progress / achievement.total) * 100} 
              className="h-2"
            />
          </div>
        ))}
      </CardContent>
    </Card>
  );
}

// Mobile Leaderboard Widget
interface MobileLeaderboardProps {
  entries: Array<{
    rank: number;
    name: string;
    score: number;
    avatar?: string;
  }>;
  userRank?: number;
}

export function MobileLeaderboard({ entries, userRank }: MobileLeaderboardProps) {
  const [showFull, setShowFull] = useState(false);

  return (
    <Card>
      <CardHeader className="pb-3">
        <CardTitle className="flex items-center justify-between text-base">
          <div className="flex items-center gap-2">
            <Trophy className="h-5 w-5" />
            Leaderboard
          </div>
          <Sheet open={showFull} onOpenChange={setShowFull}>
            <SheetTrigger asChild>
              <Button variant="ghost" size="sm">
                <ChevronRight className="h-4 w-4" />
              </Button>
            </SheetTrigger>
            <SheetContent side="bottom" className="h-[80vh]">
              <SheetHeader>
                <SheetTitle>Full Leaderboard</SheetTitle>
              </SheetHeader>
              <div className="mt-4 space-y-2">
                {entries.map((entry) => (
                  <div key={entry.rank} className="flex items-center justify-between p-3 bg-muted rounded-lg">
                    <div className="flex items-center gap-3">
                      <div className="w-8 h-8 bg-primary rounded-full flex items-center justify-center text-white text-sm font-bold">
                        {entry.rank}
                      </div>
                      <div>
                        <div className="font-medium">{entry.name}</div>
                        <div className="text-sm text-muted-foreground">Score: {entry.score}</div>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </SheetContent>
          </Sheet>
        </CardTitle>
      </CardHeader>
      <CardContent className="space-y-2">
        {entries.slice(0, 3).map((entry) => (
          <div key={entry.rank} className="flex items-center justify-between">
            <div className="flex items-center gap-2">
              <div className="w-6 h-6 bg-primary rounded-full flex items-center justify-center text-white text-xs font-bold">
                {entry.rank}
              </div>
              <span className="text-sm font-medium">{entry.name}</span>
            </div>
            <span className="text-sm text-muted-foreground">{entry.score}</span>
          </div>
        ))}
        
        {userRank && userRank > 3 && (
          <div className="pt-2 border-t">
            <div className="flex items-center justify-between bg-blue-50 p-2 rounded">
              <div className="flex items-center gap-2">
                <div className="w-6 h-6 bg-blue-500 rounded-full flex items-center justify-center text-white text-xs font-bold">
                  {userRank}
                </div>
                <span className="text-sm font-medium text-blue-900">You</span>
              </div>
            </div>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

// Mobile Pressure Event Notification
interface MobilePressureEventProps {
  event: {
    id: string;
    title: string;
    message: string;
    intensity: 'low' | 'medium' | 'high';
  };
  show: boolean;
  onAcknowledge: () => void;
}

export function MobilePressureEvent({ event, show, onAcknowledge }: MobilePressureEventProps) {
  const getIntensityColor = (intensity: string) => {
    switch (intensity) {
      case 'high': return 'from-red-500 to-red-600';
      case 'medium': return 'from-orange-500 to-orange-600';
      default: return 'from-blue-500 to-blue-600';
    }
  };

  return (
    <AnimatePresence>
      {show && (
        <motion.div
          initial={{ x: '100%' }}
          animate={{ x: 0 }}
          exit={{ x: '100%' }}
          className="fixed top-20 right-4 left-4 z-40"
        >
          <Card className={`bg-gradient-to-r ${getIntensityColor(event.intensity)} text-white shadow-lg`}>
            <CardContent className="p-4">
              <div className="flex items-start justify-between">
                <div className="flex-1">
                  <h4 className="font-bold text-sm mb-1">{event.title}</h4>
                  <p className="text-xs opacity-90 mb-3">{event.message}</p>
                  <Button
                    size="sm"
                    variant="secondary"
                    onClick={onAcknowledge}
                    className="text-xs"
                  >
                    Acknowledge
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </motion.div>
      )}
    </AnimatePresence>
  );
}
