"use client";

import * as React from "react";
import { ChevronLeft, ChevronRight, MoreHorizontal } from "lucide-react";
import { cn } from "@/lib/utils";
import { Button, ButtonProps } from "@/components/ui/button";

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  className?: string;
}

export function Pagination({ 
  currentPage, 
  totalPages, 
  onPageChange,
  className
}: PaginationProps) {
  // Memoize all the onClick handlers to prevent infinite loops
  const handlePrevious = React.useCallback(() => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  }, [currentPage, onPageChange]);

  const handleNext = React.useCallback(() => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  }, [currentPage, totalPages, onPageChange]);

  const handleFirstPage = React.useCallback(() => {
    if (currentPage !== 1) {
      onPageChange(1);
    }
  }, [currentPage, onPageChange]);

  const handleLastPage = React.useCallback(() => {
    if (currentPage !== totalPages) {
      onPageChange(totalPages);
    }
  }, [currentPage, totalPages, onPageChange]);

  const handlePageClick = React.useCallback((page: number) => {
    if (currentPage !== page) {
      onPageChange(page);
    }
  }, [currentPage, onPageChange]);

  return (
    <nav
      role="navigation"
      aria-label="pagination"
      className={cn("mx-auto flex w-full justify-center", className)}
    >
      <PaginationContent>
        <PaginationItem>
          <PaginationPrevious 
            onClick={handlePrevious}
            className={currentPage === 1 ? "pointer-events-none opacity-50" : "cursor-pointer"}
          />
        </PaginationItem>
        
        {/* First page */}
        <PaginationItem>
          <PaginationLink 
            onClick={handleFirstPage}
            isActive={currentPage === 1}
          >
            1
          </PaginationLink>
        </PaginationItem>
        
        {/* Ellipsis for many pages */}
        {currentPage > 3 && (
          <PaginationItem>
            <PaginationEllipsis />
          </PaginationItem>
        )}
        
        {/* Current page and surrounding pages */}
        {currentPage > 2 && (
          <PaginationItem>
            <PaginationLink 
              onClick={() => handlePageClick(currentPage - 1)}
            >
              {currentPage - 1}
            </PaginationLink>
          </PaginationItem>
        )}
        
        {currentPage > 1 && currentPage < totalPages && (
          <PaginationItem>
            <PaginationLink 
              isActive={true}
            >
              {currentPage}
            </PaginationLink>
          </PaginationItem>
        )}
        
        {currentPage < totalPages - 1 && (
          <PaginationItem>
            <PaginationLink 
              onClick={() => handlePageClick(currentPage + 1)}
            >
              {currentPage + 1}
            </PaginationLink>
          </PaginationItem>
        )}
        
        {/* Ellipsis for many pages */}
        {currentPage < totalPages - 2 && (
          <PaginationItem>
            <PaginationEllipsis />
          </PaginationItem>
        )}
        
        {/* Last page */}
        {totalPages > 1 && (
          <PaginationItem>
            <PaginationLink 
              onClick={handleLastPage}
              isActive={currentPage === totalPages}
            >
              {totalPages}
            </PaginationLink>
          </PaginationItem>
        )}
        
        <PaginationItem>
          <PaginationNext 
            onClick={handleNext}
            className={currentPage === totalPages ? "pointer-events-none opacity-50" : "cursor-pointer"}
          />
        </PaginationItem>
      </PaginationContent>
    </nav>
  );
}

export const PaginationContent = React.memo(React.forwardRef<
  HTMLUListElement,
  React.ComponentProps<"ul">
>(({ className, ...props }, ref) => (
  <ul
    ref={ref}
    className={cn("flex flex-row items-center gap-1", className)}
    {...props}
  />
)));
PaginationContent.displayName = "PaginationContent";

export const PaginationItem = React.memo(React.forwardRef<
  HTMLLIElement,
  React.ComponentProps<"li">
>(({ className, ...props }, ref) => (
  <li ref={ref} className={cn("", className)} {...props} />
)));
PaginationItem.displayName = "PaginationItem";

interface PaginationLinkProps extends React.ComponentProps<"button"> {
  isActive?: boolean;
  onClick?: () => void;
}

export const PaginationLink = React.memo(React.forwardRef<
  HTMLButtonElement,
  PaginationLinkProps
>(({ className, isActive, onClick, ...props }, ref) => (
  <Button
    ref={ref}
    onClick={onClick}
    variant={isActive ? "default" : "outline"}
    size="icon"
    className={cn(
      "h-9 w-9",
      className
    )}
    {...props}
  />
)));
PaginationLink.displayName = "PaginationLink";

export const PaginationPrevious = React.memo(React.forwardRef<
  HTMLButtonElement,
  ButtonProps
>(({ className, ...props }, ref) => (
  <Button
    ref={ref}
    variant="outline"
    size="icon"
    className={cn("h-9 w-9 gap-1", className)}
    {...props}
  >
    <ChevronLeft className="h-4 w-4" />
    <span className="sr-only">Previous</span>
  </Button>
)));
PaginationPrevious.displayName = "PaginationPrevious";

export const PaginationNext = React.memo(React.forwardRef<
  HTMLButtonElement,
  ButtonProps
>(({ className, ...props }, ref) => (
  <Button
    ref={ref}
    variant="outline"
    size="icon"
    className={cn("h-9 w-9 gap-1", className)}
    {...props}
  >
    <ChevronRight className="h-4 w-4" />
    <span className="sr-only">Next</span>
  </Button>
)));
PaginationNext.displayName = "PaginationNext";

export const PaginationEllipsis = React.memo(React.forwardRef<
  HTMLSpanElement,
  React.ComponentProps<"span">
>(({ className, ...props }, ref) => (
  <span
    ref={ref}
    className={cn("flex h-9 w-9 items-center justify-center", className)}
    {...props}
  >
    <MoreHorizontal className="h-4 w-4" />
    <span className="sr-only">More pages</span>
  </span>
)));
PaginationEllipsis.displayName = "PaginationEllipsis";