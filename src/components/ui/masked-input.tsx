"use client";

import { forwardRef, useEffect, useState, useCallback } from "react";
import { Input } from "@/components/ui/input";
import type { ComponentPropsWithoutRef } from "react";

interface MaskedInputProps extends Omit<ComponentPropsWithoutRef<typeof Input>, "onChange"> {
	mask?: string;
	onChange?: (value: string) => void;
}

export const MaskedInput = forwardRef<HTMLInputElement, MaskedInputProps>(
	({ mask, value, onChange, ...props }, ref) => {
		const [displayValue, setDisplayValue] = useState("");

		const formatValue = useCallback((input: string) => {
			if (!mask) return input;

			let formatted = "";
			let inputIndex = 0;

			for (let i = 0; i < mask.length && inputIndex < input.length; i++) {
				if (mask[i] === "#") {
					if (/\d/.test(input[inputIndex])) {
						formatted += input[inputIndex];
						inputIndex++;
					} else {
						inputIndex++;
						i--;
					}
				} else if (mask[i] === "a") {
					if (/[a-zA-Z]/.test(input[inputIndex])) {
						formatted += input[inputIndex];
						inputIndex++;
					} else {
						inputIndex++;
						i--;
					}
				} else {
					formatted += mask[i];
					if (input[inputIndex] === mask[i]) {
						inputIndex++;
					}
				}
			}

			return formatted;
		}, [mask]);

		const handleChange = (e: React.ChangeEvent<HTMLInputElement>) => {
			const input = e.target.value.replace(/[^\d]/g, "");
			const formatted = formatValue(input);
			setDisplayValue(formatted);
			onChange?.(input);
		};

		useEffect(() => {
			if (value) {
				setDisplayValue(formatValue(value.toString()));
			}
		}, [value, formatValue]);

		return (
			<Input
				{...props}
				ref={ref}
				value={displayValue}
				onChange={handleChange}
			/>
		);
	}
);

MaskedInput.displayName = "MaskedInput";