"use client";

import { useState, useEffect, useRef } from 'react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { Input } from '@/components/ui/input';
import { 
  MessageSquare, 
  Reply, 
  MoreHorizontal, 
  ThumbsUp, 
  Heart, 
  Smile,
  Send,
  Plus,
  Check,
  X,
  Edit,
  Trash2,
  AtSign
} from 'lucide-react';
import { 
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';
import { collaborationService } from '@/lib/services/collaboration-service';
import { useCollaboration } from '@/lib/collaboration/collaboration-context';
import type { CommentThread, Comment, Mention } from '@/lib/types/collaboration';
import { format } from 'date-fns';

interface ThreadedCommentsProps {
  documentId: string;
  selectedText?: string;
  anchor?: any;
}

export function ThreadedComments({ documentId, selectedText, anchor }: ThreadedCommentsProps) {
  const { onCommentCreated } = useCollaboration();
  const [threads, setThreads] = useState<CommentThread[]>([]);
  const [newThreadTitle, setNewThreadTitle] = useState('');
  const [newThreadComment, setNewThreadComment] = useState('');
  const [showNewThread, setShowNewThread] = useState(false);
  const [replyingTo, setReplyingTo] = useState<string | null>(null);
  const [replyContent, setReplyContent] = useState('');
  const [editingComment, setEditingComment] = useState<string | null>(null);
  const [editContent, setEditContent] = useState('');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadCommentThreads();
  }, [documentId]);

  useEffect(() => {
    const handleCommentCreated = (threadId: string, comment: Comment) => {
      setThreads(prev => prev.map(thread => 
        thread.id === threadId 
          ? { ...thread, comments: [...thread.comments, comment] }
          : thread
      ));
    };

    onCommentCreated(handleCommentCreated);
  }, [onCommentCreated]);

  const loadCommentThreads = async () => {
    try {
      setLoading(true);
      const threadsData = await collaborationService.getCommentThreads(documentId);
      setThreads(threadsData);
    } catch (error) {
      console.error('Failed to load comment threads:', error);
    } finally {
      setLoading(false);
    }
  };

  const createNewThread = async () => {
    if (!newThreadComment.trim()) return;

    try {
      const thread = await collaborationService.createCommentThread(documentId, {
        title: newThreadTitle || undefined,
        type: 'discussion',
        anchor: selectedText ? { 
          type: 'text', 
          context: selectedText,
          ...anchor 
        } : undefined,
        initialComment: newThreadComment
      });

      setThreads(prev => [thread, ...prev]);
      setNewThreadTitle('');
      setNewThreadComment('');
      setShowNewThread(false);
    } catch (error) {
      console.error('Failed to create comment thread:', error);
    }
  };

  const addReply = async (threadId: string) => {
    if (!replyContent.trim()) return;

    try {
      const comment = await collaborationService.addComment(threadId, replyContent);
      setThreads(prev => prev.map(thread => 
        thread.id === threadId 
          ? { ...thread, comments: [...thread.comments, comment] }
          : thread
      ));
      setReplyContent('');
      setReplyingTo(null);
    } catch (error) {
      console.error('Failed to add reply:', error);
    }
  };

  const updateComment = async (commentId: string) => {
    if (!editContent.trim()) return;

    try {
      const updatedComment = await collaborationService.updateComment(commentId, editContent);
      setThreads(prev => prev.map(thread => ({
        ...thread,
        comments: thread.comments.map(comment => 
          comment.id === commentId ? updatedComment : comment
        )
      })));
      setEditingComment(null);
      setEditContent('');
    } catch (error) {
      console.error('Failed to update comment:', error);
    }
  };

  const deleteComment = async (commentId: string) => {
    try {
      await collaborationService.deleteComment(commentId);
      setThreads(prev => prev.map(thread => ({
        ...thread,
        comments: thread.comments.filter(comment => comment.id !== commentId)
      })));
    } catch (error) {
      console.error('Failed to delete comment:', error);
    }
  };

  const resolveThread = async (threadId: string) => {
    try {
      const updatedThread = await collaborationService.resolveThread(threadId);
      setThreads(prev => prev.map(thread => 
        thread.id === threadId ? updatedThread : thread
      ));
    } catch (error) {
      console.error('Failed to resolve thread:', error);
    }
  };

  const addReaction = async (commentId: string, emoji: string) => {
    try {
      const updatedComment = await collaborationService.addReaction(commentId, emoji);
      setThreads(prev => prev.map(thread => ({
        ...thread,
        comments: thread.comments.map(comment => 
          comment.id === commentId ? updatedComment : comment
        )
      })));
    } catch (error) {
      console.error('Failed to add reaction:', error);
    }
  };

  const renderComment = (comment: Comment, isReply = false) => (
    <div key={comment.id} className={`${isReply ? 'ml-8 mt-2' : 'mt-4'} border-l-2 border-muted pl-4`}>
      <div className="flex items-start gap-3">
        <Avatar className="h-8 w-8">
          <AvatarImage src={comment.user.avatar} />
          <AvatarFallback>{comment.user.name.charAt(0).toUpperCase()}</AvatarFallback>
        </Avatar>
        
        <div className="flex-1">
          <div className="flex items-center gap-2 mb-1">
            <span className="font-medium text-sm">{comment.user.name}</span>
            <span className="text-xs text-muted-foreground">
              {format(new Date(comment.createdAt), 'MMM d, yyyy h:mm a')}
            </span>
            {comment.isEdited && (
              <Badge variant="outline" className="text-xs">edited</Badge>
            )}
          </div>
          
          {editingComment === comment.id ? (
            <div className="space-y-2">
              <Textarea
                value={editContent}
                onChange={(e) => setEditContent(e.target.value)}
                className="min-h-[80px]"
              />
              <div className="flex gap-2">
                <Button size="sm" onClick={() => updateComment(comment.id)}>
                  <Check className="h-4 w-4 mr-1" />
                  Save
                </Button>
                <Button 
                  size="sm" 
                  variant="outline" 
                  onClick={() => {
                    setEditingComment(null);
                    setEditContent('');
                  }}
                >
                  <X className="h-4 w-4 mr-1" />
                  Cancel
                </Button>
              </div>
            </div>
          ) : (
            <>
              <p className="text-sm mb-2">{comment.content}</p>
              
              {/* Reactions */}
              {comment.reactions.length > 0 && (
                <div className="flex gap-1 mb-2">
                  {comment.reactions.map((reaction, index) => (
                    <Button
                      key={index}
                      size="sm"
                      variant="outline"
                      className="h-6 px-2 text-xs"
                      onClick={() => addReaction(comment.id, reaction.emoji)}
                    >
                      {reaction.emoji} {reaction.userId ? 1 : 0}
                    </Button>
                  ))}
                </div>
              )}
              
              {/* Actions */}
              <div className="flex items-center gap-2">
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => setReplyingTo(comment.id)}
                >
                  <Reply className="h-3 w-3 mr-1" />
                  Reply
                </Button>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => addReaction(comment.id, '👍')}
                >
                  <ThumbsUp className="h-3 w-3" />
                </Button>
                
                <Button
                  size="sm"
                  variant="ghost"
                  onClick={() => addReaction(comment.id, '❤️')}
                >
                  <Heart className="h-3 w-3" />
                </Button>
                
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button size="sm" variant="ghost">
                      <MoreHorizontal className="h-3 w-3" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent>
                    <DropdownMenuItem 
                      onClick={() => {
                        setEditingComment(comment.id);
                        setEditContent(comment.content);
                      }}
                    >
                      <Edit className="h-4 w-4 mr-2" />
                      Edit
                    </DropdownMenuItem>
                    <DropdownMenuItem 
                      onClick={() => deleteComment(comment.id)}
                      className="text-destructive"
                    >
                      <Trash2 className="h-4 w-4 mr-2" />
                      Delete
                    </DropdownMenuItem>
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </>
          )}
        </div>
      </div>
      
      {/* Reply form */}
      {replyingTo === comment.id && (
        <div className="mt-3 ml-11">
          <Textarea
            value={replyContent}
            onChange={(e) => setReplyContent(e.target.value)}
            placeholder="Write a reply..."
            className="min-h-[80px] mb-2"
          />
          <div className="flex gap-2">
            <Button size="sm" onClick={() => addReply(comment.threadId)}>
              <Send className="h-4 w-4 mr-1" />
              Reply
            </Button>
            <Button 
              size="sm" 
              variant="outline" 
              onClick={() => {
                setReplyingTo(null);
                setReplyContent('');
              }}
            >
              Cancel
            </Button>
          </div>
        </div>
      )}
      
      {/* Nested replies */}
      {comment.replies.map(reply => renderComment(reply, true))}
    </div>
  );

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            Comments & Discussions
          </CardTitle>
          <Button size="sm" onClick={() => setShowNewThread(true)}>
            <Plus className="h-4 w-4 mr-2" />
            New Thread
          </Button>
        </div>
      </CardHeader>
      
      <CardContent className="space-y-4">
        {/* New thread form */}
        {showNewThread && (
          <Card>
            <CardContent className="p-4 space-y-3">
              <Input
                value={newThreadTitle}
                onChange={(e) => setNewThreadTitle(e.target.value)}
                placeholder="Thread title (optional)"
              />
              {selectedText && (
                <div className="p-2 bg-muted rounded text-sm">
                  <strong>Selected text:</strong> "{selectedText}"
                </div>
              )}
              <Textarea
                value={newThreadComment}
                onChange={(e) => setNewThreadComment(e.target.value)}
                placeholder="Start a discussion..."
                className="min-h-[100px]"
              />
              <div className="flex gap-2">
                <Button onClick={createNewThread}>
                  <Send className="h-4 w-4 mr-2" />
                  Create Thread
                </Button>
                <Button 
                  variant="outline" 
                  onClick={() => {
                    setShowNewThread(false);
                    setNewThreadTitle('');
                    setNewThreadComment('');
                  }}
                >
                  Cancel
                </Button>
              </div>
            </CardContent>
          </Card>
        )}
        
        {/* Comment threads */}
        {threads.length === 0 ? (
          <div className="text-center py-8">
            <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
            <p className="text-muted-foreground">No comments yet</p>
            <p className="text-sm text-muted-foreground">Start a discussion to collaborate with your team</p>
          </div>
        ) : (
          <div className="space-y-6">
            {threads.map((thread) => (
              <Card key={thread.id}>
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-3">
                    <div className="flex items-center gap-2">
                      {thread.title && (
                        <h4 className="font-medium">{thread.title}</h4>
                      )}
                      <Badge variant={thread.status === 'open' ? 'default' : 'secondary'}>
                        {thread.status}
                      </Badge>
                      <Badge variant="outline">{thread.type}</Badge>
                    </div>
                    {thread.status === 'open' && (
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => resolveThread(thread.id)}
                      >
                        <Check className="h-4 w-4 mr-1" />
                        Resolve
                      </Button>
                    )}
                  </div>
                  
                  {thread.anchor && (
                    <div className="p-2 bg-muted rounded text-sm mb-3">
                      <strong>Context:</strong> "{thread.anchor.context}"
                    </div>
                  )}
                  
                  <div className="space-y-2">
                    {thread.comments.map(comment => renderComment(comment))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        )}
      </CardContent>
    </Card>
  );
}
