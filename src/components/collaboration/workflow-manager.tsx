"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Progress } from '@/components/ui/progress';
import { 
  Workflow, 
  Play, 
  Pause, 
  Square, 
  CheckCircle, 
  Clock, 
  User, 
  Plus,
  Eye,
  Edit,
  Trash2,
  ArrowRight
} from 'lucide-react';
import { 
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from '@/components/ui/dialog';
import { collaborationService } from '@/lib/services/collaboration-service';
import { useCollaboration } from '@/lib/collaboration/collaboration-context';
import type { 
  WorkflowTemplate, 
  WorkflowInstance, 
  WorkflowStep,
  Task 
} from '@/lib/types/collaboration';
import { format } from 'date-fns';

interface WorkflowManagerProps {
  documentId: string;
}

export function WorkflowManager({ documentId }: WorkflowManagerProps) {
  const { onWorkflowStatusChanged } = useCollaboration();
  const [templates, setTemplates] = useState<WorkflowTemplate[]>([]);
  const [instances, setInstances] = useState<WorkflowInstance[]>([]);
  const [selectedTemplate, setSelectedTemplate] = useState<WorkflowTemplate | null>(null);
  const [selectedInstance, setSelectedInstance] = useState<WorkflowInstance | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadWorkflowData();
  }, [documentId]);

  useEffect(() => {
    const handleWorkflowStatusChanged = (workflowInstanceId: string, status: string) => {
      setInstances(prev => prev.map(instance => 
        instance.id === workflowInstanceId 
          ? { ...instance, status: status as any }
          : instance
      ));
    };

    onWorkflowStatusChanged(handleWorkflowStatusChanged);
  }, [onWorkflowStatusChanged]);

  const loadWorkflowData = async () => {
    try {
      setLoading(true);
      const [templatesData, instancesData] = await Promise.all([
        collaborationService.getWorkflowTemplates(),
        collaborationService.getWorkflowInstances(documentId)
      ]);
      setTemplates(templatesData);
      setInstances(instancesData);
    } catch (error) {
      console.error('Failed to load workflow data:', error);
    } finally {
      setLoading(false);
    }
  };

  const startWorkflow = async (templateId: string) => {
    try {
      const instance = await collaborationService.createWorkflowInstance(templateId, documentId);
      setInstances(prev => [...prev, instance]);
    } catch (error) {
      console.error('Failed to start workflow:', error);
    }
  };

  const executeStep = async (instanceId: string, stepId: string) => {
    try {
      const updatedInstance = await collaborationService.executeWorkflowStep(instanceId, stepId);
      setInstances(prev => prev.map(instance => 
        instance.id === instanceId ? updatedInstance : instance
      ));
    } catch (error) {
      console.error('Failed to execute workflow step:', error);
    }
  };

  const cancelWorkflow = async (instanceId: string) => {
    try {
      const updatedInstance = await collaborationService.cancelWorkflowInstance(instanceId);
      setInstances(prev => prev.map(instance => 
        instance.id === instanceId ? updatedInstance : instance
      ));
    } catch (error) {
      console.error('Failed to cancel workflow:', error);
    }
  };

  const getWorkflowProgress = (instance: WorkflowInstance) => {
    const totalSteps = instance.template.steps.length;
    const currentStepIndex = instance.template.steps.findIndex(step => step.id === instance.currentStepId);
    const completedSteps = currentStepIndex >= 0 ? currentStepIndex : totalSteps;
    return (completedSteps / totalSteps) * 100;
  };

  const getStepStatus = (step: WorkflowStep, instance: WorkflowInstance) => {
    const currentStepIndex = instance.template.steps.findIndex(s => s.id === instance.currentStepId);
    const stepIndex = instance.template.steps.findIndex(s => s.id === step.id);
    
    if (instance.status === 'completed') return 'completed';
    if (instance.status === 'cancelled') return 'cancelled';
    if (stepIndex < currentStepIndex) return 'completed';
    if (stepIndex === currentStepIndex) return 'active';
    return 'pending';
  };

  const renderWorkflowStep = (step: WorkflowStep, instance: WorkflowInstance, index: number) => {
    const status = getStepStatus(step, instance);
    const task = instance.tasks.find(t => t.workflowStepId === step.id);
    
    return (
      <div key={step.id} className="flex items-center gap-4 p-4 border rounded-lg">
        <div className="flex items-center gap-2">
          <div className={`w-8 h-8 rounded-full flex items-center justify-center text-sm font-medium ${
            status === 'completed' ? 'bg-green-500 text-white' :
            status === 'active' ? 'bg-blue-500 text-white' :
            status === 'cancelled' ? 'bg-red-500 text-white' :
            'bg-gray-200 text-gray-600'
          }`}>
            {status === 'completed' ? <CheckCircle className="h-4 w-4" /> : index + 1}
          </div>
          {index < instance.template.steps.length - 1 && (
            <ArrowRight className="h-4 w-4 text-muted-foreground" />
          )}
        </div>
        
        <div className="flex-1">
          <div className="flex items-center justify-between">
            <div>
              <h4 className="font-medium">{step.name}</h4>
              <p className="text-sm text-muted-foreground">{step.description}</p>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="outline">{step.type}</Badge>
                {step.assigneeId && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <User className="h-3 w-3" />
                    Assigned to {step.assigneeId}
                  </div>
                )}
                {step.dueInHours && (
                  <div className="flex items-center gap-1 text-xs text-muted-foreground">
                    <Clock className="h-3 w-3" />
                    Due in {step.dueInHours}h
                  </div>
                )}
              </div>
            </div>
            
            {status === 'active' && (
              <Button 
                size="sm" 
                onClick={() => executeStep(instance.id, step.id)}
              >
                Execute Step
              </Button>
            )}
          </div>
          
          {task && (
            <div className="mt-2 p-2 bg-muted rounded text-sm">
              <p><strong>Task:</strong> {task.title}</p>
              <p><strong>Status:</strong> {task.status}</p>
              {task.dueAt && (
                <p><strong>Due:</strong> {format(new Date(task.dueAt), 'MMM d, yyyy h:mm a')}</p>
              )}
            </div>
          )}
        </div>
      </div>
    );
  };

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h3 className="text-lg font-semibold">Workflow Management</h3>
          <p className="text-sm text-muted-foreground">
            Manage document review and approval workflows
          </p>
        </div>
        <Dialog>
          <DialogTrigger asChild>
            <Button>
              <Plus className="h-4 w-4 mr-2" />
              Start Workflow
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-2xl">
            <DialogHeader>
              <DialogTitle>Start New Workflow</DialogTitle>
              <DialogDescription>
                Choose a workflow template to start the review process
              </DialogDescription>
            </DialogHeader>
            <div className="space-y-4">
              {templates.map((template) => (
                <Card key={template.id} className="cursor-pointer hover:bg-muted/50">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between">
                      <div>
                        <h4 className="font-medium">{template.name}</h4>
                        <p className="text-sm text-muted-foreground">{template.description}</p>
                        <p className="text-xs text-muted-foreground mt-1">
                          {template.steps.length} steps
                        </p>
                      </div>
                      <Button onClick={() => startWorkflow(template.id)}>
                        <Play className="h-4 w-4 mr-2" />
                        Start
                      </Button>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          </DialogContent>
        </Dialog>
      </div>

      {/* Active Workflow Instances */}
      <div className="space-y-4">
        <h4 className="font-medium">Active Workflows</h4>
        {instances.length === 0 ? (
          <Card>
            <CardContent className="p-8 text-center">
              <Workflow className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">No active workflows</p>
              <p className="text-sm text-muted-foreground">Start a workflow to begin the review process</p>
            </CardContent>
          </Card>
        ) : (
          instances.map((instance) => (
            <Card key={instance.id}>
              <CardHeader>
                <div className="flex items-center justify-between">
                  <div>
                    <CardTitle className="text-lg">{instance.template.name}</CardTitle>
                    <p className="text-sm text-muted-foreground">{instance.template.description}</p>
                  </div>
                  <div className="flex items-center gap-2">
                    <Badge variant={
                      instance.status === 'completed' ? 'default' :
                      instance.status === 'in_progress' ? 'secondary' :
                      instance.status === 'cancelled' ? 'destructive' : 'outline'
                    }>
                      {instance.status}
                    </Badge>
                    {instance.status === 'in_progress' && (
                      <Button 
                        size="sm" 
                        variant="outline"
                        onClick={() => cancelWorkflow(instance.id)}
                      >
                        <Square className="h-4 w-4 mr-2" />
                        Cancel
                      </Button>
                    )}
                  </div>
                </div>
                
                {/* Progress */}
                <div className="space-y-2">
                  <div className="flex items-center justify-between text-sm">
                    <span>Progress</span>
                    <span>{Math.round(getWorkflowProgress(instance))}%</span>
                  </div>
                  <Progress value={getWorkflowProgress(instance)} />
                </div>
              </CardHeader>
              
              <CardContent className="space-y-4">
                <div className="text-sm text-muted-foreground">
                  Started {format(new Date(instance.startedAt), 'MMM d, yyyy h:mm a')}
                  {instance.completedAt && (
                    <> • Completed {format(new Date(instance.completedAt), 'MMM d, yyyy h:mm a')}</>
                  )}
                </div>
                
                {/* Workflow Steps */}
                <div className="space-y-3">
                  {instance.template.steps.map((step, index) => 
                    renderWorkflowStep(step, instance, index)
                  )}
                </div>
              </CardContent>
            </Card>
          ))
        )}
      </div>
    </div>
  );
}
