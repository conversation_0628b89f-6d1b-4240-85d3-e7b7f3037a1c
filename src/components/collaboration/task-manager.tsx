"use client";

import { useState, useEffect } from 'react';
import { <PERSON>, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { But<PERSON> } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Textarea } from '@/components/ui/textarea';
import { <PERSON><PERSON>, <PERSON>bsContent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  CheckSquare, 
  Clock, 
  User, 
  AlertTriangle,
  CheckCircle,
  XCircle,
  MessageSquare,
  Calendar,
  Filter,
  SortAsc
} from 'lucide-react';
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { collaborationService } from '@/lib/services/collaboration-service';
import { useCollaboration } from '@/lib/collaboration/collaboration-context';
import type { Task, TaskComment } from '@/lib/types/collaboration';
import { format, isAfter, isBefore, addDays } from 'date-fns';

interface TaskManagerProps {
  documentId?: string;
  userId?: string;
}

export function TaskManager({ documentId, userId }: TaskManagerProps) {
  const { onTaskAssigned } = useCollaboration();
  const [tasks, setTasks] = useState<Task[]>([]);
  const [filteredTasks, setFilteredTasks] = useState<Task[]>([]);
  const [selectedTask, setSelectedTask] = useState<Task | null>(null);
  const [newComment, setNewComment] = useState('');
  const [statusFilter, setStatusFilter] = useState<string>('all');
  const [priorityFilter, setPriorityFilter] = useState<string>('all');
  const [sortBy, setSortBy] = useState<string>('dueDate');
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadTasks();
  }, [documentId, userId]);

  useEffect(() => {
    const handleTaskAssigned = (task: Task) => {
      setTasks(prev => [task, ...prev]);
    };

    onTaskAssigned(handleTaskAssigned);
  }, [onTaskAssigned]);

  useEffect(() => {
    filterAndSortTasks();
  }, [tasks, statusFilter, priorityFilter, sortBy]);

  const loadTasks = async () => {
    try {
      setLoading(true);
      const filters: any = {};
      if (documentId) filters.documentId = documentId;
      if (userId) filters.assigneeId = userId;
      
      const tasksData = await collaborationService.getTasks(filters);
      setTasks(tasksData);
    } catch (error) {
      console.error('Failed to load tasks:', error);
    } finally {
      setLoading(false);
    }
  };

  const filterAndSortTasks = () => {
    let filtered = [...tasks];

    // Apply status filter
    if (statusFilter !== 'all') {
      filtered = filtered.filter(task => task.status === statusFilter);
    }

    // Apply priority filter
    if (priorityFilter !== 'all') {
      filtered = filtered.filter(task => task.priority === priorityFilter);
    }

    // Apply sorting
    filtered.sort((a, b) => {
      switch (sortBy) {
        case 'dueDate':
          if (!a.dueAt && !b.dueAt) return 0;
          if (!a.dueAt) return 1;
          if (!b.dueAt) return -1;
          return new Date(a.dueAt).getTime() - new Date(b.dueAt).getTime();
        case 'priority':
          const priorityOrder = { urgent: 0, high: 1, medium: 2, low: 3 };
          return priorityOrder[a.priority] - priorityOrder[b.priority];
        case 'created':
          return new Date(b.assignedAt).getTime() - new Date(a.assignedAt).getTime();
        default:
          return 0;
      }
    });

    setFilteredTasks(filtered);
  };

  const updateTaskStatus = async (taskId: string, status: string, comment?: string) => {
    try {
      const updatedTask = await collaborationService.updateTaskStatus(taskId, status, comment);
      setTasks(prev => prev.map(task => 
        task.id === taskId ? updatedTask : task
      ));
      if (selectedTask?.id === taskId) {
        setSelectedTask(updatedTask);
      }
    } catch (error) {
      console.error('Failed to update task status:', error);
    }
  };

  const addTaskComment = async (taskId: string) => {
    if (!newComment.trim()) return;

    try {
      const updatedTask = await collaborationService.addTaskComment(taskId, newComment);
      setTasks(prev => prev.map(task => 
        task.id === taskId ? updatedTask : task
      ));
      if (selectedTask?.id === taskId) {
        setSelectedTask(updatedTask);
      }
      setNewComment('');
    } catch (error) {
      console.error('Failed to add task comment:', error);
    }
  };

  const getTaskPriorityColor = (priority: string) => {
    switch (priority) {
      case 'urgent': return 'bg-red-500';
      case 'high': return 'bg-orange-500';
      case 'medium': return 'bg-yellow-500';
      case 'low': return 'bg-green-500';
      default: return 'bg-gray-500';
    }
  };

  const getTaskStatusColor = (status: string) => {
    switch (status) {
      case 'completed': return 'text-green-600';
      case 'in_progress': return 'text-blue-600';
      case 'overdue': return 'text-red-600';
      case 'cancelled': return 'text-gray-600';
      default: return 'text-yellow-600';
    }
  };

  const isTaskOverdue = (task: Task) => {
    return task.dueAt && isAfter(new Date(), new Date(task.dueAt)) && task.status !== 'completed';
  };

  const getTasksByStatus = (status: string) => {
    return filteredTasks.filter(task => task.status === status);
  };

  const renderTaskCard = (task: Task) => (
    <Card 
      key={task.id} 
      className={`cursor-pointer hover:bg-muted/50 ${
        selectedTask?.id === task.id ? 'ring-2 ring-primary' : ''
      } ${isTaskOverdue(task) ? 'border-red-200' : ''}`}
      onClick={() => setSelectedTask(task)}
    >
      <CardContent className="p-4">
        <div className="flex items-start justify-between">
          <div className="flex items-start gap-3 flex-1">
            <div className={`w-3 h-3 rounded-full mt-1 ${getTaskPriorityColor(task.priority)}`} />
            <div className="flex-1">
              <h4 className="font-medium">{task.title}</h4>
              <p className="text-sm text-muted-foreground mt-1">{task.description}</p>
              
              <div className="flex items-center gap-4 mt-2 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <User className="h-3 w-3" />
                  {task.assignee.name}
                </div>
                {task.dueAt && (
                  <div className={`flex items-center gap-1 ${
                    isTaskOverdue(task) ? 'text-red-600' : ''
                  }`}>
                    <Clock className="h-3 w-3" />
                    {format(new Date(task.dueAt), 'MMM d, yyyy')}
                  </div>
                )}
                <div className="flex items-center gap-1">
                  <Calendar className="h-3 w-3" />
                  {format(new Date(task.assignedAt), 'MMM d')}
                </div>
              </div>
            </div>
          </div>
          
          <div className="flex flex-col items-end gap-2">
            <Badge variant="outline" className={getTaskStatusColor(task.status)}>
              {task.status}
            </Badge>
            <Badge variant="outline">{task.type}</Badge>
            {isTaskOverdue(task) && (
              <Badge variant="destructive" className="text-xs">
                <AlertTriangle className="h-3 w-3 mr-1" />
                Overdue
              </Badge>
            )}
          </div>
        </div>
      </CardContent>
    </Card>
  );

  if (loading) {
    return (
      <Card>
        <CardContent className="p-6">
          <div className="flex items-center justify-center">
            <div className="animate-spin rounded-full h-6 w-6 border-b-2 border-primary"></div>
          </div>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
      {/* Task List */}
      <div className="lg:col-span-2 space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-lg font-semibold">Task Management</h3>
          <div className="flex items-center gap-2">
            <Select value={statusFilter} onValueChange={setStatusFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Status" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Status</SelectItem>
                <SelectItem value="pending">Pending</SelectItem>
                <SelectItem value="in_progress">In Progress</SelectItem>
                <SelectItem value="completed">Completed</SelectItem>
                <SelectItem value="overdue">Overdue</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={priorityFilter} onValueChange={setPriorityFilter}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Priority" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Priority</SelectItem>
                <SelectItem value="urgent">Urgent</SelectItem>
                <SelectItem value="high">High</SelectItem>
                <SelectItem value="medium">Medium</SelectItem>
                <SelectItem value="low">Low</SelectItem>
              </SelectContent>
            </Select>
            
            <Select value={sortBy} onValueChange={setSortBy}>
              <SelectTrigger className="w-32">
                <SelectValue placeholder="Sort by" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="dueDate">Due Date</SelectItem>
                <SelectItem value="priority">Priority</SelectItem>
                <SelectItem value="created">Created</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>

        <Tabs defaultValue="kanban" className="space-y-4">
          <TabsList>
            <TabsTrigger value="kanban">Kanban View</TabsTrigger>
            <TabsTrigger value="list">List View</TabsTrigger>
          </TabsList>

          <TabsContent value="kanban" className="space-y-4">
            <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
              {/* Pending Tasks */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground">
                  PENDING ({getTasksByStatus('pending').length})
                </h4>
                <div className="space-y-2">
                  {getTasksByStatus('pending').map(renderTaskCard)}
                </div>
              </div>

              {/* In Progress Tasks */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground">
                  IN PROGRESS ({getTasksByStatus('in_progress').length})
                </h4>
                <div className="space-y-2">
                  {getTasksByStatus('in_progress').map(renderTaskCard)}
                </div>
              </div>

              {/* Completed Tasks */}
              <div className="space-y-3">
                <h4 className="font-medium text-sm text-muted-foreground">
                  COMPLETED ({getTasksByStatus('completed').length})
                </h4>
                <div className="space-y-2">
                  {getTasksByStatus('completed').map(renderTaskCard)}
                </div>
              </div>
            </div>
          </TabsContent>

          <TabsContent value="list" className="space-y-2">
            {filteredTasks.length === 0 ? (
              <Card>
                <CardContent className="p-8 text-center">
                  <CheckSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No tasks found</p>
                </CardContent>
              </Card>
            ) : (
              filteredTasks.map(renderTaskCard)
            )}
          </TabsContent>
        </Tabs>
      </div>

      {/* Task Details */}
      <div className="space-y-4">
        {selectedTask ? (
          <Card>
            <CardHeader>
              <div className="flex items-start justify-between">
                <div>
                  <CardTitle className="text-lg">{selectedTask.title}</CardTitle>
                  <p className="text-sm text-muted-foreground mt-1">{selectedTask.description}</p>
                </div>
                <Badge variant="outline" className={getTaskPriorityColor(selectedTask.priority)}>
                  {selectedTask.priority}
                </Badge>
              </div>
            </CardHeader>
            
            <CardContent className="space-y-4">
              {/* Task Info */}
              <div className="space-y-2 text-sm">
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Status:</span>
                  <Badge variant="outline" className={getTaskStatusColor(selectedTask.status)}>
                    {selectedTask.status}
                  </Badge>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Type:</span>
                  <span>{selectedTask.type}</span>
                </div>
                <div className="flex justify-between">
                  <span className="text-muted-foreground">Assigned to:</span>
                  <span>{selectedTask.assignee.name}</span>
                </div>
                {selectedTask.dueAt && (
                  <div className="flex justify-between">
                    <span className="text-muted-foreground">Due date:</span>
                    <span className={isTaskOverdue(selectedTask) ? 'text-red-600' : ''}>
                      {format(new Date(selectedTask.dueAt), 'MMM d, yyyy h:mm a')}
                    </span>
                  </div>
                )}
              </div>

              {/* Status Actions */}
              {selectedTask.status !== 'completed' && selectedTask.status !== 'cancelled' && (
                <div className="flex gap-2">
                  {selectedTask.status === 'pending' && (
                    <Button 
                      size="sm" 
                      onClick={() => updateTaskStatus(selectedTask.id, 'in_progress')}
                    >
                      Start Task
                    </Button>
                  )}
                  {selectedTask.status === 'in_progress' && (
                    <Button 
                      size="sm" 
                      onClick={() => updateTaskStatus(selectedTask.id, 'completed')}
                    >
                      <CheckCircle className="h-4 w-4 mr-2" />
                      Complete
                    </Button>
                  )}
                  <Button 
                    size="sm" 
                    variant="outline"
                    onClick={() => updateTaskStatus(selectedTask.id, 'cancelled')}
                  >
                    <XCircle className="h-4 w-4 mr-2" />
                    Cancel
                  </Button>
                </div>
              )}

              {/* Comments */}
              <div className="space-y-3">
                <h4 className="font-medium flex items-center gap-2">
                  <MessageSquare className="h-4 w-4" />
                  Comments ({selectedTask.comments.length})
                </h4>
                
                <div className="space-y-3 max-h-64 overflow-y-auto">
                  {selectedTask.comments.map((comment) => (
                    <div key={comment.id} className="flex gap-3 p-3 bg-muted rounded">
                      <Avatar className="h-6 w-6">
                        <AvatarFallback className="text-xs">
                          {comment.user.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1">
                        <div className="flex items-center gap-2 mb-1">
                          <span className="text-sm font-medium">{comment.user.name}</span>
                          <span className="text-xs text-muted-foreground">
                            {format(new Date(comment.createdAt), 'MMM d, h:mm a')}
                          </span>
                        </div>
                        <p className="text-sm">{comment.content}</p>
                      </div>
                    </div>
                  ))}
                </div>

                {/* Add Comment */}
                <div className="space-y-2">
                  <Textarea
                    value={newComment}
                    onChange={(e) => setNewComment(e.target.value)}
                    placeholder="Add a comment..."
                    className="min-h-[80px]"
                  />
                  <Button 
                    size="sm" 
                    onClick={() => addTaskComment(selectedTask.id)}
                    disabled={!newComment.trim()}
                  >
                    Add Comment
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        ) : (
          <Card>
            <CardContent className="p-8 text-center">
              <CheckSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <p className="text-muted-foreground">Select a task to view details</p>
            </CardContent>
          </Card>
        )}
      </div>
    </div>
  );
}
