"use client";

import { useState, useEffect, useRef, useCallback } from 'react';
import { Card, Card<PERSON>ontent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar';
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip';
import { 
  Users, 
  Share2, 
  Settings, 
  Eye, 
  EyeOff,
  Cursor,
  UserCheck,
  UserX
} from 'lucide-react';
import { useCollaboration } from '@/lib/collaboration/collaboration-context';
import { collaborationService } from '@/lib/services/collaboration-service';
import type { 
  CollaborationSession, 
  SessionParticipant, 
  DocumentOperation,
  CursorPosition 
} from '@/lib/types/collaboration';

interface RealTimeEditorProps {
  documentId: string;
  content: string;
  onContentChange: (content: string) => void;
  readOnly?: boolean;
}

export function RealTimeEditor({ 
  documentId, 
  content, 
  onContentChange, 
  readOnly = false 
}: RealTimeEditorProps) {
  const {
    isConnected,
    currentSession,
    participants,
    joinSession,
    leaveSession,
    sendOperation,
    updateCursor,
    onOperationReceived,
    onCursorUpdate,
    onParticipantJoin,
    onParticipantLeave,
    onPresenceChange
  } = useCollaboration();

  const [isCollaborating, setIsCollaborating] = useState(false);
  const [showCursors, setShowCursors] = useState(true);
  const [participantCursors, setParticipantCursors] = useState<Map<string, CursorPosition>>(new Map());
  const [localContent, setLocalContent] = useState(content);
  const editorRef = useRef<HTMLTextAreaElement>(null);
  const lastOperationRef = useRef<string>('');

  // Initialize collaboration session
  useEffect(() => {
    if (isCollaborating && !currentSession) {
      startCollaboration();
    }
  }, [isCollaborating]);

  // Set up event handlers
  useEffect(() => {
    const handleOperation = (operation: DocumentOperation) => {
      if (operation.userId === lastOperationRef.current) return; // Skip own operations
      
      applyOperation(operation);
    };

    const handleCursorUpdate = (userId: string, cursor: CursorPosition) => {
      setParticipantCursors(prev => new Map(prev.set(userId, cursor)));
    };

    const handleParticipantJoin = (participant: SessionParticipant) => {
      console.log(`${participant.user.name} joined the collaboration`);
    };

    const handleParticipantLeave = (userId: string) => {
      setParticipantCursors(prev => {
        const newMap = new Map(prev);
        newMap.delete(userId);
        return newMap;
      });
    };

    onOperationReceived(handleOperation);
    onCursorUpdate(handleCursorUpdate);
    onParticipantJoin(handleParticipantJoin);
    onParticipantLeave(handleParticipantLeave);

    return () => {
      // Cleanup would go here if needed
    };
  }, [onOperationReceived, onCursorUpdate, onParticipantJoin, onParticipantLeave]);

  const startCollaboration = async () => {
    try {
      // Create or join existing session
      const sessions = await collaborationService.getActiveSessions(documentId);
      let session;
      
      if (sessions.length > 0) {
        // Join existing session
        session = sessions[0];
        await joinSession(session.id);
      } else {
        // Create new session
        session = await collaborationService.createSession(documentId);
        await joinSession(session.id);
      }
      
      setIsCollaborating(true);
    } catch (error) {
      console.error('Failed to start collaboration:', error);
      setIsCollaborating(false);
    }
  };

  const stopCollaboration = async () => {
    try {
      await leaveSession();
      setIsCollaborating(false);
      setParticipantCursors(new Map());
    } catch (error) {
      console.error('Failed to stop collaboration:', error);
    }
  };

  const applyOperation = (operation: DocumentOperation) => {
    const currentContent = localContent;
    let newContent = currentContent;

    switch (operation.type) {
      case 'insert':
        newContent = 
          currentContent.slice(0, operation.position) + 
          (operation.content || '') + 
          currentContent.slice(operation.position);
        break;
      case 'delete':
        newContent = 
          currentContent.slice(0, operation.position) + 
          currentContent.slice(operation.position + (operation.length || 0));
        break;
      // Add more operation types as needed
    }

    setLocalContent(newContent);
    onContentChange(newContent);
  };

  const handleTextChange = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    const newContent = e.target.value;
    const oldContent = localContent;
    
    if (newContent === oldContent) return;

    // Calculate the operation
    const operation = calculateOperation(oldContent, newContent);
    
    if (operation && isCollaborating) {
      lastOperationRef.current = operation.userId || '';
      sendOperation(operation);
    }

    setLocalContent(newContent);
    onContentChange(newContent);
  };

  const handleCursorMove = (e: React.ChangeEvent<HTMLTextAreaElement>) => {
    if (!isCollaborating || !editorRef.current) return;

    const cursor: CursorPosition = {
      x: e.target.selectionStart,
      y: e.target.selectionEnd,
      selection: e.target.selectionStart !== e.target.selectionEnd ? {
        start: e.target.selectionStart,
        end: e.target.selectionEnd
      } : undefined
    };

    updateCursor(cursor);
  };

  const calculateOperation = (oldContent: string, newContent: string): Omit<DocumentOperation, 'id' | 'timestamp' | 'applied'> | null => {
    // Simple diff algorithm - in production, use a more sophisticated approach
    if (newContent.length > oldContent.length) {
      // Insert operation
      for (let i = 0; i < Math.min(oldContent.length, newContent.length); i++) {
        if (oldContent[i] !== newContent[i]) {
          return {
            sessionId: currentSession?.id || '',
            userId: '', // Will be set by the server
            type: 'insert',
            position: i,
            content: newContent.slice(i, i + (newContent.length - oldContent.length))
          };
        }
      }
      // Insert at end
      return {
        sessionId: currentSession?.id || '',
        userId: '',
        type: 'insert',
        position: oldContent.length,
        content: newContent.slice(oldContent.length)
      };
    } else if (newContent.length < oldContent.length) {
      // Delete operation
      for (let i = 0; i < Math.min(oldContent.length, newContent.length); i++) {
        if (oldContent[i] !== newContent[i]) {
          return {
            sessionId: currentSession?.id || '',
            userId: '',
            type: 'delete',
            position: i,
            length: oldContent.length - newContent.length
          };
        }
      }
      // Delete from end
      return {
        sessionId: currentSession?.id || '',
        userId: '',
        type: 'delete',
        position: newContent.length,
        length: oldContent.length - newContent.length
      };
    }
    
    return null;
  };

  const renderParticipantCursors = () => {
    if (!showCursors || !editorRef.current) return null;

    return Array.from(participantCursors.entries()).map(([userId, cursor]) => {
      const participant = participants.find(p => p.userId === userId);
      if (!participant) return null;

      // Calculate cursor position in the editor
      // This is a simplified version - in production, you'd need more sophisticated positioning
      const cursorStyle = {
        position: 'absolute' as const,
        left: `${cursor.x * 8}px`, // Approximate character width
        top: `${Math.floor(cursor.x / 80) * 20}px`, // Approximate line height
        width: '2px',
        height: '20px',
        backgroundColor: getParticipantColor(userId),
        zIndex: 10
      };

      return (
        <div key={userId} style={cursorStyle}>
          <div 
            className="absolute -top-6 left-0 px-2 py-1 text-xs text-white rounded"
            style={{ backgroundColor: getParticipantColor(userId) }}
          >
            {participant.user.name}
          </div>
        </div>
      );
    });
  };

  const getParticipantColor = (userId: string) => {
    // Generate consistent color for each participant
    const colors = ['#3B82F6', '#EF4444', '#10B981', '#F59E0B', '#8B5CF6', '#EC4899'];
    const index = userId.split('').reduce((acc, char) => acc + char.charCodeAt(0), 0);
    return colors[index % colors.length];
  };

  return (
    <Card className="h-full">
      <CardHeader>
        <div className="flex items-center justify-between">
          <CardTitle className="flex items-center gap-2">
            <Users className="h-5 w-5" />
            Real-time Collaboration
            {isConnected && (
              <Badge variant="outline" className="text-green-600 border-green-600">
                Connected
              </Badge>
            )}
          </CardTitle>
          <div className="flex items-center gap-2">
            {isCollaborating && (
              <>
                <TooltipProvider>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Button
                        size="sm"
                        variant="outline"
                        onClick={() => setShowCursors(!showCursors)}
                      >
                        {showCursors ? <Eye className="h-4 w-4" /> : <EyeOff className="h-4 w-4" />}
                      </Button>
                    </TooltipTrigger>
                    <TooltipContent>
                      {showCursors ? 'Hide cursors' : 'Show cursors'}
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
                
                <Button size="sm" variant="outline" onClick={stopCollaboration}>
                  <UserX className="h-4 w-4 mr-2" />
                  Leave
                </Button>
              </>
            )}
            
            {!isCollaborating && (
              <Button size="sm" onClick={() => setIsCollaborating(true)} disabled={!isConnected}>
                <UserCheck className="h-4 w-4 mr-2" />
                Start Collaboration
              </Button>
            )}
          </div>
        </div>
        
        {/* Participants */}
        {isCollaborating && participants.length > 0 && (
          <div className="flex items-center gap-2">
            <span className="text-sm text-muted-foreground">Participants:</span>
            <div className="flex -space-x-2">
              {participants.map((participant) => (
                <TooltipProvider key={participant.userId}>
                  <Tooltip>
                    <TooltipTrigger asChild>
                      <Avatar className="h-8 w-8 border-2 border-background">
                        <AvatarImage src={participant.user.avatar} />
                        <AvatarFallback style={{ backgroundColor: getParticipantColor(participant.userId) }}>
                          {participant.user.name.charAt(0).toUpperCase()}
                        </AvatarFallback>
                      </Avatar>
                    </TooltipTrigger>
                    <TooltipContent>
                      <div>
                        <p className="font-medium">{participant.user.name}</p>
                        <p className="text-xs text-muted-foreground">
                          {participant.presence} • Joined {new Date(participant.joinedAt).toLocaleTimeString()}
                        </p>
                      </div>
                    </TooltipContent>
                  </Tooltip>
                </TooltipProvider>
              ))}
            </div>
          </div>
        )}
      </CardHeader>
      
      <CardContent className="relative">
        <div className="relative">
          <textarea
            ref={editorRef}
            value={localContent}
            onChange={handleTextChange}
            onSelect={handleCursorMove}
            onKeyUp={handleCursorMove}
            onClick={handleCursorMove}
            readOnly={readOnly}
            className="w-full h-96 p-4 border rounded-md resize-none font-mono text-sm"
            placeholder="Start typing to collaborate in real-time..."
          />
          {renderParticipantCursors()}
        </div>
        
        {isCollaborating && (
          <div className="mt-4 text-xs text-muted-foreground">
            Real-time collaboration is active. Changes are synchronized automatically.
          </div>
        )}
      </CardContent>
    </Card>
  );
}
