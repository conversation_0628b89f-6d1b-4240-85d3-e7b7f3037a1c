"use client";

import { useState, useEffect } from 'react';
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Badge } from '@/components/ui/badge';
import { Ta<PERSON>, <PERSON><PERSON><PERSON>ontent, TabsList, TabsTrigger } from '@/components/ui/tabs';
import { 
  Users, 
  MessageSquare, 
  CheckSquare, 
  Workflow, 
  Clock, 
  TrendingUp,
  Plus,
  Eye,
  Edit,
  Share2,
  Bell
} from 'lucide-react';
import { collaborationService } from '@/lib/services/collaboration-service';
import { useCollaboration } from '@/lib/collaboration/collaboration-context';
import type { 
  CollaborationSession, 
  Task, 
  CommentThread, 
  WorkflowInstance,
  CollaborationMetrics 
} from '@/lib/types/collaboration';
import { format } from 'date-fns';

interface CollaborationDashboardProps {
  documentId?: string;
}

export function CollaborationDashboard({ documentId }: CollaborationDashboardProps) {
  const { isConnected, currentSession } = useCollaboration();
  const [activeSessions, setActiveSessions] = useState<CollaborationSession[]>([]);
  const [myTasks, setMyTasks] = useState<Task[]>([]);
  const [recentComments, setRecentComments] = useState<CommentThread[]>([]);
  const [activeWorkflows, setActiveWorkflows] = useState<WorkflowInstance[]>([]);
  const [metrics, setMetrics] = useState<CollaborationMetrics | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    loadDashboardData();
  }, [documentId]);

  const loadDashboardData = async () => {
    try {
      setLoading(true);
      
      // Load active collaboration sessions
      const sessions = await collaborationService.getActiveSessions(documentId);
      setActiveSessions(sessions);

      // Load my tasks
      const tasks = await collaborationService.getTasks({
        status: 'pending'
      });
      setMyTasks(tasks);

      // Load recent comment threads
      if (documentId) {
        const threads = await collaborationService.getCommentThreads(documentId);
        setRecentComments(threads.slice(0, 5)); // Show latest 5
      }

      // Load active workflows
      const workflows = await collaborationService.getWorkflowInstances(documentId);
      setActiveWorkflows(workflows.filter(w => w.status === 'in_progress'));

      // Load collaboration metrics
      const metricsData = await collaborationService.getCollaborationMetrics();
      setMetrics(metricsData);

    } catch (error) {
      console.error('Failed to load dashboard data:', error);
    } finally {
      setLoading(false);
    }
  };

  const handleStartCollaboration = async () => {
    if (!documentId) return;
    
    try {
      const session = await collaborationService.createSession(documentId);
      setActiveSessions(prev => [...prev, session]);
    } catch (error) {
      console.error('Failed to start collaboration:', error);
    }
  };

  if (loading) {
    return (
      <div className="flex items-center justify-center h-64">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">Collaboration Dashboard</h2>
          <p className="text-muted-foreground">
            Manage real-time collaboration, workflows, and team communication
          </p>
        </div>
        <div className="flex items-center gap-2">
          <Badge variant={isConnected ? "default" : "destructive"}>
            {isConnected ? "Connected" : "Disconnected"}
          </Badge>
          {documentId && (
            <Button onClick={handleStartCollaboration}>
              <Users className="h-4 w-4 mr-2" />
              Start Collaboration
            </Button>
          )}
        </div>
      </div>

      {/* Metrics Overview */}
      {metrics && (
        <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Active Sessions</p>
                  <p className="text-2xl font-bold">{metrics.activeCollaborations}</p>
                </div>
                <Users className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Total Comments</p>
                  <p className="text-2xl font-bold">{metrics.totalComments}</p>
                </div>
                <MessageSquare className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Tasks Completed</p>
                  <p className="text-2xl font-bold">{metrics.tasksCompleted}</p>
                </div>
                <CheckSquare className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
          
          <Card>
            <CardContent className="p-4">
              <div className="flex items-center justify-between">
                <div>
                  <p className="text-sm text-muted-foreground">Workflows Done</p>
                  <p className="text-2xl font-bold">{metrics.workflowsCompleted}</p>
                </div>
                <Workflow className="h-8 w-8 text-primary" />
              </div>
            </CardContent>
          </Card>
        </div>
      )}

      {/* Main Content Tabs */}
      <Tabs defaultValue="sessions" className="space-y-4">
        <TabsList>
          <TabsTrigger value="sessions">Active Sessions</TabsTrigger>
          <TabsTrigger value="tasks">My Tasks</TabsTrigger>
          <TabsTrigger value="comments">Recent Comments</TabsTrigger>
          <TabsTrigger value="workflows">Workflows</TabsTrigger>
        </TabsList>

        {/* Active Sessions */}
        <TabsContent value="sessions" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Collaboration Sessions</CardTitle>
              <CardDescription>
                Real-time collaborative editing sessions currently in progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              {activeSessions.length === 0 ? (
                <div className="text-center py-8">
                  <Users className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No active collaboration sessions</p>
                  {documentId && (
                    <Button onClick={handleStartCollaboration} className="mt-4">
                      Start New Session
                    </Button>
                  )}
                </div>
              ) : (
                <div className="space-y-4">
                  {activeSessions.map((session) => (
                    <div key={session.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="flex -space-x-2">
                          {session.participants.slice(0, 3).map((participant) => (
                            <div
                              key={participant.userId}
                              className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium border-2 border-background"
                            >
                              {participant.user.name.charAt(0).toUpperCase()}
                            </div>
                          ))}
                          {session.participants.length > 3 && (
                            <div className="w-8 h-8 rounded-full bg-muted text-muted-foreground flex items-center justify-center text-sm font-medium border-2 border-background">
                              +{session.participants.length - 3}
                            </div>
                          )}
                        </div>
                        <div>
                          <p className="font-medium">Document Collaboration</p>
                          <p className="text-sm text-muted-foreground">
                            {session.participants.length} participant{session.participants.length !== 1 ? 's' : ''}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{session.status}</Badge>
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-2" />
                          View
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* My Tasks */}
        <TabsContent value="tasks" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>My Tasks</CardTitle>
              <CardDescription>
                Tasks assigned to you that require attention
              </CardDescription>
            </CardHeader>
            <CardContent>
              {myTasks.length === 0 ? (
                <div className="text-center py-8">
                  <CheckSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No pending tasks</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {myTasks.map((task) => (
                    <div key={task.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className={`w-3 h-3 rounded-full ${
                          task.priority === 'urgent' ? 'bg-red-500' :
                          task.priority === 'high' ? 'bg-orange-500' :
                          task.priority === 'medium' ? 'bg-yellow-500' : 'bg-green-500'
                        }`} />
                        <div>
                          <p className="font-medium">{task.title}</p>
                          <p className="text-sm text-muted-foreground">{task.description}</p>
                          {task.dueAt && (
                            <p className="text-xs text-muted-foreground flex items-center mt-1">
                              <Clock className="h-3 w-3 mr-1" />
                              Due {format(new Date(task.dueAt), 'MMM d, yyyy')}
                            </p>
                          )}
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{task.type}</Badge>
                        <Button size="sm">
                          <Edit className="h-4 w-4 mr-2" />
                          Work on Task
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Recent Comments */}
        <TabsContent value="comments" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Recent Comments</CardTitle>
              <CardDescription>
                Latest comment threads and discussions
              </CardDescription>
            </CardHeader>
            <CardContent>
              {recentComments.length === 0 ? (
                <div className="text-center py-8">
                  <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No recent comments</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {recentComments.map((thread) => (
                    <div key={thread.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <div className="w-8 h-8 rounded-full bg-primary text-primary-foreground flex items-center justify-center text-sm font-medium">
                          {thread.creator.name.charAt(0).toUpperCase()}
                        </div>
                        <div>
                          <p className="font-medium">{thread.title || 'Discussion Thread'}</p>
                          <p className="text-sm text-muted-foreground">
                            {thread.comments.length} comment{thread.comments.length !== 1 ? 's' : ''} • 
                            {format(new Date(thread.updatedAt), 'MMM d, yyyy')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant={thread.status === 'open' ? 'default' : 'secondary'}>
                          {thread.status}
                        </Badge>
                        <Button size="sm" variant="outline">
                          <MessageSquare className="h-4 w-4 mr-2" />
                          View Thread
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>

        {/* Active Workflows */}
        <TabsContent value="workflows" className="space-y-4">
          <Card>
            <CardHeader>
              <CardTitle>Active Workflows</CardTitle>
              <CardDescription>
                Workflows currently in progress
              </CardDescription>
            </CardHeader>
            <CardContent>
              {activeWorkflows.length === 0 ? (
                <div className="text-center py-8">
                  <Workflow className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
                  <p className="text-muted-foreground">No active workflows</p>
                </div>
              ) : (
                <div className="space-y-4">
                  {activeWorkflows.map((workflow) => (
                    <div key={workflow.id} className="flex items-center justify-between p-4 border rounded-lg">
                      <div className="flex items-center gap-4">
                        <Workflow className="h-8 w-8 text-primary" />
                        <div>
                          <p className="font-medium">{workflow.template.name}</p>
                          <p className="text-sm text-muted-foreground">{workflow.template.description}</p>
                          <p className="text-xs text-muted-foreground">
                            Started {format(new Date(workflow.startedAt), 'MMM d, yyyy')}
                          </p>
                        </div>
                      </div>
                      <div className="flex items-center gap-2">
                        <Badge variant="outline">{workflow.status}</Badge>
                        <Button size="sm" variant="outline">
                          <Eye className="h-4 w-4 mr-2" />
                          View Progress
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              )}
            </CardContent>
          </Card>
        </TabsContent>
      </Tabs>
    </div>
  );
}
