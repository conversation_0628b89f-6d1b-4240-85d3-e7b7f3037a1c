"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, CardHeader, Card<PERSON>itle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Ta<PERSON>, <PERSON><PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Calendar, FileText, ExternalLink, Filter } from "lucide-react"
import { cn } from "@/lib/utils"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuCheckboxItem,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"

interface DocketEntry {
  date_filed: string
  description: string
  document_number: string
  document_url?: string
  pacer_doc_id?: string
}

interface DocketTimelineProps {
  docket: {
    docket_number: string
    court_id: string
    case_name: string
    date_filed: string
    date_argued?: string
    date_reargued?: string
    date_cert_granted?: string
    date_terminated?: string
    entries: DocketEntry[]
  }
}

export function DocketTimeline({ docket }: DocketTimelineProps) {
  const [activeTab, setActiveTab] = useState<"timeline" | "table">("timeline")
  const [filterType, setFilterType] = useState<string[]>([])

  // Sort entries by date (newest first for the table view)
  const sortedEntries = [...(docket?.entries || [])].sort(
    (a, b) => new Date(b.date_filed).getTime() - new Date(a.date_filed).getTime(),
  )

  // For timeline view, we want oldest first
  const timelineEntries = [...sortedEntries].reverse()

  // Get unique document types for filtering
  const documentTypes = Array.from(
    new Set((docket?.entries || []).map((entry) => getDocumentType(entry.description)).filter(Boolean)),
  )

  // Filter entries based on selected document types
  const filteredEntries =
    filterType.length > 0
      ? sortedEntries.filter((entry) => filterType.includes(getDocumentType(entry.description)))
      : sortedEntries

  const filteredTimelineEntries =
    filterType.length > 0
      ? timelineEntries.filter((entry) => filterType.includes(getDocumentType(entry.description)))
      : timelineEntries

  // Helper function to extract document type from description
  function getDocumentType(description: string): string {
    // Common document types in federal court dockets
    const types = [
      "Motion",
      "Order",
      "Notice",
      "Minute Entry",
      "Memorandum",
      "Opinion",
      "Judgment",
      "Complaint",
      "Answer",
      "Brief",
      "Declaration",
      "Affidavit",
      "Transcript",
      "Exhibit",
      "Reply",
      "Response",
      "Stipulation",
      "Summons",
      "Subpoena",
      "Petition",
    ]

    for (const type of types) {
      if (description.includes(type)) {
        return type
      }
    }

    return "Other"
  }

  // Helper function to format date
  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  // Helper function to get a color for the timeline node based on document type
  function getTimelineColor(entry: DocketEntry): string {
    const type = getDocumentType(entry.description)

    switch (type) {
      case "Motion":
        return "bg-blue-500"
      case "Order":
        return "bg-purple-500"
      case "Judgment":
        return "bg-green-500"
      case "Opinion":
        return "bg-amber-500"
      case "Complaint":
        return "bg-red-500"
      case "Brief":
        return "bg-indigo-500"
      case "Notice":
        return "bg-teal-500"
      default:
        return "bg-gray-500"
    }
  }

  // Helper function to get document URL if available
  function getDocumentUrl(entry: DocketEntry): string | null {
    if (entry.document_url) {
      return entry.document_url
    }

    if (entry.pacer_doc_id) {
      return `https://www.courtlistener.com/docket/${docket.docket_number}/document/${entry.pacer_doc_id}/`
    }

    return null
  }

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3 flex flex-row justify-between items-center">
        <div>
          <CardTitle className="text-lg font-semibold text-foreground">Docket Timeline</CardTitle>
          <div className="text-sm text-muted-foreground">
            {docket.docket_number} • {docket.court_id.toUpperCase()}
          </div>
        </div>

        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" size="sm" className="h-8 text-xs">
              <Filter className="h-3.5 w-3.5 mr-1.5" />
              Filter
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end" className="w-48">
            {documentTypes.map((type) => (
              <DropdownMenuCheckboxItem
                key={type}
                checked={filterType.includes(type)}
                onCheckedChange={(checked) => {
                  if (checked) {
                    setFilterType([...filterType, type])
                  } else {
                    setFilterType(filterType.filter((t) => t !== type))
                  }
                }}
              >
                {type}
              </DropdownMenuCheckboxItem>
            ))}
            {filterType.length > 0 && (
              <Button variant="ghost" size="sm" className="w-full mt-2 h-7 text-xs" onClick={() => setFilterType([])}>
                Clear Filters
              </Button>
            )}
          </DropdownMenuContent>
        </DropdownMenu>
      </CardHeader>

      <CardContent className="p-0">
        <Tabs value={activeTab} onValueChange={(value) => setActiveTab(value as "timeline" | "table")} className="w-full">
          <TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
            <TabsTrigger
              value="timeline"
              className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Timeline View
            </TabsTrigger>
            <TabsTrigger
              value="table"
              className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
            >
              Table View
            </TabsTrigger>
          </TabsList>

          <TabsContent value="timeline" className="mt-0">
            <ScrollArea className="h-[500px] p-4">
              {filteredTimelineEntries.length > 0 ? (
                <div className="relative pl-8 pb-8">
                  {/* Vertical line */}
                  <div className="absolute left-4 top-0 bottom-0 w-px bg-border dark:bg-neutral-700"></div>

                  {/* Case filed marker */}
                  {docket.date_filed && (
                    <div className="relative mb-8">
                      <div className="absolute left-[-1rem] w-8 h-8 rounded-full bg-primary flex items-center justify-center">
                        <Calendar className="h-4 w-4 text-primary-foreground" />
                      </div>
                      <div className="ml-4 pt-1">
                        <h3 className="text-sm font-medium">Case Filed</h3>
                        <time className="text-xs text-muted-foreground">{formatDate(docket.date_filed)}</time>
                      </div>
                    </div>
                  )}

                  {/* Timeline entries */}
                  {filteredTimelineEntries.map((entry, index) => (
                    <div key={index} className="relative mb-8">
                      <div
                        className={cn(
                          "absolute left-[-1rem] w-8 h-8 rounded-full flex items-center justify-center",
                          getTimelineColor(entry),
                        )}
                      >
                        <FileText className="h-4 w-4 text-white" />
                      </div>
                      <div className="ml-4 pt-1">
                        <div className="flex items-center">
                          <h3 className="text-sm font-medium">
                            {entry.document_number && `#${entry.document_number}`}
                          </h3>
                          <Badge className="ml-2 text-xs" variant="outline">
                            {getDocumentType(entry.description)}
                          </Badge>
                        </div>
                        <time className="text-xs text-muted-foreground block mb-1">{formatDate(entry.date_filed)}</time>
                        <p className="text-sm">{entry.description}</p>

                        {getDocumentUrl(entry) && (
                          <a
                            href={getDocumentUrl(entry)!}
                            target="_blank"
                            rel="noopener noreferrer"
                            className="inline-flex items-center text-primary hover:underline text-xs mt-1"
                          >
                            <ExternalLink className="h-3 w-3 mr-1" />
                            View Document
                          </a>
                        )}
                      </div>
                    </div>
                  ))}

                  {/* Case terminated marker (if applicable) */}
                  {docket.date_terminated && (
                    <div className="relative">
                      <div className="absolute left-[-1rem] w-8 h-8 rounded-full bg-red-500 flex items-center justify-center">
                        <Calendar className="h-4 w-4 text-white" />
                      </div>
                      <div className="ml-4 pt-1">
                        <h3 className="text-sm font-medium">Case Terminated</h3>
                        <time className="text-xs text-muted-foreground">{formatDate(docket.date_terminated)}</time>
                      </div>
                    </div>
                  )}
                </div>
              ) : (
                <div className="p-8 text-center text-muted-foreground">
                  <p>No docket entries match your filter criteria.</p>
                </div>
              )}
            </ScrollArea>
          </TabsContent>

          <TabsContent value="table" className="mt-0">
            <div className="overflow-x-auto">
              <table className="w-full border-collapse">
                <thead>
                  <tr className="bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700">
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">#</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Date</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Type</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Description</th>
                    <th className="px-4 py-2 text-left text-xs font-medium text-muted-foreground">Document</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-border dark:divide-neutral-700">
                  {filteredEntries.length > 0 ? (
                    filteredEntries.map((entry, index) => (
                      <tr key={index} className="hover:bg-secondary/10 dark:hover:bg-[#252525]">
                        <td className="px-4 py-3 text-sm">{entry.document_number || "-"}</td>
                        <td className="px-4 py-3 text-sm whitespace-nowrap">{formatDate(entry.date_filed)}</td>
                        <td className="px-4 py-3">
                          <Badge variant="outline" className="text-xs">
                            {getDocumentType(entry.description)}
                          </Badge>
                        </td>
                        <td className="px-4 py-3 text-sm">{entry.description}</td>
                        <td className="px-4 py-3">
                          {getDocumentUrl(entry) ? (
                            <a
                              href={getDocumentUrl(entry)!}
                              target="_blank"
                              rel="noopener noreferrer"
                              className="inline-flex items-center text-primary hover:underline text-xs"
                            >
                              <ExternalLink className="h-3 w-3 mr-1" />
                              View
                            </a>
                          ) : (
                            <span className="text-xs text-muted-foreground">-</span>
                          )}
                        </td>
                      </tr>
                    ))
                  ) : (
                    <tr>
                      <td colSpan={5} className="px-4 py-8 text-center text-muted-foreground">
                        No docket entries match your filter criteria.
                      </td>
                    </tr>
                  )}
                </tbody>
              </table>
            </div>
          </TabsContent>
        </Tabs>
      </CardContent>
    </Card>
  )
}
