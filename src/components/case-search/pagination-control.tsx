"use client";

import { <PERSON><PERSON> } from "@/components/ui/button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import { ChevronLeft, ChevronRight } from "lucide-react";

interface PaginationControlsProps {
	currentPage: number;
	totalPages: number;
	pageSize: number;
	hasNextPage: boolean;
	hasPreviousPage: boolean;
	isLoading: boolean;
	onPageChange: (page: number) => void;
	onPageSizeChange: (size: number) => void;
}

export function PaginationControls({
	currentPage,
	totalPages,
	pageSize,
	hasNextPage,
	hasPreviousPage,
	isLoading,
	onPageChange,
	onPageSizeChange,
}: PaginationControlsProps) {
	return (
		<div className="flex flex-col sm:flex-row justify-between items-center gap-4 w-full">
			<div className="flex items-center space-x-2">
				<span className="text-xs text-muted-foreground">Results per page:</span>
				<Select
					value={pageSize.toString()}
					onValueChange={(value) => onPageSizeChange(Number.parseInt(value))}
				>
					<SelectTrigger className="h-7 w-16 text-xs">
						<SelectValue placeholder={pageSize.toString()} />
					</SelectTrigger>
					<SelectContent>
						<SelectItem value="5">5</SelectItem>
						<SelectItem value="10">10</SelectItem>
						<SelectItem value="20">20</SelectItem>
						<SelectItem value="50">50</SelectItem>
					</SelectContent>
				</Select>
			</div>

			<div className="flex items-center">
				<Button
					variant="outline"
					size="sm"
					className="h-8 w-8 p-0 rounded-r-none"
					onClick={() => onPageChange(currentPage - 1)}
					disabled={!hasPreviousPage || isLoading}
				>
					<ChevronLeft className="h-4 w-4" />
					<span className="sr-only">Previous Page</span>
				</Button>

				<div className="px-4 h-8 flex items-center justify-center border-y border-border dark:border-neutral-700">
					<span className="text-xs font-medium">
						Page {currentPage} of {totalPages}
					</span>
				</div>

				<Button
					variant="outline"
					size="sm"
					className="h-8 w-8 p-0 rounded-l-none"
					onClick={() => onPageChange(currentPage + 1)}
					disabled={!hasNextPage || isLoading}
				>
					<ChevronRight className="h-4 w-4" />
					<span className="sr-only">Next Page</span>
				</Button>
			</div>
		</div>
	);
}
