"use client";

import { useState, useEffect } from "react";
import {
	<PERSON>,
	CardContent,
	CardDescription,
	<PERSON><PERSON><PERSON><PERSON>,
	Card<PERSON><PERSON>er,
	CardTitle,
} from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Ta<PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, Ta<PERSON>Trigger } from "@/components/ui/tabs";
import { ScrollArea } from "@/components/ui/scroll-area";
import {
	ArrowLeft,
	Calendar,
	Gavel,
	BookOpen,
	FileText,
	ExternalLink,
	Users,
	Scale,
	Copy,
	Download,
	Check,
	Clock,
	Loader2,
} from "lucide-react";
import { courtListenerService } from "@/lib/services/court-listener-service";
import { DocketTimeline } from "./docket-timeline-events";
import { CaseTimelineEvents } from "./case-timeline-events";

interface Citation {
	volume: number;
	reporter: string;
	page: string;
}

interface Opinion {
	type: 'lead-opinion' | 'concurrence-opinion' | 'dissent';
	author_id?: string;
	per_curiam?: boolean;
	snippet?: string;
}

export interface CaseData {
	docket_id: string;
	cluster_id?: string;
	caseName: string;
	caseNameFull: string;
	dateFiled: string;
	dateArgued?: string;
	dateReargued?: string;
	dateTerminated?: string;
	court: string;
	status?: string;
	judge?: string;
	docketNumber?: string;
	absolute_url: string; // Added this property
	citation?: string[];
	citeCount?: number;
	opinions?: Opinion[];
	attorney?: string;
}

interface DocketEntry {
	date_filed: string;
	description: string;
	document_number: string;
	pacer_doc_id?: string;
}

interface DocketData {
	docket_number: string;
	court_id: string;
	court: string;
	case_name: string;
	date_filed: string | null;
	date_argued: string | null;
	date_reargued: string | null;
	date_cert_granted: string | null;
	date_terminated: string | null;
	date_cert_denied: string | null;
}

interface ClusterData {
	citations?: Citation[];
	procedural_history?: string;
}

interface CaseDetailProps {
	caseData: CaseData;
	onBack: () => void;
}

export function CaseDetail({ caseData, onBack }: CaseDetailProps) {
	const [activeTab, setActiveTab] = useState("overview");
	const [copied, setCopied] = useState(false);
	const [docketData, setDocketData] = useState<DocketData | null>(null);
	const [docketEntries, setDocketEntries] = useState<DocketEntry[]>([]);
	const [clusterData, setClusterData] = useState<ClusterData | null>(null);
	const [isLoading, setIsLoading] = useState(false);
	const [error, setError] = useState<string | null>(null);

	useEffect(() => {
		const fetchDocketData = async () => {
			if (!caseData.docket_id) return;

			setIsLoading(true);
			setError(null);

			try {
				// Fetch docket information
				const docketResponse = await courtListenerService.getDocket(
					caseData.docket_id
				);
				setDocketData(docketResponse.data);

				// Fetch docket entries
				const entriesResponse = await courtListenerService.getDocketEntries(
					caseData.docket_id
				);
				setDocketEntries(entriesResponse.data.results);

				// Fetch cluster information if available
				if (caseData.cluster_id) {
					const clusterResponse = await courtListenerService.getCluster(
						caseData.cluster_id
					);
					setClusterData(clusterResponse.data);
				}
			} catch (err) {
				console.error("Error fetching docket data:", err);
				setError("Failed to load docket information");
			} finally {
				setIsLoading(false);
			}
		};

		fetchDocketData();
	}, [caseData.docket_id, caseData.cluster_id]);

	const copyToClipboard = (text: string) => {
		navigator.clipboard
			.writeText(text)
			.then(() => {
				setCopied(true);
				setTimeout(() => setCopied(false), 2000);
			})
			.catch((err) => {
				console.error("Failed to copy:", err);
			});
	};

	const downloadCitation = () => {
		// Create citation text in a common format
		const citationText =
			caseData.citation && caseData.citation.length > 0
				? `${caseData.caseNameFull}, ${caseData.citation[0]} (${new Date(
						caseData.dateFiled
				  ).getFullYear()})`
				: `${caseData.caseNameFull} (${new Date(
						caseData.dateFiled
				  ).getFullYear()})`;

		// Create a blob and download it
		const blob = new Blob([citationText], { type: "text/plain" });
		const url = URL.createObjectURL(blob);
		const a = document.createElement("a");
		a.href = url;
		a.download = `${caseData.caseName.replace(/\s+/g, "_")}_citation.txt`;
		document.body.appendChild(a);
		a.click();
		document.body.removeChild(a);
		URL.revokeObjectURL(url);
	};

	// Check if we have multiple key dates to show the timeline
	const hasMultipleDates =
		[
			caseData.dateFiled,
			caseData.dateArgued,
			caseData.dateReargued,
			caseData.dateTerminated,
		].filter(Boolean).length > 1;

	// Helper function to ensure we have a valid date string
	const getValidDate = (dateString: string | null | undefined, fallback?: string | undefined): string | undefined => {
		if (dateString) return dateString;
		if (fallback) return fallback;
		return undefined;
	};

	// Prepare docket data for the DocketTimeline component
	const docketTimelineData = docketData
		? {
				docket_number: docketData.docket_number,
				court_id: docketData.court_id,
				case_name: docketData.case_name,
				date_filed: getValidDate(docketData.date_filed, caseData.dateFiled) || new Date().toISOString(),
				date_argued: getValidDate(docketData.date_argued, caseData.dateArgued),
				date_reargued: getValidDate(docketData.date_reargued, caseData.dateReargued),
				date_cert_granted: getValidDate(docketData.date_cert_granted),
				date_terminated: getValidDate(docketData.date_terminated, caseData.dateTerminated),
				entries: docketEntries.map((entry) => ({
					date_filed: entry.date_filed,
					description: entry.description,
					document_number: entry.document_number,
					pacer_doc_id: entry.pacer_doc_id,
				})),
		  }
		: null;

	return (
		<Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
			<CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
				<div className="flex items-start justify-between">
					<Button
						variant="ghost"
						size="sm"
						onClick={onBack}
						className="mr-2 -ml-2"
					>
						<ArrowLeft className="h-4 w-4 mr-1" />
						Back to Results
					</Button>

					<div className="flex gap-2">
						{caseData.status && (
							<Badge
								variant="outline"
								className="bg-green-500/20 text-green-500 border-green-500/30"
							>
								{caseData.status}
							</Badge>
						)}
					</div>
				</div>

				<CardTitle className="text-xl font-bold mt-2">
					{caseData.caseName}
				</CardTitle>
				<CardDescription className="text-muted-foreground">
					{caseData.court} •{" "}
					{caseData.dateFiled &&
						new Date(caseData.dateFiled).toLocaleDateString()}
				</CardDescription>
			</CardHeader>

			<CardContent className="p-0">
				<Tabs value={activeTab} onValueChange={setActiveTab} className="w-full">
					<TabsList className="w-full rounded-none bg-secondary/20 dark:bg-[#252525] border-y border-border dark:border-neutral-700 h-auto py-1">
						<TabsTrigger
							value="overview"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Overview
						</TabsTrigger>
						<TabsTrigger
							value="citations"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							Citations
						</TabsTrigger>
						<TabsTrigger
							value="docket"
							className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
						>
							<Clock className="h-3.5 w-3.5 mr-1.5" />
							Docket Timeline
						</TabsTrigger>
						{caseData.opinions && caseData.opinions.length > 0 && (
							<TabsTrigger
								value="opinions"
								className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
							>
								Opinions
							</TabsTrigger>
						)}
						{caseData.attorney && (
							<TabsTrigger
								value="attorneys"
								className="text-xs data-[state=active]:bg-secondary/50 dark:data-[state=active]:bg-[#2a2a2a]"
							>
								Attorneys
							</TabsTrigger>
						)}
					</TabsList>

					<TabsContent value="overview" className="p-4 mt-0">
						<div className="space-y-4">
							{/* Add the case timeline events at the top of the overview */}
							{hasMultipleDates && <CaseTimelineEvents caseData={caseData} />}

							<div>
								<h4 className="text-sm font-medium mb-2">Case Name (Full)</h4>
								<p className="text-sm bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
									{caseData.caseNameFull}
								</p>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<h4 className="text-sm font-medium mb-2 flex items-center">
										<Calendar className="h-4 w-4 mr-1.5 text-blue-500" />
										Date Filed
									</h4>
									<p className="text-sm">
										{caseData.dateFiled
											? new Date(caseData.dateFiled).toLocaleDateString()
											: "Not available"}
									</p>
								</div>
								<div>
									<h4 className="text-sm font-medium mb-2 flex items-center">
										<Gavel className="h-4 w-4 mr-1.5 text-blue-500" />
										Court
									</h4>
									<p className="text-sm">{caseData.court}</p>
								</div>
							</div>

							<div className="grid grid-cols-1 md:grid-cols-2 gap-4">
								<div>
									<h4 className="text-sm font-medium mb-2 flex items-center">
										<FileText className="h-4 w-4 mr-1.5 text-blue-500" />
										Docket Number
									</h4>
									<p className="text-sm">
										{caseData.docketNumber ||
											docketData?.docket_number ||
											"Not available"}
									</p>
								</div>
								<div>
									<h4 className="text-sm font-medium mb-2 flex items-center">
										<Users className="h-4 w-4 mr-1.5 text-blue-500" />
										Judges
									</h4>
									<p className="text-sm">{caseData.judge || "Not available"}</p>
								</div>
							</div>

							{caseData.citeCount && caseData.citeCount > 0 && (
								<div>
									<h4 className="text-sm font-medium mb-2 flex items-center">
										<Scale className="h-4 w-4 mr-1.5 text-blue-500" />
										Citation Impact
									</h4>
									<p className="text-sm">
										Cited by {caseData.citeCount} other cases
									</p>
								</div>
							)}

							{clusterData?.procedural_history && (
								<div>
									<h4 className="text-sm font-medium mb-2 flex items-center">
										<Clock className="h-4 w-4 mr-1.5 text-blue-500" />
										Procedural History
									</h4>
									<p className="text-sm bg-secondary/20 dark:bg-[#252525] p-3 rounded-md whitespace-pre-wrap">
										{clusterData.procedural_history}
									</p>
								</div>
							)}

							<div className="pt-2">
								<a
									href={`https://www.courtlistener.com${caseData.absolute_url}`}
									target="_blank"
									rel="noopener noreferrer"
									className="inline-flex items-center text-primary hover:underline"
								>
									<ExternalLink className="h-4 w-4 mr-1.5" />
									View on CourtListener
								</a>
							</div>
						</div>
					</TabsContent>

					<TabsContent value="citations" className="p-4 mt-0">
						<h4 className="text-sm font-medium mb-3">Citations</h4>
						{caseData.citation && caseData.citation.length > 0 ? (
							<div className="space-y-2">
								{caseData.citation.map((citation: string, index: number) => (
									<div
										key={index}
										className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md"
									>
										<div className="flex justify-between items-center">
											<div className="flex items-center">
												<BookOpen className="h-4 w-4 mr-2 text-blue-500" />
												<span className="text-sm font-medium">{citation}</span>
											</div>
											<Button
												variant="ghost"
												size="sm"
												className="h-7 w-7 p-0"
												onClick={() => copyToClipboard(citation)}
											>
												<Copy className="h-3.5 w-3.5" />
												<span className="sr-only">Copy citation</span>
											</Button>
										</div>
									</div>
								))}
							</div>
						) : clusterData?.citations && clusterData.citations.length > 0 ? (
							<div className="space-y-2">
								{clusterData.citations.map((citation: Citation, index: number) => (
									<div
										key={index}
										className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md"
									>
										<div className="flex justify-between items-center">
											<div className="flex items-center">
												<BookOpen className="h-4 w-4 mr-2 text-blue-500" />
												<span className="text-sm font-medium">
													{citation.volume} {citation.reporter} {citation.page}
												</span>
											</div>
											<Button
												variant="ghost"
												size="sm"
												className="h-7 w-7 p-0"
												onClick={() =>
													copyToClipboard(
														`${citation.volume} ${citation.reporter} ${citation.page}`
													)
												}
											>
												<Copy className="h-3.5 w-3.5" />
												<span className="sr-only">Copy citation</span>
											</Button>
										</div>
									</div>
								))}
							</div>
						) : (
							<p className="text-sm text-muted-foreground">
								No citations available for this case.
							</p>
						)}
					</TabsContent>

					<TabsContent value="docket" className="mt-0">
						{isLoading ? (
							<div className="flex justify-center items-center p-12">
								<Loader2 className="h-8 w-8 animate-spin text-primary" />
								<span className="ml-2">Loading docket information...</span>
							</div>
						) : error ? (
							<div className="p-8 text-center text-muted-foreground">
								<p>{error}</p>
								<p className="text-sm mt-2">
									Try refreshing the page or check back later.
								</p>
							</div>
						) : docketTimelineData ? (
							<DocketTimeline docket={docketTimelineData} />
						) : (
							<div className="p-8 text-center text-muted-foreground">
								<p>No docket information available for this case.</p>
							</div>
						)}
					</TabsContent>

					{caseData.opinions && caseData.opinions.length > 0 && (
						<TabsContent value="opinions" className="mt-0">
							<ScrollArea className="h-[400px]">
								<div className="p-4">
									<h4 className="text-sm font-medium mb-3">Opinions</h4>
									<div className="space-y-4">
										{caseData.opinions.map((opinion: Opinion, index: number) => (
											<div
												key={index}
												className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md"
											>
												<div className="flex justify-between items-start">
													<h5 className="text-sm font-medium">
														{opinion.type === "lead-opinion"
															? "Majority Opinion"
															: opinion.type === "concurrence-opinion"
															? "Concurring Opinion"
															: opinion.type === "dissent"
															? "Dissenting Opinion"
															: "Opinion"}
													</h5>
													{opinion.author_id && (
														<Badge variant="outline" className="text-xs">
															{opinion.per_curiam ? "Per Curiam" : "Authored"}
														</Badge>
													)}
												</div>
												{opinion.snippet && (
													<div className="mt-2 text-xs text-muted-foreground">
														<p className="whitespace-pre-wrap line-clamp-4">
															{opinion.snippet}
														</p>
													</div>
												)}
											</div>
										))}
									</div>
								</div>
							</ScrollArea>
						</TabsContent>
					)}

					{caseData.attorney && (
						<TabsContent value="attorneys" className="p-4 mt-0">
							<h4 className="text-sm font-medium mb-3">Attorneys</h4>
							<div className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
								<p className="text-sm whitespace-pre-wrap">
									{caseData.attorney}
								</p>
							</div>
						</TabsContent>
					)}
				</Tabs>
			</CardContent>

			<CardFooter className="flex justify-between p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
				<div className="text-xs text-muted-foreground">
					Data from CourtListener
				</div>
				<div className="flex gap-2">
					<Button
						variant="outline"
						size="sm"
						className="h-7 text-xs bg-secondary/50 dark:bg-[#2a2a2a]"
						onClick={() => {
							const citationText =
								caseData.citation && caseData.citation.length > 0
									? `${caseData.caseNameFull}, ${
											caseData.citation[0]
									  } (${new Date(caseData.dateFiled).getFullYear()})`
									: `${caseData.caseNameFull} (${new Date(
											caseData.dateFiled
									  ).getFullYear()})`;
							copyToClipboard(citationText);
						}}
					>
						{copied ? (
							<>
								<Check className="h-3.5 w-3.5 mr-1.5" />
								Copied!
							</>
						) : (
							<>
								<Copy className="h-3.5 w-3.5 mr-1.5" />
								Copy
							</>
						)}
					</Button>
					<Button
						variant="outline"
						size="sm"
						className="h-7 text-xs bg-secondary/50 dark:bg-[#2a2a2a]"
						onClick={downloadCitation}
					>
						<Download className="h-3.5 w-3.5 mr-1.5" />
						Export
					</Button>
				</div>
			</CardFooter>
		</Card>
	);
}
