"use client"

import { <PERSON>, CardContent, CardDescription, CardHeader, Card<PERSON>itle, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Calendar, Gavel, BookOpen, FileText } from "lucide-react"
import { PaginationControls } from "./pagination-control"
import { type CaseData } from "./case-detail"; // Import the correct CaseData type

interface CaseSearchResultsProps {
  results: CaseData[];
  isLoading: boolean;
  error: string | null;
  onSelectCase: (caseData: CaseData) => void;
  totalResults: number;
  currentPage: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
  pageSize: number;
  onPageChange: (page: number) => void;
  onPageSizeChange: (size: number) => void;
}

export function CaseSearchResults({
  results,
  isLoading,
  error,
  onSelectCase,
  totalResults,
  currentPage,
  totalPages,
  hasNextPage,
  hasPreviousPage,
  pageSize,
  onPageChange,
  onPageSizeChange,
}: CaseSearchResultsProps) {
  // Calculate the range of results being displayed
  const startResult = totalResults === 0 ? 0 : (currentPage - 1) * pageSize + 1
  const endResult = Math.min(currentPage * pageSize, totalResults)

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <div className="flex justify-between items-center">
          <div>
            <CardTitle className="text-lg font-semibold text-foreground">Search Results</CardTitle>
            <CardDescription className="text-muted-foreground">
              {totalResults > 0
                ? `Showing ${startResult}-${endResult} of ${totalResults} results`
                : "Enter search criteria to find cases"}
            </CardDescription>
          </div>
          {results.length > 0 && (
            <Badge variant="outline" className="bg-primary/20 text-primary border-primary/30">
              Page {currentPage} of {totalPages}
            </Badge>
          )}
        </div>
      </CardHeader>

      <CardContent className="p-0">
        {isLoading ? (
          <div className="p-4 space-y-4">
            {[...Array(3)].map((_, i) => (
              <div key={i} className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md">
                <Skeleton className="h-5 w-3/4 mb-2" />
                <div className="flex gap-2">
                  <Skeleton className="h-4 w-20" />
                  <Skeleton className="h-4 w-20" />
                </div>
              </div>
            ))}
          </div>
        ) : error ? (
          <div className="p-8 text-center text-muted-foreground">
            <p>{error}</p>
          </div>
        ) : results.length > 0 ? (
          <ScrollArea className="h-[calc(100vh-380px)]">
            <div className="p-4 space-y-3">
              {results.map((caseData, index) => (
                <div
                  key={index}
                  className="bg-secondary/20 dark:bg-[#252525] p-3 rounded-md hover:bg-secondary/30 dark:hover:bg-[#2a2a2a] transition-colors"
                >
                  <div className="flex justify-between items-start">
                    <h3 className="text-base font-medium mb-2">{caseData.caseName}</h3>
                    <Button variant="ghost" size="sm" className="h-7 text-xs" onClick={() => onSelectCase(caseData)}>
                      View Details
                    </Button>
                  </div>

                  <p className="text-sm text-muted-foreground mb-2 line-clamp-2">{caseData.caseNameFull}</p>

                  <div className="flex flex-wrap gap-2 mt-3">
                    {caseData.dateFiled && (
                      <Badge variant="outline" className="text-xs flex items-center">
                        <Calendar className="h-3 w-3 mr-1" />
                        {new Date(caseData.dateFiled).toLocaleDateString()}
                      </Badge>
                    )}

                    <Badge variant="outline" className="text-xs flex items-center">
                      <Gavel className="h-3 w-3 mr-1" />
                      {caseData.court}
                    </Badge>

                    {caseData.citation && caseData.citation.length > 0 && (
                      <Badge variant="outline" className="text-xs flex items-center">
                        <BookOpen className="h-3 w-3 mr-1" />
                        {caseData.citation[0]}
                      </Badge>
                    )}

                    {caseData.status && (
                      <Badge variant="outline" className="text-xs flex items-center">
                        <FileText className="h-3 w-3 mr-1" />
                        {caseData.status}
                      </Badge>
                    )}
                  </div>

                  {caseData.citeCount !== undefined && caseData.citeCount > 0 && (
                    <div className="mt-2 text-xs text-muted-foreground">Cited by {caseData.citeCount} cases</div>
                  )}
                </div>
              ))}
            </div>
          </ScrollArea>
        ) : (
          <div className="p-8 text-center text-muted-foreground">
            <BookOpen className="h-12 w-12 mx-auto mb-3 opacity-50" />
            <p>Enter search criteria to find legal cases</p>
            <p className="text-sm mt-1">Try searching by keyword, citation, case name, or court</p>
          </div>
        )}
      </CardContent>

      {results.length > 0 && (
        <CardFooter className="p-3 bg-secondary/30 dark:bg-[#252525] border-t border-border dark:border-neutral-700">
          <PaginationControls
            currentPage={currentPage}
            totalPages={totalPages}
            pageSize={pageSize}
            hasNextPage={hasNextPage}
            hasPreviousPage={hasPreviousPage}
            isLoading={isLoading}
            onPageChange={onPageChange}
            onPageSizeChange={onPageSizeChange}
          />
        </CardFooter>
      )}
    </Card>
  )
}
