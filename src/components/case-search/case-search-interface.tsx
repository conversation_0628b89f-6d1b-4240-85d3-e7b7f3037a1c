"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Label } from "@/components/ui/label"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Loader2, Search, Filter, Calendar } from "lucide-react"
import type { SearchParams } from "@/lib/services/court-listener-service"

interface CaseSearchInterfaceProps {
  onSearch: (params: SearchParams) => Promise<void>
  isSearching: boolean
}

export function CaseSearchInterface({ onSearch, isSearching }: CaseSearchInterfaceProps) {
  const [searchParams, setSearchParams] = useState<SearchParams>({
    query: "",
    page_size: 10,
  })

  const handleInputChange = (field: keyof SearchParams, value: string) => {
    setSearchParams((prev) => ({ ...prev, [field]: value }))
  }

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    onSearch(searchParams)
  }

  const clearSearch = () => {
    setSearchParams({ query: "", page_size: 10 })
  }

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <CardTitle className="text-lg font-semibold text-foreground">Search Parameters</CardTitle>
        <CardDescription className="text-muted-foreground">
          Find cases by keyword, citation, or metadata
        </CardDescription>
      </CardHeader>

      <CardContent className="p-4">
        <form onSubmit={handleSubmit} className="space-y-4">
          <div className="space-y-2">
            <Label htmlFor="query">Keyword Search</Label>
            <div className="relative">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-muted-foreground" />
              <Input
                id="query"
                placeholder="Search by keyword, case name, or citation"
                value={searchParams.query || ""}
                onChange={(e) => handleInputChange("query", e.target.value)}
                className="pl-8"
              />
            </div>
          </div>

          <Accordion type="single" collapsible className="w-full">
            <AccordionItem value="advanced-filters">
              <AccordionTrigger className="text-sm font-medium py-2">
                <div className="flex items-center">
                  <Filter className="h-4 w-4 mr-2" />
                  Advanced Filters
                </div>
              </AccordionTrigger>
              <AccordionContent className="space-y-4 pt-2">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="citation">Citation</Label>
                    <Input
                      id="citation"
                      placeholder="e.g., 410 U.S. 113"
                      value={searchParams.citation || ""}
                      onChange={(e) => handleInputChange("citation", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="case_name">Case Name</Label>
                    <Input
                      id="case_name"
                      placeholder="e.g., Roe v. Wade"
                      value={searchParams.case_name || ""}
                      onChange={(e) => handleInputChange("case_name", e.target.value)}
                    />
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="court">Court</Label>
                    <Select
                      value={searchParams.court || ""}
                      onValueChange={(value) => handleInputChange("court", value)}
                    >
                      <SelectTrigger id="court">
                        <SelectValue placeholder="Select court" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="scotus">Supreme Court</SelectItem>
                        <SelectItem value="ca1">1st Circuit</SelectItem>
                        <SelectItem value="ca2">2nd Circuit</SelectItem>
                        <SelectItem value="ca3">3rd Circuit</SelectItem>
                        <SelectItem value="ca4">4th Circuit</SelectItem>
                        <SelectItem value="ca5">5th Circuit</SelectItem>
                        <SelectItem value="ca6">6th Circuit</SelectItem>
                        <SelectItem value="ca7">7th Circuit</SelectItem>
                        <SelectItem value="ca8">8th Circuit</SelectItem>
                        <SelectItem value="ca9">9th Circuit</SelectItem>
                        <SelectItem value="ca10">10th Circuit</SelectItem>
                        <SelectItem value="ca11">11th Circuit</SelectItem>
                        <SelectItem value="cadc">D.C. Circuit</SelectItem>
                        <SelectItem value="cafc">Federal Circuit</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="jurisdiction">Jurisdiction</Label>
                    <Select
                      value={searchParams.jurisdiction || ""}
                      onValueChange={(value) => handleInputChange("jurisdiction", value)}
                    >
                      <SelectTrigger id="jurisdiction">
                        <SelectValue placeholder="Select jurisdiction" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="F">Federal</SelectItem>
                        <SelectItem value="S">State</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                </div>

                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="filed_after" className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Filed After
                    </Label>
                    <Input
                      id="filed_after"
                      type="date"
                      value={searchParams.filed_after || ""}
                      onChange={(e) => handleInputChange("filed_after", e.target.value)}
                    />
                  </div>
                  <div className="space-y-2">
                    <Label htmlFor="filed_before" className="flex items-center">
                      <Calendar className="h-4 w-4 mr-1" />
                      Filed Before
                    </Label>
                    <Input
                      id="filed_before"
                      type="date"
                      value={searchParams.filed_before || ""}
                      onChange={(e) => handleInputChange("filed_before", e.target.value)}
                    />
                  </div>
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="flex justify-between pt-2">
            <Button type="button" variant="outline" onClick={clearSearch} disabled={isSearching}>
              Clear
            </Button>
            <Button
              type="submit"
              disabled={
                isSearching ||
                (!searchParams.query && !searchParams.citation && !searchParams.case_name && !searchParams.court)
              }
            >
              {isSearching ? (
                <>
                  <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                  Searching...
                </>
              ) : (
                <>
                  <Search className="mr-2 h-4 w-4" />
                  Search
                </>
              )}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  )
}
