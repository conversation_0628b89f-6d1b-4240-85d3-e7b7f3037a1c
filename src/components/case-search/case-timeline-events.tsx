"use client"

import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { cn } from "@/lib/utils"

interface CaseTimelineEventsProps {
  caseData: {
    dateFiled: string
    dateArgued?: string
    dateReargued?: string
    dateCertGranted?: string
    dateTerminated?: string
    status?: string
  }
}

export function CaseTimelineEvents({ caseData }: CaseTimelineEventsProps) {
  // Collect all available dates
  const events = [
    {
      type: "Filed",
      date: caseData.dateFiled,
      color: "bg-blue-500",
    },
    ...(caseData.dateCertGranted
      ? [
          {
            type: "Cert Granted",
            date: caseData.dateCertGranted,
            color: "bg-purple-500",
          },
        ]
      : []),
    ...(caseData.dateArgued
      ? [
          {
            type: "Argued",
            date: caseData.dateArgued,
            color: "bg-amber-500",
          },
        ]
      : []),
    ...(caseData.dateReargued
      ? [
          {
            type: "Reargued",
            date: caseData.dateReargued,
            color: "bg-indigo-500",
          },
        ]
      : []),
    ...(caseData.dateTerminated
      ? [
          {
            type: "Terminated",
            date: caseData.dateTerminated,
            color: "bg-red-500",
          },
        ]
      : []),
  ].filter((event) => event.date)

  // Sort events by date
  events.sort((a, b) => new Date(a.date).getTime() - new Date(b.date).getTime())

  // Format date for display
  function formatDate(dateString: string): string {
    return new Date(dateString).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  return (
    <Card className="w-full border-border bg-card dark:bg-[#1e1e1e] dark:border-neutral-700">
      <CardHeader className="bg-secondary/30 dark:bg-[#252525] pb-3">
        <CardTitle className="text-sm font-semibold text-foreground">Key Case Events</CardTitle>
      </CardHeader>
      <CardContent className="p-4">
        <div className="relative">
          {/* Horizontal timeline line */}
          <div className="absolute left-0 right-0 top-4 h-0.5 bg-border dark:bg-neutral-700"></div>

          <div className="relative flex justify-between">
            {events.map((event, index) => (
              <div key={index} className="flex flex-col items-center text-center">
                <div className={cn("z-10 w-8 h-8 rounded-full flex items-center justify-center", event.color)}>
                  <span className="text-xs font-bold text-white">{index + 1}</span>
                </div>
                <div className="mt-2">
                  <Badge variant="outline" className="text-xs">
                    {event.type}
                  </Badge>
                  <div className="text-xs text-muted-foreground mt-1">{formatDate(event.date)}</div>
                </div>
              </div>
            ))}
          </div>

          {/* Current status indicator */}
          {caseData.status && (
            <div className="mt-6 pt-4 border-t border-border dark:border-neutral-700 text-center">
              <Badge
                variant="outline"
                className={cn(
                  "text-xs",
                  caseData.dateTerminated
                    ? "bg-red-500/20 text-red-500 border-red-500/30"
                    : "bg-green-500/20 text-green-500 border-green-500/30",
                )}
              >
                Current Status: {caseData.status}
              </Badge>
            </div>
          )}
        </div>
      </CardContent>
    </Card>
  )
}
