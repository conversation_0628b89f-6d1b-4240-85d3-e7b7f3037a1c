"use client";

import React from "react";
import { PrecedentCard } from "./precedent-card";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { ArrowLeft, Download, Filter } from "lucide-react";
import { PrecedentAnalysisResult } from "@/lib/services/precedent-analysis-service";
import { Skeleton } from "@/components/ui/skeleton";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { Card, CardContent } from "@/components/ui/card";

interface PrecedentAnalysisResultsProps {
  results: PrecedentAnalysisResult[];
  documentId: string;
  onBack: () => void;
  isLoading?: boolean;
}

export function PrecedentAnalysisResults({
  results,
  documentId,
  onBack,
  isLoading = false,
}: PrecedentAnalysisResultsProps) {
  const [sortBy, setSortBy] = React.useState<string>("relevance");
  const [filterImpact, setFilterImpact] = React.useState<string>("all");
  const [filterAnalysisSource, setFilterAnalysisSource] = React.useState<string>("all");

  // Sort and filter precedents
  const filteredResults = React.useMemo(() => {
    let filtered = [...results];
    
    // Apply impact filter
    if (filterImpact !== "all") {
      filtered = filtered.filter(precedent => precedent.impact === filterImpact);
    }
    
    // Apply analysis source filter
    if (filterAnalysisSource !== "all") {
      filtered = filtered.filter(precedent => {
        if (filterAnalysisSource === "ai" && precedent.aiAnalysisDetails?.source === "AI") {
          return true;
        }
        if (filterAnalysisSource === "rule" && (!precedent.aiAnalysisDetails || precedent.aiAnalysisDetails.source === "RuleBasedHybrid")) {
          return true;
        }
        return false;
      });
    }
    
    // Apply sorting
    switch (sortBy) {
      case "relevance":
        return filtered.sort((a, b) => b.relevanceScore - a.relevanceScore);
      case "category":
        return filtered.sort((a, b) => a.category.localeCompare(b.category));
      default:
        return filtered;
    }
  }, [results, sortBy, filterImpact, filterAnalysisSource]);

  // Generate a CSV of the analysis results
  const handleExportCSV = () => {
    const headers = [
      "Citation",
      "Relevance Score",
      "Impact",
      "Category",
      "Key Points",
      "Recommendation",
      "Analysis Source"
    ].join(",");

    const rows = results.map(precedent => {
      return [
        `"${precedent.citation}"`,
        precedent.relevanceScore,
        `"${precedent.impact}"`,
        `"${precedent.category}"`,
        `"${precedent.keyPoints.map(kp => kp.text).join("; ")}"`,
        `"${precedent.recommendation || ""}"`,
        `"${precedent.aiAnalysisDetails?.source || "RuleBasedHybrid"}"`,
      ].join(",");
    });

    const csv = [headers, ...rows].join("\n");
    const blob = new Blob([csv], { type: "text/csv;charset=utf-8;" });
    const url = URL.createObjectURL(blob);
    const link = document.createElement("a");
    link.setAttribute("href", url);
    link.setAttribute("download", `precedent-analysis-${documentId}.csv`);
    document.body.appendChild(link);
    link.click();
    document.body.removeChild(link);
  };

  if (isLoading) {
    return (
      <div className="space-y-6">
        <div className="flex justify-between items-center">
          <Button variant="outline" onClick={onBack} disabled>
            <ArrowLeft className="mr-2 h-4 w-4" />
            Back
          </Button>
        </div>
        
        <div className="space-y-4">
          {Array.from({ length: 3 }).map((_, i) => (
            <Card key={i} className="mb-4">
              <CardContent className="p-6">
                <div className="flex justify-between items-start">
                  <div className="space-y-2">
                    <Skeleton className="h-6 w-40" />
                    <Skeleton className="h-4 w-60" />
                  </div>
                  <div className="space-y-2">
                    <Skeleton className="h-5 w-20" />
                    <Skeleton className="h-5 w-24" />
                  </div>
                </div>
                <div className="mt-4 space-y-2">
                  <Skeleton className="h-4 w-24" />
                  <Skeleton className="h-4 w-full" />
                  <Skeleton className="h-4 w-full" />
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    );
  }

  return (
    <div className="space-y-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <Button variant="outline" onClick={onBack}>
          <ArrowLeft className="mr-2 h-4 w-4" />
          Back
        </Button>
        
        <div className="flex flex-col sm:flex-row gap-2 w-full sm:w-auto">
          <div className="flex items-center gap-2">
            <Filter className="h-4 w-4 text-muted-foreground" />
            <Select value={filterImpact} onValueChange={setFilterImpact}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Filter by impact" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Impacts</SelectItem>
                <SelectItem value="positive">Positive</SelectItem>
                <SelectItem value="negative">Negative</SelectItem>
                <SelectItem value="neutral">Neutral</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <div className="flex items-center gap-2">
            <Select value={filterAnalysisSource} onValueChange={setFilterAnalysisSource}>
              <SelectTrigger className="w-[140px]">
                <SelectValue placeholder="Analysis source" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Sources</SelectItem>
                <SelectItem value="ai">AI Analysis</SelectItem>
                <SelectItem value="rule">Rule-Based</SelectItem>
              </SelectContent>
            </Select>
          </div>
          
          <Select value={sortBy} onValueChange={setSortBy}>
            <SelectTrigger className="w-[140px]">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="relevance">Relevance</SelectItem>
              <SelectItem value="category">Category</SelectItem>
            </SelectContent>
          </Select>
          
          <Button variant="outline" onClick={handleExportCSV}>
            <Download className="mr-2 h-4 w-4" />
            Export CSV
          </Button>
        </div>
      </div>
      
      {filteredResults.length === 0 ? (
        <div className="text-center py-12">
          <p className="text-muted-foreground">No precedents found matching the current filters.</p>
        </div>
      ) : (
        <div className="space-y-4">
          <div className="flex justify-between items-center">
            <p className="text-sm text-muted-foreground">
              Showing {filteredResults.length} of {results.length} precedents
            </p>
          </div>
          
          {filteredResults.map((precedent, index) => (
            <PrecedentCard key={index} precedent={precedent} />
          ))}
        </div>
      )}
    </div>
  );
}
