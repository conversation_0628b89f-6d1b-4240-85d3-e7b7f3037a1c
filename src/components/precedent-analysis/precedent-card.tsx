"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible";
import { But<PERSON> } from "@/components/ui/button";
import { ChevronDown, ChevronUp, ExternalLink } from "lucide-react";
import type { PrecedentAnalysisResult } from "@/lib/services/precedent-analysis-service";
import { cn } from "@/lib/utils";

interface PrecedentCardProps {
  precedent: PrecedentAnalysisResult;
}

export function PrecedentCard({ precedent }: PrecedentCardProps) {
  const [isOpen, setIsOpen] = React.useState(false);

  // Determine impact color
  const getImpactColor = (impact: string) => {
    switch (impact) {
      case "positive":
        return "bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-100";
      case "negative":
        return "bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-100";
      case "neutral":
      default:
        return "bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-100";
    }
  };

  // Format relevance score as percentage
  const relevancePercentage = Math.round(precedent.relevanceScore * 100);

  return (
    <Card className="mb-4">
      <CardHeader className="pb-2">
        <div className="flex justify-between items-start">
          <div>
            <CardTitle className="text-lg font-bold">{precedent.citation}</CardTitle>
            <CardDescription className="mt-1">
              {precedent.keyPoints[0]?.text || "No key points available"}
            </CardDescription>
          </div>
          <div className="flex flex-col items-end gap-2">
            <Badge className={cn(getImpactColor(precedent.impact), "capitalize")}>
              {precedent.impact} Impact
            </Badge>
            <Badge variant="outline" className="whitespace-nowrap">
              Relevance: {relevancePercentage}%
            </Badge>
          </div>
        </div>
      </CardHeader>
      <CardContent className="pt-0">
        <div className="mb-3">
          <Badge variant="secondary" className="mr-2">
            {precedent.category}
          </Badge>
        </div>

        <Collapsible open={isOpen} onOpenChange={setIsOpen} className="space-y-2">
          <div className="flex flex-col space-y-2">
            {precedent.keyPoints.slice(1, 3).map((point, index) => (
              <p key={index} className="text-sm text-muted-foreground">
                {point.text}
                {point.type && <span className="ml-1 text-xs text-muted-foreground">({point.type})</span>}
              </p>
            ))}
          </div>

          <CollapsibleContent className="space-y-4">
            {precedent.keyPoints.slice(3).map((point, index) => (
              <p key={index + 3} className="text-sm text-muted-foreground">
                {point.text}
                {point.type && <span className="ml-1 text-xs text-muted-foreground">({point.type})</span>}
              </p>
            ))}

            {precedent.recommendation && (
              <div className="mt-4 p-3 bg-muted rounded-md">
                <div className="flex justify-between items-center mb-1">
                  <h4 className="text-sm font-medium">Recommendation</h4>
                  {precedent.aiAnalysisDetails && (
                    <Badge variant="outline" className="text-xs">
                      {precedent.aiAnalysisDetails.source === "AI" ? "AI Analysis" : "Hybrid Analysis"}
                    </Badge>
                  )}
                </div>
                <p className="text-sm">{precedent.recommendation}</p>
              </div>
            )}
            
            {precedent.aiAnalysisDetails && precedent.aiAnalysisDetails.reasoning && 
             (!precedent.recommendation || !precedent.recommendation.includes(precedent.aiAnalysisDetails.reasoning)) && (
              <div className="mt-4 p-3 bg-muted/70 rounded-md">
                <h4 className="text-sm font-medium mb-1">Analysis Reasoning</h4>
                <p className="text-sm">{precedent.aiAnalysisDetails.reasoning}</p>
              </div>
            )}

            {precedent.relatedCases.length > 0 && (
              <div className="mt-4">
                <h4 className="text-sm font-medium mb-2">Related Cases</h4>
                <div className="space-y-2">
                  {precedent.relatedCases.map((relatedCase, index) => (
                    <div
                      key={index}
                      className="flex justify-between items-center p-2 bg-muted/50 rounded-md text-sm"
                    >
                      <div>
                        <p className="font-medium">{relatedCase.caseName}</p>
                        <p className="text-xs text-muted-foreground">
                          {relatedCase.citation} • {relatedCase.court} • {relatedCase.year}
                        </p>
                      </div>
                      <div className="flex items-center">
                        <Badge
                          variant="outline"
                          className="mr-2 text-xs capitalize"
                        >
                          {relatedCase.relationship}
                        </Badge>
                        <Button variant="ghost" size="icon" className="h-6 w-6">
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      </div>
                    </div>
                  ))}
                </div>
              </div>
            )}
          </CollapsibleContent>

          <CollapsibleTrigger asChild>
            <Button variant="ghost" size="sm" className="w-full">
              {isOpen ? (
                <ChevronUp className="h-4 w-4 mr-2" />
              ) : (
                <ChevronDown className="h-4 w-4 mr-2" />
              )}
              {isOpen ? "Show Less" : "Show More"}
            </Button>
          </CollapsibleTrigger>
        </Collapsible>
      </CardContent>
    </Card>
  );
}
