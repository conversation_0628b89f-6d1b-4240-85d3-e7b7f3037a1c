"use client";

import React from "react";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card";
import { Form, FormControl, FormDescription, FormField, FormItem, FormLabel } from "@/components/ui/form";
import { Switch } from "@/components/ui/switch";
import { <PERSON>lider } from "@/components/ui/slider";
import { Button } from "@/components/ui/button";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import * as z from "zod";
import { PrecedentAnalysisOptions } from "@/lib/services/precedent-analysis-service";
import { Scale } from "lucide-react";

const formSchema = z.object({
  includeRecommendations: z.boolean().default(true),
  categorize: z.boolean().default(true),
  assessImpact: z.boolean().default(true),
  maxRelatedCases: z.number().min(1).max(20).default(5),
  minRelevanceScore: z.number().min(0).max(1).default(0.3),
  useAIAnalysis: z.boolean().default(false),
  aiFocus: z.object({
    prioritizeImpact: z.boolean().default(false),
    detailLevel: z.enum(["concise", "standard", "detailed"]).default("standard"),
  }).optional(),
});

interface PrecedentAnalysisFormProps {
  documentId: string;
  onAnalyze: (documentId: string, options: PrecedentAnalysisOptions) => void;
  isLoading?: boolean;
}

export function PrecedentAnalysisForm({ documentId, onAnalyze, isLoading = false }: PrecedentAnalysisFormProps) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      includeRecommendations: true,
      categorize: true,
      assessImpact: true,
      maxRelatedCases: 5,
      minRelevanceScore: 0.3,
      useAIAnalysis: false,
      aiFocus: {
        prioritizeImpact: false,
        detailLevel: "standard",
      },
    },
  });

  const handleSubmit = form.handleSubmit((data) => {
    onAnalyze(documentId, data);
  });

  return (
    <Card className="w-full">
      <CardHeader>
        <CardTitle className="flex items-center">
          <Scale className="mr-2 h-5 w-5" />
          Precedent Analysis Options
        </CardTitle>
        <CardDescription>
          Configure how precedents in your document should be analyzed
        </CardDescription>
      </CardHeader>
      <CardContent>
        <Form {...form}>
          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="useAIAnalysis"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm bg-muted/50">
                      <div className="space-y-0.5">
                        <FormLabel>Use AI Analysis</FormLabel>
                        <FormDescription>
                          Enable AI-driven analysis for relevance, impact, and categorization
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />

                {form.watch("useAIAnalysis") && (
                  <div className="space-y-4 pl-4 border-l-2 border-muted">
                    <FormField
                      control={form.control}
                      name="aiFocus.prioritizeImpact"
                      render={({ field }) => (
                        <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                          <div className="space-y-0.5">
                            <FormLabel>Prioritize Impact Analysis</FormLabel>
                            <FormDescription>
                              Instruct AI to place special emphasis on impact assessment
                            </FormDescription>
                          </div>
                          <FormControl>
                            <Switch
                              checked={field.value}
                              onCheckedChange={field.onChange}
                            />
                          </FormControl>
                        </FormItem>
                      )}
                    />
                    <FormField
                      control={form.control}
                      name="aiFocus.detailLevel"
                      render={({ field }) => (
                        <FormItem className="space-y-2">
                          <FormLabel>AI Detail Level</FormLabel>
                          <div className="flex flex-col space-y-1">
                            <div className="flex justify-between text-xs text-muted-foreground">
                              <span>Concise</span>
                              <span>Standard</span>
                              <span>Detailed</span>
                            </div>
                            <div className="flex items-center space-x-2">
                              <Button
                                type="button"
                                variant={field.value === "concise" ? "default" : "outline"}
                                size="sm"
                                className="flex-1"
                                onClick={() => field.onChange("concise")}
                              >
                                Concise
                              </Button>
                              <Button
                                type="button"
                                variant={field.value === "standard" ? "default" : "outline"}
                                size="sm"
                                className="flex-1"
                                onClick={() => field.onChange("standard")}
                              >
                                Standard
                              </Button>
                              <Button
                                type="button"
                                variant={field.value === "detailed" ? "default" : "outline"}
                                size="sm"
                                className="flex-1"
                                onClick={() => field.onChange("detailed")}
                              >
                                Detailed
                              </Button>
                            </div>
                          </div>
                          <FormDescription>
                            Level of detail for AI reasoning in analysis
                          </FormDescription>
                        </FormItem>
                      )}
                    />
                  </div>
                )}

                <FormField
                  control={form.control}
                  name="includeRecommendations"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Include Recommendations</FormLabel>
                        <FormDescription>
                          Generate recommendations based on precedent analysis
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="categorize"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Categorize Precedents</FormLabel>
                        <FormDescription>
                          Categorize precedents by legal domain (if AI analysis is not used)
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={form.watch("useAIAnalysis")}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
                <FormField
                  control={form.control}
                  name="assessImpact"
                  render={({ field }) => (
                    <FormItem className="flex flex-row items-center justify-between rounded-lg border p-3 shadow-sm">
                      <div className="space-y-0.5">
                        <FormLabel>Assess Impact</FormLabel>
                        <FormDescription>
                          Evaluate the impact of precedents (if AI analysis is not used)
                        </FormDescription>
                      </div>
                      <FormControl>
                        <Switch
                          checked={field.value}
                          onCheckedChange={field.onChange}
                          disabled={form.watch("useAIAnalysis")}
                        />
                      </FormControl>
                    </FormItem>
                  )}
                />
              </div>

              <div className="space-y-4">
                <FormField
                  control={form.control}
                  name="maxRelatedCases"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel>Maximum Related Cases</FormLabel>
                      <div className="flex flex-col space-y-1">
                        <FormControl>
                          <Slider
                            min={1}
                            max={20}
                            step={1}
                            defaultValue={[field.value]}
                            onValueChange={(value) => field.onChange(value[0])}
                          />
                        </FormControl>
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>1</span>
                          <span>{field.value}</span>
                          <span>20</span>
                        </div>
                      </div>
                      <FormDescription>
                        Maximum number of related cases to return for each precedent
                      </FormDescription>
                    </FormItem>
                  )}
                />

                <FormField
                  control={form.control}
                  name="minRelevanceScore"
                  render={({ field }) => (
                    <FormItem className="space-y-2">
                      <FormLabel>Minimum Relevance Score</FormLabel>
                      <div className="flex flex-col space-y-1">
                        <FormControl>
                          <Slider
                            min={0}
                            max={1}
                            step={0.05}
                            defaultValue={[field.value]}
                            onValueChange={(value) => field.onChange(value[0])}
                          />
                        </FormControl>
                        <div className="flex justify-between text-xs text-muted-foreground">
                          <span>0.0</span>
                          <span>{field.value.toFixed(2)}</span>
                          <span>1.0</span>
                        </div>
                      </div>
                      <FormDescription>
                        Minimum relevance score for including a precedent
                      </FormDescription>
                    </FormItem>
                  )}
                />
              </div>
            </div>

            <Button type="submit" className="w-full" disabled={isLoading}>
              {isLoading ? "Analyzing..." : "Analyze Precedents"}
            </Button>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
