"use client";

import { useCreateAnnotationSchema } from "@/lib/services/template-service";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface SchemaField {
  name: string;
  type: "text" | "number" | "date" | "select";
  required: boolean;
  options?: string[];
}

export default function SchemaCreateForm() {
  const router = useRouter();
  const createSchema = useCreateAnnotationSchema();
  const [fields, setFields] = useState<SchemaField[]>([]);

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();
    const formData = new FormData(event.target as HTMLFormElement);

    const schemaData = {
      name: formData.get("name") as string,
      fields: fields,
    };

    try {
      await createSchema.mutateAsync(schemaData);
      router.push("/annotations/schemas");
    } catch (error) {
      console.error("Failed to create schema:", error);
    }
  };

  const addField = () => {
    setFields([...fields, { name: "", type: "text", required: false }]);
  };

  const updateField = (index: number, updates: Partial<SchemaField>) => {
    const newFields = [...fields];
    newFields[index] = { ...newFields[index], ...updates };
    setFields(newFields);
  };

  const removeField = (index: number) => {
    setFields(fields.filter((_, i) => i !== index));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div>
        <label className="block mb-2">Schema Name</label>
        <Input
          name="name"
          placeholder="Enter schema name"
          required
          className="w-full"
        />
      </div>

      <div>
        <div className="flex justify-between items-center mb-4">
          <h3 className="text-lg font-semibold">Schema Fields</h3>
          <Button type="button" onClick={addField} variant="outline">
            Add Field
          </Button>
        </div>

        <div className="space-y-4">
          {fields.map((field, index) => (
            <div key={index} className="flex gap-4 items-start p-4 border rounded">
              <div className="flex-1 space-y-4">
                <Input
                  value={field.name}
                  onChange={(e) => updateField(index, { name: e.target.value })}
                  placeholder="Field name"
                />
                <Select
                  value={field.type}
                  onValueChange={(value) => 
                    updateField(index, { 
                      type: value as SchemaField["type"],
                      options: value === "select" ? [] : undefined
                    })
                  }
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select field type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="text">Text</SelectItem>
                    <SelectItem value="number">Number</SelectItem>
                    <SelectItem value="date">Date</SelectItem>
                    <SelectItem value="select">Select</SelectItem>
                  </SelectContent>
                </Select>

                {field.type === "select" && (
                  <Input
                    value={field.options?.join(", ") || ""}
                    onChange={(e) =>
                      updateField(index, {
                        options: e.target.value.split(",").map((s) => s.trim())
                      })
                    }
                    placeholder="Options (comma-separated)"
                  />
                )}
              </div>
              <div className="flex items-start gap-2">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={field.required}
                    onChange={(e) =>
                      updateField(index, { required: e.target.checked })
                    }
                    className="mr-2"
                  />
                  Required
                </label>
                <Button
                  type="button"
                  onClick={() => removeField(index)}
                  variant="destructive"
                  size="sm"
                >
                  Remove
                </Button>
              </div>
            </div>
          ))}
        </div>
      </div>

      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
        <Button type="submit" disabled={createSchema.isPending}>
          {createSchema.isPending ? "Creating..." : "Create Schema"}
        </Button>
      </div>
    </form>
  );
}