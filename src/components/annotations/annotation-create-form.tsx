"use client";

import { useCreateAnnotation } from "@/lib/services/template-service";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { useRouter } from "next/navigation";
import { useState } from "react";

interface AnnotationFormData {
  title?: string;
  category?: string;
  date?: string;
  confidentiality?: string;
  [key: string]: string | undefined;
}

export default function AnnotationCreateForm() {
  const router = useRouter();
  const createAnnotation = useCreateAnnotation();
  const [schemaId, setSchemaId] = useState("");
  const [formData, setFormData] = useState<AnnotationFormData>({});

  const handleSubmit = async (event: React.FormEvent) => {
    event.preventDefault();

    const annotationData = {
      documentId: "", // This would come from the route or props
      schemaId,
      data: formData,
    };

    try {
      await createAnnotation.mutateAsync(annotationData);
      router.push("/annotations");
    } catch (error) {
      console.error("Failed to create annotation:", error);
    }
  };

  // Mock schema fields - in real app, these would come from the selected schema
  const schemaFields = [
    { name: "title", type: "text", required: true },
    { name: "category", type: "select", required: true, options: ["Legal", "Financial", "Technical"] },
    { name: "date", type: "date", required: true },
    { name: "confidentiality", type: "select", required: true, options: ["Public", "Internal", "Confidential"] },
  ];

  const updateField = (name: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [name]: value,
    }));
  };

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="mb-6">
        <label className="block mb-2">Select Schema</label>
        <Select value={schemaId} onValueChange={setSchemaId}>
          <SelectTrigger>
            <SelectValue placeholder="Select a schema..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="legal">Legal Document Schema</SelectItem>
            <SelectItem value="financial">Financial Report Schema</SelectItem>
          </SelectContent>
        </Select>
      </div>

      {schemaId && (
        <div className="space-y-4">
          {schemaFields.map((field) => (
            <div key={field.name} className="space-y-2">
              <label className="block">
                {field.name.charAt(0).toUpperCase() + field.name.slice(1)}
                {field.required && <span className="text-red-500 ml-1">*</span>}
              </label>

              {field.type === "select" ? (
                <Select 
                  value={formData[field.name]} 
                  onValueChange={(value) => updateField(field.name, value)}
                >
                  <SelectTrigger>
                    <SelectValue placeholder="Select..." />
                  </SelectTrigger>
                  <SelectContent>
                    {field.options?.map((option) => (
                      <SelectItem key={option} value={option}>
                        {option}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              ) : field.type === "date" ? (
                <Input
                  type="date"
                  value={formData[field.name] || ""}
                  onChange={(e) => updateField(field.name, e.target.value)}
                  required={field.required}
                  className="w-full"
                />
              ) : (
                <Input
                  type={field.type}
                  value={formData[field.name] || ""}
                  onChange={(e) => updateField(field.name, e.target.value)}
                  required={field.required}
                  className="w-full"
                />
              )}
            </div>
          ))}
        </div>
      )}

      <div className="flex justify-end gap-4">
        <Button type="button" variant="outline" onClick={() => router.back()}>
          Cancel
        </Button>
        <Button 
          type="submit" 
          disabled={createAnnotation.isPending || !schemaId}
        >
          {createAnnotation.isPending ? "Creating..." : "Create Annotation"}
        </Button>
      </div>
    </form>
  );
}