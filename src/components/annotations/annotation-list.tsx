"use client";

import { Card } from "@/components/ui/card";
import { But<PERSON> } from "@/components/ui/button";
import Link from "next/link";

export default function AnnotationList() {
  return (
    <div>
      <div className="grid gap-6">
        {/* Annotation Schemas Section */}
        <section>
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Annotation Schemas</h2>
            <Link href="/annotations/schemas/create">
              <Button>Create Schema</Button>
            </Link>
          </div>

          <Card className="p-8">
            <div className="flex flex-col items-center justify-center text-center space-y-4">
              <p className="text-muted-foreground">No schemas found.</p>
              <p className="text-sm text-muted-foreground">
                Create an annotation schema to get started with document annotations.
              </p>
              <Link href="/annotations/schemas/create">
                <Button>Create First Schema</Button>
              </Link>
            </div>
          </Card>
        </section>

        {/* Recent Annotations Section */}
        <section className="mt-8">
          <div className="flex justify-between items-center mb-6">
            <h2 className="text-xl font-semibold">Recent Annotations</h2>
            <Link href="/annotations/create">
              <Button>Create Annotation</Button>
            </Link>
          </div>

          <Card className="p-8">
            <div className="flex flex-col items-center justify-center text-center space-y-4">
              <p className="text-muted-foreground">No annotations yet.</p>
              <p className="text-sm text-muted-foreground">
                Start by creating an annotation for your documents.
              </p>
              <Link href="/annotations/create">
                <Button>Create First Annotation</Button>
              </Link>
            </div>
          </Card>
        </section>
      </div>
    </div>
  );
}