// Negotiation Simulator Types

export type ContractType =
	| "SOFTWARE_LICENSE"
	| "EMPLOYMENT_AGREEMENT"
	| "SERVICE_AGREEMENT"
	| "PURCHASE_AGREEMENT"
	| "PARTNERSHIP_AGREEMENT"
	| "NDA"
	| "LEASE_AGREEMENT"
	| "CONSULTING_AGREEMENT"
	| "OTHER";

export type Industry =
	| "TECHNOLOGY"
	| "HEALTHCARE"
	| "FINANCE"
	| "LEGAL"
	| "REAL_ESTATE"
	| "MANUFACTURING"
	| "RETAIL"
	| "EDUCATION"
	| "OTHER";

export type Difficulty = "BEGINNER" | "INTERMEDIATE" | "EXPERT";

export type PartyRole =
	| "BUYER"
	| "SELLER"
	| "VENDOR"
	| "CLIENT"
	| "CONTRACTOR"
	| "LICENSOR"
	| "LICENSEE"
	| "EMPLOYER"
	| "EMPLOYEE";

export type NegotiationStyle =
	| "AGGRESSIVE"
	| "COLLABORATIVE"
	| "ANALYTICAL"
	| "COMPETITIVE"
	| "ACCOMMODATING"
	| "DIPLOMATIC";

export type SessionStatus = "ACTIVE" | "COMPLETED" | "PAUSED" | "ABANDONED";

export type MoveStrategy =
	| "COLLABORATIVE"
	| "COMPETITIVE"
	| "VALUE_BASED"
	| "CONCESSION"
	| "ANCHORING"
	| "DEADLINE_PRESSURE";

export type CommunicationStyle =
	| "FORMAL"
	| "CASUAL"
	| "TECHNICAL"
	| "DIPLOMATIC";
export type DecisionSpeed = "FAST" | "MODERATE" | "DELIBERATE";
export type ConcessionPattern = "EARLY" | "GRADUAL" | "LATE" | "MINIMAL";

export interface Terms {
	price?: number;
	currency?: string;
	paymentTerms?: string;
	deliveryDate?: Date;
	warranties?: string[];
	liabilities?: string[];
	terminationClauses?: string[];
	intellectualProperty?: string[];
	confidentiality?: string[];
	customTerms?: Record<string, any>;
}

export interface PartyProfile {
	name: string;
	role: PartyRole;
	priorities: string[];
	negotiationStyle: NegotiationStyle;
	constraints: Record<string, any>;
	budget?: {
		min: number;
		max: number;
		currency: string;
	};
	timeline?: {
		urgency: "LOW" | "MEDIUM" | "HIGH";
		deadline?: Date;
	};
}

export interface AIPersonalityProfile {
	aggressiveness: number; // 0-1
	flexibility: number; // 0-1
	riskTolerance: number; // 0-1
	communicationStyle: CommunicationStyle;
	decisionSpeed: DecisionSpeed;
	concessionPattern: ConcessionPattern;
}

export interface NegotiationConstraints {
	maxRounds: number;
	mustHaveTerms: string[];
	dealBreakers: string[];
	flexibleTerms?: string[];
	timeLimit?: number; // minutes
}

export interface TimelineOptions {
	startDate: Date;
	expectedDuration: number; // minutes
	maxDuration: number; // minutes
}

export interface NegotiationScenario {
	id: string;
	name: string;
	description: string;
	industry: Industry;
	contractType: ContractType;
	difficulty: Difficulty;
	parties: PartyProfile[];
	initialOffer: Terms;
	constraints: NegotiationConstraints;
	timeline: TimelineOptions;
	isTemplate: boolean;
	tags: string[];
	createdBy: string;
	organizationId: string;
	createdAt: Date;
	updatedAt: Date;
}

export interface NegotiationMove {
	id: string;
	roundNumber: number;
	playerId: string; // 'user' or 'ai'
	timestamp: Date;
	offer: Terms;
	message: string;
	strategy: MoveStrategy;
	reasoning?: string;
	aiResponse?: {
		responseTime: number;
		confidenceScore: number;
		reasoning: string;
	};
}

export interface NegotiationRound {
	roundNumber: number;
	userMove: NegotiationMove;
	aiMove?: NegotiationMove;
	feedback?: {
		score: number;
		strengths: string[];
		improvements: string[];
		suggestions: string[];
	};
}

export interface NegotiationMetrics {
	totalRounds: number;
	averageResponseTime: number;
	finalScore: number;
	dealClosed: boolean;
	userSatisfaction?: number;
	aiSatisfaction?: number;
	keyMetrics: {
		communicationEffectiveness: number;
		strategicThinking: number;
		flexibilityScore: number;
		timeManagement: number;
	};
}

export interface NegotiationSession {
	id: string;
	scenarioId: string;
	userId: string;
	status: SessionStatus;
	rounds: NegotiationRound[];
	metrics: NegotiationMetrics;
	aiPersonality: AIPersonalityProfile;
	currentTerms?: Terms;
	startedAt: Date;
	completedAt?: Date;
	pausedAt?: Date;
	lastActivity: Date;
}

export interface CreateScenarioRequest {
	name: string;
	description: string;
	industry: Industry;
	contractType: ContractType;
	difficulty: Difficulty;
	parties: Omit<PartyProfile, "name">[];
	initialOffer: Terms;
	constraints: NegotiationConstraints;
	timeline: Omit<TimelineOptions, "startDate">;
	tags?: string[];
}

export interface StartSessionRequest {
	scenarioId: string;
	aiPersonality?: Partial<AIPersonalityProfile>;
	sourcePlaybookId?: string;
	sourceDocumentId?: string;
	userPerformanceContext?: UserPerformanceContext;
}

export interface UserPerformanceContext {
	averageScore?: number;
	strongStrategies?: MoveStrategy[];
	weakAreas?: string[];
	preferredStyle?: NegotiationStyle;
	skillLevel?: Difficulty;
}

export interface MakeMoveRequest {
	offer: Terms;
	message: string;
	strategy: MoveStrategy;
	reasoning?: string;
}

export interface SessionAnalytics {
	totalSessions: number;
	completedSessions: number;
	completionRate: number;
	averageScore: number;
	averageRounds: number;
	averageDuration: number;
	recentSessions: NegotiationSession[];
	performanceTrends: {
		date: string;
		score: number;
		rounds: number;
		duration: number;
	}[];
}

// API Response Types
export interface NegotiationSimulatorApiResponse<T = any> {
	success: boolean;
	data: T;
	message?: string;
}

export interface NegotiationSimulatorError {
	success: false;
	error: {
		code: string;
		message: string;
		details?: unknown;
	};
}

// UI Configuration Types
export const CONTRACT_TYPE_OPTIONS: { value: ContractType; label: string }[] = [
	{ value: "SOFTWARE_LICENSE", label: "Software License Agreement" },
	{ value: "EMPLOYMENT_AGREEMENT", label: "Employment Agreement" },
	{ value: "SERVICE_AGREEMENT", label: "Service Agreement" },
	{ value: "PURCHASE_AGREEMENT", label: "Purchase Agreement" },
	{ value: "PARTNERSHIP_AGREEMENT", label: "Partnership Agreement" },
	{ value: "NDA", label: "Non-Disclosure Agreement" },
	{ value: "LEASE_AGREEMENT", label: "Lease Agreement" },
	{ value: "CONSULTING_AGREEMENT", label: "Consulting Agreement" },
	{ value: "OTHER", label: "Other" },
];

export const INDUSTRY_OPTIONS: { value: Industry; label: string }[] = [
	{ value: "TECHNOLOGY", label: "Technology" },
	{ value: "HEALTHCARE", label: "Healthcare" },
	{ value: "FINANCE", label: "Finance" },
	{ value: "LEGAL", label: "Legal Services" },
	{ value: "REAL_ESTATE", label: "Real Estate" },
	{ value: "MANUFACTURING", label: "Manufacturing" },
	{ value: "RETAIL", label: "Retail" },
	{ value: "EDUCATION", label: "Education" },
	{ value: "OTHER", label: "Other" },
];

export const DIFFICULTY_OPTIONS: {
	value: Difficulty;
	label: string;
	description: string;
}[] = [
	{
		value: "BEGINNER",
		label: "Beginner",
		description: "Simple scenarios with clear objectives and cooperative AI",
	},
	{
		value: "INTERMEDIATE",
		label: "Intermediate",
		description: "Moderate complexity with some conflicting interests",
	},
	{
		value: "EXPERT",
		label: "Expert",
		description: "Complex scenarios with aggressive AI and tight constraints",
	},
];

export const STRATEGY_OPTIONS: {
	value: MoveStrategy;
	label: string;
	description: string;
}[] = [
	{
		value: "COLLABORATIVE",
		label: "Collaborative",
		description: "Focus on mutual benefits",
	},
	{
		value: "COMPETITIVE",
		label: "Competitive",
		description: "Maximize your advantage",
	},
	{
		value: "VALUE_BASED",
		label: "Value-Based",
		description: "Emphasize total value proposition",
	},
	{
		value: "CONCESSION",
		label: "Concession",
		description: "Make strategic concessions",
	},
	{
		value: "ANCHORING",
		label: "Anchoring",
		description: "Set strong initial position",
	},
	{
		value: "DEADLINE_PRESSURE",
		label: "Deadline Pressure",
		description: "Use time constraints",
	},
];

// Integration-specific types
export interface CreateScenarioFromPlaybookRequest {
	difficulty?: Difficulty;
	focusAreas?: string[];
	aiPersonality?: Partial<AIPersonalityProfile>;
	customizations?: {
		maxRounds?: number;
		timeLimit?: number;
		specificTerms?: string[];
	};
}

export interface CreateScenarioFromDocumentRequest {
	name: string;
	description?: string;
	difficulty?: Difficulty;
	extractionOptions?: {
		focusOnRisks?: boolean;
		includeRecommendations?: boolean;
		targetRole?: "buyer" | "seller" | "vendor" | "client";
	};
}

export interface UserNegotiationProfile {
	userId: string;
	totalSessions: number;
	completedSessions: number;
	averageScore: number;
	averageRounds: number;
	averageDuration: number;
	strongStrategies: string[];
	weakAreas: string[];
	preferredStyle: string;
	skillLevel: Difficulty;
	performanceHistory: PerformanceHistoryEntry[];
	lastUpdated: Date;
}

export interface PerformanceHistoryEntry {
	date: Date;
	score: number;
	rounds: number;
	duration: number;
	strategiesUsed: string[];
	sessionId: string;
}

export interface UpdateUserNegotiationProfileRequest {
	strongStrategies?: string[];
	weakAreas?: string[];
	preferredStyle?: string;
	skillLevel?: Difficulty;
}

export interface SkillGapAnalysisRequest {
	targetScenarioId?: string;
	targetDifficulty?: Difficulty;
	focusAreas?: string[];
}

export interface SkillGapAnalysisResponse {
	currentSkillLevel: Difficulty;
	targetSkillLevel: Difficulty;
	identifiedGaps: string[];
	recommendedPractice: {
		scenarios: string[];
		focusAreas: string[];
		estimatedPracticeTime: number;
		priorityOrder: string[];
	};
	improvementPlan: {
		shortTerm: string[];
		mediumTerm: string[];
		longTerm: string[];
	};
}

export interface CrossFeatureUsageResponse {
	playbookToSimulatorConversion: {
		totalPlaybooks: number;
		playbooksWithSimulation: number;
		conversionRate: number;
	};
	simulatorToPlaybookUsage: {
		totalSessions: number;
		sessionsWithPlaybookContext: number;
		contextUsageRate: number;
	};
	userEngagement: {
		averageSessionsPerPlaybook: number;
		averagePlaybooksPerUser: number;
		crossFeatureRetention: number;
	};
}

export interface PersonalizedRecommendationsResponse {
	recommendations: {
		type:
			| "practice_scenario"
			| "skill_focus"
			| "strategy_improvement"
			| "document_analysis";
		title: string;
		description: string;
		priority: "high" | "medium" | "low";
		estimatedTime: number;
		actionUrl?: string;
		metadata?: Record<string, any>;
	}[];
	reasoning: string;
	confidenceScore: number;
}
