// Deposition Preparation Types

export type DepositionStatus = "DRAFT" | "IN_PROGRESS" | "COMPLETED";
export type QuestionCategory =
	| "GENERAL"
	| "CREDIBILITY"
	| "CONSISTENCY"
	| "DOCUMENTATION"
	| "EXPERT_QUALIFICATION"
	| "IMPEACHMENT";
export type QuestionPriority = "high" | "medium" | "low";
export type QuestionGenerationCategory =
	| "GENERAL"
	| "CREDIBILITY"
	| "CONSISTENCY"
	| "DOCUMENTATION";

// Category mapping types
export type CategoryInputFormat =
	| "background"
	| "general-info"
	| "basic-info"
	| "witness-background"
	| "general-questions"
	| "fact-finding"
	| "facts-and-evidence"
	| "witness-reliability"
	| "credibility"
	| "timeline"
	| "chronology"
	| "sequence-of-events"
	| "consistency-check"
	| "document-review"
	| "documentation"
	| "records-review"
	| "evidence-review"
	| "expert-qualification"
	| "expert"
	| "expertise"
	| "qualifications"
	| "impeachment"
	| "credibility-challenge"
	| "prior-inconsistency"
	| "contradiction";

export interface CategoryMappingConfig {
	standardCategory: QuestionCategory;
	inputVariations: string[];
}

export const CATEGORY_MAPPINGS: Record<
	QuestionCategory,
	CategoryMappingConfig
> = {
	GENERAL: {
		standardCategory: "GENERAL",
		inputVariations: [
			"background",
			"general-info",
			"basic-info",
			"witness-background",
			"general-questions",
		],
	},
	CREDIBILITY: {
		standardCategory: "CREDIBILITY",
		inputVariations: [
			"fact-finding",
			"facts-and-evidence",
			"witness-reliability",
			"credibility",
		],
	},
	CONSISTENCY: {
		standardCategory: "CONSISTENCY",
		inputVariations: [
			"timeline",
			"chronology",
			"sequence-of-events",
			"consistency-check",
		],
	},
	DOCUMENTATION: {
		standardCategory: "DOCUMENTATION",
		inputVariations: [
			"document-review",
			"documentation",
			"records-review",
			"evidence-review",
		],
	},
	EXPERT_QUALIFICATION: {
		standardCategory: "EXPERT_QUALIFICATION",
		inputVariations: [
			"expert-qualification",
			"expert",
			"expertise",
			"qualifications",
		],
	},
	IMPEACHMENT: {
		standardCategory: "IMPEACHMENT",
		inputVariations: [
			"impeachment",
			"credibility-challenge",
			"prior-inconsistency",
			"contradiction",
		],
	},
};

export interface DepositionPreparation {
	id: string;
	organizationId?: string;
	userId?: string;
	caseId?: string;
	title: string;
	description?: string;
	targetWitnesses: string[];
	caseContext: string;
	keyIssues: string[];
	relatedDocumentIds: string[];
	questions: DepositionQuestion[];
	status: DepositionStatus;
	createdAt: string;
	updatedAt: string;
}

export interface DepositionQuestion {
	id: string;
	text: string;
	category: QuestionCategory;
	purpose: string;
	targetWitness?: string;
	priority: QuestionPriority;
	notes?: string;
	suggestedFollowUps?: string[];
	relatedDocuments?: string[];
	createdAt: string;
	updatedAt: string;
}

// DTOs for API requests

export interface CreateDepositionPreparationDto {
	title: string;
	description?: string;
	targetWitnesses: string[];
	caseContext: string;
	keyIssues: string[];
	relatedDocumentIds: string[];
}

export interface UpdateDepositionPreparationDto {
	title?: string;
	description?: string;
	targetWitnesses?: string[];
	caseContext?: string;
	keyIssues?: string[];
	relatedDocumentIds?: string[];
	status?: DepositionStatus;
}

export interface CreateDepositionQuestionDto {
	text: string;
	category: CategoryInputFormat | QuestionCategory;
	purpose: string;
	targetWitness?: string;
	priority: QuestionPriority;
	notes?: string;
	suggestedFollowUps?: string[];
	relatedDocuments?: string[];
}

export interface UpdateDepositionQuestionDto {
	text?: string;
	purpose?: string;
	priority?: QuestionPriority;
	notes?: string;
}

export interface GenerateQuestionsDto {
	caseContext: string;
	keyIssues: string[];
	targetWitnesses: string[];
	focusAreas?: string[];
	questionCount?: number;
	questionCategories?: (CategoryInputFormat | QuestionCategory)[];
	includeFollowUps?: boolean;
	priorityLevel?: QuestionPriority | "all";
}

export interface QuestionGenerationResult {
	questions: DepositionQuestion[];
	metadata: {
		generatedAt: string;
		generationDurationMs: number;
		modelUsed: string;
	};
}

// Deposition Analysis Types

// Legacy format for analysis list endpoint
export interface KeyTestimonyAnalysisLegacy {
	witness: string;
	testimony: string;
	credibilityScore: number;
	confidence: number;
	keyPoints: string[];
}

// New format for individual analysis endpoint
export interface KeyTestimonyAnalysis {
	speaker: string;
	statement: string;
	credibilityScore: number;
	confidence: string;
	reasoning: string;
	supportingEvidence: string[];
}

// Legacy format for analysis list endpoint
export interface CrossExaminationSuggestionLegacy {
	witness: string;
	topic: string;
	suggestedQuestions: string[];
	suggestedFollowUps: string[];
	rationale: string;
	estimatedImpact: "HIGH" | "MEDIUM" | "LOW";
}

// New format for individual analysis endpoint (mixed format from API)
export interface CrossExaminationSuggestion {
	topic: string;
	question: string;
	purpose: string;
	legalBasis: string;
	suggestedFollowUps?: string[]; // Optional field from legacy format
}

export interface Inconsistency {
	witness: string;
	statement1: string;
	statement2: string;
	context: string;
	severity: "HIGH" | "MEDIUM" | "LOW";
}

export interface PotentialImpeachmentOpportunity {
	statement: string;
	conflictingEvidence: string;
	suggestedApproach: string;
}

export interface TimelineEvent {
	event: string;
	timestamp: string;
	relevance: number;
	notes: string;
}

export interface DepositionAnalysisMetadata {
	modelUsed: string;
	confidence: string;
	analyzedAt: string;
	analysisDurationMs: number;
}

export interface DepositionAnalysisResult {
	id: string;
	depositionId: string;
	transcript: string;
	caseContext: string;
	focusAreas: string[];
	overallCredibilityScore: number;
	keyTestimonyAnalysis: KeyTestimonyAnalysis[];
	crossExaminationSuggestions: CrossExaminationSuggestion[];
	inconsistencies: Inconsistency[];
	keyFindings: string[];
	potentialImpeachmentOpportunities: PotentialImpeachmentOpportunity[];
	timelineAnalysis: TimelineEvent[];
	metadata: DepositionAnalysisMetadata;
	userId: string;
	organizationId: string;
	caseId: string;
}

// Legacy response format for analyses list endpoint
export interface DepositionAnalysisResponseLegacy {
	id: string;
	depositionId: string;
	transcript: string;
	caseContext: string;
	focusAreas: string[];
	overallCredibilityScore: number;
	keyTestimonyAnalysis: KeyTestimonyAnalysisLegacy[];
	crossExaminationSuggestions: CrossExaminationSuggestionLegacy[];
	inconsistencies: Inconsistency[];
	keyFindings: string[];
	potentialImpeachmentOpportunities: unknown[];
	timelineAnalysis: unknown[];
	metadata: DepositionAnalysisMetadata;
	userId: string;
	organizationId: string;
	caseId: string;
}

// New response format for individual analysis endpoint
export interface DepositionAnalysisResponse {
	id: string;
	depositionId: string;
	transcript: string;
	caseContext: string;
	focusAreas: string[];
	overallCredibilityScore: number;
	keyTestimonyAnalysis: KeyTestimonyAnalysis[];
	crossExaminationSuggestions: CrossExaminationSuggestion[];
	inconsistencies: Inconsistency[];
	keyFindings: string[];
	potentialImpeachmentOpportunities: PotentialImpeachmentOpportunity[];
	timelineAnalysis: TimelineEvent[];
	metadata: DepositionAnalysisMetadata;
	userId: string;
	organizationId: string;
	caseId: string;
}

export interface AnalyzeDepositionDto {
	transcript: string;
	caseContext: string;
	focusAreas: string[];
	depositionId?: string;
}

// Type alias for backward compatibility
export type DepositionAnalysis = DepositionAnalysisResponse;
