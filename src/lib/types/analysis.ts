// Base types
export interface Party {
	name: string;
	role: string;
	address?: string;
	email?: string;
	registrationNumber?: string;
}

export interface ClauseMetadata {
	obligations: string[];
	rights: string[];
	restrictions?: string[];
	definitions?: Record<string, string>;
	section: string | string[];
}

export interface Clause {
	title: string;
	content: string;
	riskLevel: "high" | "medium" | "low" | "none";
	riskDescription: string;
	metadata: ClauseMetadata;
	type?: string;
}

export interface EnrichedCitation {
	id: string;
	rawCitation: string;
	normalizedCitation: string;
	type: string;
	source: string;
	metadata: {
		title: string;
		date: string;
		status: string;
		version: string;
	};
	links: {
		sourceUrl: string;
		pdfUrl: string;
		apiUrl: string;
	};
	confidence: number;
}

// Base analysis interface with common fields
interface BaseAnalysisResult {
	identifiedCitations?: string[];
	enrichedCitations?: EnrichedCitation[];
}

// Document type-specific interfaces
export interface ContractAnalysisResult extends BaseAnalysisResult {
	documentType:
		| "CONTRACT"
		| "AGREEMENT"
		| "EMPLOYMENT_CONTRACT"
		| "LICENSING_AGREEMENT"
		| "NDA"
		| "CONTRACTOR AGREEMENT"
		| "SERVICE AGREEMENT";
	parties: Party[];
	effectiveDate: string;
	terminationDate: string | null;
	clauses: Clause[];
	specialTerms?: string[];
	governingLaw: string;
	summary: string;
}

export interface LegalOpinionAnalysisResult extends BaseAnalysisResult {
	documentType: "LEGAL_OPINION";
	question: string;
	conclusion: string;
	reasoning: string;
	citations: string[];
	confidence: "high" | "medium" | "low";
	limitations: string;
}

export interface PolicyAnalysisResult extends BaseAnalysisResult {
	documentType: "POLICY" | "PRIVACY_POLICY" | "TERMS_OF_SERVICE";
	scope: string;
	effectiveDate: string;
	sections: {
		title: string;
		content: string;
		importance: "high" | "medium" | "low";
	}[];
	complianceRequirements: string;
	reviewPeriod: string | null;
}

export interface LegislationAnalysisResult extends BaseAnalysisResult {
	documentType: "LEGISLATION" | "STATUTE" | "REGULATION" | "AGENCY_RULE";
	title: string;
	effectiveDate: string;
	jurisdiction: string;
	sections: {
		title: string;
		content: string;
	}[];
	definitions: Record<string, string>;
	penalties: string;
}

export interface CourtFilingAnalysisResult extends BaseAnalysisResult {
	documentType: "COURT_FILING" | "PLEADING" | "BRIEF" | "MOTION";
	caseNumber: string;
	parties: Party[];
	filingType: string;
	relief: string;
	keyArguments: string[];
	courtInfo: {
		jurisdiction: string;
		courtName: string;
	};
	filingDate: string;
	procedureStage: string;
}

export interface GeneralAnalysisResult extends BaseAnalysisResult {
	documentType: "GENERAL";
	title: string | null;
	keyPoints: string[];
	contentSummary: string;
}

// New document type interfaces
export interface CorporateDocumentAnalysisResult extends BaseAnalysisResult {
	documentType: "BYLAWS" | "ARTICLES_OF_INCORPORATION" | "BOARD_RESOLUTION";
	entityName: string;
	entityType: string;
	effectiveDate: string;
	governingProvisions: {
		title: string;
		content: string;
		significance: "high" | "medium" | "low";
	}[];
	jurisdictionOfFormation: string;
	keyStakeholders: {
		name: string;
		role: string;
		rights: string[];
		obligations: string[];
	}[];
	summary: string;
}

export interface EstatePlanningAnalysisResult extends BaseAnalysisResult {
	documentType: "WILL" | "TRUST" | "POWER_OF_ATTORNEY";
	principal: string;
	effectiveDate: string;
	beneficiaries: {
		name: string;
		relationship: string;
		benefits: string;
	}[];
	assets: {
		description: string;
		disposition: string;
	}[];
	executors: {
		name: string;
		powers: string[];
	}[];
	conditions: string[];
	revocability: "revocable" | "irrevocable";
	governingLaw: string;
}

export interface IntellectualPropertyAnalysisResult extends BaseAnalysisResult {
	documentType: "PATENT" | "TRADEMARK_REGISTRATION" | "COPYRIGHT_REGISTRATION";
	owner: string;
	filingDate: string;
	registrationNumber: string;
	description: string;
	scope: string;
	duration: string;
	territory: string[];
	restrictions: string[];
}

export interface RealEstateAnalysisResult extends BaseAnalysisResult {
	documentType: "DEED" | "MORTGAGE" | "LEASE";
	propertyAddress: string;
	parties: {
		name: string;
		role: "grantor" | "grantee" | "lender" | "borrower" | "seller" | "buyer" | "other";
	}[];
	legalDescription: string;
	consideration: string;
	encumbrances: string[];
	covenants: string[];
	effectiveDate: string;
	recordingInformation: string;
}

export interface FinancialDocumentAnalysisResult extends BaseAnalysisResult {
	documentType: "SECURITIES_FILING" | "PROSPECTUS" | "LOAN_AGREEMENT";
	issuer: string;
	filingDate: string;
	financialObligations: {
		description: string;
		amount: string;
		terms: string;
	}[];
	disclosures: string[];
	risks: string[];
	maturityDate: string;
	governingLaw: string;
}

// Union type for all possible analysis results
export type AnalysisResult =
	| ContractAnalysisResult
	| LegalOpinionAnalysisResult
	| PolicyAnalysisResult
	| LegislationAnalysisResult
	| CourtFilingAnalysisResult
	| GeneralAnalysisResult
	| CorporateDocumentAnalysisResult
	| EstatePlanningAnalysisResult
	| IntellectualPropertyAnalysisResult
	| RealEstateAnalysisResult
	| FinancialDocumentAnalysisResult;

export interface DocumentAnalysis {
	analysisId: string;
	result: AnalysisResult;
}
