// Collaboration Types for Frontend Integration

export interface User {
  id: string;
  name: string;
  email: string;
  avatar?: string;
  role?: string;
}

// Real-time Collaboration Types
export interface CollaborationSession {
  id: string;
  documentId: string;
  organizationId: string;
  participants: SessionParticipant[];
  status: 'active' | 'paused' | 'ended';
  createdAt: string;
  updatedAt: string;
  settings: {
    maxParticipants: number;
    allowAnonymous: boolean;
    recordChanges: boolean;
  };
}

export interface SessionParticipant {
  userId: string;
  user: User;
  joinedAt: string;
  lastSeen: string;
  cursor?: CursorPosition;
  presence: 'active' | 'idle' | 'away';
  permissions: ParticipantPermissions;
}

export interface CursorPosition {
  x: number;
  y: number;
  selection?: {
    start: number;
    end: number;
  };
}

export interface ParticipantPermissions {
  canEdit: boolean;
  canComment: boolean;
  canShare: boolean;
  canManageWorkflow: boolean;
}

// Document Operations for Real-time Editing
export interface DocumentOperation {
  id: string;
  sessionId: string;
  userId: string;
  type: 'insert' | 'delete' | 'format' | 'move';
  position: number;
  content?: string;
  length?: number;
  attributes?: Record<string, any>;
  timestamp: string;
  applied: boolean;
}

// Workflow Management Types
export interface WorkflowTemplate {
  id: string;
  name: string;
  description: string;
  organizationId: string;
  steps: WorkflowStep[];
  triggers: WorkflowTrigger[];
  isActive: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

export interface WorkflowStep {
  id: string;
  name: string;
  description: string;
  type: 'review' | 'approval' | 'comment' | 'edit' | 'sign' | 'notify';
  order: number;
  assigneeType: 'user' | 'role' | 'group';
  assigneeId: string;
  dueInHours?: number;
  isRequired: boolean;
  conditions?: WorkflowCondition[];
  actions: WorkflowAction[];
}

export interface WorkflowTrigger {
  event: 'document_uploaded' | 'document_updated' | 'comment_added' | 'manual';
  conditions?: WorkflowCondition[];
}

export interface WorkflowCondition {
  field: string;
  operator: 'equals' | 'contains' | 'greater_than' | 'less_than';
  value: any;
}

export interface WorkflowAction {
  type: 'assign_task' | 'send_notification' | 'update_status' | 'create_comment';
  parameters: Record<string, any>;
}

export interface WorkflowInstance {
  id: string;
  templateId: string;
  template: WorkflowTemplate;
  documentId: string;
  organizationId: string;
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'failed';
  currentStepId?: string;
  startedAt: string;
  completedAt?: string;
  createdBy: string;
  tasks: Task[];
  metadata: Record<string, any>;
}

// Task Management Types
export interface Task {
  id: string;
  workflowInstanceId?: string;
  workflowStepId?: string;
  documentId: string;
  organizationId: string;
  title: string;
  description: string;
  type: 'review' | 'approval' | 'comment' | 'edit' | 'sign' | 'notify';
  status: 'pending' | 'in_progress' | 'completed' | 'cancelled' | 'overdue';
  priority: 'low' | 'medium' | 'high' | 'urgent';
  assigneeId: string;
  assignee: User;
  assignedBy: string;
  assignedAt: string;
  dueAt?: string;
  completedAt?: string;
  completedBy?: string;
  metadata: Record<string, any>;
  comments: TaskComment[];
}

export interface TaskComment {
  id: string;
  taskId: string;
  userId: string;
  user: User;
  content: string;
  createdAt: string;
  updatedAt: string;
}

// Threaded Discussions Types
export interface CommentThread {
  id: string;
  documentId: string;
  organizationId: string;
  title?: string;
  type: 'discussion' | 'review' | 'approval' | 'question';
  status: 'open' | 'resolved' | 'closed';
  anchor?: DocumentAnchor;
  createdBy: string;
  creator: User;
  createdAt: string;
  updatedAt: string;
  resolvedAt?: string;
  resolvedBy?: string;
  comments: Comment[];
  watchers: string[];
  metadata: Record<string, any>;
}

export interface DocumentAnchor {
  type: 'text' | 'section' | 'page' | 'element';
  startPosition?: number;
  endPosition?: number;
  sectionId?: string;
  pageNumber?: number;
  elementId?: string;
  context?: string;
}

export interface Comment {
  id: string;
  threadId: string;
  parentId?: string;
  userId: string;
  user: User;
  content: string;
  mentions: Mention[];
  reactions: Reaction[];
  attachments: Attachment[];
  createdAt: string;
  updatedAt: string;
  editedAt?: string;
  isEdited: boolean;
  replies: Comment[];
}

export interface Mention {
  userId: string;
  user: User;
  position: number;
  length: number;
}

export interface Reaction {
  emoji: string;
  userId: string;
  user: User;
  createdAt: string;
}

export interface Attachment {
  id: string;
  filename: string;
  size: number;
  mimeType: string;
  url: string;
  uploadedAt: string;
}

// Notification Types
export interface Notification {
  id: string;
  userId: string;
  organizationId: string;
  type: 'task_assigned' | 'comment_mention' | 'workflow_completed' | 'document_shared' | 'deadline_reminder';
  title: string;
  message: string;
  data: Record<string, any>;
  isRead: boolean;
  createdAt: string;
  readAt?: string;
  actionUrl?: string;
}

// Analytics Types
export interface CollaborationMetrics {
  activeCollaborations: number;
  totalParticipants: number;
  averageSessionDuration: number;
  documentsWithComments: number;
  totalComments: number;
  workflowsCompleted: number;
  averageWorkflowTime: number;
  tasksCompleted: number;
  averageTaskTime: number;
}

export interface TeamAnalytics {
  period: {
    start: string;
    end: string;
  };
  metrics: CollaborationMetrics;
  userActivity: UserActivity[];
  workflowPerformance: WorkflowPerformance[];
  documentEngagement: DocumentEngagement[];
}

export interface UserActivity {
  userId: string;
  user: User;
  collaborationSessions: number;
  commentsCreated: number;
  tasksCompleted: number;
  documentsShared: number;
  lastActive: string;
}

export interface WorkflowPerformance {
  templateId: string;
  templateName: string;
  instancesCompleted: number;
  averageCompletionTime: number;
  successRate: number;
  bottlenecks: string[];
}

export interface DocumentEngagement {
  documentId: string;
  documentName: string;
  collaborators: number;
  comments: number;
  workflows: number;
  lastActivity: string;
}

// WebSocket Event Types
export interface WebSocketEvent {
  type: string;
  data: any;
  timestamp: string;
}

export interface SessionJoinEvent extends WebSocketEvent {
  type: 'session:join';
  data: {
    sessionId: string;
    participant: SessionParticipant;
  };
}

export interface SessionLeaveEvent extends WebSocketEvent {
  type: 'session:leave';
  data: {
    sessionId: string;
    userId: string;
  };
}

export interface DocumentOperationEvent extends WebSocketEvent {
  type: 'document:operation';
  data: {
    sessionId: string;
    operation: DocumentOperation;
  };
}

export interface CursorUpdateEvent extends WebSocketEvent {
  type: 'cursor:update';
  data: {
    sessionId: string;
    userId: string;
    cursor: CursorPosition;
  };
}

export interface PresenceChangeEvent extends WebSocketEvent {
  type: 'user:presence_changed';
  data: {
    sessionId: string;
    userId: string;
    presence: 'active' | 'idle' | 'away';
  };
}

export interface CommentCreatedEvent extends WebSocketEvent {
  type: 'comment:created';
  data: {
    threadId: string;
    comment: Comment;
  };
}

export interface TaskAssignedEvent extends WebSocketEvent {
  type: 'task:assigned';
  data: {
    task: Task;
  };
}

export interface WorkflowStatusChangedEvent extends WebSocketEvent {
  type: 'workflow:status_changed';
  data: {
    workflowInstanceId: string;
    status: string;
    currentStepId?: string;
  };
}
