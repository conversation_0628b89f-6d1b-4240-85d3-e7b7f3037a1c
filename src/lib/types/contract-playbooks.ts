// Contract Playbooks Type Definitions
export interface ContractPlaybook {
	id: string;
	organizationId: string;
	name: string;
	contractType:
		| "msa"
		| "nda"
		| "sow"
		| "employment"
		| "vendor"
		| "licensing"
		| "partnership"
		| "custom";
	description?: string;
	version: string;
	rules: PlaybookRule[];
	metadata: PlaybookMetadata;
	isActive: boolean;
	isTemplate: boolean;
	createdBy: string;
	updatedBy: string;
	createdAt: string;
	updatedAt: string;
}

export interface PlaybookRule {
	id: string;
	name: string;
	category: string;
	ruleType:
		| "required_clause"
		| "prohibited_clause"
		| "conditional_clause"
		| "formatting_rule"
		| "calculation_rule";
	severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
	criteria: RuleCriteria;
	acceptableLanguage: AcceptableLanguage;
	unacceptableTerms: UnacceptableTerms;
	description: string;
	isActive: boolean;
}

export interface RuleCriteria {
	keywords?: string[];
	semanticConcepts?: string[];
	patterns?: string[];
	conditions?: Record<string, unknown>;
}

export interface AcceptableLanguage {
	examples?: string[];
	templates?: string[];
	alternatives?: string[];
}

export interface UnacceptableTerms {
	prohibited?: string[];
	flagged?: string[];
	warnings?: string[];
}

export interface PlaybookMetadata {
	industry?: string;
	jurisdiction?: string;
	riskProfile?: string;
	tags?: string[];
	customFields?: Record<string, unknown>;
}

export interface ContractAnalysis {
	id: string;
	organizationId: string;
	contractId: string;
	playbookId: string;
	playbookName: string;
	overallScore: number; // 0-100
	riskLevel: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
	status: "PENDING" | "IN_PROGRESS" | "COMPLETED" | "FAILED";
	deviations: Deviation[];
	summary: AnalysisSummary;
	metrics: AnalysisMetrics;
	metadata: AnalysisMetadata;
	analyzedBy: string;
	analyzedAt: string;
	createdAt: string;
	updatedAt: string;
}

export interface Deviation {
	ruleId: string;
	ruleName: string;
	severity: "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";
	description: string;
	location: {
		page?: number;
		section?: string;
		paragraph?: number;
	};
	recommendation?: string;
	confidence: number;
}

export interface AnalysisSummary {
	totalRules: number;
	passedRules: number;
	failedRules: number;
	warnings: number;
	criticalIssues: number;
	recommendations: string[];
}

export interface AnalysisMetrics {
	processingTime: number;
	confidenceScore: number;
	coveragePercentage: number;
	riskFactors: string[];
}

export interface AnalysisMetadata {
	documentType?: string;
	documentLength?: number;
	analysisVersion?: string;
	aiModel?: string;
}

// Request/Response Types
export interface PlaybookSearchParams {
	query?: string;
	contractType?: string;
	isActive?: boolean;
	isTemplate?: boolean;
	page?: number;
	limit?: number;
}

export interface PlaybooksResponse {
	playbooks: ContractPlaybook[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

export interface CreatePlaybookRequest {
	name: string;
	contractType: string;
	description?: string;
	version: string;
	rules: CreatePlaybookRule[];
	metadata: PlaybookMetadata;
	isActive?: boolean;
	isTemplate?: boolean;
}

export interface CreatePlaybookRule {
	name: string;
	category: string;
	ruleType: PlaybookRule["ruleType"];
	severity: PlaybookRule["severity"];
	criteria: RuleCriteria;
	acceptableLanguage?: AcceptableLanguage;
	unacceptableTerms?: UnacceptableTerms;
	description: string;
	isActive?: boolean;
}

export interface UpdatePlaybookRequest {
	name?: string;
	description?: string;
	version?: string;
	rules?: UpdatePlaybookRule[];
	metadata?: PlaybookMetadata;
	isActive?: boolean;
	isTemplate?: boolean;
}

export interface UpdatePlaybookRule extends CreatePlaybookRule {
	id?: string;
}

export interface AnalyzeContractRequest {
	contractId: string;
	playbookId: string;
	documentType?: string;  // The analyzed document type that matches analysis.ts types
	options?: {
		includeRecommendations?: boolean;
		riskThreshold?: number; // 1-5
		aiAnalysis?: boolean;
		detailedReport?: boolean;
	};
}

export interface AnalysisSearchParams {
	contractId?: string;
	playbookId?: string;
	riskLevel?: string;
	status?: string;
	startDate?: string;
	endDate?: string;
	page?: number;
	limit?: number;
}

export interface AnalysesResponse {
	analyses: ContractAnalysis[];
	total: number;
	page: number;
	limit: number;
	totalPages: number;
}

export interface PlaybookAnalytics {
	playbookId: string;
	totalAnalyses: number;
	averageScore: number;
	riskDistribution: {
		LOW: number;
		MEDIUM: number;
		HIGH: number;
		CRITICAL: number;
	};
	commonDeviations: Array<{
		ruleId: string;
		ruleName: string;
		frequency: number;
		averageSeverity: string;
	}>;
	performanceMetrics: {
		averageProcessingTime: number;
		averageConfidenceScore: number;
	};
	timeSeriesData: Array<{
		date: string;
		analysisCount: number;
		averageScore: number;
	}>;
}

export interface DuplicatePlaybookRequest {
	name: string;
}

export interface ExportPlaybookResponse {
	playbook: ContractPlaybook;
	exportFormat: string;
	exportedAt: string;
	version: string;
}

export interface ImportPlaybookRequest {
	playbookData: Partial<ContractPlaybook>;
	options?: {
		overwriteExisting?: boolean;
		validateRules?: boolean;
	};
}

// Error Types
export interface ContractPlaybookError {
	statusCode: number;
	message: string;
	error: string;
	details?: Record<string, unknown>;
	path: string;
	timestamp: string;
}

// Contract Types for UI
export const CONTRACT_TYPES = [
	{ value: "msa", label: "Master Service Agreement" },
	{ value: "nda", label: "Non-Disclosure Agreement" },
	{ value: "sow", label: "Statement of Work" },
	{ value: "employment", label: "Employment Contract" },
	{ value: "vendor", label: "Vendor Agreement" },
	{ value: "licensing", label: "Licensing Agreement" },
	{ value: "partnership", label: "Partnership Agreement" },
	{ value: "custom", label: "Custom Contract" },
] as const;

export const RULE_TYPES = [
	{ value: "required_clause", label: "Required Clause" },
	{ value: "prohibited_clause", label: "Prohibited Clause" },
	{ value: "conditional_clause", label: "Conditional Clause" },
	{ value: "formatting_rule", label: "Formatting Rule" },
	{ value: "calculation_rule", label: "Calculation Rule" },
] as const;

export const SEVERITY_LEVELS = [
	{ value: "LOW", label: "Low", color: "text-green-600" },
	{ value: "MEDIUM", label: "Medium", color: "text-yellow-600" },
	{ value: "HIGH", label: "High", color: "text-orange-600" },
	{ value: "CRITICAL", label: "Critical", color: "text-red-600" },
] as const;

export const RISK_LEVELS = [
	{ value: "LOW", label: "Low Risk", color: "bg-green-100 text-green-800" },
	{
		value: "MEDIUM",
		label: "Medium Risk",
		color: "bg-yellow-100 text-yellow-800",
	},
	{ value: "HIGH", label: "High Risk", color: "bg-orange-100 text-orange-800" },
	{
		value: "CRITICAL",
		label: "Critical Risk",
		color: "bg-red-100 text-red-800",
	},
] as const;

export const ANALYSIS_STATUS = [
	{ value: "PENDING", label: "Pending", color: "bg-gray-100 text-gray-800" },
	{
		value: "IN_PROGRESS",
		label: "In Progress",
		color: "bg-blue-100 text-blue-800",
	},
	{
		value: "COMPLETED",
		label: "Completed",
		color: "bg-green-100 text-green-800",
	},
	{ value: "FAILED", label: "Failed", color: "bg-red-100 text-red-800" },
] as const;
