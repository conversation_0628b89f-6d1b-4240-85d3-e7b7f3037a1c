// Negotiation Playbook Types

export type DocumentType = 'CONTRACT' | 'AGREEMENT' | 'POLICY' | 'TERMS' | 'OTHER';

export type Priority = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

export type RiskLevel = 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';

export type StrategicCategory = 'TIMING' | 'APPROACH' | 'LEVERAGE' | 'FALLBACK' | 'PREPARATION';

export interface NegotiationPoint {
  title: string;
  description: string;
  currentPosition: string;
  recommendedPosition: string;
  priority: Priority;
  rationale: string;
}

export interface StrategicRecommendation {
  category: StrategicCategory;
  recommendation: string;
  reasoning: string;
}

export interface RiskFactor {
  factor: string;
  severity: RiskLevel;
  mitigation: string;
}

export interface RiskAssessment {
  overallRiskLevel: RiskLevel;
  riskFactors: RiskFactor[];
}

export interface NegotiationSimulation {
  scenario: string;
  response: string;
  expectedOutcome: string;
}

// Backend API Response Types (actual structure from backend)
export interface SimulationScenario {
  type: string;
  trigger: string;
  responseStrategy: string;
  expectedOutcome: string;
  _id: string;
}

export interface NegotiationStrategy {
  section: string;
  recommendations: string[];
  simulationScenarios: SimulationScenario[];
  riskLevel: string;
  priority: number;
  alternativeLanguage: string;
  _id: string;
}

// Backend API Response Structure (actual from backend)
export interface BackendNegotiationPlaybook {
  _id: string;
  documentId: string;
  strategies: NegotiationStrategy[];
  overallAssessment: string;
  keyLeveragePoints: string[];
  dealBreakers: string[];
  timestamp: string;
  isTemplate: boolean;
  tags: string[];
  usageCount: number;
  createdAt: string;
  updatedAt: string;
  __v: number;
}

// Frontend Display Structure (transformed for UI)
export interface NegotiationPlaybook {
  id: string;
  documentId: string;
  executiveSummary: string;
  keyNegotiationPoints: NegotiationPoint[];
  strategicRecommendations: StrategicRecommendation[];
  riskAssessment: RiskAssessment;
  negotiationSimulations: NegotiationSimulation[];
  createdAt: string;
  updatedAt: string;
}

export interface GeneratePlaybookOptions {
  documentType: DocumentType;
  focusAreas?: string[];
  includeSimulations?: boolean;
  organizationPreferences?: string;
}

export interface PlaybookApiResponse {
  success: boolean;
  data: NegotiationPlaybook;
}

export interface PlaybookError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: any;
  };
}

// Common focus areas for easy selection
export const COMMON_FOCUS_AREAS = [
  'termination',
  'liability', 
  'payment',
  'intellectual_property',
  'confidentiality',
  'dispute_resolution',
  'force_majeure',
  'warranties'
] as const;

// Document type options
export const DOCUMENT_TYPE_OPTIONS: { value: DocumentType; label: string }[] = [
  { value: 'CONTRACT', label: 'Legal Contract' },
  { value: 'AGREEMENT', label: 'Service Agreement' },
  { value: 'POLICY', label: 'Company Policy' },
  { value: 'TERMS', label: 'Terms of Service' },
  { value: 'OTHER', label: 'Other Document' }
];
