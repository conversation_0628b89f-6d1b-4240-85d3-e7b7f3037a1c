// Sample Playbooks Type Definitions

export type ContractType = 
  | "NDA"
  | "SERVICE_AGREEMENT" 
  | "EMPLOYMENT_CONTRACT"
  | "LICENSING_AGREEMENT"
  | "PURCHASE_AGREEMENT"
  | "CONSULTING_AGREEMENT"
  | "MASTER_SERVICE_AGREEMENT"
  | "VENDOR_AGREEMENT"
  | "SOFTWARE_LICENSE"
  | "PARTNERSHIP_AGREEMENT";

export type Industry = 
  | "TECHNOLOGY"
  | "HEALTHCARE" 
  | "FINANCE"
  | "LEGAL"
  | "REAL_ESTATE"
  | "MANUFACTURING"
  | "RETAIL"
  | "EDUCATION"
  | "GENERAL";

export type Difficulty = "beginner" | "intermediate" | "expert";

export type Priority = "low" | "medium" | "high" | "critical";

export type RiskLevel = "low" | "medium" | "high" | "critical";

export type RiskProfile = "Low" | "Medium" | "High" | "Critical";

export type Severity = "LOW" | "MEDIUM" | "HIGH" | "CRITICAL";

export type RuleType = 
  | "required_clause"
  | "prohibited_clause" 
  | "conditional_clause"
  | "formatting_rule"
  | "calculation_rule";

// Sample Negotiation Playbook Types (Updated to match API response)
export interface SampleNegotiationSimulationScenario {
  type: string;
  trigger: string;
  responseStrategy: string;
  expectedOutcome: string;
}

export interface SampleNegotiationStrategy {
  section: string;
  recommendations: string[];
  riskLevel: string;
  priority: number;
  alternativeLanguage: string;
  simulationScenarios: SampleNegotiationSimulationScenario[];
}

export interface SampleNegotiationPlaybook {
  documentId: string;
  templateName: string;
  templateDescription: string;
  contractType: string;
  industry: string;
  difficulty: string;
  isTemplate: boolean;
  tags: string[];
  strategies: SampleNegotiationStrategy[];
  overallAssessment: string;
  keyLeveragePoints: string[];
  dealBreakers: string[];
  timestamp: string;
  usageCount: number;
}

// Sample Contract Playbook Types
export interface SampleRuleCriteria {
  keywords?: string[];
  patterns?: string[];
  semanticConcepts?: string[];
  contextRequirements?: string[];
}

export interface SampleAcceptableLanguage {
  preferred?: string[];
  acceptable?: string[];
  fallbackPositions?: string[];
}

export interface SampleUnacceptableTerms {
  prohibited?: string[];
  requiresEscalation?: string[];
  autoReject?: string[];
}

export interface SampleNegotiationGuidance {
  strategy: string;
  alternatives?: string[];
  businessImpact: string;
}

export interface SampleContractRule {
  id: string;
  name: string;
  category: string;
  ruleType: RuleType;
  severity: Severity;
  criteria: SampleRuleCriteria;
  acceptableLanguage: SampleAcceptableLanguage;
  unacceptableTerms: SampleUnacceptableTerms;
  negotiationGuidance: SampleNegotiationGuidance;
  description?: string;
  isActive: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface SampleContractPlaybook {
  id: string;
  organizationId: string;
  name: string;
  contractType: ContractType;
  description: string;
  version: string;
  industry?: Industry;
  riskProfile?: RiskProfile;
  tags?: string[];
  rules: SampleContractRule[];
  metadata: {
    industry?: string;
    jurisdiction?: string;
    riskProfile?: string;
    author?: string;
    tags?: string[];
    version?: string;
    lastUpdated?: string;
    totalRules?: number;
    riskDistribution?: Record<Severity, number>;
  };
  isActive: boolean;
  isTemplate: boolean;
  createdBy: string;
  createdAt: string;
  updatedAt: string;
}

// API Response Types
export interface SamplePlaybooksResponse<T> {
  samples: T[];
  total: number;
  contractTypeDistribution?: Array<{ contractType: ContractType; count: number }>;
  difficultyDistribution?: Array<{ difficulty: Difficulty; count: number }>;
  riskProfileDistribution?: Array<{ riskProfile: RiskProfile; count: number }>;
  industryDistribution?: Array<{ industry: Industry; count: number }>;
}

export interface SamplePlaybookStatsResponse {
  totalSamples: number;
  totalUserPlaybooks?: number;
  contractTypeDistribution: Array<{ contractType: ContractType; count: number }>;
  difficultyDistribution?: Array<{ difficulty: Difficulty; count: number }>;
  riskProfileDistribution?: Array<{ riskProfile: RiskProfile; count: number }>;
  industryDistribution?: Array<{ industry: Industry; count: number }>;
}

// Search and Filter Types
export interface SampleNegotiationPlaybookFilters {
  contractType?: ContractType;
  industry?: Industry;
  difficulty?: Difficulty;
  tags?: string;
}

export interface SampleContractPlaybookFilters {
  contractType?: ContractType;
  industry?: Industry;
  riskProfile?: RiskProfile;
  tags?: string;
}

// Clone Response Types
export interface ClonedNegotiationPlaybook {
  _id: string;
  documentId: string;
  organizationId: string;
  name: string;
  isTemplate: boolean;
  strategies: SampleNegotiationStrategy[];
  simulationScenarios: SampleNegotiationSimulationScenario[];
  overallAssessment: string;
  createdAt: string;
  metadata: {
    clonedFrom: string;
    clonedAt: string;
  };
}

export interface ClonedContractPlaybook {
  _id: string;
  id: string;
  organizationId: string;
  name: string;
  isTemplate: boolean;
  rules: SampleContractRule[];
  metadata: {
    clonedFrom: string;
    clonedAt: string;
    version: string;
    lastUpdated: string;
    author: string;
    totalRules: number;
    riskDistribution: Record<Severity, number>;
  };
}

// Error Types
export interface SamplePlaybookError {
  statusCode: number;
  message: string;
  error: string;
}
