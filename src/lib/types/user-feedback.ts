// User Feedback Types

export type FeedbackType = 'thumbs_up' | 'thumbs_down' | 'correction' | 'suggestion' | 'general';
export type FeedbackStatus = 'pending' | 'reviewed' | 'implemented' | 'rejected';
export type FeedbackSource = 'document_analysis' | 'chat' | 'document_comparison' | 'citation_analysis';
export type VoteType = 'upvote' | 'downvote';
export type CorrectionStatus = 'pending' | 'approved' | 'rejected' | 'implemented';
export type SourceType = 'document' | 'analysis' | 'chat' | 'comparison';

export interface DocumentFeedbackContext {
  documentId: string;
  sectionId?: string;
  paragraph?: number;
  pageNumber?: number;
  selection?: string;
  documentType?: string;
  section?: string;  // For legal opinion sections
  clauseTitle?: string;
  clauseContent?: string;
  artifactType?: string;
  contentTitle?: string;  // Generic title for any content section
  contentType?: string;   // Type of content being referenced
  analysis?: {
    type?: string;
    details?: Record<string, string>;
    category?: string;
    score?: number;
  };
}

export interface ChatFeedbackContext {
  sessionId: string;
  messageId?: string;
  interactionId?: string;
}

export interface DocumentStats {
  length: number;
  sections?: number;
  paragraphs?: number;
  words?: number;
}

export interface ComparisonFeedbackContext {
  comparisonId: string;
  documentId1?: string;
  documentId2?: string;
  sectionId?: string;
  comparisonType?: 'visual-diff' | 'semantic' | 'structural';
  documentStats?: {
    documentA: DocumentStats;
    documentB: DocumentStats;
  };
}

export interface CitationAnalysisFeedbackContext {
  citationId: string;
  relationshipId?: string;
  analysisType?: string;
}

export type FeedbackContextData = Partial<
  DocumentFeedbackContext &
  ChatFeedbackContext &
  ComparisonFeedbackContext &
  CitationAnalysisFeedbackContext
>;

export interface Feedback {
  id: string;
  content: string;
  type: FeedbackType;
  status: FeedbackStatus;
  source: FeedbackSource;
  userId: string;
  organizationId: string;
  contextData: FeedbackContextData;
  sourceId: string;
  rating?: number;
  adminResponse?: string;
  adminResponseDate?: string;
  isAnonymous: boolean;
  upvotes: number;
  downvotes: number;
  tags: string[];
  categoryId?: string;
  createdAt: string;
  updatedAt: string;
}

export interface CreateFeedbackRequest {
  content: string;
  type: FeedbackType;
  source: FeedbackSource;
  contextData: FeedbackContextData;
  sourceId: string;
  rating?: number;
  isAnonymous?: boolean;
}

export interface UpdateFeedbackRequest {
  status?: FeedbackStatus;
  adminResponse?: string;
  categoryId?: string;
  tags?: string[];
}

export interface FeedbackVoteRequest {
  voteType: VoteType;
}

export interface FeedbackCategory {
  id: string;
  name: string;
  description: string;
  isDefault: boolean;
  isActive: boolean;
  organizationId: string;
  createdBy: string;
  priority: number;
  color: string;
  tags: string[];
  createdAt: string;
  updatedAt: string;
}

export interface CreateCategoryRequest {
  name: string;
  description: string;
  color: string;
  priority: number;
  isDefault?: boolean;
  isActive?: boolean;
  tags?: string[];
}

export interface FeedbackCorrection {
  id: string;
  feedbackId: string;
  originalContent: string;
  correctedContent: string;
  userId: string;
  organizationId: string;
  status: CorrectionStatus;
  contextData: FeedbackContextData;
  sourceId: string;
  sourceType: SourceType;
  upvotes: number;
  downvotes: number;
  tags: string[];
  isIncorporatedInTraining: boolean;
  createdAt: string;
  updatedAt: string;
}

export interface CreateCorrectionRequest {
  feedbackId: string;
  originalContent: string;
  correctedContent: string;
  contextData: FeedbackContextData;
  sourceId: string;
  sourceType: SourceType;
}

export interface FeedbackAnalyticsSummary {
  totalFeedback: number;
  typeBreakdown: {
    type: FeedbackType;
    count: number;
    percentage: number;
  }[];
  statusBreakdown: {
    status: FeedbackStatus;
    count: number;
    percentage: number;
  }[];
  sourceBreakdown: {
    source: FeedbackSource;
    count: number;
    percentage: number;
  }[];
  categoryBreakdown: {
    id: string;
    name: string;
    count: number;
  }[];
  timeSeriesData: {
    _id: string;
    count: number;
    thumbsUp: number;
    thumbsDown: number;
    corrections: number;
  }[];
  ratings: {
    average: number;
    count: number;
  };
}

export interface FeedbackPagination {
  total: number;
  page: number;
  limit: number;
  pages: number;
}

export interface FeedbackResponse<T> {
  data: T[];
  pagination: FeedbackPagination;
}

export interface FeedbackQueryParams {
  type?: FeedbackType;
  status?: FeedbackStatus;
  source?: FeedbackSource;
  categoryId?: string;
  sourceId?: string;
  startDate?: string;
  endDate?: string;
  searchText?: string;
  page?: number;
  limit?: number;
}

export interface CorrectionQueryParams {
  feedbackId?: string;
  status?: CorrectionStatus;
  sourceId?: string;
  sourceType?: SourceType;
  searchText?: string;
  page?: number;
  limit?: number;
}

export interface AnalyticsQueryParams {
  startDate?: string;
  endDate?: string;
  sourceId?: string;
  source?: FeedbackSource;
}
