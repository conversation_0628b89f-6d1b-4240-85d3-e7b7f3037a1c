export interface RiskScorePoint {
  date: string;
  score: number;
}

export interface KeyChange {
  field: string;
  from: string;
  to: string;
  analysisId: string;
  date: string;
}

export interface ClauseCountEvolution {
  restrictive: number[];
  favorable: number[];
}

export interface AnalysisEvolutionMetrics {
  riskScoreEvolution: RiskScorePoint[];
  clauseCountEvolution: ClauseCountEvolution;
  keyChanges: KeyChange[];
}

export interface AnalysisEvolution {
  documentId: string;
  analysisCount: number;
  firstAnalysisDate: string;
  latestAnalysisDate: string;
  metrics: AnalysisEvolutionMetrics;
  totalAnalyses: number;
  analysisDates: string[];
  processingTimes: number[];
  contentLengths: number[];
  analysisIds: string[];
}
