export type SubscriptionTier = "law_student" | "lawyer" | "law_firm";
export type SubscriptionStatus =
	| "active"
	| "canceled"
	| "past_due"
	| "incomplete";

// Define enum for subscription tiers to ensure consistency
export enum SubscriptionTierEnum {
	LAW_STUDENT = "law_student", // Replaces 'free'
	LAWYER = "lawyer",           // Replaces 'pro'
	LAW_FIRM = "law_firm",       // Replaces 'admin'
}

// Legacy tier mapping for backward compatibility
export const LEGACY_TIER_MAPPING = {
	"free": "law_student",
	"pro": "lawyer",
	"admin": "law_firm",
} as const;

// Helper function to map legacy tiers to new tiers
export function mapLegacyTier(tier: string): SubscriptionTier {
	return LEGACY_TIER_MAPPING[tier as keyof typeof LEGACY_TIER_MAPPING] || tier as SubscriptionTier;
}

// Helper function to get display name for tier
export function getTierDisplayName(tier: SubscriptionTier): string {
	const displayNames = {
		"law_student": "🎓 Law Student",
		"lawyer": "⚖️ Lawyer",
		"law_firm": "🏢 Law Firm",
	};
	return displayNames[tier] || tier;
}

export interface UsageStats {
	documentsProcessed: number;
	analysisCount: number;
	lastUpdated: string;
}

// Credit system types
export interface CreditTransaction {
	type: "allocation" | "purchase" | "usage" | "refund" | "expiration";
	amount: number;
	balance: number;
	featureName?: string;
	transactionId?: string;
	timestamp: string;
	description: string;
}

export interface AutoRecharge {
	enabled: boolean;
	threshold: number;
	amount: number;
	stripePaymentMethodId?: string;
}

export interface CreditBalance {
	current: number;
	monthlyAllocation: number;
	totalEarned: number;
	totalSpent: number;
	lastAllocation: string;
}

export interface CreditPackage {
	id: string;
	name: string;
	credits: number;
	bonus: number;
	price: number;
	total: number;
	popular?: boolean;
	targetTier?: SubscriptionTier;
	description?: string;
}

export interface FeatureCost {
	featureName: string;
	credits: number;
	category: "free" | "basic" | "advanced" | "premium";
	description: string;
}

export interface SubscriptionFeature {
	id: string;
	name: string;
	description: string;
	tiers: SubscriptionTier[];
}

export interface SubscriptionPlan {
	id: SubscriptionTier;
	name: string;
	description: string;
	price: number;
	features: string[];
	limits: {
		documentLimit: number | "unlimited";
		// Analysis limits removed - now unlimited for all tiers within credit allocation
	};
}

export interface Subscription {
	organizationId: string;
	tier: SubscriptionTier;
	status: SubscriptionStatus;
	currentPeriodStart: string;
	currentPeriodEnd: string;
	cancelAtPeriodEnd: boolean;
	trialTier?: SubscriptionTier;
	trialEndDate?: string;
	features: string[];
	usageStats: UsageStats;
	// Credit system fields
	creditBalance: number;
	monthlyCreditsAllocation: number;
	totalCreditsEarned: number;
	totalCreditsSpent: number;
	lastCreditAllocation: string;
	creditHistory: CreditTransaction[];
	autoRecharge: AutoRecharge;
}

// Define feature lists by tier
export const tierFeatures = {
	[SubscriptionTierEnum.LAW_STUDENT]: [
		"basic_analysis",
		"document_upload",
		"chat",
		"basic_comparison",
		"basic_citation_analysis",
		"document_organization",
		"user_feedback",
		"threaded_discussions",
	],
	[SubscriptionTierEnum.LAWYER]: [
		"basic_analysis",
		"document_upload",
		"chat",
		"advanced_analysis",
		"bulk_upload",
		"priority_processing",
		"basic_comparison",
		"enhanced_comparison",
		"basic_citation_analysis",
		"enhanced_citation_analysis",
		"document_organization",
		"user_feedback",
		"custom_training",
		"advanced_document_organization",
		"advanced_analytics",
		"document_comparison",
		"negotiation_playbook",
		"privilege_log_automation",
		"negotiation_simulator",
		"contract_playbooks",
		"compliance_auditor",
		"section_comparison",
		"ai_insights",
		"export_capabilities",
		"api_access",
		"team_collaboration",
		"custom_workflows",
		"advanced_search",
		"audit_trail",
		"data_retention",
		"priority_support",
		"real_time_collaboration",
		"threaded_discussions",
		"workflow_management",
		"task_management",
		"advanced_sharing",
	],
	[SubscriptionTierEnum.LAW_FIRM]: [
		"basic_analysis",
		"document_upload",
		"chat",
		"advanced_analysis",
		"bulk_upload",
		"priority_processing",
		"basic_comparison",
		"enhanced_comparison",
		"basic_citation_analysis",
		"enhanced_citation_analysis",
		"document_organization",
		"user_feedback",
		"custom_training",
		"advanced_document_organization",
		"advanced_analytics",
		"document_comparison",
		"negotiation_playbook",
		"privilege_log_automation",
		"negotiation_simulator",
		"contract_playbooks",
		"compliance_auditor",
		"section_comparison",
		"ai_insights",
		"export_capabilities",
		"api_access",
		"team_collaboration",
		"custom_workflows",
		"advanced_search",
		"audit_trail",
		"data_retention",
		"priority_support",
		"precedent_analysis",
		"template_generation",
		"document_automation",
		"ai_assisted_drafting",
		"clause_intelligence",
		"deposition_preparation",
		"litigation_support",
		"contract_risk_scoring",
		"real_time_collaboration",
		"threaded_discussions",
		"workflow_management",
		"task_management",
		"advanced_sharing",
		"team_analytics",
	],
};

export const SUBSCRIPTION_FEATURES: SubscriptionFeature[] = [
	// Basic features (Free tier and above)
	{
		id: "basic_analysis",
		name: "Basic Analysis",
		description: "Standard document analysis and insights",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "document_upload",
		name: "Document Upload",
		description: "Upload individual documents for analysis",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "chat",
		name: "Chat Interface",
		description: "Interact with documents using natural language",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "basic_comparison",
		name: "Basic Comparison",
		description: "Compare documents with basic difference highlighting",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "basic_citation_analysis",
		name: "Basic Citation Analysis",
		description: "Identify and analyze basic citations in documents",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "document_organization",
		name: "Document Organization",
		description: "Organize documents with tags and folders",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "user_feedback",
		name: "User Feedback",
		description: "Provide feedback on analysis results",
		tiers: ["law_student", "lawyer", "law_firm"],
	},

	// Lawyer features
	{
		id: "advanced_analysis",
		name: "Advanced Analysis",
		description: "Deep insights and comprehensive document understanding",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "bulk_upload",
		name: "Bulk Upload",
		description: "Upload and process multiple documents at once",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "priority_processing",
		name: "Priority Processing",
		description: "Faster document processing and analysis",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "enhanced_comparison",
		name: "Enhanced Comparison",
		description: "Advanced document comparison with detailed insights",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "enhanced_citation_analysis",
		name: "Enhanced Citation Analysis",
		description: "Advanced citation analysis with precedent tracking",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "custom_training",
		name: "Custom Training",
		description: "Train models on your specific document types",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "advanced_document_organization",
		name: "Advanced Document Organization",
		description:
			"Advanced organization features including nested folders and bulk operations",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "advanced_analytics",
		name: "Advanced Analytics",
		description: "Detailed usage and document analytics",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "document_comparison",
		name: "Document Comparison",
		description: "Compare documents side by side with difference highlighting",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "negotiation_playbook",
		name: "Negotiation Playbook",
		description:
			"AI-powered strategic recommendations for document negotiations",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "privilege_log_automation",
		name: "Privilege Log Automation",
		description: "AI-powered detection and management of privileged content",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "negotiation_simulator",
		name: "Negotiation Simulator",
		description: "AI-powered negotiation training with realistic scenarios",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "contract_playbooks",
		name: "Contract Playbooks",
		description:
			"Automated contract analysis with customizable rule-based playbooks",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "compliance_auditor",
		name: "Compliance Auditor",
		description:
			"AI-powered regulatory compliance analysis for legal documents",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "section_comparison",
		name: "Section Comparison",
		description: "Compare specific sections across multiple documents",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "ai_insights",
		name: "AI Insights",
		description: "Advanced AI-powered insights and recommendations",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "export_capabilities",
		name: "Export Capabilities",
		description:
			"Export analysis results, playbooks, and reports in multiple formats",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "api_access",
		name: "API Access",
		description: "Programmatic access to document analysis and features",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "team_collaboration",
		name: "Team Collaboration",
		description: "Share documents and analysis results with team members",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "custom_workflows",
		name: "Custom Workflows",
		description: "Create and automate custom document processing workflows",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "advanced_search",
		name: "Advanced Search",
		description:
			"Powerful search across all documents with filters and semantic search",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "audit_trail",
		name: "Audit Trail",
		description: "Complete audit trail of all document activities and changes",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "data_retention",
		name: "Extended Data Retention",
		description: "Extended data retention and backup capabilities",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "priority_support",
		name: "Priority Support",
		description: "Priority customer support with faster response times",
		tiers: ["lawyer", "law_firm"],
	},

	// Collaboration Features
	{
		id: "real_time_collaboration",
		name: "Real-time Collaboration",
		description: "Live collaborative editing with cursor tracking and presence awareness",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "threaded_discussions",
		name: "Threaded Discussions",
		description: "Document comments, @mentions, and threaded discussions",
		tiers: ["law_student", "lawyer", "law_firm"],
	},
	{
		id: "workflow_management",
		name: "Workflow Management",
		description: "Custom approval workflows and task assignment",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "task_management",
		name: "Task Management",
		description: "Comprehensive task tracking with deadlines and notifications",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "advanced_sharing",
		name: "Advanced Document Sharing",
		description: "External sharing with granular permissions and expiration",
		tiers: ["lawyer", "law_firm"],
	},
	{
		id: "team_analytics",
		name: "Team Analytics",
		description: "Collaboration metrics and team productivity insights",
		tiers: ["law_firm"],
	},
];

export const SUBSCRIPTION_PLANS: SubscriptionPlan[] = [
	{
		id: "law_student",
		name: "Law Student",
		description: "Essential document analysis tools for law students and legal education. 50 monthly credits for AI-powered features.",
		price: 0,
		features: tierFeatures[SubscriptionTierEnum.LAW_STUDENT],
		limits: {
			documentLimit: 10,
		},
	},
	{
		id: "lawyer",
		name: "Lawyer",
		description:
			"Professional plan for individual lawyers with advanced features. Includes unlimited document analysis, advanced AI insights, priority support, 500 monthly credits, contract playbooks, and legal research tools.",
		price: 29.99,
		features: tierFeatures[SubscriptionTierEnum.LAWYER],
		limits: {
			documentLimit: 200,
		},
	},
	{
		id: "law_firm",
		name: "Law Firm",
		description:
			"Enterprise plan for law firms with team collaboration features. Everything in Pro plus team collaboration, advanced workflow management, unlimited users, 2000 monthly credits, custom integrations, and dedicated support.",
		price: 99.99,
		features: tierFeatures[SubscriptionTierEnum.LAW_FIRM],
		limits: {
			documentLimit: "unlimited",
		},
	},
];

// Credit allocations by tier
export const TIER_CREDIT_ALLOCATIONS = {
	[SubscriptionTierEnum.LAW_STUDENT]: 50,
	[SubscriptionTierEnum.LAWYER]: 500,
	[SubscriptionTierEnum.LAW_FIRM]: 2000,
};

// Feature costs configuration
// 🎯 KEY PRINCIPLE: Credits are ONLY consumed for AI-powered features
// All CRUD operations (Create, Read, Update, Delete) are completely FREE
export const FEATURE_COSTS: Record<string, FeatureCost> = {
	// 🆓 FREE FEATURES (0 Credits) - Basic CRUD operations and essential functionality
	document_upload: {
		featureName: "document_upload",
		credits: 0,
		category: "free",
		description: "Upload and process documents",
	},
	document_organization: {
		featureName: "document_organization",
		credits: 0,
		category: "free",
		description: "Organize and categorize documents",
	},
	user_feedback: {
		featureName: "user_feedback",
		credits: 0,
		category: "free",
		description: "Provide feedback on the system",
	},
	clause_library: {
		featureName: "clause_library",
		credits: 0,
		category: "free",
		description: "Access to clause library and suggestions",
	},
	contract_playbooks: {
		featureName: "contract_playbooks",
		credits: 0,
		category: "free",
		description: "Contract playbook creation and management",
	},
	deposition_preparation: {
		featureName: "deposition_preparation",
		credits: 0,
		category: "free",
		description: "Deposition preparation and management",
	},
	audit_logs: {
		featureName: "audit_logs",
		credits: 0,
		category: "free",
		description: "Comprehensive audit logging",
	},
	data_export: {
		featureName: "data_export",
		credits: 0,
		category: "free",
		description: "Data export capabilities",
	},
	custom_integrations: {
		featureName: "custom_integrations",
		credits: 0,
		category: "free",
		description: "Custom integration development",
	},
	white_label_options: {
		featureName: "white_label_options",
		credits: 0,
		category: "free",
		description: "White label customization options",
	},
	dedicated_support: {
		featureName: "dedicated_support",
		credits: 0,
		category: "free",
		description: "Dedicated customer support",
	},
	api_access: {
		featureName: "api_access",
		credits: 0,
		category: "free",
		description: "API access for integrations",
	},

	// 💡 BASIC AI FEATURES (1 Credit) - Essential AI-powered functionality
	basic_analysis: {
		featureName: "basic_analysis",
		credits: 1,
		category: "basic",
		description: "Basic document analysis and insights",
	},
	chat: {
		featureName: "chat",
		credits: 1,
		category: "basic",
		description: "AI chat responses about documents",
	},
	basic_comparison: {
		featureName: "basic_comparison",
		credits: 1,
		category: "basic",
		description: "Basic AI document comparison",
	},
	basic_citation_analysis: {
		featureName: "basic_citation_analysis",
		credits: 1,
		category: "basic",
		description: "Basic citation analysis with simple relationship mapping",
	},
	clause_identification: {
		featureName: "clause_identification",
		credits: 1,
		category: "basic",
		description: "AI clause identification in documents",
	},
	playbook_analysis: {
		featureName: "playbook_analysis",
		credits: 1,
		category: "basic",
		description: "Contract playbook AI analysis",
	},

	// 🚀 ADVANCED AI FEATURES (2 Credits) - Sophisticated AI analysis and automation
	advanced_analysis: {
		featureName: "advanced_analysis",
		credits: 2,
		category: "advanced",
		description: "Advanced document analysis with deep insights",
	},
	enhanced_comparison: {
		featureName: "enhanced_comparison",
		credits: 2,
		category: "advanced",
		description: "Advanced document comparison with detailed insights",
	},
	precedent_analysis: {
		featureName: "precedent_analysis",
		credits: 2,
		category: "advanced",
		description: "AI-powered precedent analysis",
	},
	template_generation: {
		featureName: "template_generation",
		credits: 2,
		category: "advanced",
		description: "AI-powered template generation",
	},
	deviation_detection: {
		featureName: "deviation_detection",
		credits: 2,
		category: "advanced",
		description: "AI-powered deviation detection from standard terms",
	},
	deposition_insights: {
		featureName: "deposition_insights",
		credits: 2,
		category: "advanced",
		description: "Advanced insights from deposition analysis",
	},
	advanced_citation_analysis: {
		featureName: "advanced_citation_analysis",
		credits: 2,
		category: "advanced",
		description: "Advanced citation analysis with complex relationship mapping",
	},
	compliance_audit: {
		featureName: "compliance_audit",
		credits: 2,
		category: "advanced",
		description: "AI-powered compliance auditing",
	},
	negotiation_playbook: {
		featureName: "negotiation_playbook",
		credits: 2,
		category: "advanced",
		description: "AI-powered negotiation strategy analysis",
	},
	system_analytics: {
		featureName: "system_analytics",
		credits: 2,
		category: "advanced",
		description: "AI-powered system-wide analytics and reporting",
	},
	trend_analysis: {
		featureName: "trend_analysis",
		credits: 2,
		category: "advanced",
		description: "AI-powered trend analysis and insights",
	},

	// 💎 PREMIUM AI FEATURES (3 Credits) - Most advanced AI-powered capabilities
	document_automation: {
		featureName: "document_automation",
		credits: 3,
		category: "premium",
		description: "Automated document processing and generation",
	},
	privilege_detection: {
		featureName: "privilege_detection",
		credits: 3,
		category: "premium",
		description: "AI-powered privilege detection and analysis",
	},
	redaction_automation: {
		featureName: "redaction_automation",
		credits: 3,
		category: "premium",
		description: "AI-powered redaction automation",
	},
	contract_risk_scoring: {
		featureName: "contract_risk_scoring",
		credits: 3,
		category: "premium",
		description: "AI-powered contract risk assessment",
	},
	deposition_analysis: {
		featureName: "deposition_analysis",
		credits: 3,
		category: "premium",
		description: "AI-powered deposition analysis",
	},
	ai_question_generation: {
		featureName: "ai_question_generation",
		credits: 3,
		category: "premium",
		description: "AI-generated questions for depositions",
	},
	litigation_support: {
		featureName: "litigation_support",
		credits: 3,
		category: "premium",
		description: "Comprehensive litigation support tools",
	},
	negotiation_simulator: {
		featureName: "negotiation_simulator",
		credits: 3,
		category: "premium",
		description: "AI-powered negotiation simulation",
	},
	negotiation_training: {
		featureName: "negotiation_training",
		credits: 3,
		category: "premium",
		description: "AI-powered negotiation training",
	},
	advanced_analytics: {
		featureName: "advanced_analytics",
		credits: 3,
		category: "premium",
		description: "AI-powered advanced analytics and insights",
	},
	predictive_analytics: {
		featureName: "predictive_analytics",
		credits: 3,
		category: "premium",
		description: "AI-powered predictive analytics and forecasting",
	},
	risk_forecasting: {
		featureName: "risk_forecasting",
		credits: 3,
		category: "premium",
		description: "AI-powered risk forecasting and prediction",
	},
	custom_ai_models: {
		featureName: "custom_ai_models",
		credits: 3,
		category: "premium",
		description: "Custom AI model training and deployment",
	},
};

// Credit packages for purchase - organized by tier
export const CREDIT_PACKAGES: CreditPackage[] = [
	// Student Credit Package
	{
		id: "student",
		name: "Student Credit Package",
		credits: 50,
		bonus: 0,
		price: 499, // 4.99 in cents
		total: 50,
		popular: false,
		targetTier: "law_student",
		description: "50 credits for law students",
	},
	// Lawyer Small Credit Package
	{
		id: "lawyer_small",
		name: "Lawyer Starter Pack",
		credits: 200,
		bonus: 20,
		price: 1999, // 19.99 in cents
		total: 220,
		popular: true,
		targetTier: "lawyer",
		description: "200 credits + 20 bonus for practicing attorneys",
	},
	// Lawyer Large Credit Package
	{
		id: "lawyer_large",
		name: "Lawyer Professional Pack",
		credits: 500,
		bonus: 75,
		price: 4499, // 44.99 in cents
		total: 575,
		popular: false,
		targetTier: "lawyer",
		description: "500 credits + 75 bonus for busy legal practices",
	},
	// Firm Standard Credit Package
	{
		id: "firm_standard",
		name: "Law Firm Standard Pack",
		credits: 1000,
		bonus: 200,
		price: 7999, // 79.99 in cents
		total: 1200,
		popular: false,
		targetTier: "law_firm",
		description: "1000 credits + 200 bonus for growing legal teams",
	},
	// Firm Enterprise Credit Package
	{
		id: "firm_enterprise",
		name: "Law Firm Enterprise Pack",
		credits: 5000,
		bonus: 1500,
		price: 34999, // 349.99 in cents
		total: 6500,
		popular: false,
		targetTier: "law_firm",
		description: "5000 credits + 1500 bonus for large legal organizations",
	},
];
