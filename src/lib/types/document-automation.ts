// Document Automation Types
export interface DraftingPrompt {
  prompt: string;
  keyTerms?: string[];
  requiredClauses?: string[];
  context?: string;
}

export interface AIAssistedDraftingRequest {
  documentType: DocumentType;
  draftingPrompt: DraftingPrompt;
  organizationPreferences?: string;
  useClauseLibrary?: boolean;
  includeDisclaimers?: boolean;
  jurisdiction?: string;
}

export interface AIAssistedDraftingResponse {
  content: string;
  documentType: string;
  metadata: {
    generatedAt: string;
    generationDurationMs: number;
    modelUsed: string;
    clausesUsed: number;
    organizationClausesUsed: number;
  };
  suggestedClauses: SuggestedClause[];
  recommendations: string[];
}

export interface SuggestedClause {
  type: string;
  content: string;
  source: 'ai_generated' | 'organization' | 'library';
  confidence: number;
  position?: 'beginning' | 'middle' | 'end';
  explanation?: string;
  relevanceScore?: number;
}

export interface RelatedDocumentsRequest {
  primaryDocumentId: string;
  documentTypes: RelatedDocumentType[];
  additionalContent?: string;
  autoPopulate?: boolean;
}

export interface RelatedDocumentsResponse {
  documents: GeneratedDocument[];
  metadata: {
    primaryDocumentId: string;
    generatedAt: string;
    generationDurationMs: number;
    documentsGenerated: number;
  };
}

export interface GeneratedDocument {
  type: string;
  title: string;
  content: string;
  metadata: {
    generatedFrom: string;
    autoPopulated: boolean;
    hasAdditionalContent: boolean;
  };
}

export interface ClauseIntelligenceRequest {
  documentType: string;
  currentContent: string;
  sectionType?: string;
  context?: string;
  includeOrgClauses?: boolean;
}

export interface ClauseIntelligenceResponse {
  suggestedClauses: SuggestedClause[];
  autoPopulationSuggestions: AutoPopulationSuggestion[];
  missingClauses: MissingClause[];
}

export interface AutoPopulationSuggestion {
  sectionName: string;
  suggestedContent: string;
  confidence: number;
}

export interface MissingClause {
  type: string;
  importance: 'critical' | 'important' | 'optional';
  description: string;
}

export interface ClauseLibraryBuildRequest {
  includeExistingClauses?: boolean;
  minimumConfidence?: number;
  maxClausesPerCategory?: number;
  documentTypes?: string[];
  analysisDepth?: 'basic' | 'detailed' | 'comprehensive';
}

export interface ClauseLibraryBuildResponse {
  extractedClauses: ExtractedClause[];
  totalDocumentsAnalyzed: number;
  totalClausesExtracted: number;
  categoriesFound: string[];
  processingDurationMs: number;
}

export interface ExtractedClause {
  content: string;
  category: string;
  confidence: number;
  sourceDocument: string;
  patternType: string;
}

export type DocumentType = 
  | 'contract'
  | 'nda'
  | 'msa'
  | 'sow'
  | 'amendment'
  | 'addendum'
  | 'schedule'
  | 'exhibit'
  | 'privacy_policy'
  | 'terms_of_service'
  | 'employment_agreement'
  | 'lease_agreement'
  | 'other';

export type RelatedDocumentType = 
  | 'schedule'
  | 'exhibit'
  | 'addendum'
  | 'amendment'
  | 'appendix';

export interface DocumentAutomationError {
  statusCode: number;
  message: string;
  error: string;
  details?: Array<{
    field: string;
    constraints: string[];
  }>;
  path?: string;
  timestamp?: string;
}
