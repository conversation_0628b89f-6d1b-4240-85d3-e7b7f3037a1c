// Compliance Auditor Types

export type ComplianceStatus = 'pending' | 'in_progress' | 'completed' | 'failed';
export type RiskLevel = 'low' | 'medium' | 'high' | 'critical';
export type FindingType = 'violation' | 'warning' | 'recommendation' | 'compliant';
export type Severity = 'low' | 'medium' | 'high' | 'critical';
export type Industry = 'Healthcare' | 'Financial' | 'Technology' | 'Legal' | 'Government' | 'Other';

export interface ComplianceAuditResult {
  id: string;
  documentId: string;
  userId: string;
  organizationId: string;
  profileId?: string;
  frameworks: string[];
  status: ComplianceStatus;
  overallScore: number;
  riskLevel: RiskLevel;
  findings: ComplianceFinding[];
  recommendations: ComplianceRecommendation[];
  frameworkResults: Record<string, FrameworkResult>;
  auditDate: Date;
  completedAt?: Date;
  metadata: {
    documentType: string;
    documentSize: number;
    processingTime: number;
    aiModel: string;
  };
}

export interface ComplianceFinding {
  id: string;
  type: FindingType;
  framework: string;
  rule: string;
  article?: string;
  section?: string;
  description: string;
  location: string;
  severity: Severity;
  confidence: number;
  suggestedFix?: string;
  relatedFindings?: string[];
}

export interface ComplianceRecommendation {
  priority: 'low' | 'medium' | 'high' | 'critical';
  category: string;
  description: string;
  suggestedText?: string;
}

export interface FrameworkResult {
  score: number;
  violations: number;
  warnings: number;
  compliant: number;
}

export interface ComplianceProfile {
  id: string;
  name: string;
  description: string;
  industry: Industry;
  organizationId: string;
  frameworks: FrameworkConfig[];
  riskThresholds: RiskThresholds;
  customRequirements: CustomRequirement[];
  isDefault: boolean;
  createdBy: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface FrameworkConfig {
  name: string;
  version: string;
  enabled: boolean;
  weight: number;
  customRules?: CustomRule[];
  excludedRules?: string[];
  applicableArticles?: string[];
  riskMultiplier?: number;
}

export interface CustomRule {
  id: string;
  description: string;
  pattern: string;
  severity: Severity;
  category: string;
  enabled: boolean;
  regex?: boolean;
  caseSensitive?: boolean;
}

export interface RiskThresholds {
  low: number;
  medium: number;
  high: number;
}

export interface CustomRequirement {
  category: string;
  requirement: string;
  mandatory: boolean;
}

export interface RegulatoryFramework {
  id: string;
  name: string;
  version: string;
  jurisdiction: string;
  categories: string[];
  articles?: FrameworkArticle[];
  rules?: FrameworkRule[];
}

export interface FrameworkArticle {
  number: string;
  title: string;
  description: string;
  requirements: string[];
}

export interface FrameworkRule {
  section: string;
  title: string;
  requirements: string[];
}

export interface ComplianceAnalytics {
  totalAudits: number;
  averageScore: number;
  complianceRate: number;
  riskDistribution: Record<RiskLevel, number>;
  frameworkBreakdown: Record<string, {
    audits: number;
    averageScore: number;
    commonViolations: string[];
  }>;
  trends: {
    monthlyScores: number[];
    improvementRate: number;
  };
  complianceByDocumentType?: Record<string, {
    averageScore: number;
    totalAudits: number;
    complianceRate: number;
  }>;
  recentActivity?: {
    auditsThisMonth: number;
  };
  topRiskAreas?: Array<{
    area: string;
    occurrences: number;
    averageSeverity: 'low' | 'medium' | 'high';
    trend: 'increasing' | 'decreasing' | 'stable';
  }>;
  recommendations?: Array<{
    priority: 'low' | 'medium' | 'high';
    category: string;
    description: string;
    impact: string;
  }>;
}

export interface AuditRequest {
  documentId: string;
  frameworks: string[];
  profileId?: string;
  options: {
    includeRecommendations: boolean;
    detailedAnalysis: boolean;
    riskThreshold: 'low' | 'medium' | 'high';
  };
}

export interface CreateProfileRequest {
  name: string;
  description: string;
  industry: Industry;
  frameworks: FrameworkConfig[];
  riskThresholds: RiskThresholds;
  customRequirements: CustomRequirement[];
}
