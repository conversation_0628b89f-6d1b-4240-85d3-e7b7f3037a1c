export interface ClauseTemplate {
  id: string;
  name: string;
  content: string;
  category: string;
  tags: string[];
  isPublic: boolean;
  createdAt: string;
  updatedAt: string;
  usageCount?: number;
  effectivenessScore?: number;
}

export interface ClauseMatch {
  name: string;
  content: string;
  category: string;
  similarity: number;
  templateId: string;
  startIndex: number;
  endIndex: number;
}

export interface GenerateTemplateRequest {
  documentContent: string;
  name: string;
  category: string;
  tags: string[];
  isPublic?: boolean;
}