// Privilege Log Automation Types

export enum PrivilegeType {
  ATTORNEY_CLIENT = 'attorney_client',
  WORK_PRODUCT = 'work_product',
  CONFIDENTIAL_COMMUNICATION = 'confidential_communication',
  TRADE_SECRET = 'trade_secret',
  MEDICAL_PRIVILEGE = 'medical_privilege',
  SPOUSAL_PRIVILEGE = 'spousal_privilege',
  OTHER = 'other'
}

export enum PrivilegeStatus {
  DETECTED = 'detected',
  UNDER_REVIEW = 'under_review',
  CONFIRMED = 'confirmed',
  REJECTED = 'rejected',
  REDACTED = 'redacted'
}

export enum PrivilegeLogStatus {
  PENDING = 'pending',
  IN_PROGRESS = 'in_progress',
  COMPLETED = 'completed',
  FAILED = 'failed'
}

export type DetectionMethod = 'pattern' | 'ai' | 'manual';

export interface PrivilegedContent {
  id: string;
  documentId: string;
  startPosition: number;
  endPosition: number;
  content: string;
  privilegeType: PrivilegeType;
  confidenceScore: number; // 0.0 to 1.0
  detectionMethod: DetectionMethod;
  status: PrivilegeStatus;
  reviewedBy?: string;
  reviewedAt?: Date;
  redactionApplied: boolean;
  redactionReason?: string;
}

export interface RedactionSuggestion {
  contentId: string;
  originalText: string;
  suggestedRedaction: string;
  reason: string;
  privilegeType: PrivilegeType;
  confidenceScore: number;
  requiresReview: boolean;
}

export interface AnalysisSummary {
  totalItemsFound: number;
  highConfidenceItems: number;
  requiresManualReview: number;
  autoRedactable: number;
}

export interface AnalysisMetadata {
  analysisDate: Date;
  detectionMethods: DetectionMethod[];
  aiModelUsed?: string;
  processingTime: number;
}

export interface PrivilegeAnalysisResult {
  documentId: string;
  privilegedContent: PrivilegedContent[];
  redactionSuggestions: RedactionSuggestion[];
  summary: AnalysisSummary;
  analysisMetadata: AnalysisMetadata;
}

export interface PrivilegeLogEntry {
  id: string;
  documentId: string;
  documentTitle: string;
  privilegedContent: PrivilegedContent[];
  totalPrivilegedItems: number;
  totalRedactions: number;
  analysisDate: Date;
  lastReviewDate?: Date;
  status: PrivilegeLogStatus;
  reviewedBy?: string;
  organizationId: string;
  createdBy: string;
}

export interface AnalysisOptions {
  includeAIAnalysis?: boolean; // Default: true
  confidenceThreshold?: number; // 0.0-1.0, Default: 0.7
  privilegeTypes?: PrivilegeType[]; // Optional filter
  autoRedact?: boolean; // Default: false
  requireManualReview?: boolean; // Default: true
}

export interface ReviewPrivilegeRequest {
  status: PrivilegeStatus;
  reason?: string;
  applyRedaction?: boolean;
}

export interface ApplyRedactionRequest {
  contentId: string;
  redactionText?: string;
  reason: string;
}

export interface BulkRedactionRequest {
  contentIds: string[];
  redactionText?: string;
  reason: string;
}

export interface PrivilegeLogApiResponse {
  success: boolean;
  data: PrivilegeAnalysisResult | PrivilegeLogEntry;
}

export interface PrivilegeLogListResponse {
  success: boolean;
  data: PrivilegeLogEntry[];
  pagination: {
    total: number;
    page: number;
    limit: number;
    pages: number;
  };
}

export interface PrivilegeLogError {
  success: false;
  error: {
    code: string;
    message: string;
    details?: unknown;
  };
}

// Common privilege type options for UI
export const PRIVILEGE_TYPE_OPTIONS: { value: PrivilegeType; label: string }[] = [
  { value: PrivilegeType.ATTORNEY_CLIENT, label: 'Attorney-Client Privilege' },
  { value: PrivilegeType.WORK_PRODUCT, label: 'Work Product' },
  { value: PrivilegeType.CONFIDENTIAL_COMMUNICATION, label: 'Confidential Communication' },
  { value: PrivilegeType.TRADE_SECRET, label: 'Trade Secret' },
  { value: PrivilegeType.MEDICAL_PRIVILEGE, label: 'Medical Privilege' },
  { value: PrivilegeType.SPOUSAL_PRIVILEGE, label: 'Spousal Privilege' },
  { value: PrivilegeType.OTHER, label: 'Other' }
];

// Status display configurations
export const PRIVILEGE_STATUS_CONFIG = {
  [PrivilegeStatus.DETECTED]: {
    label: 'Detected',
    color: 'bg-yellow-100 text-yellow-800 border-yellow-200',
    icon: '🔍'
  },
  [PrivilegeStatus.UNDER_REVIEW]: {
    label: 'Under Review',
    color: 'bg-blue-100 text-blue-800 border-blue-200',
    icon: '👁️'
  },
  [PrivilegeStatus.CONFIRMED]: {
    label: 'Confirmed',
    color: 'bg-green-100 text-green-800 border-green-200',
    icon: '✅'
  },
  [PrivilegeStatus.REJECTED]: {
    label: 'Rejected',
    color: 'bg-red-100 text-red-800 border-red-200',
    icon: '❌'
  },
  [PrivilegeStatus.REDACTED]: {
    label: 'Redacted',
    color: 'bg-gray-100 text-gray-800 border-gray-200',
    icon: '🔒'
  }
};

// Confidence score helpers
export const getConfidenceColor = (score: number): string => {
  if (score >= 0.9) return 'text-green-600';
  if (score >= 0.7) return 'text-yellow-600';
  return 'text-red-600';
};

export const getConfidenceLabel = (score: number): string => {
  if (score >= 0.9) return 'High';
  if (score >= 0.7) return 'Medium';
  return 'Low';
};
