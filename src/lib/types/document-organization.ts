// Document Organization Types

// Tag Types
export interface Tag {
  id: string
  name: string
  color: string
  description?: string
  organizationId: string
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface CreateTagDto {
  name: string
  color: string
  description?: string
}

export interface UpdateTagDto {
  name?: string
  color?: string
  description?: string
}

// Folder Types
export interface Folder {
  id: string
  name: string
  description?: string
  path: string
  parentId: string | null
  documents: string[]
  organizationId: string
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface CreateFolderDto {
  name: string
  description?: string
  parentId?: string | null
}

export interface UpdateFolderDto {
  name?: string
  description?: string
  parentId?: string | null
}

// Document in Folder Type
export interface FolderDocument {
  id: string
  folderId: string
  folderName: string
  filename: string
  type: string
  extension: string
  size: number
  uploadDate: string
  title: string
}

// Saved Search Types
export interface SearchCriteria {
  text?: string
  documentType?: string
  dateFrom?: string
  dateTo?: string
  metadata?: Record<string, string>
}

export interface SavedSearch {
  id: string
  name: string
  description?: string
  criteria: SearchCriteria
  notificationsEnabled: boolean
  notificationFrequency?: "daily" | "weekly" | "monthly"
  sharedWith?: string[]
  organizationId: string
  createdBy: string
  createdAt: string
  updatedAt: string
}

export interface CreateSavedSearchDto {
  name: string
  description?: string
  criteria: SearchCriteria
  notificationsEnabled?: boolean
  notificationFrequency?: "daily" | "weekly" | "monthly"
}

export interface UpdateSavedSearchDto {
  name?: string
  description?: string
  criteria?: SearchCriteria
  notificationsEnabled?: boolean
  notificationFrequency?: "daily" | "weekly" | "monthly"
}

export interface SearchResult {
  id: string
  title: string
  documentType: string
  createdAt: string
}

export interface ExecuteSearchResult {
  query: SearchCriteria
  results: SearchResult[]
}

export interface ShareSearchDto {
  userIds: string[]
}
