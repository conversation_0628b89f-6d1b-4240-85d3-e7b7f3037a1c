import type { SampleNegotiationPlaybook } from "@/lib/types/sample-playbooks";

export const sampleNegotiationPlaybooks: SampleNegotiationPlaybook[] = [
  {
    documentId: "template-nda-beginner",
    templateName: "Non-Disclosure Agreement - Essentials",
    templateDescription: "Key negotiation points for standard NDAs",
    contractType: "NDA",
    industry: "general",
    difficulty: "beginner",
    isTemplate: true,
    tags: ["nda", "confidentiality", "basic"],
    strategies: [
      {
        section: "Definition of Confidential Information",
        recommendations: [
          "Ensure mutual confidentiality (both parties protected)",
          "Exclude publicly available information from definition",
          "Include specific carve-outs for independently developed information"
        ],
        riskLevel: "medium",
        priority: 1,
        alternativeLanguage: "Confidential Information shall not include information that: (a) is publicly available, (b) was known prior to disclosure, or (c) is independently developed.",
        simulationScenarios: [
          {
            type: "concession",
            trigger: "Other party wants one-way NDA only",
            responseStrategy: "Explain mutual benefit and propose reciprocal terms",
            expectedOutcome: "Agreement on mutual NDA with balanced protections"
          }
        ]
      },
      {
        section: "Term and Duration",
        recommendations: [
          "Limit confidentiality period to reasonable timeframe (3-5 years)",
          "Include return/destruction of materials clause",
          "Specify survival of obligations post-termination"
        ],
        riskLevel: "low",
        priority: 2,
        alternativeLanguage: "Confidentiality obligations shall survive for three (3) years from disclosure date or termination of this Agreement, whichever is later.",
        simulationScenarios: [
          {
            type: "compromise",
            trigger: "Other party requests perpetual confidentiality",
            responseStrategy: "Propose longer but finite term (5-7 years) for truly sensitive information",
            expectedOutcome: "Tiered confidentiality periods based on information sensitivity"
          }
        ]
      }
    ],
    overallAssessment: "NDAs should provide balanced protection while allowing normal business operations. Focus on mutual terms and reasonable time limits.",
    keyLeveragePoints: [
      "Mutual need for confidentiality protection",
      "Standard industry practices",
      "Reciprocal business relationship"
    ],
    dealBreakers: [
      "Perpetual confidentiality obligations",
      "Overly broad definition of confidential information",
      "One-way protection favoring other party"
    ],
    timestamp: "2025-06-04T10:15:53.136Z",
    usageCount: 0
  },
  {
    documentId: "template-service-agreement-beginner",
    templateName: "Service Agreement - Professional Services",
    templateDescription: "Essential compliance rules for professional service agreements",
    contractType: "SERVICE_AGREEMENT",
    industry: "professional_services",
    difficulty: "beginner",
    isTemplate: true,
    tags: ["service-agreement", "consulting", "professional"],
    strategies: [
      {
        section: "Payment Terms",
        recommendations: [
          "Negotiate favorable payment schedules",
          "Include late payment penalties",
          "Define clear invoicing procedures"
        ],
        riskLevel: "medium",
        priority: 1,
        alternativeLanguage: "Payment shall be due within thirty (30) days of invoice date. Late payments shall incur interest at 1.5% per month.",
        simulationScenarios: [
          {
            type: "negotiation",
            trigger: "Client requests extended payment terms",
            responseStrategy: "Counter with early payment discounts",
            expectedOutcome: "Balanced payment terms"
          }
        ]
      },
      {
        section: "Scope of Work",
        recommendations: [
          "Define clear deliverables and timelines",
          "Include change order procedures",
          "Specify acceptance criteria"
        ],
        riskLevel: "high",
        priority: 2,
        alternativeLanguage: "Services shall be performed in accordance with the Statement of Work attached hereto and incorporated by reference.",
        simulationScenarios: [
          {
            type: "boundary_setting",
            trigger: "Scope creep during project execution",
            responseStrategy: "Reference change order procedures",
            expectedOutcome: "Controlled scope management"
          }
        ]
      }
    ],
    overallAssessment: "Focus on balanced risk allocation and clear performance expectations",
    keyLeveragePoints: [
      "Service quality guarantees",
      "Intellectual property rights"
    ],
    dealBreakers: [
      "Unlimited liability",
      "Unreasonable termination clauses"
    ],
    timestamp: "2025-06-04T10:16:53.136Z",
    usageCount: 0
  },
  {
    documentId: "template-employment-contract-intermediate",
    templateName: "Employment Contract - Compliance",
    templateDescription: "Compliance rules for employment contracts",
    contractType: "EMPLOYMENT_CONTRACT",
    industry: "general",
    difficulty: "intermediate",
    isTemplate: true,
    tags: ["employment", "termination", "compliance"],
    strategies: [
      {
        section: "Intellectual Property",
        recommendations: [
          "Retain ownership of pre-existing IP",
          "Negotiate work-for-hire vs. licensing terms",
          "Define scope of work product clearly"
        ],
        riskLevel: "high",
        priority: 1,
        alternativeLanguage: "Background IP remains with consultant. Work product created specifically for Client shall be owned by Client, subject to Consultant's retained rights in pre-existing materials.",
        simulationScenarios: [
          {
            type: "negotiation",
            trigger: "Client demands all IP ownership",
            responseStrategy: "Propose licensing with retained background IP",
            expectedOutcome: "Balanced IP allocation"
          }
        ]
      },
      {
        section: "Liability and Indemnification",
        recommendations: [
          "Cap liability at contract value",
          "Include mutual indemnification clauses",
          "Exclude consequential damages"
        ],
        riskLevel: "high",
        priority: 2,
        alternativeLanguage: "Total liability shall not exceed the total fees paid under this Agreement. Each party shall indemnify the other for third-party claims arising from their negligent acts.",
        simulationScenarios: [
          {
            type: "risk_management",
            trigger: "Unlimited liability exposure requested",
            responseStrategy: "Counter with reasonable liability caps",
            expectedOutcome: "Limited liability exposure"
          }
        ]
      }
    ],
    overallAssessment: "Balance IP protection with client needs while managing liability exposure",
    keyLeveragePoints: [
      "Specialized expertise",
      "Market rate benchmarks"
    ],
    dealBreakers: [
      "Unlimited liability",
      "Loss of all IP rights"
    ],
    timestamp: "2025-06-04T10:17:53.136Z",
    usageCount: 0
  }
];

// Helper functions for filtering and searching
export function filterSampleNegotiationPlaybooks(
  playbooks: SampleNegotiationPlaybook[],
  filters: {
    contractType?: string;
    industry?: string;
    difficulty?: string;
    tags?: string;
  }
): SampleNegotiationPlaybook[] {
  return playbooks.filter(playbook => {
    if (filters.contractType && playbook.contractType !== filters.contractType) {
      return false;
    }
    if (filters.industry && playbook.industry !== filters.industry) {
      return false;
    }
    if (filters.difficulty && playbook.difficulty !== filters.difficulty) {
      return false;
    }
    if (filters.tags) {
      const searchTags = filters.tags.toLowerCase().split(',').map(tag => tag.trim());
      const playbookTags = playbook.tags.map(tag => tag.toLowerCase());
      if (!searchTags.some(tag => playbookTags.includes(tag))) {
        return false;
      }
    }
    return true;
  });
}

export function getSampleNegotiationPlaybookStats(playbooks: SampleNegotiationPlaybook[]) {
  const contractTypeDistribution = playbooks.reduce((acc, playbook) => {
    const existing = acc.find(item => item.contractType === playbook.contractType);
    if (existing) {
      existing.count++;
    } else {
      acc.push({ contractType: playbook.contractType, count: 1 });
    }
    return acc;
  }, [] as Array<{ contractType: string; count: number }>);

  const difficultyDistribution = playbooks.reduce((acc, playbook) => {
    const existing = acc.find(item => item.difficulty === playbook.difficulty);
    if (existing) {
      existing.count++;
    } else {
      acc.push({ difficulty: playbook.difficulty, count: 1 });
    }
    return acc;
  }, [] as Array<{ difficulty: string; count: number }>);

  const industryDistribution = playbooks.reduce((acc, playbook) => {
    if (playbook.industry) {
      const existing = acc.find(item => item.industry === playbook.industry);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ industry: playbook.industry, count: 1 });
      }
    }
    return acc;
  }, [] as Array<{ industry: string; count: number }>);

  return {
    totalSamples: playbooks.length,
    contractTypeDistribution,
    difficultyDistribution,
    industryDistribution
  };
}
