import type { SampleContractPlaybook } from "@/lib/types/sample-playbooks";

export const sampleContractPlaybooks: SampleContractPlaybook[] = [
  {
    id: "sample-nda-playbook",
    name: "Standard NDA Compliance Playbook",
    contractType: "NDA",
    description: "Comprehensive compliance rules for Non-Disclosure Agreements",
    industry: "GENERAL",
    riskProfile: "Medium",
    tags: ["confidentiality", "mutual", "standard"],
    rules: [
      {
        id: "nda-mutual-confidentiality",
        name: "Mutual Confidentiality Requirement",
        category: "Confidentiality",
        ruleType: "required_clause",
        severity: "HIGH",
        criteria: {
          keywords: ["mutual", "confidentiality", "both parties"],
          patterns: ["mutual.*confidential", "both.*parties.*confidential"],
          semanticConcepts: ["mutual confidentiality", "reciprocal protection"],
          contextRequirements: ["confidentiality section"]
        },
        acceptableLanguage: {
          preferred: ["Both parties acknowledge that confidential information may be disclosed"],
          acceptable: ["Each party may disclose confidential information"],
          fallbackPositions: ["Confidential information disclosed by either party"]
        },
        unacceptableTerms: {
          prohibited: ["one-way confidentiality", "unilateral confidentiality"],
          requiresEscalation: ["perpetual confidentiality"],
          autoReject: ["no confidentiality obligations"]
        },
        negotiationGuidance: {
          strategy: "Insist on mutual confidentiality",
          alternatives: ["Propose separate mutual NDA"],
          businessImpact: "One-way NDAs create unbalanced risk exposure"
        }
      },
      {
        id: "nda-term-limitation",
        name: "Confidentiality Term Limitation",
        category: "Duration",
        ruleType: "required_clause",
        severity: "MEDIUM",
        criteria: {
          keywords: ["term", "duration", "period", "years"],
          patterns: ["\\d+\\s*years?", "term.*\\d+"],
          semanticConcepts: ["time limitation", "duration clause"],
          contextRequirements: ["term section", "duration clause"]
        },
        acceptableLanguage: {
          preferred: ["3 years from disclosure", "5 years from the date"],
          acceptable: ["reasonable period", "commercially reasonable term"],
          fallbackPositions: ["industry standard duration"]
        },
        unacceptableTerms: {
          prohibited: ["perpetual", "indefinite", "forever"],
          requiresEscalation: ["more than 10 years"],
          autoReject: ["unlimited duration"]
        },
        negotiationGuidance: {
          strategy: "Negotiate 3-5 year terms as industry standard",
          alternatives: ["Different terms for different information types"],
          businessImpact: "Indefinite terms create ongoing compliance burden"
        }
      },
      {
        id: "nda-definition-scope",
        name: "Clear Definition of Confidential Information",
        category: "Definitions",
        ruleType: "required_clause",
        severity: "HIGH",
        criteria: {
          keywords: ["confidential information", "proprietary", "definition"],
          patterns: ["confidential.*means", "includes.*but.*not.*limited"],
          semanticConcepts: ["information definition", "scope of confidentiality"],
          contextRequirements: ["definitions section"]
        },
        acceptableLanguage: {
          preferred: ["Clearly defined categories of confidential information"],
          acceptable: ["Reasonable scope with specific examples"],
          fallbackPositions: ["Industry standard definitions"]
        },
        unacceptableTerms: {
          prohibited: ["all information", "any information"],
          requiresEscalation: ["overly broad definitions"],
          autoReject: ["undefined scope"]
        },
        negotiationGuidance: {
          strategy: "Ensure specific, reasonable scope",
          alternatives: ["Separate categories for different information types"],
          businessImpact: "Overly broad definitions create compliance challenges"
        }
      }
    ],
    metadata: {
      version: "1.0",
      lastUpdated: "2024-01-15",
      author: "Legal Compliance Team",
      totalRules: 3,
      riskDistribution: {
        "LOW": 0,
        "MEDIUM": 1,
        "HIGH": 2,
        "CRITICAL": 0
      }
    }
  },
  {
    id: "sample-service-agreement-playbook",
    name: "Service Agreement Compliance Playbook",
    contractType: "SERVICE_AGREEMENT",
    description: "Liability caps and risk management for service agreements",
    industry: "GENERAL",
    riskProfile: "Medium",
    tags: ["services", "liability", "risk-management"],
    rules: [
      {
        id: "service-liability-cap",
        name: "Liability Limitation Clause",
        category: "Liability",
        ruleType: "required_clause",
        severity: "CRITICAL",
        criteria: {
          keywords: ["liability", "limited", "cap", "maximum"],
          patterns: ["liability.*limited.*to", "maximum.*liability"],
          semanticConcepts: ["liability limitation", "damage caps"],
          contextRequirements: ["liability section"]
        },
        acceptableLanguage: {
          preferred: ["Liability limited to fees paid in preceding 12 months"],
          acceptable: ["Liability capped at contract value"],
          fallbackPositions: ["Reasonable liability limitations"]
        },
        unacceptableTerms: {
          prohibited: ["unlimited liability", "no liability cap"],
          requiresEscalation: ["liability exceeding contract value"],
          autoReject: ["unlimited damages"]
        },
        negotiationGuidance: {
          strategy: "Insist on liability caps equal to fees paid",
          alternatives: ["Mutual liability limitations"],
          businessImpact: "Unlimited liability creates unacceptable business risk"
        }
      },
      {
        id: "service-termination-rights",
        name: "Balanced Termination Rights",
        category: "Termination",
        ruleType: "required_clause",
        severity: "HIGH",
        criteria: {
          keywords: ["termination", "terminate", "notice", "convenience"],
          patterns: ["terminate.*convenience", "\\d+.*days.*notice"],
          semanticConcepts: ["termination rights", "notice periods"],
          contextRequirements: ["termination section"]
        },
        acceptableLanguage: {
          preferred: ["Either party may terminate with 30 days notice"],
          acceptable: ["Mutual termination rights"],
          fallbackPositions: ["Reasonable notice periods"]
        },
        unacceptableTerms: {
          prohibited: ["immediate termination", "no notice required"],
          requiresEscalation: ["unilateral termination rights"],
          autoReject: ["termination without cause by client only"]
        },
        negotiationGuidance: {
          strategy: "Ensure mutual termination rights with adequate notice",
          alternatives: ["Different notice periods for different circumstances"],
          businessImpact: "Unbalanced termination rights create business uncertainty"
        }
      }
    ],
    metadata: {
      version: "1.0",
      lastUpdated: "2024-01-15",
      author: "Legal Compliance Team",
      totalRules: 2,
      riskDistribution: {
        "LOW": 0,
        "MEDIUM": 0,
        "HIGH": 1,
        "CRITICAL": 1
      }
    }
  },
  {
    id: "sample-employment-contract-playbook",
    name: "Employment Contract Compliance Playbook",
    contractType: "EMPLOYMENT_CONTRACT",
    description: "Termination rights and labor law compliance",
    industry: "GENERAL",
    riskProfile: "High",
    tags: ["employment", "termination", "labor-law"],
    rules: [
      {
        id: "employment-at-will",
        name: "At-Will Employment Clause",
        category: "Employment Terms",
        ruleType: "required_clause",
        severity: "HIGH",
        criteria: {
          keywords: ["at-will", "employment", "terminate", "any time"],
          patterns: ["at.?will.*employment", "terminate.*any.*time"],
          semanticConcepts: ["at-will employment", "termination flexibility"],
          contextRequirements: ["employment terms section"]
        },
        acceptableLanguage: {
          preferred: ["Employment is at-will and may be terminated by either party"],
          acceptable: ["Either party may terminate employment at any time"],
          fallbackPositions: ["Standard at-will employment terms"]
        },
        unacceptableTerms: {
          prohibited: ["guaranteed employment", "employment for specific term"],
          requiresEscalation: ["termination only for cause"],
          autoReject: ["permanent employment"]
        },
        negotiationGuidance: {
          strategy: "Maintain at-will employment flexibility",
          alternatives: ["Severance provisions for certain terminations"],
          businessImpact: "Non at-will employment creates termination complications"
        }
      }
    ],
    metadata: {
      version: "1.0",
      lastUpdated: "2024-01-15",
      author: "Legal Compliance Team",
      totalRules: 1,
      riskDistribution: {
        "LOW": 0,
        "MEDIUM": 0,
        "HIGH": 1,
        "CRITICAL": 0
      }
    }
  }
];

// Helper functions for filtering and searching
export function filterSampleContractPlaybooks(
  playbooks: SampleContractPlaybook[],
  filters: {
    contractType?: string;
    industry?: string;
    riskProfile?: string;
    tags?: string;
  }
): SampleContractPlaybook[] {
  return playbooks.filter(playbook => {
    if (filters.contractType && playbook.contractType !== filters.contractType) {
      return false;
    }
    if (filters.industry && playbook.industry !== filters.industry) {
      return false;
    }
    if (filters.riskProfile && playbook.riskProfile !== filters.riskProfile) {
      return false;
    }
    if (filters.tags) {
      const searchTags = filters.tags.toLowerCase().split(',').map(tag => tag.trim());
      const playbookTags = playbook.tags?.map(tag => tag.toLowerCase()) || [];
      if (!searchTags.some(tag => playbookTags.includes(tag))) {
        return false;
      }
    }
    return true;
  });
}

export function getSampleContractPlaybookStats(playbooks: SampleContractPlaybook[]) {
  const contractTypeDistribution = playbooks.reduce((acc, playbook) => {
    const existing = acc.find(item => item.contractType === playbook.contractType);
    if (existing) {
      existing.count++;
    } else {
      acc.push({ contractType: playbook.contractType, count: 1 });
    }
    return acc;
  }, [] as Array<{ contractType: string; count: number }>);

  const riskProfileDistribution = playbooks.reduce((acc, playbook) => {
    if (playbook.riskProfile) {
      const existing = acc.find(item => item.riskProfile === playbook.riskProfile);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ riskProfile: playbook.riskProfile, count: 1 });
      }
    }
    return acc;
  }, [] as Array<{ riskProfile: string; count: number }>);

  const industryDistribution = playbooks.reduce((acc, playbook) => {
    if (playbook.industry) {
      const existing = acc.find(item => item.industry === playbook.industry);
      if (existing) {
        existing.count++;
      } else {
        acc.push({ industry: playbook.industry, count: 1 });
      }
    }
    return acc;
  }, [] as Array<{ industry: string; count: number }>);

  return {
    totalSamples: playbooks.length,
    contractTypeDistribution,
    riskProfileDistribution,
    industryDistribution
  };
}

export const availableContractTypes = [
  "NDA",
  "SERVICE_AGREEMENT", 
  "EMPLOYMENT_CONTRACT",
  "LICENSING_AGREEMENT",
  "PURCHASE_AGREEMENT"
];
