"use client";

import {
  createContext,
  useContext,
  ReactNode
} from "react";
import { useRouter } from "next/navigation";
import {
  useLogin,
  useReg<PERSON>,
  useMF<PERSON>tart,
  useMF<PERSON>alidate,
  useUser,
  useProfile,
  clearAuthData,
  AuthError,
} from "./auth-service";
import { AUTH_CONFIG, COOKIE_OPTIONS } from "@/lib/config";
import { setCookie } from "cookies-next";

export interface AuthUser {
  id: string;
  email: string;
  roles: string[];
  organizationId: string;
  mfaEnabled: boolean;
  subscription?: {
    tier: string;
  };
}

export interface AuthContextType {
  user: AuthUser | null;
  login: (email: string, password: string, mfaToken?: string) => Promise<void>;
  register: (data: { 
    firstName: string;
    lastName: string;
    email: string;
    password: string;
  }) => Promise<void>;
  logout: () => void;
  startMFA: (email: string) => Promise<{ tempToken: string; methods: Array<"app" | "sms"> }>;
  validateMFA: (data: { tempToken: string; code: string; method: "app" | "sms" }) => Promise<void>;
  refreshProfile: () => Promise<void>;
  isLoading: boolean;
  isAuthenticated: boolean;
  loginWithToken: (token: string) => Promise<void>;
}

export const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: ReactNode }) {
  const router = useRouter();
  const { data: user, isLoading: isLoadingUser, refetch: refetchUser } = useUser();
  const profileQuery = useProfile();
  const loginMutation = useLogin();
  const registerMutation = useRegister();
  const mfaStartMutation = useMFAStart();
  const mfaValidateMutation = useMFAValidate();

  const login = async (email: string, password: string, mfaToken?: string) => {
    try {
      await loginMutation.mutateAsync({ email, password, mfaToken });
      // After successful login, redirect to chat page
      router.push(AUTH_CONFIG.routes.chat);
    } catch (err) {
      if (err instanceof AuthError) {
        if (err.code === "MFA_REQUIRED") {
          // Let the component handle MFA flow
          throw err;
        }
        if (err.code === "TOKEN_EXPIRED") {
          // Token expiration is already handled by auth-service
          throw new Error("Your session has expired. Please log in again.");
        }
      }
      throw new Error("Login failed. Please check your credentials.");
    }
  };

  const register = async (data: { 
    firstName: string;
    lastName: string;
    email: string;
    password: string;
  }) => {
    try {
      await registerMutation.mutateAsync(data);
      // After successful registration, redirect to chat page
      router.push(AUTH_CONFIG.routes.chat);
    } catch (err) {
      if (err instanceof AuthError) {
        if (err.code === "EMAIL_EXISTS") {
          throw new Error("This email is already registered");
        }
        throw new Error(err.message);
      }
      throw new Error("Registration failed. Please try again.");
    }
  };

  const logout = () => {
    clearAuthData();
    router.push(AUTH_CONFIG.routes.login);
  };

  const startMFA = async (email: string) => {
    try {
      const result = await mfaStartMutation.mutateAsync(email);
      return result;
    } catch (err) {
      if (err instanceof AuthError) {
        throw new Error(err.message);
      }
      throw new Error("Failed to start MFA process");
    }
  };

  const refreshProfile = async () => {
    try {
      await profileQuery.refetch();
      await refetchUser();
    } catch (err) {
      if (err instanceof AuthError && err.code === "TOKEN_EXPIRED") {
        // Let the auth-service handle the redirect
        throw err;
      }
      throw err;
    }
  };

  const validateMFA = async (data: { tempToken: string; code: string; method: "app" | "sms" }) => {
    try {
      await mfaValidateMutation.mutateAsync(data);
      router.push(AUTH_CONFIG.routes.chat);
    } catch (err) {
      if (err instanceof AuthError) {
        throw new Error(err.message);
      }
      throw new Error("An unexpected error occurred");
    }
  };

  const loginWithToken = async (token: string) => {
    setCookie(AUTH_CONFIG.accessTokenKey, token, COOKIE_OPTIONS);
    await refreshProfile();
    router.push(AUTH_CONFIG.routes.dashboard);
  };

  const value: AuthContextType = {
    user: user || null,
    login,
    register,
    logout,
    startMFA,
    validateMFA,
    refreshProfile,
    isLoading: isLoadingUser,
    isAuthenticated: !!user,
    loginWithToken,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error("useAuth must be used within an AuthProvider");
  }
  return context;
}
