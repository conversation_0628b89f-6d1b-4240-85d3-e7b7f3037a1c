/**
 * Gamification API Client
 * Handles all gamification-related API calls
 */

import { apiClient } from './client';

// Types
export interface UserGamificationProfile {
  userId: string;
  organizationId: string;
  level: {
    current: number;
    title: string;
    currentXP: number;
    totalXP: number;
    xpToNext: number;
    progress: number;
  };
  achievements: UserAchievement[];
  statistics: {
    totalSessions: number;
    completedSessions: number;
    averageScore: number;
    winRate: number;
    currentStreak: number;
    bestStreak: number;
    achievementsCount: number;
  };
  unlockedContent: {
    characters: string[];
    scenarios: string[];
    features: string[];
  };
}

export interface UserAchievement {
  achievementId: string;
  unlockedAt: Date;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  badge: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  category: string;
  requirements: AchievementRequirement;
  rewards: AchievementReward;
  isActive: boolean;
}

export interface AchievementRequirement {
  type: 'session_completion' | 'performance_streak' | 'total_sessions' | 'relationship_milestone';
  conditions: Record<string, any>;
}

export interface AchievementReward {
  xp: number;
  credits?: number;
  unlocks?: string[];
}

export interface AICharacter {
  id: string;
  name: string;
  title: string;
  company: string;
  avatar: string;
  difficulty: number;
  specialties: string[];
  backstory: string;
  personality: AIPersonalityProfile;
  unlockRequirements?: UnlockRequirement;
  isActive: boolean;
}

export interface AIPersonalityProfile {
  aggressiveness: number;
  flexibility: number;
  riskTolerance: number;
  communicationStyle: string;
  decisionSpeed: string;
  concessionPattern: string;
}

export interface UnlockRequirement {
  level?: number;
  achievements?: string[];
  sessionsCompleted?: number;
}

export interface CharacterRelationship {
  userId: string;
  characterId: string;
  respectLevel: number;
  trustLevel: number;
  relationshipStatus: string;
  totalInteractions: number;
  bonuses: RelationshipBonuses;
  lastInteraction?: Date;
}

export interface RelationshipBonuses {
  betterStartingTerms: boolean;
  increasedFlexibility: number;
  insiderInformation: boolean;
  relationshipDiscount?: number;
}

export interface LeaderboardEntry {
  rank: number;
  userId: string;
  name: string;
  title: string;
  organization: string;
  score: number;
  totalDeals: number;
  winRate: number;
  averageRounds: number;
  totalXP: number;
  avatar?: string;
}

export interface LeaderboardResponse {
  rankings: LeaderboardEntry[];
  userRank?: number;
  totalParticipants: number;
  timeframe: string;
  scope: string;
  lastUpdated: Date;
}

export interface PressureEvent {
  id: string;
  type: 'stakeholder' | 'market' | 'time' | 'competitor';
  title: string;
  message: string;
  intensity: 'low' | 'medium' | 'high';
  impact: PressureEventImpact;
  timestamp: Date;
}

export interface PressureEventImpact {
  userStress: number;
  timeMultiplier: number;
  leverageChange: number;
  aiAggressiveness?: number;
}

export interface LevelUpdate {
  previousLevel: number;
  newLevel: number;
  xpGained: number;
  totalXP: number;
  leveledUp: boolean;
  newTitle: string;
  newUnlocks: string[];
}

// API Client Class
export class GamificationAPI {
  
  // User Profile Management
  async getUserProfile(userId: string): Promise<UserGamificationProfile> {
    const response = await apiClient.get(`/api/gamification/users/${userId}/profile`);
    return response.data;
  }

  async updateUserProfile(userId: string, updates: Partial<UserGamificationProfile>): Promise<UserGamificationProfile> {
    const response = await apiClient.put(`/api/gamification/users/${userId}/profile`, updates);
    return response.data;
  }

  // Experience and Leveling
  async awardExperience(userId: string, amount: number, source: string, metadata?: any): Promise<LevelUpdate> {
    const response = await apiClient.post(`/api/gamification/users/${userId}/experience`, {
      amount,
      source,
      metadata
    });
    return response.data;
  }

  async getLevelRequirements(): Promise<any[]> {
    const response = await apiClient.get('/api/gamification/level-requirements');
    return response.data;
  }

  // Achievements
  async getUserAchievements(userId: string): Promise<UserAchievement[]> {
    const response = await apiClient.get(`/api/gamification/users/${userId}/achievements`);
    return response.data.achievements;
  }

  async getAllAchievements(): Promise<Achievement[]> {
    const response = await apiClient.get('/api/gamification/achievements');
    return response.data;
  }

  async checkAchievements(userId: string, sessionId: string, sessionData: any): Promise<Achievement[]> {
    const response = await apiClient.post(`/api/gamification/users/${userId}/check-achievements`, {
      sessionId,
      sessionData
    });
    return response.data.unlockedAchievements;
  }

  // Characters
  async getCharacters(userId: string, filters?: { difficulty?: number; unlocked?: boolean }): Promise<AICharacter[]> {
    const params = new URLSearchParams();
    if (filters?.difficulty) params.append('difficulty', filters.difficulty.toString());
    if (filters?.unlocked !== undefined) params.append('unlocked', filters.unlocked.toString());
    params.append('userId', userId);

    const response = await apiClient.get(`/api/gamification/characters?${params}`);
    return response.data.characters;
  }

  async getCharacter(characterId: string, userId: string): Promise<AICharacter & { relationship?: CharacterRelationship }> {
    const response = await apiClient.get(`/api/gamification/characters/${characterId}?userId=${userId}`);
    return response.data;
  }

  async getUnlockedCharacters(userId: string): Promise<string[]> {
    const response = await apiClient.get(`/api/gamification/users/${userId}/unlocked-characters`);
    return response.data.characters;
  }

  // Character Relationships
  async getCharacterRelationships(userId: string): Promise<Record<string, CharacterRelationship>> {
    const response = await apiClient.get(`/api/gamification/users/${userId}/character-relationships`);
    return response.data.relationships;
  }

  async updateCharacterRelationship(
    userId: string, 
    characterId: string, 
    updates: Partial<CharacterRelationship>
  ): Promise<CharacterRelationship> {
    const response = await apiClient.put(
      `/api/gamification/users/${userId}/character-relationships/${characterId}`,
      updates
    );
    return response.data;
  }

  // Leaderboards
  async getLeaderboard(
    timeframe: 'weekly' | 'monthly' | 'all_time' = 'weekly',
    scope: 'global' | 'organization' | 'industry' = 'global',
    scopeId?: string,
    limit: number = 50,
    userId?: string
  ): Promise<LeaderboardResponse> {
    const params = new URLSearchParams({
      timeframe,
      scope,
      limit: limit.toString()
    });
    
    if (scopeId) params.append('scopeId', scopeId);
    if (userId) params.append('userId', userId);

    const response = await apiClient.get(`/api/gamification/leaderboards/negotiation?${params}`);
    return response.data;
  }

  async getOrganizationLeaderboard(
    organizationId: string,
    timeframe: 'weekly' | 'monthly' | 'all_time' = 'weekly'
  ): Promise<LeaderboardResponse> {
    const response = await apiClient.get(
      `/api/gamification/leaderboards/organization/${organizationId}?timeframe=${timeframe}`
    );
    return response.data;
  }

  // Pressure Events
  async getPressureEvents(): Promise<PressureEvent[]> {
    const response = await apiClient.get('/api/gamification/pressure-events');
    return response.data.events;
  }

  async triggerPressureEvent(sessionId: string, eventId: string, customData?: any): Promise<void> {
    await apiClient.post(`/api/gamification/sessions/${sessionId}/pressure-events`, {
      eventId,
      customData
    });
  }

  // Session Integration
  async initializeSessionGamification(sessionId: string, userId: string, characterId: string): Promise<any> {
    const response = await apiClient.post(`/api/gamification/sessions/${sessionId}/initialize`, {
      userId,
      characterId
    });
    return response.data;
  }

  async updateSessionGamification(sessionId: string, moveData: any): Promise<{
    xpEarned: number;
    achievementsUnlocked: Achievement[];
    levelUpdate?: LevelUpdate;
    pressureEvents: PressureEvent[];
    liveScore: number;
  }> {
    const response = await apiClient.post(`/api/gamification/sessions/${sessionId}/update`, moveData);
    return response.data;
  }

  async finalizeSessionGamification(sessionId: string): Promise<{
    finalXP: number;
    finalAchievements: Achievement[];
    relationshipUpdates: Record<string, CharacterRelationship>;
    levelUpdate?: LevelUpdate;
  }> {
    const response = await apiClient.post(`/api/gamification/sessions/${sessionId}/finalize`);
    return response.data;
  }
}

// Export singleton instance
export const gamificationAPI = new GamificationAPI();
