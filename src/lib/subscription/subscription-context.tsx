"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import {
	Subscription,
	SubscriptionTier,
	SUBSCRIPTION_FEATURES,
	CreditBalance,
	CreditTransaction,
	CreditPackage,
	FeatureCost,
	tierFeatures,
} from "../types/subscription";
import { subscriptionService } from "../services/subscription-service";
import { creditService } from "../services/credit-service";
import { useOrganization } from "../organization/organization-context";

interface SubscriptionContextType {
	subscription: Subscription | null;
	isLoading: boolean;
	error: Error | null;
	hasFeature: (featureId: string) => boolean;
	isInTrial: () => boolean;
	getRemainingTrialDays: () => number;
	getUsagePercentage: (type: "documents" | "analysis") => number;
	createCheckoutSession: (tier: SubscriptionTier) => Promise<string>;
	cancelSubscription: () => Promise<void>;
	getBillingPortalUrl: () => Promise<string>;
	refreshSubscription: () => Promise<void>;
	downgradeToFreeTier: () => Promise<void>;
	// Credit management functions
	creditBalance: CreditBalance | null;
	getCreditHistory: () => Promise<CreditTransaction[]>;
	checkCreditsForFeature: (featureName: string) => Promise<boolean>;
	useCreditsForFeature: (featureName: string) => Promise<boolean>;
	getCreditPackages: () => Promise<CreditPackage[]>;
	createCreditCheckoutSession: (packageId: string) => Promise<string>;
	getFeatureCost: (featureName: string) => FeatureCost | null;
	refreshCreditBalance: () => Promise<void>;
}

const SubscriptionContext = createContext<SubscriptionContextType | undefined>(
	undefined
);

export function SubscriptionProvider({
	children,
}: {
	children: React.ReactNode;
}) {
	const [subscription, setSubscription] = useState<Subscription | null>(null);
	const [isLoading, setIsLoading] = useState<boolean>(true);
	const [error, setError] = useState<Error | null>(null);
	const [creditBalance, setCreditBalance] = useState<CreditBalance | null>(
		null
	);

	const { organization } = useOrganization();

	const fetchSubscription = async () => {
		if (!organization?.id) {
			setIsLoading(false);
			return;
		}

		try {
			setIsLoading(true);
			setError(null);
			const subscriptionData = await subscriptionService.getSubscription(
				organization.id
			);
			setSubscription(subscriptionData);

			// Extract credit balance from subscription response
			if (subscriptionData) {
				const balance: CreditBalance = {
					current: subscriptionData.creditBalance || 0,
					monthlyAllocation: subscriptionData.monthlyCreditsAllocation || 0,
					totalEarned: subscriptionData.totalCreditsEarned || 0,
					totalSpent: subscriptionData.totalCreditsSpent || 0,
					lastAllocation: subscriptionData.lastCreditAllocation || new Date().toISOString(),
				};
				setCreditBalance(balance);
			}
		} catch (err) {
			console.error("Error fetching subscription:", err);
			setError(
				err instanceof Error ? err : new Error("Failed to fetch subscription")
			);
		} finally {
			setIsLoading(false);
		}
	};

	useEffect(() => {
		fetchSubscription();
	}, [organization?.id]);

	const hasFeature = (featureId: string): boolean => {
		if (!subscription) return false;

		// Check if feature is in trial tier
		if (isInTrial() && subscription.trialTier) {
			const feature = SUBSCRIPTION_FEATURES.find((f) => f.id === featureId);
			if (feature && feature.tiers.includes(subscription.trialTier!)) {
				return true;
			}
		}

		// Check if feature is in current subscription
		return subscription.features.includes(featureId);
	};

	const isInTrial = (): boolean => {
		if (
			!subscription ||
			!subscription.trialTier ||
			!subscription.trialEndDate
		) {
			return false;
		}

		const trialEnd = new Date(subscription.trialEndDate);
		return trialEnd > new Date();
	};

	const getRemainingTrialDays = (): number => {
		if (!isInTrial() || !subscription?.trialEndDate) return 0;

		const trialEnd = new Date(subscription.trialEndDate);
		const now = new Date();
		const diffTime = trialEnd.getTime() - now.getTime();
		return Math.ceil(diffTime / (1000 * 60 * 60 * 24));
	};

	const getUsagePercentage = (type: "documents" | "analysis"): number => {
		if (!subscription) return 0;

		const { tier, usageStats } = subscription;

		// Find the plan for the current tier
		const plan = SUBSCRIPTION_PLANS.find((p) => p.id === tier);
		if (!plan) return 0;

		if (type === "documents") {
			const limit = plan.limits.documentLimit;
			if (limit === "unlimited") return 0;
			const usage = usageStats.documentsProcessed;
			return Math.min(Math.round((usage / (limit as number)) * 100), 100);
		} else {
			// 🎯 Analysis limits removed - now unlimited within credit allocation
			// Analysis usage is no longer tracked by count but by credits
			return 0; // Always return 0 since analysis is unlimited within credit allocation
		}
	};

	const createCheckoutSession = async (
		tier: SubscriptionTier
	): Promise<string> => {
		if (!organization?.id) {
			throw new Error("Organization ID is required");
		}

		try {
			const response = await subscriptionService.createCheckoutSession(
				organization.id,
				tier
			);

			// Handle both sessionUrl and sessionId in the response
			if (response.sessionUrl) {
				return response.sessionUrl;
			} else if (response.sessionId) {
				// If we get a sessionId, use the correct Stripe checkout URL format
				return `https://checkout.stripe.com/c/pay/${response.sessionId}`;
			} else {
				throw new Error("No session URL or ID returned from the server");
			}
		} catch (err) {
			console.error("Error creating checkout session:", err);
			throw err;
		}
	};

	const cancelSubscription = async (): Promise<void> => {
		if (!organization?.id) {
			throw new Error("Organization ID is required");
		}

		try {
			await subscriptionService.cancelSubscription(organization.id);
			await fetchSubscription(); // Refresh subscription data
		} catch (err) {
			console.error("Error canceling subscription:", err);
			throw err;
		}
	};

	const getBillingPortalUrl = async (): Promise<string> => {
		if (!organization?.id) {
			throw new Error("Organization ID is required");
		}

		try {
			const { url } = await subscriptionService.getBillingPortalUrl(
				organization.id
			);
			return url;
		} catch (err) {
			console.error("Error getting billing portal URL:", err);
			throw err;
		}
	};

	const refreshSubscription = async (): Promise<void> => {
		await fetchSubscription();
	};

	const downgradeToFreeTier = async (): Promise<void> => {
		if (!organization?.id) {
			throw new Error("Organization ID is required");
		}

		try {
			await subscriptionService.downgradeToFreeTier(organization.id);
			await fetchSubscription(); // Refresh subscription data
		} catch (err) {
			console.error("Error downgrading to free tier:", err);
			throw err;
		}
	};

	// Credit management functions
	const refreshCreditBalance = async (): Promise<void> => {
		if (!organization?.id) return;

		try {
			// Get fresh subscription data which includes credit balance
			const subscriptionData = await subscriptionService.getSubscription(organization.id);
			if (subscriptionData) {
				const balance: CreditBalance = {
					current: subscriptionData.creditBalance || 0,
					monthlyAllocation: subscriptionData.monthlyCreditsAllocation || 0,
					totalEarned: subscriptionData.totalCreditsEarned || 0,
					totalSpent: subscriptionData.totalCreditsSpent || 0,
					lastAllocation: subscriptionData.lastCreditAllocation || new Date().toISOString(),
				};
				setCreditBalance(balance);
				setSubscription(subscriptionData); // Also update subscription data
			}
		} catch (err) {
			console.error("Error fetching credit balance:", err);
		}
	};

	const getCreditHistory = async (): Promise<CreditTransaction[]> => {
		if (!organization?.id) {
			throw new Error("Organization ID is required");
		}

		try {
			// First try to get from current subscription data
			if (subscription?.creditHistory) {
				return subscription.creditHistory;
			}

			// Fallback to API call if not available in subscription
			return await creditService.getCreditHistory(organization.id);
		} catch (err) {
			console.error("Error fetching credit history:", err);
			throw err;
		}
	};

	const checkCreditsForFeature = async (
		featureName: string
	): Promise<boolean> => {
		if (!organization?.id) {
			throw new Error("Organization ID is required");
		}

		try {
			const result = await creditService.checkCreditsForFeature(
				organization.id,
				featureName
			);
			return result.hasCredits;
		} catch (err) {
			console.error("Error checking credits for feature:", err);
			throw err;
		}
	};

	const useCreditsForFeature = async (
		featureName: string
	): Promise<boolean> => {
		if (!organization?.id) {
			throw new Error("Organization ID is required");
		}

		try {
			const result = await creditService.useCreditsForFeature({
				organizationId: organization.id,
				featureName,
			});

			// Update credit balance after successful usage
			if (result.success) {
				await refreshCreditBalance();
			}

			return result.success;
		} catch (err) {
			console.error("Error using credits for feature:", err);
			throw err;
		}
	};

	const getCreditPackages = async (): Promise<CreditPackage[]> => {
		try {
			return await creditService.getCreditPackages();
		} catch (err) {
			console.error("Error fetching credit packages:", err);
			throw err;
		}
	};

	const createCreditCheckoutSession = async (
		packageId: string
	): Promise<string> => {
		if (!organization?.id) {
			throw new Error("Organization ID is required");
		}

		try {
			const response = await creditService.createCreditCheckoutSession(
				organization.id,
				packageId
			);

			// Handle both sessionUrl and sessionId in the response
			if (response.sessionUrl) {
				return response.sessionUrl;
			} else if (response.sessionId) {
				return `https://checkout.stripe.com/c/pay/${response.sessionId}`;
			} else {
				throw new Error("No session URL or ID returned from the server");
			}
		} catch (err) {
			console.error("Error creating credit checkout session:", err);
			throw err;
		}
	};

	const getFeatureCost = (featureName: string): FeatureCost | null => {
		return creditService.getFeatureCost(featureName);
	};

	return (
		<SubscriptionContext.Provider
			value={{
				subscription,
				isLoading,
				error,
				hasFeature,
				isInTrial,
				getRemainingTrialDays,
				getUsagePercentage,
				createCheckoutSession,
				cancelSubscription,
				getBillingPortalUrl,
				refreshSubscription,
				downgradeToFreeTier,
				// Credit management
				creditBalance,
				getCreditHistory,
				checkCreditsForFeature,
				useCreditsForFeature,
				getCreditPackages,
				createCreditCheckoutSession,
				getFeatureCost,
				refreshCreditBalance,
			}}
		>
			{children}
		</SubscriptionContext.Provider>
	);
}

export function useSubscription() {
	const context = useContext(SubscriptionContext);
	if (context === undefined) {
		throw new Error(
			"useSubscription must be used within a SubscriptionProvider"
		);
	}
	return context;
}

// Import here to avoid circular dependency
import { SUBSCRIPTION_PLANS } from "../types/subscription";
