import { apiClient } from "../config";
import type { DocumentAnalysis } from "@/lib/types/analysis";

export interface Citation {
  rawText: string;       // Original citation text (e.g., "5 U.S. 137")
  title: string;         // Case name (e.g., "Marbury v. Madison")
  url: string;           // Full URL to the opinion
  court?: string;        // Court that issued the opinion
  year?: number;         // Year of the decision
  confidence: number;    // Confidence score for the extraction
  metadata: {            // Additional metadata
    type: string;        // Type of citation
    source: string;      // Source of the citation
    normalizedCitation: string; // Standardized citation format
    jurisdiction?: string; // Jurisdiction
    pdfUrl?: string;     // URL to PDF version
    apiUrl?: string;     // URL to API endpoint
  }
}
export interface ChatMessage {
	id: string;
	role: "user" | "assistant";
	content: string;
	timestamp: string;
	attachments?: {
		filename: string;
		path: string;
		mimeType: string;
		description?: string;
		uploadedAt: string;
	}[];
	citations?:Citation[]
	references?: {
		documentId: string;
		sectionId: string;
		text: string;
	}[];
	metadata?: {
		analysisData?: DocumentAnalysis;
	};
}

export interface ChatSession {
	id: string;
	documentId?: string;
	title: string;
	createdAt: string;
	userId?: string;
	role?: string;
	organizationId?: string;
}

export interface Thread {
	id: string;
	sessionId: string;
	title: string;
	createdAt: string;
	updatedAt: string;
	firstMessageId: string;
	parentThreadId?: string;
}

export interface ChatAnalytics {
	messages: {
		total: number;
		userMessages: number;
		aiMessages: number;
	};
	feedback: {
		helpful: number;
		total: number;
		rating: number;
	};
	topTopics: string[];
	engagementMetrics: {
		averageResponseTime: string;
		sessionDuration: string;
	};
}

class ChatService {
	async createSession(
		documentId?: string,
		title?: string
	): Promise<ChatSession> {
		const response = await apiClient.post<ChatSession>(
			`/chat/sessions`,
			{
				documentId,
				title,
			}
		);
		return response.data;
	}

	async getSessions(): Promise<ChatSession[]> {
		const response = await apiClient.get<ChatSession[]>(`/chat/sessions`);
		return response.data;
	}

	async getSessionMessages(
		sessionId: string,
		page: number = 1,
		limit: number = 10,
		sort: "asc" | "desc" = "desc",
		sortBy: "timestamp" | "role" = "timestamp"
	): Promise<{
		items: ChatMessage[];
		meta: {
			totalItems: number;
			itemsPerPage: number;
			currentPage: number;
			totalPages: number;
			hasPreviousPage: boolean;
			hasNextPage: boolean;
		};
	}> {
		const response = await apiClient.get<{
			items: ChatMessage[];
			meta: {
				totalItems: number;
				itemsPerPage: number;
				currentPage: number;
				totalPages: number;
				hasPreviousPage: boolean;
				hasNextPage: boolean;
			};
		}>(
			`/chat/sessions/${sessionId}/paginated-messages`,
			{
				params: {
					page,
					limit,
					sort,
					sortBy,
				}
			}
		);
		return response.data;
	}

	async sendMessage(
		sessionId: string,
		content: string,
		relatedDocumentIds?: string[],
		attachments?: {
			filename: string;
			path: string;
			mimeType: string;
			description?: string;
		}[],
		references?: {
			documentId: string;
			sectionId: string;
			text: string;
		}[]
	): Promise<ChatMessage> {
		const response = await apiClient.post<ChatMessage>(
			`/chat/messages`,
			{
				sessionId,
				content,
				relatedDocumentIds,
				attachments,
				references,
			}
		);
		return response.data;
	}

	async uploadAttachment(file: File): Promise<{
		filename: string;
		path: string;
		mimeType: string;
		size: number;
	}> {
		const formData = new FormData();
		formData.append("file", file);

		const response = await apiClient.post<{ attachment: { filename: string; path: string; mimeType: string; size: number; } }>(
			`/chat/messages/upload`,
			formData,
			{
				headers: {
					"Content-Type": "multipart/form-data",
				},
			}
		);
		return response.data.attachment;
	}

	async createThread(
		sessionId: string,
		messageId: string,
		title: string,
		parentThreadId?: string
	): Promise<Thread> {
		const response = await apiClient.post<Thread>(
			`/chat/threads`,
			{
				sessionId,
				messageId,
				title,
				parentThreadId,
			}
		);
		return response.data;
	}

	async getThreadMessages(threadId: string): Promise<ChatMessage[]> {
		const response = await apiClient.get<ChatMessage[]>(`/chat/threads/${threadId}/messages`);
		return response.data;
	}

	async getSessionThreads(sessionId: string): Promise<Thread[]> {
		const response = await apiClient.get<Thread[]>(`/chat/threads/session/${sessionId}`);
		return response.data;
	}

	async getSessionAnalytics(sessionId: string): Promise<ChatAnalytics> {
		const response = await apiClient.get<{ analytics: ChatAnalytics }>(`/chat/analytics/sessions/${sessionId}`);
		return response.data.analytics;
	}

	async submitFeedback(
		sessionId: string,
		feedback: { isHelpful: boolean; rating: number; comments?: string }
	): Promise<void> {
		await apiClient.post(
			`/chat/analytics/sessions/${sessionId}/feedback`,
			feedback
		);
	}

	async updateSessionTopics(
		sessionId: string,
		topics: string[]
	): Promise<{
		sessionId: string;
		topics: string[];
		updatedAt: string;
	}> {
		const response = await apiClient.put<{
			sessionId: string;
			topics: string[];
			updatedAt: string;
		}>(
			`/chat/analytics/sessions/${sessionId}/topics`,
			{
				topics,
			}
		);
		return response.data;
	}

	async deleteSession(sessionId: string): Promise<void> {
		await apiClient.delete(`/chat/sessions/${sessionId}`);
	}
}

export const chatService = new ChatService();
