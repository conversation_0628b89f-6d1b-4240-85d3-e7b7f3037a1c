import { AxiosError } from "axios";
import { apiClient } from "@/lib/config";
import type {
	NegotiationScenario,
	NegotiationSession,
	CreateScenarioRequest,
	StartSessionRequest,
	MakeMoveRequest,
	SessionAnalytics,
	NegotiationSimulatorApiResponse,
	CreateScenarioFromPlaybookRequest,
	CreateScenarioFromDocumentRequest,
	UserNegotiationProfile,
	UpdateUserNegotiationProfileRequest,
	PerformanceHistoryEntry,
	SkillGapAnalysisRequest,
	SkillGapAnalysisResponse,
	CrossFeatureUsageResponse,
	PersonalizedRecommendationsResponse,
} from "@/lib/types/negotiation-simulator";

export class NegotiationSimulatorError extends Error {
	constructor(
		message: string,
		public code: string,
		public statusCode?: number,
		public details?: unknown
	) {
		super(message);
		this.name = "NegotiationSimulatorError";
	}
}

class NegotiationSimulatorService {
	private baseUrl = "/negotiation-simulator";

	/**
	 * Transform backend session response to frontend structure
	 */
	private transformBackendSession(backendSession: any): NegotiationSession {
		return {
			...backendSession,
			id: backendSession._id || backendSession.id,
			// Fix status case mismatch
			status: backendSession.status?.toUpperCase() || 'ACTIVE',
			// Fix field name mismatch
			startedAt: new Date(backendSession.startTime || backendSession.startedAt),
			lastActivity: new Date(backendSession.updatedAt || backendSession.lastActivity || backendSession.startTime),
			completedAt: backendSession.endTime ? new Date(backendSession.endTime) : undefined,
			pausedAt: backendSession.pausedAt ? new Date(backendSession.pausedAt) : undefined,
			// Fix AI personality case mismatch
			aiPersonality: {
				...backendSession.aiPersonality,
				communicationStyle: backendSession.aiPersonality.communicationStyle.toUpperCase(),
				decisionSpeed: backendSession.aiPersonality.decisionSpeed.toUpperCase(),
				concessionPattern: backendSession.aiPersonality.concessionPattern?.toUpperCase() || 'GRADUAL'
			},
			// Ensure metrics has all required properties
			metrics: {
				...backendSession.metrics,
				finalScore: backendSession.metrics?.overallScore || 0,
				keyMetrics: {
					communicationEffectiveness: backendSession.metrics?.communicationQuality || 0,
					strategicThinking: backendSession.metrics?.strategicEffectiveness || 0,
					flexibilityScore: backendSession.metrics?.timeEfficiency || 0,
					timeManagement: backendSession.metrics?.timeEfficiency || 0,
					...backendSession.metrics?.keyMetrics
				},
				...backendSession.metrics
			}
		};
	}

	// Scenario Management
	async createScenario(
		data: CreateScenarioRequest
	): Promise<NegotiationScenario> {
		try {
			const response = await apiClient.post<
				NegotiationSimulatorApiResponse<NegotiationScenario>
			>(`${this.baseUrl}/scenarios`, data);
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to create scenario");
		}
	}

	async getScenarios(
		params: {
			industry?: string;
			difficulty?: string;
			contractType?: string;
			page?: number;
			limit?: number;
		} = {}
	): Promise<{ scenarios: NegotiationScenario[]; total: number }> {
		try {
			const queryString = new URLSearchParams(
				Object.entries(params)
					.filter(([_, value]) => value !== undefined)
					.map(([key, value]) => [key, String(value)])
			).toString();

			const response = await apiClient.get<any>(
				`${this.baseUrl}/scenarios${queryString ? `?${queryString}` : ""}`
			);

			// Handle both possible response formats:
			// 1. Standard wrapped format: { success: true, data: { scenarios: [...], total: number } }
			// 2. Direct array format: [...]
			if (Array.isArray(response.data)) {
				// Direct array format
				return {
					scenarios: response.data,
					total: response.data.length
				};
			} else if (response.data.data) {
				// Standard wrapped format
				return response.data.data;
			} else {
				// Fallback
				return {
					scenarios: response.data.scenarios || [],
					total: response.data.total || 0
				};
			}
		} catch (error) {
			this.handleError(error, "Failed to fetch scenarios");
		}
	}

	async getScenario(scenarioId: string): Promise<NegotiationScenario> {
		try {
			const response = await apiClient.get<
				NegotiationSimulatorApiResponse<NegotiationScenario>
			>(`${this.baseUrl}/scenarios/${scenarioId}`);
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch scenario");
		}
	}

	async deleteScenario(scenarioId: string): Promise<void> {
		try {
			await apiClient.delete(`${this.baseUrl}/scenarios/${scenarioId}`);
		} catch (error) {
			this.handleError(error, "Failed to delete scenario");
		}
	}

	// Session Management
	async startSession(data: StartSessionRequest): Promise<NegotiationSession> {
		try {
			const response = await apiClient.post<any>(
				`${this.baseUrl}/sessions`,
				data
			);

			// Handle both possible response formats:
			// 1. Standard wrapped format: { success: true, data: { session } }
			// 2. Direct session format: { session object }
			let session;
			if (response.data.data) {
				// Standard wrapped format
				session = response.data.data;
			} else {
				// Direct session format
				session = response.data;
			}

			return this.transformBackendSession(session);
		} catch (error) {
			this.handleError(error, "Failed to start negotiation session");
		}
	}

	async getSessions(
		params: {
			status?: string;
			page?: number;
			limit?: number;
		} = {}
	): Promise<{ sessions: NegotiationSession[]; total: number }> {
		try {
			const queryString = new URLSearchParams(
				Object.entries(params)
					.filter(([_, value]) => value !== undefined)
					.map(([key, value]) => [key, String(value)])
			).toString();

			const response = await apiClient.get<any>(
				`${this.baseUrl}/sessions${queryString ? `?${queryString}` : ""}`
			);

			// Handle both possible response formats:
			// 1. Standard wrapped format: { success: true, data: { sessions: [...], total: number } }
			// 2. Direct array format: [...]
			if (Array.isArray(response.data)) {
				// Direct array format
				const transformedSessions = response.data.map((session: any) => this.transformBackendSession(session));
				return {
					sessions: transformedSessions,
					total: transformedSessions.length
				};
			} else if (response.data.data) {
				// Standard wrapped format
				const transformedSessions = response.data.data.sessions.map((session: any) => this.transformBackendSession(session));
				return {
					sessions: transformedSessions,
					total: response.data.data.total
				};
			} else {
				// Fallback format
				const sessions = response.data.sessions || [];
				const transformedSessions = sessions.map((session: any) => this.transformBackendSession(session));
				return {
					sessions: transformedSessions,
					total: response.data.total || 0
				};
			}
		} catch (error) {
			this.handleError(error, "Failed to fetch sessions");
		}
	}

	async getSession(sessionId: string): Promise<NegotiationSession> {
		try {
			const response = await apiClient.get<any>(
				`${this.baseUrl}/sessions/${sessionId}`
			);

			// Handle both possible response formats
			let session;
			if (response.data.data) {
				session = response.data.data;
			} else {
				session = response.data;
			}

			return this.transformBackendSession(session);
		} catch (error) {
			this.handleError(error, "Failed to fetch session");
		}
	}

	async pauseSession(sessionId: string): Promise<NegotiationSession> {
		try {
			const response = await apiClient.put<any>(
				`${this.baseUrl}/sessions/${sessionId}/pause`
			);

			// Handle both possible response formats
			let session;
			if (response.data.data) {
				session = response.data.data;
			} else {
				session = response.data;
			}

			return this.transformBackendSession(session);
		} catch (error) {
			this.handleError(error, "Failed to pause session");
		}
	}

	async resumeSession(sessionId: string): Promise<NegotiationSession> {
		try {
			const response = await apiClient.put<any>(
				`${this.baseUrl}/sessions/${sessionId}/resume`
			);

			// Handle both possible response formats
			let session;
			if (response.data.data) {
				session = response.data.data;
			} else {
				session = response.data;
			}

			return this.transformBackendSession(session);
		} catch (error) {
			this.handleError(error, "Failed to resume session");
		}
	}

	async abandonSession(sessionId: string): Promise<void> {
		try {
			await apiClient.put(`${this.baseUrl}/sessions/${sessionId}/abandon`);
		} catch (error) {
			this.handleError(error, "Failed to abandon session");
		}
	}

	// Negotiation Actions
	async makeMove(
		sessionId: string,
		data: MakeMoveRequest
	): Promise<NegotiationSession> {
		try {
			const response = await apiClient.post<any>(
				`${this.baseUrl}/sessions/${sessionId}/moves`,
				{ move: data }
			);

			// Handle both possible response formats
			let session;
			if (response.data.data) {
				session = response.data.data;
			} else {
				session = response.data;
			}

			return this.transformBackendSession(session);
		} catch (error) {
			this.handleError(error, "Failed to make negotiation move");
		}
	}

	// Analytics & Evaluation
	async evaluateSession(sessionId: string): Promise<NegotiationSession> {
		try {
			const response = await apiClient.post<
				NegotiationSimulatorApiResponse<NegotiationSession>
			>(`${this.baseUrl}/sessions/${sessionId}/evaluate`);
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to evaluate session");
		}
	}

	async getAnalytics(): Promise<SessionAnalytics> {
		try {
			const response = await apiClient.get<
				NegotiationSimulatorApiResponse<SessionAnalytics>
			>(`${this.baseUrl}/analytics/overview`);
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch analytics");
		}
	}

	// Template Management
	async getTemplateScenarios(): Promise<NegotiationScenario[]> {
		try {
			const response = await apiClient.get<
				NegotiationSimulatorApiResponse<NegotiationScenario[]>
			>(`${this.baseUrl}/scenarios/templates`);
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch template scenarios");
		}
	}

	async cloneScenario(
		scenarioId: string,
		name?: string
	): Promise<NegotiationScenario> {
		try {
			const response = await apiClient.post<
				NegotiationSimulatorApiResponse<NegotiationScenario>
			>(`${this.baseUrl}/scenarios/${scenarioId}/clone`, { name });
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to clone scenario");
		}
	}

	// Real-time updates (for future WebSocket integration)
	async getSessionUpdates(sessionId: string): Promise<NegotiationSession> {
		try {
			const response = await apiClient.get<
				NegotiationSimulatorApiResponse<NegotiationSession>
			>(`${this.baseUrl}/sessions/${sessionId}/updates`);
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to get session updates");
		}
	}

	// Integration endpoints
	async createScenarioFromPlaybook(
		documentId: string,
		request: CreateScenarioFromPlaybookRequest
	): Promise<NegotiationScenario> {
		try {
			const response = await apiClient.post<NegotiationScenario>(
				`/documents/${documentId}/negotiation-playbook/create-scenario`,
				request
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to create scenario from playbook");
		}
	}

	async getScenariosFromPlaybook(
		playbookId: string,
		params: { page?: number; limit?: number } = {}
	): Promise<{ scenarios: NegotiationScenario[]; total: number } | null> {
		try {
			const queryString = new URLSearchParams(
				Object.entries(params)
					.filter(([_, value]) => value !== undefined)
					.map(([key, value]) => [key, String(value)])
			).toString();

			const response = await apiClient.get<NegotiationScenario[]>(
				`${this.baseUrl}/scenarios/from-playbook/${playbookId}${
					queryString ? `?${queryString}` : ""
				}`
			);

			// The API returns a direct array of scenarios, not wrapped in the standard response format
			const scenarios = response.data;
			return {
				scenarios,
				total: scenarios.length
			};
		} catch (error) {
			// If the endpoint doesn't exist (404) or there are no scenarios, return empty result
			if (error instanceof AxiosError && error.response?.status === 404) {
				return { scenarios: [], total: 0 };
			}
			this.handleError(error, "Failed to fetch scenarios from playbook");
		}
	}

	async createScenarioFromDocument(
		documentId: string,
		request: CreateScenarioFromDocumentRequest
	): Promise<NegotiationScenario> {
		try {
			const response = await apiClient.post<
				NegotiationSimulatorApiResponse<NegotiationScenario>
			>(`${this.baseUrl}/scenarios/from-document/${documentId}`, request);
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to create scenario from document");
		}
	}

	// User profile endpoints
	async getUserNegotiationProfile(): Promise<UserNegotiationProfile> {
		try {
			const response = await apiClient.get<
				NegotiationSimulatorApiResponse<UserNegotiationProfile>
			>("/api/users/negotiation-profile");
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to get user negotiation profile");
		}
	}

	async updateUserNegotiationProfile(
		updates: UpdateUserNegotiationProfileRequest
	): Promise<UserNegotiationProfile> {
		try {
			const response = await apiClient.put<
				NegotiationSimulatorApiResponse<UserNegotiationProfile>
			>("/api/users/negotiation-profile", updates);
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to update user negotiation profile");
		}
	}

	async getPerformanceHistory(
		userId: string,
		params: { timeframe?: string; page?: number; limit?: number } = {}
	): Promise<{
		data: PerformanceHistoryEntry[];
		total: number;
		page: number;
		limit: number;
		totalPages: number;
	}> {
		try {
			const queryString = new URLSearchParams(
				Object.entries(params)
					.filter(([_, value]) => value !== undefined)
					.map(([key, value]) => [key, String(value)])
			).toString();

			const response = await apiClient.get<
				NegotiationSimulatorApiResponse<{
					data: PerformanceHistoryEntry[];
					total: number;
					page: number;
					limit: number;
					totalPages: number;
				}>
			>(
				`/api/users/${userId}/performance-history${
					queryString ? `?${queryString}` : ""
				}`
			);

			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to get performance history");
		}
	}

	async generateSkillGapAnalysis(
		userId: string,
		request: SkillGapAnalysisRequest
	): Promise<SkillGapAnalysisResponse> {
		try {
			const response = await apiClient.post<
				NegotiationSimulatorApiResponse<SkillGapAnalysisResponse>
			>(`/api/users/${userId}/skill-gap-analysis`, request);
			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to generate skill gap analysis");
		}
	}

	// Cross-feature analytics endpoints
	async getCrossFeatureUsage(
		params: {
			timeframe?: string;
			userId?: string;
			organizationId?: string;
		} = {}
	): Promise<CrossFeatureUsageResponse> {
		try {
			const queryString = new URLSearchParams(
				Object.entries(params)
					.filter(([_, value]) => value !== undefined)
					.map(([key, value]) => [key, String(value)])
			).toString();

			const response = await apiClient.get<
				NegotiationSimulatorApiResponse<CrossFeatureUsageResponse>
			>(
				`/api/analytics/cross-feature-usage${
					queryString ? `?${queryString}` : ""
				}`
			);

			return response.data.data;
		} catch (error) {
			this.handleError(error, "Failed to get cross-feature usage analytics");
		}
	}

	async generatePersonalizedRecommendations(request: {
		userId: string;
		context: "playbook" | "simulator" | "general";
		targetArea?: string;
		currentDocumentId?: string;
		currentSessionId?: string;
	}): Promise<PersonalizedRecommendationsResponse> {
		try {
			const response = await apiClient.post<
				NegotiationSimulatorApiResponse<PersonalizedRecommendationsResponse>
			>("/api/analytics/generate-personalized-recommendations", request);
			return response.data.data;
		} catch (error) {
			this.handleError(
				error,
				"Failed to generate personalized recommendations"
			);
		}
	}

	private handleError(error: unknown, defaultMessage: string): never {
		if (error instanceof AxiosError) {
			const errorData = error.response?.data as any;

			// Handle specific error cases
			if (error.response?.status === 403) {
				throw new NegotiationSimulatorError(
					"Negotiation simulator feature requires PRO subscription",
					"FEATURE_NOT_AVAILABLE",
					403,
					{ upgradeUrl: "/subscription/upgrade" }
				);
			}

			if (error.response?.status === 404) {
				throw new NegotiationSimulatorError(
					"Resource not found or access denied",
					"NOT_FOUND",
					404
				);
			}

			if (error.response?.status === 400) {
				throw new NegotiationSimulatorError(
					errorData?.error?.message || "Invalid request parameters",
					"VALIDATION_ERROR",
					400,
					errorData?.error?.details
				);
			}

			throw new NegotiationSimulatorError(
				errorData?.error?.message || defaultMessage,
				errorData?.error?.code || "OPERATION_FAILED",
				error.response?.status,
				errorData?.error?.details
			);
		}

		throw new NegotiationSimulatorError(defaultMessage, "UNKNOWN_ERROR", 500);
	}
}

// Export singleton instance
export const negotiationSimulatorService = new NegotiationSimulatorService();
