# Negotiation Session Workflow

## Overview

The Negotiation Session system provides an interactive, AI-powered environment where users can practice real-world negotiation skills. Sessions are based on scenarios generated from document analysis, creating a seamless bridge between strategic learning and practical application.

## System Architecture

### Component Relationship

```
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│   Document Upload   │───▶│ Negotiation Playbook│───▶│ Practice Scenario   │
│                     │    │                     │    │                     │
│ • Contract Analysis │    │ • Strategic Analysis│    │ • Scenario Setup    │
│ • Content Extraction│    │ • Risk Assessment   │    │ • Party Definitions │
│ • Type Detection    │    │ • Leverage Points   │    │ • Initial Offers    │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
                                                                  │
                                                                  ▼
┌─────────────────────┐    ┌─────────────────────┐    ┌─────────────────────┐
│ Performance Review  │◀───│ Negotiation Session │◀───│ Session Creation    │
│                     │    │                     │    │                     │
│ • Detailed Metrics  │    │ • Interactive Rounds│    │ • AI Personality    │
│ • Skill Assessment  │    │ • Real-time Feedback│    │ • User Preferences  │
│ • Improvement Tips  │    │ • Strategy Tracking │    │ • Session Options   │
└─────────────────────┘    └─────────────────────┘    └─────────────────────┘
```

## Session Lifecycle

### Phase 1: Session Initialization

#### 1.1 Session Creation
**Endpoint**: `POST /api/negotiation-simulator/sessions`

**Request Structure**:
```json
{
  "scenarioId": "scenario-uuid",
  "aiPersonality": {
    "aggressiveness": 0.6,
    "flexibility": 0.7,
    "riskTolerance": 0.5,
    "communicationStyle": "diplomatic",
    "decisionSpeed": "moderate",
    "concessionPattern": "gradual"
  },
  "customConstraints": {
    "maxRounds": 8,
    "timeLimit": 45
  }
}
```

#### 1.2 AI Personality Configuration
The system generates an AI counterparty with configurable personality traits:

- **Aggressiveness** (0.0-1.0): How assertive the AI will be in negotiations
- **Flexibility** (0.0-1.0): Willingness to compromise on terms
- **Risk Tolerance** (0.0-1.0): Comfort level with uncertain outcomes
- **Communication Style**: formal, casual, technical, diplomatic
- **Decision Speed**: fast, moderate, deliberate
- **Concession Pattern**: early, gradual, late, minimal

#### 1.3 Session Initialization Response
```json
{
  "sessionId": "session-uuid",
  "scenarioId": "scenario-uuid",
  "status": "active",
  "startTime": "2025-06-04T15:04:47.321Z",
  "currentRound": 0,
  "metrics": {
    "totalRounds": 0,
    "totalTime": 0,
    "agreementReached": false,
    "strategicEffectiveness": 0,
    "communicationQuality": 0,
    "overallScore": 0
  },
  "aiPersonality": {
    "aggressiveness": 0.6,
    "flexibility": 0.7,
    "communicationStyle": "diplomatic"
  }
}
```

### Phase 2: Interactive Negotiation

#### 2.1 Making Negotiation Moves
**Endpoint**: `POST /api/negotiation-simulator/sessions/{sessionId}/moves`

**User Move Structure**:
```json
{
  "move": {
    "offer": {
      "price": 95000,
      "currency": "USD",
      "paymentTerms": "Net 15",
      "warranties": ["Standard warranties", "Performance guarantee"],
      "liabilities": ["Limited to contract value"],
      "terminationClauses": ["30-day notice period"],
      "customTerms": {
        "intellectualProperty": "Client retains all IP rights",
        "confidentiality": "5-year confidentiality period"
      }
    },
    "message": "We appreciate your initial proposal. Based on our analysis, we'd like to suggest these adjustments that create better value alignment for both parties.",
    "strategy": "collaborative",
    "reasoning": "Reducing price but improving payment terms to maintain cash flow while addressing key risk areas identified in our playbook analysis."
  }
}
```

#### 2.2 AI Response Generation
The AI analyzes the user's move and generates a realistic response based on:

- **Scenario Context**: Contract type, industry, and constraints
- **AI Personality**: Configured behavioral traits
- **Negotiation History**: Previous rounds and patterns
- **Strategic Analysis**: Playbook-derived insights
- **Market Context**: Industry-standard practices

**AI Response Structure**:
```json
{
  "session": {
    "sessionId": "session-uuid",
    "currentRound": 2,
    "status": "active",
    "metrics": {
      "totalRounds": 2,
      "strategicEffectiveness": 0.78,
      "communicationQuality": 0.85
    }
  },
  "aiResponse": {
    "offer": {
      "price": 97000,
      "paymentTerms": "Net 20",
      "liabilities": ["Limited to 1.5x contract value"],
      "customTerms": {
        "intellectualProperty": "Shared IP rights for derivative works"
      }
    },
    "message": "Thank you for your thoughtful proposal. We can work with the pricing direction, but need some adjustments on payment terms and liability allocation to make this work for our risk profile.",
    "reasoning": "Accepting price reduction shows flexibility, but maintaining better payment terms and liability protection aligns with our risk management requirements.",
    "strategy": "analytical",
    "confidence": 0.82,
    "nextMoveHint": "Consider offering early payment discounts to justify shorter payment terms",
    "riskAssessment": {
      "level": "medium",
      "factors": ["Payment terms risk", "Liability exposure"]
    }
  }
}
```

#### 2.3 Round-by-Round Progression

**Round Structure**:
```json
{
  "roundNumber": 3,
  "timestamp": "2025-06-04T15:15:22.445Z",
  "party": "user",
  "offer": { /* offer terms */ },
  "message": "Negotiation message",
  "strategy": "compromise",
  "reasoning": "Strategic reasoning",
  "responseTime": 45,
  "confidence": 0.9
}
```

**Negotiation Flow Example**:
1. **Opening**: User makes initial offer based on playbook recommendations
2. **Counter**: AI responds with strategic counter-offer
3. **Negotiation**: Back-and-forth refinement of terms
4. **Compromise**: Gradual convergence toward agreement
5. **Resolution**: Final agreement or negotiation breakdown

### Phase 3: Session Management

#### 3.1 Session Control Operations

**Pause Session**: `PUT /api/negotiation-simulator/sessions/{sessionId}/pause`
- Temporarily suspends active negotiation
- Preserves current state and metrics
- Allows user to review strategy

**Resume Session**: `PUT /api/negotiation-simulator/sessions/{sessionId}/resume`
- Reactivates paused session
- Continues from last round
- Maintains negotiation context

**Abandon Session**: `PUT /api/negotiation-simulator/sessions/{sessionId}/abandon`
- Ends session without completion
- Records partial metrics for learning
- Provides abandonment feedback

#### 3.2 Session Status Management

**Status Types**:
- `active`: Currently in progress
- `paused`: Temporarily suspended
- `completed`: Successfully finished
- `failed`: Negotiation breakdown
- `abandoned`: User-terminated
- `expired`: Time/round limit exceeded

### Phase 4: Performance Evaluation

#### 4.1 Real-Time Metrics
The system continuously tracks performance indicators:

```json
{
  "metrics": {
    "totalRounds": 6,
    "totalTime": 1847,
    "agreementReached": true,
    "dealValue": 96500,
    "concessionsMade": {
      "byUser": 3,
      "byAI": 4
    },
    "strategicEffectiveness": 0.82,
    "communicationQuality": 0.88,
    "timeEfficiency": 0.75,
    "overallScore": 0.82,
    "playbookAdherence": 0.79
  }
}
```

**Metric Definitions**:
- **Strategic Effectiveness**: How well user applied negotiation strategies
- **Communication Quality**: Clarity and professionalism of messages
- **Time Efficiency**: Speed of decision-making and responses
- **Playbook Adherence**: Alignment with recommended strategies
- **Overall Score**: Weighted combination of all metrics

#### 4.2 Comprehensive Evaluation
**Endpoint**: `POST /api/negotiation-simulator/sessions/{sessionId}/evaluate`

**Evaluation Response**:
```json
{
  "sessionId": "session-uuid",
  "overallScore": 0.82,
  "detailedMetrics": {
    "strategicEffectiveness": 0.82,
    "communicationQuality": 0.88,
    "timeEfficiency": 0.75,
    "playbookAdherence": 0.79,
    "riskManagement": 0.85
  },
  "strengths": [
    "Excellent use of leverage points from playbook analysis",
    "Strong communication style with clear reasoning",
    "Effective compromise timing and concession strategy",
    "Good risk assessment and mitigation approaches"
  ],
  "improvements": [
    "Could have pushed harder on liability limitations",
    "Missed opportunity to leverage IP ownership terms",
    "Payment terms negotiation could be more aggressive",
    "Consider using time pressure more strategically"
  ],
  "playbookAnalysis": {
    "strategiesUsed": [
      {
        "strategy": "Liability cap negotiation",
        "effectiveness": 0.95,
        "timing": "optimal",
        "outcome": "successful"
      },
      {
        "strategy": "Payment terms optimization",
        "effectiveness": 0.70,
        "timing": "early",
        "outcome": "partial_success"
      }
    ],
    "missedOpportunities": [
      "Could have leveraged service quality guarantees earlier",
      "Didn't emphasize intellectual property ownership advantages"
    ]
  },
  "recommendations": [
    "Practice more aggressive opening positions in future sessions",
    "Review playbook strategies for liability and IP negotiations",
    "Work on time management during complex term discussions",
    "Consider role-playing different personality types for variety"
  ],
  "nextSteps": [
    "Try the same scenario with different AI personality settings",
    "Practice with a more aggressive AI counterparty",
    "Focus on specific weak areas identified in this session"
  ]
}
```

## Advanced Features

### AI Personality Adaptation
The AI can adjust its behavior based on:
- User's negotiation style and effectiveness
- Session difficulty settings
- Real-time performance indicators
- Historical user preferences

### Scenario Complexity Scaling
- **Beginner**: Simple terms, cooperative AI, clear guidance
- **Intermediate**: Multiple variables, balanced AI, strategic hints
- **Expert**: Complex terms, challenging AI, minimal assistance

### Learning Integration
- **Playbook Correlation**: Direct connection to document analysis insights
- **Strategy Tracking**: Monitor which playbook recommendations work best
- **Skill Development**: Progressive difficulty based on performance
- **Knowledge Retention**: Spaced repetition of challenging concepts

## Session Analytics

### Individual Performance Tracking
- Session completion rates
- Average negotiation scores
- Improvement trends over time
- Strategy effectiveness patterns

### Organizational Insights
- Team negotiation capabilities
- Common skill gaps
- Training effectiveness
- Best practice identification

## Technical Implementation

### Credit Consumption
- **Session Creation**: 0 credits (scenario setup)
- **AI Moves**: 2 credits per AI response
- **Evaluation**: 1 credit for comprehensive analysis
- **Session Management**: 0 credits (pause/resume/abandon)

### Performance Optimization
- **Real-time Processing**: Sub-second AI response times
- **Session Persistence**: Automatic state saving
- **Scalable Architecture**: Concurrent session support
- **Caching Strategy**: Optimized for repeated interactions

### Security & Privacy
- **User Isolation**: Sessions scoped to organization
- **Data Protection**: Encrypted session storage
- **Access Control**: Creator-only session management
- **Audit Trail**: Complete interaction logging

## Best Practices

### For Users
1. **Preparation**: Review playbook analysis before starting
2. **Strategy**: Plan opening position and fallback options
3. **Communication**: Use clear, professional language
4. **Learning**: Focus on applying specific playbook recommendations
5. **Iteration**: Practice multiple sessions with different approaches

### For Organizations
1. **Training Programs**: Structured learning paths with progressive difficulty
2. **Performance Monitoring**: Regular assessment of team capabilities
3. **Knowledge Sharing**: Share successful strategies across teams
4. **Continuous Improvement**: Regular updates based on user feedback

## API Reference

### Core Session Endpoints

#### Session Management
```http
POST   /api/negotiation-simulator/sessions              # Create new session
GET    /api/negotiation-simulator/sessions              # List user sessions
GET    /api/negotiation-simulator/sessions/{id}         # Get specific session
PUT    /api/negotiation-simulator/sessions/{id}/pause   # Pause session
PUT    /api/negotiation-simulator/sessions/{id}/resume  # Resume session
PUT    /api/negotiation-simulator/sessions/{id}/abandon # Abandon session
```

#### Negotiation Interaction
```http
POST   /api/negotiation-simulator/sessions/{id}/moves     # Make negotiation move
POST   /api/negotiation-simulator/sessions/{id}/evaluate  # Get performance evaluation
```

#### Analytics & Reporting
```http
GET    /api/negotiation-simulator/analytics/overview      # Performance overview
GET    /api/negotiation-simulator/analytics/sessions      # Session analytics
```

### Sample API Calls

#### 1. Create Session
```bash
curl -X POST http://localhost:4000/api/negotiation-simulator/sessions \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "scenarioId": "scenario-uuid",
    "aiPersonality": {
      "aggressiveness": 0.6,
      "flexibility": 0.7,
      "communicationStyle": "diplomatic"
    }
  }'
```

#### 2. Make Negotiation Move
```bash
curl -X POST http://localhost:4000/api/negotiation-simulator/sessions/{sessionId}/moves \
  -H "Authorization: Bearer {token}" \
  -H "Content-Type: application/json" \
  -d '{
    "move": {
      "offer": {
        "price": 95000,
        "paymentTerms": "Net 15"
      },
      "message": "We propose these revised terms...",
      "strategy": "collaborative"
    }
  }'
```

#### 3. Evaluate Performance
```bash
curl -X POST http://localhost:4000/api/negotiation-simulator/sessions/{sessionId}/evaluate \
  -H "Authorization: Bearer {token}"
```

## Error Handling

### Common Error Scenarios

#### Session Not Found (404)
```json
{
  "statusCode": 404,
  "message": "Session with ID {sessionId} not found",
  "error": "Not Found"
}
```

#### Session Not Active (400)
```json
{
  "statusCode": 400,
  "message": "Session is not active. Current status: completed",
  "error": "Bad Request"
}
```

#### Invalid Move Data (400)
```json
{
  "statusCode": 400,
  "message": "Validation failed",
  "error": "Bad Request",
  "details": [
    {
      "field": "offer.price",
      "message": "Price must be a positive number"
    }
  ]
}
```

#### Insufficient Credits (402)
```json
{
  "statusCode": 402,
  "message": "Insufficient credits for AI negotiation move",
  "error": "Payment Required"
}
```

## Troubleshooting Guide

### Session Issues
- **Session won't start**: Verify scenario exists and is accessible
- **AI not responding**: Check credit balance and network connectivity
- **Session stuck**: Use pause/resume or abandon session
- **Performance issues**: Reduce session complexity or AI personality settings

### Integration Issues
- **Authentication failures**: Verify JWT token validity and permissions
- **Validation errors**: Check request format against API documentation
- **Rate limiting**: Implement proper request throttling
- **Timeout issues**: Increase client timeout settings for AI responses

## Conclusion

The Negotiation Session system transforms theoretical contract analysis into practical negotiation skills through interactive, AI-powered practice. By combining document-specific insights with realistic simulation, users develop real-world capabilities that directly improve business outcomes.

The system's comprehensive tracking, detailed feedback, and adaptive learning create a complete skill development environment that scales from individual practice to organizational training programs.

### Key Benefits
- **Practical Application**: Apply playbook strategies in realistic scenarios
- **Safe Learning Environment**: Practice without real-world consequences
- **Immediate Feedback**: Real-time performance metrics and guidance
- **Skill Development**: Progressive improvement through repeated practice
- **Business Impact**: Measurable improvement in negotiation outcomes
