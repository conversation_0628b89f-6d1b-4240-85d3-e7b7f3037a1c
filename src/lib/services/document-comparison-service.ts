import { AxiosError } from "axios"
import { apiClient } from "../config"

// Types for document comparison
export interface DocumentComparisonRequest {
  primaryDocumentId: string
  relatedDocumentIds: string[]
  documentType?: string
  documentTypes?: string[]
}

export interface DocumentRelationship {
  primaryDocument: string
  relatedDocument: string
  relationship: "amendment" | "supplement" | "replacement" | "reference" | "independent"
  description: string
}

export interface DocumentSetAnalysis {
  count: number
  documentTypes: string[]
  relationships: DocumentRelationship[]
}

export interface TypeSpecificFinding {
  documentType: string
  observation: string
  implication: string
}

export interface TypeSpecificAnalysis {
  dominantDocumentType: string
  findings: TypeSpecificFinding[]
}

export interface DocumentProvision {
  document: string
  content: string
  sectionReference: string
  conflicts: string
}

export interface KeyProvision {
  topic: string
  provisions: DocumentProvision[]
  significance: "low" | "medium" | "high"
  recommendation: string
}

export interface DocumentGap {
  area: string
  description: string
  recommendation: string
  priority: "low" | "medium" | "high"
}

export interface ComparisonSummary {
  overallCoherence: "low" | "medium" | "high"
  keyInsights: string[]
  recommendations: string[]
}

export interface DocumentComparisonResult {
  documentSet: DocumentSetAnalysis
  typeSpecificAnalysis: TypeSpecificAnalysis
  keyProvisions: KeyProvision[]
  gaps: DocumentGap[]
  summary: ComparisonSummary
}

// Types for enhanced document comparison
export interface DocumentMetadata {
  id: string
  title: string
  createdAt: string
  organizationId: string
  userId: string
}

export interface EnhancedComparisonRequest {
  documentA: string
  documentB: string
  type: "similarities" | "differences" | "both"
  includeVisualDiff: boolean
  includeSectionReferences: boolean
  documentAMetadata?: DocumentMetadata
  documentBMetadata?: DocumentMetadata
}

export interface DiffItem {
  type: "equal" | "delete" | "insert"
  text: string
}

export interface DocumentStats {
  length: number
}

export interface DiffSummary {
  addedLines: number
  removedLines: number
  modifiedLines: number
  totalChanges: number
}

export interface SectionContent {
  id: string
  title: string
  content: string
  lineRange: {
    start: number
    end: number
  }
}

export interface SectionDifference {
  original: string
  modified: string
  diffType: string
  lineNumber: number
}

export interface SectionDiff {
  sectionA: SectionContent
  sectionB: SectionContent
  differences: SectionDifference[]
}

export interface EnhancedComparisonResult {
  id: string
  diffs: DiffItem[]
  metadata: {
    timestamp: string
    documentStats: {
      documentA: DocumentStats
      documentB: DocumentStats
    }
    summary: DiffSummary
  }
  visualization?: {
    htmlDiff: string
  }
  sectionDiffs?: SectionDiff[]
}

export interface VersionComparisonRequest {
  documentId: string
  versionA: number
  versionB: number
  options: {
    includeVisualDiff: boolean
    includeSectionAnalysis: boolean
  }
}

export interface VersionComparisonResult extends EnhancedComparisonResult {
  versionInfo: {
    documentId: string
    beforeVersion: number
    afterVersion: number
    versionDelta: number
    beforeTimestamp: string
    afterTimestamp: string
  }
}

export interface ExportComparisonRequest {
  format: "pdf" | "docx" | "html"
  includeMetadata: boolean
  highlightChanges: boolean
  includeSummary: boolean
  sections?: {
    include: string[]
    highlightIntensity: boolean
  }
  customization?: {
    colors: {
      addition: string
      deletion: string
      modification: string
    }
    fonts: {
      family: string
      size: number
    }
  }
}

export interface ExecutiveSummaryResult {
  summary: string
}

// Basic Document Comparison
export interface BasicComparisonRequest {
  documentA: string
  documentB: string
  documentAId?: string
  documentBId?: string
}

export interface BasicDiffSegment {
  type: "equal" | "delete" | "insert"
  text: string
}

export interface BasicDocumentStats {
  length: number
  wordCount: number
  charCount: number
}

export interface BasicComparisonSummary {
  addedLines: number
  removedLines: number
  modifiedLines: number
  totalChanges: number
}

export interface BasicComparisonVisualization {
  htmlDiff: string
  colors: {
    addition: string
    deletion: string
    modification: string
  }
}

export interface BasicComparisonResponse {
  status: string
  data: {
    diffs: BasicDiffSegment[]
    metadata: {
      timestamp: string
      documentStats: {
        documentA: BasicDocumentStats
        documentB: BasicDocumentStats
      }
      summary: BasicComparisonSummary
    }
    visualization: BasicComparisonVisualization
  }
  metadata: {
    timestamp: string
    documentStats: {
      documentA: { length: number, id?: string }
      documentB: { length: number, id?: string }
    }
  }
}

class DocumentComparisonService {
  async compareMultipleDocuments(request: DocumentComparisonRequest): Promise<DocumentComparisonResult> {
    try {
      const response = await apiClient.post<DocumentComparisonResult>(
        `/documents/analyze-multiple`,
        request
      )
      return response.data
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error comparing documents:", error.message)
      }
      throw error
    }
  }

  async compareDocuments(request: EnhancedComparisonRequest): Promise<EnhancedComparisonResult> {
    try {
      const response = await apiClient.post<{ data: EnhancedComparisonResult }>(
        `/enhanced-comparison/documents`,
        request
      )
      return response.data.data
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error in enhanced document comparison:", error.message)
      }
      throw error
    }
  }

  async compareVersions(request: VersionComparisonRequest): Promise<VersionComparisonResult> {
    try {
      const response = await apiClient.post<{ data: VersionComparisonResult }>(
        `/enhanced-comparison/versions`,
        request
      )
      return response.data.data
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error comparing document versions:", error.message)
      }
      throw error
    }
  }

  async exportComparison(comparisonId: string, request: ExportComparisonRequest): Promise<Blob | string> {
    try {
      const config = {
        headers: {
          Accept: "application/octet-stream"
        },
        responseType: request.format !== "html" ? "blob" as const : "json" as const
      }

      const response = await apiClient.post<Blob | { html: string }>(
        `/enhanced-comparison/export/${comparisonId}`,
        request,
        config
      )

      if (request.format !== "html") {
        return response.data as Blob
      } else {
        return (response.data as { html: string }).html
      }
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error exporting comparison:", error.message)
      }
      throw error
    }
  }

  async getExecutiveSummary(comparisonId: string): Promise<ExecutiveSummaryResult> {
    try {
      const response = await apiClient.get<ExecutiveSummaryResult>(
        `/enhanced-comparison/summary/${comparisonId}`
      )
      return response.data
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error getting executive summary:", error.message)
      }
      throw error
    }
  }

  async getDocumentContent(documentId: string): Promise<string> {
    try {
      const response = await apiClient.get<{ content: string }>(
        `/documents/${documentId}/content`
      )
      return response.data.content
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error getting document content:", error.message)
      }
      throw error
    }
  }

  /**
   * Performs a basic comparison between two document contents.
   * Requires 'basic_comparison' feature.
   * @param request - The comparison request containing document contents and optional IDs
   * @returns The comparison result with diffs, metadata, and visualization
   */
  async compareBasic(request: BasicComparisonRequest): Promise<BasicComparisonResponse> {
    try {
      const response = await apiClient.post<BasicComparisonResponse>(
        `/documents/comparison`,
        request
      )
      return response.data
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error in basic document comparison:", error.message)
        // Handle specific error cases
        if (error.response?.status === 403) {
          throw new Error("Basic comparison feature not available in your subscription plan")
        }
      }
      throw error
    }
  }
}

export const documentComparisonService = new DocumentComparisonService()
