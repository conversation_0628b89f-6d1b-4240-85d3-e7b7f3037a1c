import { useMutation } from "@tanstack/react-query";
import { apiClient } from "@/lib/config";
import { AxiosError } from "axios";

interface ExecutiveSummary {
  summary: string;
  recommendations: string[];
  risks: Array<{
    level: 'low' | 'medium' | 'high';
    description: string;
  }>;
}

interface TechnicalDetail {
  key: string;
  value: string | number | boolean;
  description?: string;
  metadata?: Record<string, string>;
}

interface TechnicalAnalysis {
  summary: string;
  technicalDetails: Record<string, TechnicalDetail>;
  specifications: Array<{
    category: string;
    details: string;
  }>;
}

export class AIAnalysisError extends Error {
  constructor(
    message: string,
    public code: "ANALYSIS_FAILED" | "DOCUMENT_NOT_FOUND",
    public status?: number
  ) {
    super(message);
    this.name = "AIAnalysisError";
  }
}

// API functions
async function getExecutiveSummary(documentId: string): Promise<ExecutiveSummary> {
  try {
    const response = await apiClient.get<ExecutiveSummary>(
      `/ai-analysis/documents/${documentId}/executive-summary`
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new AIAnalysisError(
        error.response?.data?.message || error.message || 'Failed to generate executive summary',
        "ANALYSIS_FAILED",
        error.response?.status
      );
    }
    throw error;
  }
}

async function getTechnicalAnalysis(documentId: string): Promise<TechnicalAnalysis> {
  try {
    const response = await apiClient.get<TechnicalAnalysis>(
      `/ai-analysis/documents/${documentId}/technical-analysis`
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new AIAnalysisError(
        error.response?.data?.message || error.message || 'Failed to generate technical analysis',
        "ANALYSIS_FAILED",
        error.response?.status
      );
    }
    throw error;
  }
}

// Query hooks
export function useExecutiveSummary() {
  return useMutation<ExecutiveSummary, AIAnalysisError, string>({
    mutationFn: getExecutiveSummary,
  });
}

export function useTechnicalAnalysis() {
  return useMutation<TechnicalAnalysis, AIAnalysisError, string>({
    mutationFn: getTechnicalAnalysis,
  });
}