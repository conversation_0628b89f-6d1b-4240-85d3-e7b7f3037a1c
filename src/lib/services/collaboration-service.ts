import { apiClient } from '../config';
import type {
  CollaborationSession,
  SessionParticipant,
  DocumentOperation,
  WorkflowTemplate,
  WorkflowInstance,
  Task,
  CommentThread,
  Comment,
  Notification,
  TeamAnalytics,
  CollaborationMetrics
} from '@/lib/types/collaboration';

class CollaborationService {
  // Real-time Collaboration Session Management
  async createSession(documentId: string, settings?: {
    maxParticipants?: number;
    allowAnonymous?: boolean;
    recordChanges?: boolean;
  }): Promise<CollaborationSession> {
    const response = await apiClient.post<CollaborationSession>('/api/collaboration/sessions', {
      documentId,
      settings: {
        maxParticipants: 10,
        allowAnonymous: false,
        recordChanges: true,
        ...settings
      }
    });
    return response.data;
  }

  async getSession(sessionId: string): Promise<CollaborationSession> {
    const response = await apiClient.get<CollaborationSession>(`/api/collaboration/sessions/${sessionId}`);
    return response.data;
  }

  async joinSession(sessionId: string): Promise<SessionParticipant> {
    const response = await apiClient.post<SessionParticipant>(`/api/collaboration/sessions/${sessionId}/join`);
    return response.data;
  }

  async leaveSession(sessionId: string): Promise<void> {
    await apiClient.delete(`/api/collaboration/sessions/${sessionId}/leave`);
  }

  async endSession(sessionId: string): Promise<void> {
    await apiClient.delete(`/api/collaboration/sessions/${sessionId}`);
  }

  async getActiveSessions(documentId?: string): Promise<CollaborationSession[]> {
    const params = documentId ? { documentId } : {};
    const response = await apiClient.get<CollaborationSession[]>('/api/collaboration/sessions', { params });
    return response.data;
  }

  // Document Operations for Real-time Editing
  async applyOperation(sessionId: string, operation: Omit<DocumentOperation, 'id' | 'timestamp' | 'applied'>): Promise<DocumentOperation> {
    const response = await apiClient.post<DocumentOperation>(`/api/collaboration/sessions/${sessionId}/operations`, operation);
    return response.data;
  }

  async getOperations(sessionId: string, since?: string): Promise<DocumentOperation[]> {
    const params = since ? { since } : {};
    const response = await apiClient.get<DocumentOperation[]>(`/api/collaboration/sessions/${sessionId}/operations`, { params });
    return response.data;
  }

  // Workflow Management
  async createWorkflowTemplate(template: Omit<WorkflowTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<WorkflowTemplate> {
    const response = await apiClient.post<WorkflowTemplate>('/api/workflow/templates', template);
    return response.data;
  }

  async getWorkflowTemplates(): Promise<WorkflowTemplate[]> {
    const response = await apiClient.get<WorkflowTemplate[]>('/api/workflow/templates');
    return response.data;
  }

  async getWorkflowTemplate(templateId: string): Promise<WorkflowTemplate> {
    const response = await apiClient.get<WorkflowTemplate>(`/api/workflow/templates/${templateId}`);
    return response.data;
  }

  async updateWorkflowTemplate(templateId: string, updates: Partial<WorkflowTemplate>): Promise<WorkflowTemplate> {
    const response = await apiClient.put<WorkflowTemplate>(`/api/workflow/templates/${templateId}`, updates);
    return response.data;
  }

  async deleteWorkflowTemplate(templateId: string): Promise<void> {
    await apiClient.delete(`/api/workflow/templates/${templateId}`);
  }

  async createWorkflowInstance(templateId: string, documentId: string, metadata?: Record<string, any>): Promise<WorkflowInstance> {
    const response = await apiClient.post<WorkflowInstance>('/api/workflow/instances', {
      templateId,
      documentId,
      metadata
    });
    return response.data;
  }

  async getWorkflowInstances(documentId?: string): Promise<WorkflowInstance[]> {
    const params = documentId ? { documentId } : {};
    const response = await apiClient.get<WorkflowInstance[]>('/api/workflow/instances', { params });
    return response.data;
  }

  async getWorkflowInstance(instanceId: string): Promise<WorkflowInstance> {
    const response = await apiClient.get<WorkflowInstance>(`/api/workflow/instances/${instanceId}`);
    return response.data;
  }

  async executeWorkflowStep(instanceId: string, stepId: string, data?: Record<string, any>): Promise<WorkflowInstance> {
    const response = await apiClient.post<WorkflowInstance>(`/api/workflow/instances/${instanceId}/steps/${stepId}/execute`, data);
    return response.data;
  }

  async cancelWorkflowInstance(instanceId: string): Promise<WorkflowInstance> {
    const response = await apiClient.post<WorkflowInstance>(`/api/workflow/instances/${instanceId}/cancel`);
    return response.data;
  }

  // Task Management
  async getTasks(filters?: {
    status?: string;
    assigneeId?: string;
    documentId?: string;
    workflowInstanceId?: string;
  }): Promise<Task[]> {
    const response = await apiClient.get<Task[]>('/api/workflow/tasks', { params: filters });
    return response.data;
  }

  async getTask(taskId: string): Promise<Task> {
    const response = await apiClient.get<Task>(`/api/workflow/tasks/${taskId}`);
    return response.data;
  }

  async updateTaskStatus(taskId: string, status: string, comment?: string): Promise<Task> {
    const response = await apiClient.put<Task>(`/api/workflow/tasks/${taskId}/status`, {
      status,
      comment
    });
    return response.data;
  }

  async assignTask(taskId: string, assigneeId: string): Promise<Task> {
    const response = await apiClient.put<Task>(`/api/workflow/tasks/${taskId}/assign`, {
      assigneeId
    });
    return response.data;
  }

  async addTaskComment(taskId: string, content: string): Promise<Task> {
    const response = await apiClient.post<Task>(`/api/workflow/tasks/${taskId}/comments`, {
      content
    });
    return response.data;
  }

  // Threaded Discussions & Comments
  async createCommentThread(documentId: string, thread: {
    title?: string;
    type: 'discussion' | 'review' | 'approval' | 'question';
    anchor?: any;
    initialComment: string;
  }): Promise<CommentThread> {
    const response = await apiClient.post<CommentThread>('/api/comments/threads', {
      documentId,
      ...thread
    });
    return response.data;
  }

  async getCommentThreads(documentId: string): Promise<CommentThread[]> {
    const response = await apiClient.get<CommentThread[]>('/api/comments/threads', {
      params: { documentId }
    });
    return response.data;
  }

  async getCommentThread(threadId: string): Promise<CommentThread> {
    const response = await apiClient.get<CommentThread>(`/api/comments/threads/${threadId}`);
    return response.data;
  }

  async addComment(threadId: string, content: string, parentId?: string): Promise<Comment> {
    const response = await apiClient.post<Comment>(`/api/comments/threads/${threadId}/comments`, {
      content,
      parentId
    });
    return response.data;
  }

  async updateComment(commentId: string, content: string): Promise<Comment> {
    const response = await apiClient.put<Comment>(`/api/comments/${commentId}`, {
      content
    });
    return response.data;
  }

  async deleteComment(commentId: string): Promise<void> {
    await apiClient.delete(`/api/comments/${commentId}`);
  }

  async resolveThread(threadId: string): Promise<CommentThread> {
    const response = await apiClient.put<CommentThread>(`/api/comments/threads/${threadId}/resolve`);
    return response.data;
  }

  async reopenThread(threadId: string): Promise<CommentThread> {
    const response = await apiClient.put<CommentThread>(`/api/comments/threads/${threadId}/reopen`);
    return response.data;
  }

  async addReaction(commentId: string, emoji: string): Promise<Comment> {
    const response = await apiClient.post<Comment>(`/api/comments/${commentId}/reactions`, {
      emoji
    });
    return response.data;
  }

  async removeReaction(commentId: string, emoji: string): Promise<Comment> {
    const response = await apiClient.delete<Comment>(`/api/comments/${commentId}/reactions/${emoji}`);
    return response.data;
  }

  // Notifications
  async getNotifications(filters?: {
    isRead?: boolean;
    type?: string;
  }): Promise<Notification[]> {
    const response = await apiClient.get<Notification[]>('/api/notifications', { params: filters });
    return response.data;
  }

  async markNotificationAsRead(notificationId: string): Promise<Notification> {
    const response = await apiClient.put<Notification>(`/api/notifications/${notificationId}/read`);
    return response.data;
  }

  async markAllNotificationsAsRead(): Promise<void> {
    await apiClient.put('/api/notifications/read-all');
  }

  // Analytics & Metrics
  async getCollaborationMetrics(period?: {
    start: string;
    end: string;
  }): Promise<CollaborationMetrics> {
    const response = await apiClient.get<CollaborationMetrics>('/api/analytics/collaboration/metrics', {
      params: period
    });
    return response.data;
  }

  async getTeamAnalytics(period?: {
    start: string;
    end: string;
  }): Promise<TeamAnalytics> {
    const response = await apiClient.get<TeamAnalytics>('/api/analytics/collaboration/team', {
      params: period
    });
    return response.data;
  }

  // Document Sharing
  async shareDocument(documentId: string, shareData: {
    emails?: string[];
    permissions: {
      canView: boolean;
      canEdit: boolean;
      canComment: boolean;
      canShare: boolean;
    };
    expiresAt?: string;
    message?: string;
  }): Promise<{ shareId: string; shareUrl: string }> {
    const response = await apiClient.post<{ shareId: string; shareUrl: string }>(`/api/documents/${documentId}/share`, shareData);
    return response.data;
  }

  async getDocumentShares(documentId: string): Promise<any[]> {
    const response = await apiClient.get<any[]>(`/api/documents/${documentId}/shares`);
    return response.data;
  }

  async revokeDocumentShare(shareId: string): Promise<void> {
    await apiClient.delete(`/api/documents/shares/${shareId}`);
  }

  // Mentions
  async getMentions(): Promise<any[]> {
    const response = await apiClient.get<any[]>('/api/comments/mentions');
    return response.data;
  }

  async searchUsers(query: string): Promise<any[]> {
    const response = await apiClient.get<any[]>('/api/users/search', {
      params: { q: query }
    });
    return response.data;
  }
}

export const collaborationService = new CollaborationService();
