import { apiClient } from "@/lib/config";
import { AxiosError } from "axios";
import type {
  ResearchSession,
  ResearchQueryRequest,
  ResearchResponse,
  FollowUpRequest,
  FollowUpResponse,
  CreateSessionRequest,
  SessionsResponse,
  SessionListParams,
  AnalyticsParams,
  AnalyticsResponse,
  LegalResearchError,
  CreditCheckResult,
} from "@/lib/types/legal-research";

class LegalResearchService {
  private readonly baseUrl = "/legal-research-assistant";

  /**
   * Perform a comprehensive legal research query
   */
  async performResearch(request: ResearchQueryRequest): Promise<ResearchResponse> {
    try {
      const response = await apiClient.post<ResearchResponse>(
        `${this.baseUrl}/research/query`,
        request
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error, "Failed to perform research query");
    }
  }

  /**
   * Ask a follow-up question within an existing session
   */
  async askFollowUp(request: FollowUpRequest): Promise<FollowUpResponse> {
    try {
      const response = await apiClient.post<FollowUpResponse>(
        `${this.baseUrl}/research/follow-up`,
        request
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error, "Failed to ask follow-up question");
    }
  }

  /**
   * Create a new research session
   */
  async createSession(request: CreateSessionRequest): Promise<ResearchSession> {
    try {
      const response = await apiClient.post<{ success: boolean; data: ResearchSession }>(
        `${this.baseUrl}/sessions`,
        request
      );
      return response.data.data;
    } catch (error) {
      throw this.handleError(error, "Failed to create research session");
    }
  }

  /**
   * Get a specific research session with its query history
   */
  async getSession(sessionId: string): Promise<ResearchSession> {
    try {
      const response = await apiClient.get<{ success: boolean; data: ResearchSession }>(
        `${this.baseUrl}/sessions/${sessionId}`
      );
      return response.data.data;
    } catch (error) {
      throw this.handleError(error, "Failed to get research session");
    }
  }

  /**
   * List all research sessions for the organization
   */
  async listSessions(params?: SessionListParams): Promise<SessionsResponse> {
    try {
      const queryParams = new URLSearchParams();
      if (params?.page) queryParams.append('page', params.page.toString());
      if (params?.limit) queryParams.append('limit', params.limit.toString());
      if (params?.tags) queryParams.append('tags', params.tags);
      if (params?.search) queryParams.append('search', params.search);

      const url = `${this.baseUrl}/sessions${queryParams.toString() ? `?${queryParams.toString()}` : ''}`;
      const response = await apiClient.get<SessionsResponse>(url);
      return response.data;
    } catch (error) {
      throw this.handleError(error, "Failed to list research sessions");
    }
  }

  /**
   * Delete a research session
   */
  async deleteSession(sessionId: string): Promise<void> {
    try {
      await apiClient.delete(`${this.baseUrl}/sessions/${sessionId}`);
    } catch (error) {
      throw this.handleError(error, "Failed to delete research session");
    }
  }

  /**
   * Update a research session
   */
  async updateSession(sessionId: string, updates: Partial<CreateSessionRequest>): Promise<ResearchSession> {
    try {
      const response = await apiClient.patch<{ success: boolean; data: ResearchSession }>(
        `${this.baseUrl}/sessions/${sessionId}`,
        updates
      );
      return response.data.data;
    } catch (error) {
      throw this.handleError(error, "Failed to update research session");
    }
  }

  /**
   * Get research analytics (Pro+ tiers only)
   */
  async getAnalytics(params: AnalyticsParams): Promise<AnalyticsResponse> {
    try {
      const queryParams = new URLSearchParams();
      queryParams.append('period', params.period);
      if (params.groupBy) queryParams.append('groupBy', params.groupBy);

      const response = await apiClient.get<AnalyticsResponse>(
        `${this.baseUrl}/analytics?${queryParams.toString()}`
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error, "Failed to get research analytics");
    }
  }

  /**
   * Check if user has sufficient credits for a research operation
   */
  async checkCredits(featureName: string): Promise<CreditCheckResult> {
    try {
      const response = await apiClient.post<CreditCheckResult>(
        `/credits/check`,
        { featureName }
      );
      return response.data;
    } catch (error) {
      throw this.handleError(error, "Failed to check credits");
    }
  }

  /**
   * Use credits for a research operation
   */
  async useCredits(featureName: string, transactionId?: string): Promise<void> {
    try {
      await apiClient.post(
        `/credits/use`,
        { featureName, transactionId }
      );
    } catch (error) {
      throw this.handleError(error, "Failed to use credits");
    }
  }

  /**
   * Get available jurisdictions for filtering
   */
  async getJurisdictions(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ jurisdictions: string[] }>(
        `${this.baseUrl}/jurisdictions`
      );
      return response.data.jurisdictions;
    } catch (error) {
      // Return default jurisdictions if API fails
      return ['federal', 'california', 'new_york', 'texas', 'florida'];
    }
  }

  /**
   * Get available practice areas for filtering
   */
  async getPracticeAreas(): Promise<string[]> {
    try {
      const response = await apiClient.get<{ practiceAreas: string[] }>(
        `${this.baseUrl}/practice-areas`
      );
      return response.data.practiceAreas;
    } catch (error) {
      // Return default practice areas if API fails
      return ['privacy', 'contracts', 'employment', 'intellectual_property', 'corporate'];
    }
  }

  /**
   * Handle API errors and convert to LegalResearchError
   */
  private handleError(error: unknown, defaultMessage: string): LegalResearchError {
    if (error instanceof AxiosError) {
      const statusCode = error.response?.status;
      const errorData = error.response?.data;

      // Handle specific error codes
      if (statusCode === 402) {
        return new LegalResearchError(
          errorData?.error?.message || "Insufficient credits",
          "INSUFFICIENT_CREDITS",
          statusCode,
          errorData?.error?.details
        );
      }

      if (statusCode === 403) {
        return new LegalResearchError(
          errorData?.error?.message || "Feature not available in your subscription tier",
          "FEATURE_NOT_AVAILABLE",
          statusCode,
          errorData?.error?.details
        );
      }

      if (statusCode === 429) {
        return new LegalResearchError(
          errorData?.error?.message || "Rate limit exceeded",
          "RATE_LIMIT_EXCEEDED",
          statusCode,
          errorData?.error?.details
        );
      }

      if (statusCode === 400) {
        return new LegalResearchError(
          errorData?.error?.message || "Invalid request",
          "INVALID_REQUEST",
          statusCode,
          errorData?.error?.details
        );
      }

      return new LegalResearchError(
        errorData?.error?.message || error.message || defaultMessage,
        "API_ERROR",
        statusCode,
        errorData?.error?.details
      );
    }

    if (error instanceof Error) {
      return new LegalResearchError(error.message, "UNKNOWN_ERROR");
    }

    return new LegalResearchError(defaultMessage, "UNKNOWN_ERROR");
  }
}

// Export singleton instance
export const legalResearchService = new LegalResearchService();
export default legalResearchService;
