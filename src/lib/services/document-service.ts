import { AxiosError } from "axios";
import { apiClient, BASE_API_URL } from "../config";

import type { DocumentAnalysis } from "@/lib/types/analysis";
import type { AnalysisEvolution } from "@/lib/types/analysis-evolution";
import type { PrecedentAnalysisOptions, PrecedentAnalysisResult } from "./precedent-analysis-service";

export interface DocumentMetadata {
  content: string;
  id: string;
  name?: string;
  filename: string;
  title?: string;
  fileType?: string;
  uploadDate: string;
  size?: number;
  status?: string;
  metadata?: {
    pageCount?: number;
    title?: string;
    author?: string;
    organizationId?: string;
    progress?: number;
    estimatedCompletion?: string;
    processingPhase?: string;
  };
  organizationId?: string;
}

export interface PaginatedResponse<T> {
  items: T[];
  total: number;
  page: number;
  limit: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export class DocumentError extends Error {
  constructor(
    message: string,
    public code: "DOCUMENT_LIMIT_REACHED" | "UPLOAD_FAILED",
    public status?: number
  ) {
    super(message);
    this.name = "DocumentError";
  }
}

export interface DocumentSectionInput {
  title: string;
  content: string;
  sectionId: string;
}

export interface CompareSectionsPayload {
  documentSections: {
    [documentId: string]: DocumentSectionInput[];
  };
  comparisonType: "both" | "differences" | "similarities";
}

export interface ComparisonDifferences {
  [aspect: string]: {
    [documentId: string]: string;
  };
}

// Document Set Information
export interface DocumentRelationship {
  primaryDocument: string;
  relatedDocument: string;
  relationship: string;
  description: string;
}

export interface DocumentSet {
  count: number;
  documentTypes: string[];
  relationships: DocumentRelationship[];
}

// Key Provisions
export interface Provision {
  document: string;
  sectionReference: string;
  content: string;
  conflicts: string;
}

export interface KeyProvision {
  topic: string;
  significance: string;
  provisions: Provision[];
  recommendation?: string;
}

// Gaps
export interface Gap {
  area: string;
  priority: string;
  description: string;
  recommendation?: string;
}

// Type Specific Analysis
export interface Finding {
  documentType: string;
  observation: string;
  implication: string;
}

export interface TypeSpecificAnalysis {
  dominantDocumentType: string;
  findings: Finding[];
}

// Summary
export interface Summary {
  overallCoherence: string;
  keyInsights: string[];
  recommendations: string[];
}

export interface CompareSectionsResponse {
  documentSet: DocumentSet;
  summary: Summary;
  keyProvisions: KeyProvision[];
  gaps: Gap[];
  typeSpecificAnalysis: TypeSpecificAnalysis;
  // Keep these for backward compatibility
  differences?: ComparisonDifferences;
  similarities?: string[];
  recommendation?: string;
}

class DocumentService {
  async uploadDocument(formData: FormData): Promise<DocumentMetadata> {
    try {
      const response = await apiClient.post<DocumentMetadata>(
        `/documents/upload`,
        formData,
        {
          headers: {
            "Content-Type": "multipart/form-data",
          },
        }
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 403) {
          throw new DocumentError(
            error.response.data.message ||
              "You have reached your document limit",
            "DOCUMENT_LIMIT_REACHED",
            403
          );
        }
        throw new DocumentError(
          "Failed to upload document",
          "UPLOAD_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  async getDocuments(
    page: number = 1,
    limit: number = 10
  ): Promise<PaginatedResponse<DocumentMetadata>> {
    try {
      const response = await apiClient.get<PaginatedResponse<DocumentMetadata>>(
        `/documents`,
        { params: { page, limit } }
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error fetching documents:", error.message);
      }
      throw error;
    }
  }

  // async getAllDocuments(): Promise<DocumentMetadata[]> {
  //   try {
  //     const response = await this.getDocuments(1, 50);
  //     return response.items;
  //   } catch (error) {
  //     console.error("Error fetching all documents:", error);
  //     throw error;
  //   }
  // }

  async getDocument(id: string): Promise<DocumentMetadata> {
    try {
      const response = await apiClient.get<DocumentMetadata>(
        `/documents/${id}`
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error fetching document:", error.message);
      }
      throw error;
    }
  }

  /**
   * Returns the URL for rendering a document in its native format
   * @param documentId The ID of the document to render
   * @returns The URL to render the document
   */
  getDocumentRenderUrl(documentId: string): string {
    return `${BASE_API_URL}/documents/${documentId}/render`;
  }

  /**
   * Downloads a document with proper authentication
   * @param documentId The ID of the document to download
   * @param filename Optional filename to use for the downloaded file
   */
  async downloadDocument(documentId: string, filename?: string): Promise<void> {
    try {
      const response = await apiClient.get(`/documents/${documentId}/render`, {
        responseType: "blob",
      });

      // Create a blob URL for the document
      const blob = new Blob([response.data as BlobPart]);
      const url = window.URL.createObjectURL(blob);

      // Create a temporary link and trigger download
      const link = document.createElement("a");
      link.href = url;
      link.download = filename || `document-${documentId}`;
      document.body.appendChild(link);
      link.click();

      // Clean up
      window.URL.revokeObjectURL(url);
      document.body.removeChild(link);
    } catch (error) {
      console.error("Error downloading document:", error);
      throw error;
    }
  }

  /**
   * Fetches document content and returns it as a data URL for web viewing
   * @param documentId The ID of the document to fetch
   * @param mimeType The MIME type of the document
   * @returns A Promise that resolves to a data URL for the document content
   */
  async getDocumentDataUrl(
    documentId: string,
    mimeType: string
  ): Promise<string> {
    try {
      const response = await apiClient.get(`/documents/${documentId}/render`, {
        responseType: "blob",
      });

      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onloadend = () => resolve(reader.result as string);
        reader.onerror = reject;
        reader.readAsDataURL(
          new Blob([response.data as BlobPart], { type: mimeType })
        );
      });
    } catch (error) {
      console.error("Error fetching document content:", error);
      throw error;
    }
  }

  /**
   * Get document content as plain text
   * @param documentId The ID of the document
   * @returns The document content as plain text
   */
  async getDocumentContent(documentId: string): Promise<string> {
    try {
      // Use the /content endpoint and expect plain text
      const response = await apiClient.get<{ content: string }>(
        `/documents/${documentId}/content`,
        {
          responseType: "json", // Expect plain text response
        }
      );

      // The response data should be the plain text content
      return response.data.content;
    } catch (error) {
      console.error("Error fetching document content:", error);
      // Provide a more specific error message if possible
      if (error instanceof AxiosError && error.response) {
        // Use the imported AxiosError
        throw new Error(
          `Failed to fetch document content: ${error.response.status} ${error.response.statusText}`
        );
      }
      throw new Error(
        "Failed to fetch document content due to a network or server error."
      );
    }
  }

  async analyzeDocument(
    documentId: string,
    options?: { documentType?: string; query?: string }
  ): Promise<DocumentAnalysis> {
    try {
      const response = await apiClient.post<DocumentAnalysis>(
        `/documents/${documentId}/analyze`,
        options || {}
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error analyzing document:", error.message);
      }
      throw error;
    }
  }

  async redoAnalysis(
    documentId: string,
    options?: { documentType?: "CONTRACT"; forceNew?: boolean }
  ): Promise<DocumentAnalysis> {
    try {
      const response = await apiClient.post<DocumentAnalysis>(
        `/documents/${documentId}/analyze`,
        {
          documentType: options?.documentType || "CONTRACT",
          forceNew: options?.forceNew ?? true,
          ...options
        }
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error redoing document analysis:", error.message);
      }
      throw error;
    }
  }

  async getExistingAnalyses(documentId: string): Promise<DocumentAnalysis[]> {
    try {
      const response = await apiClient.get<DocumentAnalysis[]>(
        `/documents/${documentId}/analysis`
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        console.error("Error fetching existing analyses:", error.message);
      }
      throw error;
    }
  }

  async getAnalysisResults(
    documentId: string,
    options?: { latest?: boolean }
  ): Promise<DocumentAnalysis> {
    try {
      const queryParams = options?.latest ? "?latest=true" : "";
      const response = await apiClient.get<DocumentAnalysis>(
        `/documents/${documentId}/analysis${queryParams}`
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new Error(
          error.response?.data?.message || "Failed to retrieve analysis results"
        );
      }
      throw error;
    }
  }

  async getAnalysisEvolution(documentId: string): Promise<AnalysisEvolution> {
    try {
      const response = await apiClient.get<AnalysisEvolution>(
        `/documents/${documentId}/analysis/evolution`
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new Error(
          error.response?.data?.message || "Failed to get analysis evolution"
        );
      }
      throw error;
    }
  }

  async deleteDocument(documentId: string): Promise<void> {
    try {
      await apiClient.delete(`/documents/${documentId}`);
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new Error(
          error.response?.data?.message || "Failed to delete document"
        );
      }
      throw error;
    }
  }

  /**
   * Compares specific sections across multiple documents.
   * Requires 'advanced_analysis' subscription.
   * @param payload - The sections to compare from different documents.
   * @returns The comparison result.
   */
  async compareDocumentSections(
    payload: CompareSectionsPayload
  ): Promise<CompareSectionsResponse> {
    try {
      const response = await apiClient.post<CompareSectionsResponse>(
        "/documents/compare-sections",
        payload
      );
      // Consider adding specific error handling for subscription checks (e.g., 402/403 status)
      return response.data;
    } catch (error) {
      console.error("Error comparing document sections:", error);
      // Rethrow or handle specific errors (e.g., subscription required)
      throw error;
    }
  }

  /**
   * Analyzes precedents cited in a document
   * Requires 'precedent_analysis' feature
   * @param documentId The ID of the document to analyze
   * @param options Options for the precedent analysis
   * @returns Array of precedent analysis results
   */
  async analyzePrecedents(
    documentId: string,
    options?: PrecedentAnalysisOptions
  ): Promise<PrecedentAnalysisResult[]> {
    try {
      const response = await apiClient.post<PrecedentAnalysisResult[]>(
        `/documents/precedents/${documentId}/analyze`,
        options || {}
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        if (error.response?.status === 403) {
          throw new Error("Access to precedent analysis feature is required");
        }
        throw new Error(
          error.response?.data?.message || "Failed to analyze precedents"
        );
      }
      throw error;
    }
  }
}

export const documentService = new DocumentService();
