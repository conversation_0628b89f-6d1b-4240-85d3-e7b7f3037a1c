import { apiClient } from "../config"
import type {
  SavedSearch,
  CreateSavedSearchDto,
  UpdateSavedSearchDto,
  ExecuteSearchResult,
  ShareSearchDto,
} from "@/lib/types/document-organization"

class SavedSearchService {

  async createSavedSearch(searchData: CreateSavedSearchDto): Promise<SavedSearch> {
    try {
      const response = await apiClient.post<SavedSearch>('/document-organization/saved-searches', searchData)
      return response.data
    } catch (error) {
      console.error("Error creating saved search:", error)
      throw error
    }
  }

  async getSavedSearches(): Promise<SavedSearch[]> {
    try {
      const response = await apiClient.get<SavedSearch[]>('/document-organization/saved-searches')
      return response.data
    } catch (error) {
      console.error("Error fetching saved searches:", error)
      throw error
    }
  }

  async getSavedSearch(id: string): Promise<SavedSearch> {
    try {
      const response = await apiClient.get<SavedSearch>(`/document-organization/saved-searches/${id}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching saved search ${id}:`, error)
      throw error
    }
  }

  async updateSavedSearch(id: string, searchData: UpdateSavedSearchDto): Promise<SavedSearch> {
    try {
      const response = await apiClient.patch<SavedSearch>(`/document-organization/saved-searches/${id}`, searchData)
      return response.data
    } catch (error) {
      console.error(`Error updating saved search ${id}:`, error)
      throw error
    }
  }

  async deleteSavedSearch(id: string): Promise<void> {
    try {
      await apiClient.delete(`/document-organization/saved-searches/${id}`)
    } catch (error) {
      console.error(`Error deleting saved search ${id}:`, error)
      throw error
    }
  }

  async executeSavedSearch(id: string): Promise<ExecuteSearchResult> {
    try {
      const response = await apiClient.post<ExecuteSearchResult>(
        `/document-organization/saved-searches/${id}/execute`,
        {},
      )
      return response.data
    } catch (error) {
      console.error(`Error executing saved search ${id}:`, error)
      throw error
    }
  }

  async shareSavedSearch(id: string, shareData: ShareSearchDto): Promise<SavedSearch> {
    try {
      const response = await apiClient.post<SavedSearch>(
        `/document-organization/saved-searches/${id}/share`,
        shareData,
      )
      return response.data
    } catch (error) {
      console.error(`Error sharing saved search ${id}:`, error)
      throw error
    }
  }

  async unshareSavedSearch(id: string, shareData: ShareSearchDto): Promise<SavedSearch> {
    try {
      const response = await apiClient.post<SavedSearch>(
        `/document-organization/saved-searches/${id}/unshare`,
        shareData,
      )
      return response.data
    } catch (error) {
      console.error(`Error unsharing saved search ${id}:`, error)
      throw error
    }
  }
}

export const savedSearchService = new SavedSearchService()
