/**
 * Chat Negotiation Service
 * Bridges the chat interface with the existing negotiation simulator backend
 */

import { apiClient } from '@/lib/config';
import { chatService } from './chat-service';
import { negotiationSimulatorService } from './negotiation-simulator-service';
import type { 
  NegotiationSession, 
  StartSessionRequest, 
  MakeMoveRequest,
  NegotiationScenario 
} from '@/lib/types/negotiation-simulator';
import type { ChatSession, ChatMessage } from './chat-service';

export interface ChatNegotiationSession {
  id: string;
  scenarioId: string;
  status: 'active' | 'completed' | 'paused';
  currentRound: number;
  extractedTerms: {
    price?: number;
    currency?: string;
    terms?: string[];
  };
  relationshipMetrics: {
    trust: number;      // 0-100
    respect: number;    // 0-100
    pressure: number;   // 0-100
  };
  score: number;
  aiPersonality: {
    characterId: string;
    aggressiveness: number;     // 0.0 - 1.0
    flexibility: number;        // 0.0 - 1.0
    riskTolerance: number;      // 0.0 - 1.0
    communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
  };
  negotiationSessionId: string;
  totalMessages: number;
  aiResponseTime: number;
  createdAt: string;
  updatedAt: string;
  lastActivityAt: string;
}

export interface ChatMoveData {
  content: string;
  extractedData?: {
    offer?: {
      price?: number;
      currency?: string;
      terms?: string[];
    };
    strategy?: string;
    sentiment?: string;
    confidence?: number;
  };
  context?: {
    userConfidence?: number;   // 0.0 - 1.0
    timeSpent?: number;        // Time spent composing message (seconds)
    [key: string]: any;
  };
}

export interface AIResponseData {
  userMove: {
    content: string;
    extractedData: any;
    timestamp: string;
    processingTime: number;    // ms
  };
  aiResponse: {
    content: string;
    suggestions?: string[];    // Strategic suggestions
    timestamp: string;
    processingTime: number;
  };
  sessionUpdate: {
    currentRound: number;
    extractedTerms: any;
    relationshipMetrics: {
      trust: number;
      respect: number;
      pressure: number;
    };
    score: number;
    status: string;
  };
  creditsConsumed: number;     // Always 3 for chat moves
}

export interface DocumentNegotiationScenario {
  id: string;
  title: string;
  description: string;
  contractType: string;
  focusAreas: string[];
  negotiationPoints: Array<{
    id: string;
    category: string;
    issue: string;
    currentTerm: string;
    suggestedImprovement: string;
    riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
    priority: number;
    negotiationStrategy: string;
    fallbackOptions: string[];
  }>;
  difficulty: 'BEGINNER' | 'INTERMEDIATE' | 'ADVANCED';
  estimatedDuration: number;
  aiPersonalityRecommendation: {
    aggressiveness: number;
    flexibility: number;
    riskTolerance: number;
    communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
  };
}

export interface DocumentContext {
  contractType: string;
  contractTitle: string;
  analysisScore: number;
  riskLevel: 'LOW' | 'MEDIUM' | 'HIGH' | 'CRITICAL';
  totalDeviations: number;
  keyFindings: string[];
}

class ChatNegotiationService {
  
  /**
   * Start a new chat-based negotiation session
   */
  async startChatNegotiation(
    scenarioId: string,
    aiPersonality?: {
      characterId?: string;
      aggressiveness?: number;
      flexibility?: number;
      riskTolerance?: number;
      communicationStyle?: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
    }
  ): Promise<ChatNegotiationSession> {
    try {
      const requestBody = {
        scenarioId,
        aiPersonality: {
          characterId: aiPersonality?.characterId || 'default_character',
          aggressiveness: aiPersonality?.aggressiveness || 0.5,
          flexibility: aiPersonality?.flexibility || 0.7,
          riskTolerance: aiPersonality?.riskTolerance || 0.6,
          communicationStyle: aiPersonality?.communicationStyle || 'DIPLOMATIC'
        },
        metadata: {
          source: 'web_app',
          version: '1.0'
        }
      };

      const response = await apiClient.post<ChatNegotiationSession>(
        '/chat-negotiation/sessions',
        requestBody
      );

      return response.data;
    } catch (error) {
      console.error('Failed to start chat negotiation:', error);
      throw error;
    }
  }

  /**
   * Send a chat message and process it as a negotiation move
   */
  async sendChatMove(
    sessionId: string,
    moveData: ChatMoveData
  ): Promise<AIResponseData> {
    try {
      const requestBody = {
        content: moveData.content,
        extractedData: moveData.extractedData,
        context: moveData.context || {}
      };

      const response = await apiClient.post<AIResponseData>(
        `/chat-negotiation/sessions/${sessionId}/moves`,
        requestBody
      );

      return response.data;
    } catch (error) {
      console.error('Failed to send chat move:', error);
      throw error;
    }
  }

  /**
   * Generate AI response based on negotiation context
   */
  private async generateAIResponse(
    session: ChatNegotiationSession,
    userMove: ChatMoveData,
    negotiationSession: NegotiationSession
  ): Promise<AIResponseData> {
    try {
      // Call backend AI response generation
      const response = await apiClient.post<AIResponseData>(
        `/chat-negotiation/sessions/${session.id}/ai-response`,
        {
          userMove,
          negotiationContext: negotiationSession,
          currentMetrics: session.relationshipMetrics
        }
      );

      return response.data;
    } catch (error) {
      // Fallback to simple response if AI service fails
      console.warn('AI response generation failed, using fallback:', error);
      return {
        content: "That's an interesting point. Let me think about how we can make this work for both of us.",
        suggestions: [
          "What's most important to you in this deal?",
          "Are there other terms we should discuss?",
          "Can we find a middle ground?"
        ],
        relationshipUpdate: {
          trust: Math.max(0, Math.min(100, session.relationshipMetrics.trust + 2)),
          respect: Math.max(0, Math.min(100, session.relationshipMetrics.respect + 1)),
          pressure: Math.max(0, Math.min(100, session.relationshipMetrics.pressure - 1))
        },
        scoreUpdate: session.score + 0.1
      };
    }
  }

  /**
   * Get chat negotiation session details
   */
  async getChatNegotiationSession(sessionId: string): Promise<ChatNegotiationSession> {
    const response = await apiClient.get<ChatNegotiationSession>(
      `/chat-negotiation/sessions/${sessionId}`
    );
    return response.data;
  }

  /**
   * Update chat negotiation session
   */
  private async updateChatNegotiationSession(
    sessionId: string,
    updates: Partial<ChatNegotiationSession>
  ): Promise<ChatNegotiationSession> {
    const response = await apiClient.put<ChatNegotiationSession>(
      `/chat-negotiation/sessions/${sessionId}`,
      updates
    );
    return response.data;
  }

  /**
   * Get chat messages for a negotiation session
   */
  async getChatMessages(sessionId: string): Promise<ChatMessage[]> {
    const session = await this.getChatNegotiationSession(sessionId);
    return chatService.getMessages(session.chatSessionId);
  }

  /**
   * Get user's chat negotiation sessions
   */
  async getUserChatNegotiationSessions(): Promise<ChatNegotiationSession[]> {
    const response = await apiClient.get<ChatNegotiationSession[]>(
      '/chat-negotiation/sessions'
    );
    return response.data;
  }

  /**
   * Pause chat negotiation session
   */
  async pauseSession(sessionId: string): Promise<ChatNegotiationSession> {
    const session = await this.getChatNegotiationSession(sessionId);
    
    // Pause the underlying negotiation session
    await negotiationSimulatorService.pauseSession(session.negotiationSessionId);
    
    // Update chat negotiation session status
    return this.updateChatNegotiationSession(sessionId, { status: 'paused' });
  }

  /**
   * Resume chat negotiation session
   */
  async resumeSession(sessionId: string): Promise<ChatNegotiationSession> {
    const session = await this.getChatNegotiationSession(sessionId);
    
    // Resume the underlying negotiation session
    await negotiationSimulatorService.resumeSession(session.negotiationSessionId);
    
    // Update chat negotiation session status
    return this.updateChatNegotiationSession(sessionId, { status: 'active' });
  }

  /**
   * Complete chat negotiation session
   */
  async completeSession(sessionId: string): Promise<{
    session: ChatNegotiationSession;
    evaluation: any;
  }> {
    const session = await this.getChatNegotiationSession(sessionId);
    
    // Get final evaluation from negotiation simulator
    const evaluation = await negotiationSimulatorService.evaluateSession(
      session.negotiationSessionId
    );
    
    // Update session status
    const completedSession = await this.updateChatNegotiationSession(sessionId, { 
      status: 'completed' 
    });
    
    return {
      session: completedSession,
      evaluation
    };
  }

  /**
   * Extract structured data from natural language message
   */
  async extractDataFromMessage(
    message: string,
    context?: {
      scenarioType?: string;
      currentRound?: number;
      [key: string]: any;
    }
  ): Promise<{
    offer: {
      price?: number;
      currency?: string;
      terms?: string[];
    };
    strategy: string;
    sentiment: string;
    confidence: number;
    extractedEntities: string[];
    processingTime: number;
  }> {
    try {
      const response = await apiClient.post(
        '/chat-negotiation/extract-data',
        {
          message,
          context
        }
      );
      return response.data;
    } catch (error) {
      console.warn('Data extraction failed, using client-side fallback:', error);
      // Fallback to client-side extraction
      return {
        offer: {},
        strategy: 'collaborative',
        sentiment: 'neutral',
        confidence: 0.5,
        extractedEntities: [],
        processingTime: 0
      };
    }
  }

  /**
   * Get contextual suggestions for the current negotiation state
   */
  async getContextualSuggestions(
    sessionId: string,
    currentMessage?: string
  ): Promise<string[]> {
    try {
      const response = await apiClient.post<{ suggestions: string[] }>(
        `/chat-negotiation/sessions/${sessionId}/suggestions`,
        { currentMessage }
      );
      return response.data.suggestions;
    } catch (error) {
      console.warn('Failed to get contextual suggestions:', error);
      return [
        "What's most important to you in this deal?",
        "Can we find a middle ground?",
        "Are there other terms we should discuss?"
      ];
    }
  }

  /**
   * Create negotiation scenario from contract analysis
   */
  async createScenarioFromAnalysis(analysisId: string): Promise<DocumentNegotiationScenario> {
    try {
      const response = await apiClient.post<DocumentNegotiationScenario>(
        '/chat-negotiation/scenarios/from-analysis',
        { analysisId }
      );
      return response.data;
    } catch (error) {
      console.error('Failed to create scenario from analysis:', error);
      throw error;
    }
  }

  /**
   * Start chat negotiation session from document analysis
   */
  async startChatNegotiationFromDocument(
    analysisId: string,
    aiPersonality?: {
      characterId?: string;
      aggressiveness?: number;
      flexibility?: number;
      riskTolerance?: number;
      communicationStyle?: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
    }
  ): Promise<ChatNegotiationSession & { documentContext: DocumentContext }> {
    try {
      const response = await apiClient.post(
        '/chat-negotiation/sessions/from-document',
        {
          analysisId,
          aiPersonality: aiPersonality || {}
        }
      );
      return response.data;
    } catch (error) {
      console.error('Failed to start chat negotiation from document:', error);
      throw error;
    }
  }

  /**
   * Get document context for a chat negotiation session
   */
  async getDocumentContext(sessionId: string): Promise<{ context: string; hasDocumentContext: boolean }> {
    try {
      const response = await apiClient.get(
        `/chat-negotiation/sessions/${sessionId}/document-context`
      );
      return response.data;
    } catch (error) {
      console.error('Failed to get document context:', error);
      return { context: '', hasDocumentContext: false };
    }
  }
}

export const chatNegotiationService = new ChatNegotiationService();
