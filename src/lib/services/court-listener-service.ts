import { apiClient } from "../config";

export interface CitationLookupResponse {
	status: string;
	data: {
		citation: string;
		cases: Array<{
			case_name: string;
			case_name_short: string;
			case_name_full: string;
			date_filed: string;
			court: string;
			jurisdiction: string;
			judges: string;
			precedential_status: string;
			absolute_url: string;
			citations: Array<{
				volume: number;
				reporter: string;
				page: string;
				type: number;
			}>;
			docket: {
				docket_number: string;
				court_id: string;
				case_name: string;
				date_filed: string;
				date_argued?: string;
				date_reargued?: string;
				date_cert_granted?: string;
				date_terminated: string;
				entries: Array<{
					date_filed: string;
					description: string;
					document_number: string;
				}>;
			};
		}>;
	};
}

export interface CaseSearchResponse {
	status: string;
	data: {
		count: number;
		next: string | null;
		previous: string | null;
		results: Array<{
			absolute_url: string;
			attorney: string;
			caseName: string;
			caseNameFull: string;
			citation: string[];
			citeCount: number;
			cluster_id: number;
			court: string;
			court_citation_string: string;
			court_id: string;
			dateArgued: string | null;
			dateFiled: string;
			dateReargued: string | null;
			dateReargumentDenied: string | null;
			docketNumber: string;
			docket_id: number;
			judge: string;
			lexisCite: string;
			meta: {
				timestamp: string;
				date_created: string;
				score: {
					bm25: number;
				};
			};
			neutralCite: string;
			non_participating_judge_ids: number[];
			opinions: Array<{
				author_id: number | null;
				cites: number[];
				download_url: string | null;
				id: number;
				joined_by_ids: number[];
				local_path: string | null;
				meta: {
					timestamp: string;
					date_created: string;
				};
				ordering_key: number | null;
				per_curiam: boolean;
				sha1: string;
				snippet: string;
				type: string;
			}>;
			panel_ids: number[];
			panel_names: string[];
			posture: string;
			procedural_history: string;
			scdb_id: string;
			sibling_ids: number[];
			source: string;
			status: string;
			suitNature: string;
			syllabus: string;
		}>;
	};
}

export interface DocketResponse {
	status: string;
	data: {
		resource_uri: string;
		id: number;
		court: string;
		court_id: string;
		clusters: string[];
		absolute_url: string;
		date_created: string;
		date_modified: string;
		date_cert_granted: string | null;
		date_cert_denied: string | null;
		date_argued: string | null;
		date_reargued: string | null;
		date_reargument_denied: string | null;
		date_filed: string | null;
		date_terminated: string | null;
		case_name_short: string;
		case_name: string;
		case_name_full: string;
		docket_number: string;
	};
}

export interface DocketEntriesResponse {
	status: string;
	data: {
		results: Array<{
			id: number;
			docket: string;
			date_created: string;
			date_modified: string;
			date_filed: string;
			entry_number: string;
			description: string;
			pacer_doc_id: string;
			document_number: string;
			attachment_number: string | null;
			recap_documents: Array<{
				id: number;
				document_number: string;
				attachment_number: string | null;
				document_type: string;
				date_uploaded: string;
				filepath_local: string;
				pacer_doc_id: string;
				is_available: boolean;
				sha1: string;
			}>;
		}>;
	};
}

export interface ClusterResponse {
	status: string;
	data: {
		id: number;
		resource_uri: string;
		absolute_url: string;
		date_created: string;
		date_modified: string;
		case_name: string;
		case_name_short: string;
		case_name_full: string;
		slug: string;
		date_filed: string;
		docket: string;
		docket_id: number;
		sub_opinions: string[];
		citations: Array<{
			volume: number;
			reporter: string;
			page: string;
			type: number;
		}>;
		attorneys: string[];
		judges: string[];
		precedential_status: string;
		source: string;
		procedural_history: string;
		posture: string;
		syllabus: string;
		citation_count: number;
		scdb_id: string;
		scdb_decision_direction: string | null;
		scdb_votes_majority: number | null;
		scdb_votes_minority: number | null;
		citation_id: number | null;
		authorities: string[];
	};
}

export interface SearchParams {
	query?: string;
	citation?: string;
	case_name?: string;
	judge?: string;
	court?: string;
	jurisdiction?: string;
	filed_after?: string;
	filed_before?: string;
	page?: number;
	page_size?: number;
}

class CourtListenerService {

	async lookupCitation(citation: string): Promise<CitationLookupResponse> {
		try {
			const response = await apiClient.get<CitationLookupResponse>(
				`/court-listener/citation/${encodeURIComponent(citation)}`,
			);
			return response.data;
		} catch (error) {
			console.error("Error looking up citation:", error);
			throw error;
		}
	}

	// Update the searchCases method to better handle pagination
	async searchCases(params: SearchParams): Promise<CaseSearchResponse> {
		try {
			const queryParams = new URLSearchParams();

			// Add all non-undefined params to the query string
			Object.entries(params).forEach(([key, value]) => {
				if (value !== undefined && value !== "") {
					queryParams.append(key, value.toString());
				}
			});

			// Ensure page and page_size are included
			if (!params.page) {
				queryParams.append("page", "1");
			}

			if (!params.page_size) {
				queryParams.append("page_size", "10");
			}

			const response = await apiClient.get<CaseSearchResponse>(
				`/court-listener/search?${queryParams.toString()}`,
			);
			return response.data;
		} catch (error) {
			console.error("Error searching cases:", error);
			throw error;
		}
	}

	// New method to get docket information
	async getDocket(id: number | string): Promise<DocketResponse> {
		try {
			const response = await apiClient.get<DocketResponse>(
				`/court-listener/docket/${id}`,
			);
			return response.data;
		} catch (error) {
			console.error("Error getting docket:", error);
			throw error;
		}
	}

	// New method to get docket entries
	async getDocketEntries(id: number | string): Promise<DocketEntriesResponse> {
		try {
			const response = await apiClient.get<DocketEntriesResponse>(
				`/court-listener/docket/${id}/entries`,
			);
			return response.data;
		} catch (error) {
			console.error("Error getting docket entries:", error);
			throw error;
		}
	}

	// New method to get cluster information
	async getCluster(id: number | string): Promise<ClusterResponse> {
		try {
			const response = await apiClient.get<ClusterResponse>(
				`/court-listener/cluster/${id}`,
			);
			return response.data;
		} catch (error) {
			console.error("Error getting cluster:", error);
			throw error;
		}
	}
}

export const courtListenerService = new CourtListenerService();
