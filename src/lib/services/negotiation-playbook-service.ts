import { AxiosError } from "axios";
import { apiClient } from "@/lib/config";
import type {
	NegotiationPlaybook,
	BackendNegotiationPlaybook,
	GeneratePlaybookOptions,
	PlaybookApiResponse,
	PlaybookError,
	NegotiationPoint,
	StrategicRecommendation,
	RiskAssessment,
	NegotiationSimulation,
} from "@/lib/types/negotiation-playbook";
import type {
	SampleNegotiationPlaybook,
	SampleNegotiationPlaybookFilters,
	SamplePlaybooksResponse,
	SamplePlaybookStatsResponse,
	ClonedNegotiationPlaybook,
} from "@/lib/types/sample-playbooks";

export class NegotiationPlaybookError extends Error {
	constructor(
		message: string,
		public code: string,
		public statusCode?: number,
		public details?: unknown
	) {
		super(message);
		this.name = "NegotiationPlaybookError";
	}
}

class NegotiationPlaybookService {
	private baseUrl = "/documents";

	/**
	 * Transform backend response to frontend structure
	 */
	private transformBackendPlaybook(backend: BackendNegotiationPlaybook): NegotiationPlaybook {
		// Transform strategies to key negotiation points (sorted by priority)
		const keyNegotiationPoints: NegotiationPoint[] = backend.strategies
			.sort((a, b) => a.priority - b.priority) // Sort by priority (1 = highest)
			.map(strategy => ({
				title: strategy.section,
				description: strategy.recommendations.join('; '),
				currentPosition: "Current contract language requires review",
				recommendedPosition: strategy.alternativeLanguage || strategy.recommendations[0] || "",
				priority: strategy.priority === 1 ? 'HIGH' : strategy.priority <= 2 ? 'MEDIUM' : 'LOW',
				rationale: strategy.recommendations.join('; ')
			}));

		// Transform strategies to strategic recommendations (categorized by risk level)
		const strategicRecommendations: StrategicRecommendation[] = backend.strategies.flatMap(strategy =>
			strategy.recommendations.map(rec => ({
				category: strategy.riskLevel === 'high' ? 'LEVERAGE' :
						 strategy.riskLevel === 'medium' ? 'APPROACH' : 'PREPARATION',
				recommendation: rec,
				reasoning: `${strategy.section}: ${strategy.riskLevel} risk priority ${strategy.priority}`
			}))
		);

		// Create comprehensive risk assessment
		const riskAssessment: RiskAssessment = {
			overallRiskLevel: backend.strategies.some(s => s.riskLevel === 'high') ? 'HIGH' :
							 backend.strategies.some(s => s.riskLevel === 'medium') ? 'MEDIUM' : 'LOW',
			riskFactors: backend.strategies
				.filter(s => s.riskLevel === 'high' || s.riskLevel === 'medium')
				.sort((a, b) => a.priority - b.priority)
				.map(strategy => ({
					factor: strategy.section,
					severity: strategy.riskLevel.toUpperCase() as 'LOW' | 'MEDIUM' | 'HIGH',
					mitigation: strategy.recommendations[0] || "Review and negotiate terms"
				}))
		};

		// Transform simulation scenarios to negotiation simulations
		const negotiationSimulations: NegotiationSimulation[] = backend.strategies.flatMap(strategy =>
			strategy.simulationScenarios.map(scenario => ({
				scenario: `${strategy.section}: ${scenario.trigger}`,
				response: scenario.responseStrategy,
				expectedOutcome: scenario.expectedOutcome
			}))
		);

		return {
			id: backend._id,
			documentId: backend.documentId,
			executiveSummary: backend.overallAssessment,
			keyNegotiationPoints,
			strategicRecommendations,
			riskAssessment,
			negotiationSimulations,
			createdAt: backend.createdAt,
			updatedAt: backend.updatedAt
		};
	}

	/**
	 * Get existing negotiation playbook for a document
	 */
	async getPlaybook(documentId: string): Promise<NegotiationPlaybook | null> {
		try {
			const response = await apiClient.get<BackendNegotiationPlaybook>(
				`${this.baseUrl}/${documentId}/negotiation-playbook`
			);
			return this.transformBackendPlaybook(response.data);
		} catch (error) {
			if (error instanceof AxiosError) {
				if (error.response?.status === 404) {
					return null; // No playbook exists
				}

				const errorData = error.response?.data as PlaybookError;
				throw new NegotiationPlaybookError(
					errorData?.error?.message ||
						"Failed to retrieve negotiation playbook",
					errorData?.error?.code || "RETRIEVAL_FAILED",
					error.response?.status,
					errorData?.error?.details
				);
			}
			throw error;
		}
	}

	/**
	 * Generate a new negotiation playbook for a document
	 */
	async generatePlaybook(
		documentId: string,
		options: GeneratePlaybookOptions
	): Promise<NegotiationPlaybook> {
		try {
			const response = await apiClient.post<BackendNegotiationPlaybook>(
				`${this.baseUrl}/${documentId}/negotiation-playbook`,
				options
			);
			return this.transformBackendPlaybook(response.data);
		} catch (error) {
			if (error instanceof AxiosError) {
				const errorData = error.response?.data as PlaybookError;

				// Handle specific error cases
				if (error.response?.status === 403) {
					throw new NegotiationPlaybookError(
						"Negotiation playbook feature requires PRO subscription",
						"FEATURE_NOT_AVAILABLE",
						403,
						{ upgradeUrl: "/subscription/upgrade" }
					);
				}

				if (error.response?.status === 404) {
					throw new NegotiationPlaybookError(
						"Document not found or access denied",
						"DOCUMENT_NOT_FOUND",
						404
					);
				}

				if (error.response?.status === 400) {
					throw new NegotiationPlaybookError(
						errorData?.error?.message || "Invalid request parameters",
						"VALIDATION_ERROR",
						400,
						errorData?.error?.details
					);
				}

				throw new NegotiationPlaybookError(
					errorData?.error?.message ||
						"Failed to generate negotiation playbook",
					errorData?.error?.code || "GENERATION_FAILED",
					error.response?.status,
					errorData?.error?.details
				);
			}
			throw error;
		}
	}

	/**
	 * Check if a playbook exists for a document (lightweight check)
	 */
	async hasPlaybook(documentId: string): Promise<boolean> {
		try {
			const playbook = await this.getPlaybook(documentId);
			return playbook !== null;
		} catch {
			// If there's an error other than 404, we assume no playbook exists
			return false;
		}
	}

	// Sample Negotiation Playbooks
	async getSamplePlaybooks(
		filters?: SampleNegotiationPlaybookFilters
	): Promise<SampleNegotiationPlaybook[]> {
		try {
			const response = await apiClient.get<SampleNegotiationPlaybook[]>(
				"/sample-playbooks",
				{ params: filters }
			);
			return response.data;
		} catch (error) {
			if (error instanceof AxiosError) {
				const errorData = error.response?.data;
				throw new NegotiationPlaybookError(
					errorData?.message || "Failed to fetch sample negotiation playbooks",
					errorData?.code || "FETCH_FAILED",
					error.response?.status
				);
			}
			throw error;
		}
	}

	async getSamplePlaybook(id: string): Promise<SampleNegotiationPlaybook> {
		try {
			const response = await apiClient.get<SampleNegotiationPlaybook>(
				`/sample-playbooks/${id}`
			);
			return response.data;
		} catch (error) {
			if (error instanceof AxiosError) {
				const errorData = error.response?.data;
				throw new NegotiationPlaybookError(
					errorData?.message || "Failed to fetch sample negotiation playbook",
					errorData?.code || "FETCH_FAILED",
					error.response?.status
				);
			}
			throw error;
		}
	}

	async getSamplePlaybookStats(): Promise<SamplePlaybookStatsResponse> {
		try {
			const response = await apiClient.get<SamplePlaybookStatsResponse>(
				"/sample-playbooks/stats/overview"
			);
			return response.data;
		} catch (error) {
			if (error instanceof AxiosError) {
				const errorData = error.response?.data;
				throw new NegotiationPlaybookError(
					errorData?.message || "Failed to fetch sample playbook statistics",
					errorData?.code || "FETCH_FAILED",
					error.response?.status
				);
			}
			throw error;
		}
	}

	async cloneSamplePlaybook(
		id: string,
		documentId: string
	): Promise<ClonedNegotiationPlaybook> {
		try {
			const response = await apiClient.post<ClonedNegotiationPlaybook>(
				`/sample-playbooks/${id}/clone/${documentId}`
			);
			return response.data;
		} catch (error) {
			if (error instanceof AxiosError) {
				const errorData = error.response?.data;

				// Handle specific error cases
				if (error.response?.status === 403) {
					throw new NegotiationPlaybookError(
						"Negotiation playbook feature requires PRO subscription",
						"FEATURE_NOT_AVAILABLE",
						403,
						{ upgradeUrl: "/subscription/upgrade" }
					);
				}

				if (error.response?.status === 404) {
					throw new NegotiationPlaybookError(
						"Sample playbook or document not found",
						"NOT_FOUND",
						404
					);
				}

				throw new NegotiationPlaybookError(
					errorData?.message || "Failed to clone sample negotiation playbook",
					errorData?.code || "CLONE_FAILED",
					error.response?.status
				);
			}
			throw error;
		}
	}
}

// Export singleton instance
export const negotiationPlaybookService = new NegotiationPlaybookService();
