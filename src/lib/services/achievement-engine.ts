/**
 * Achievement Rule Engine
 * Handles achievement checking and unlocking logic
 */

import { Achievement, AchievementRequirement } from './gamification-service';

export interface SessionData {
  sessionId: string;
  userId: string;
  characterId: string;
  moves: NegotiationMove[];
  finalScore: number;
  duration: number;
  outcome: 'success' | 'failure' | 'abandoned';
  metrics: SessionMetrics;
  startedAt: Date;
  completedAt?: Date;
}

export interface NegotiationMove {
  id: string;
  round: number;
  strategy: string;
  message: string;
  offer: any;
  aiResponse: any;
  timestamp: Date;
  effectiveness: number;
}

export interface SessionMetrics {
  communicationEffectiveness: number;
  strategicThinking: number;
  flexibilityScore: number;
  timeManagement: number;
  relationshipBuilding: number;
  dealValue: number;
  concessionEfficiency: number;
}

export interface UserStats {
  totalSessions: number;
  completedSessions: number;
  averageScore: number;
  winRate: number;
  currentStreak: number;
  bestStreak: number;
  totalXP: number;
  level: number;
  characterRelationships: Record<string, any>;
  recentSessions: SessionData[];
}

export class AchievementEngine {
  private achievements: Achievement[] = [];

  constructor(achievements: Achievement[]) {
    this.achievements = achievements;
  }

  /**
   * Check which achievements should be unlocked based on session data
   */
  async checkAchievements(
    sessionData: SessionData,
    userStats: UserStats,
    unlockedAchievements: string[]
  ): Promise<Achievement[]> {
    const newlyUnlocked: Achievement[] = [];

    for (const achievement of this.achievements) {
      // Skip if already unlocked
      if (unlockedAchievements.includes(achievement.id)) {
        continue;
      }

      // Skip if not active
      if (!achievement.isActive) {
        continue;
      }

      // Check if requirements are met
      if (await this.checkRequirement(achievement.requirements, sessionData, userStats)) {
        newlyUnlocked.push(achievement);
      }
    }

    return newlyUnlocked;
  }

  /**
   * Check if a specific achievement requirement is met
   */
  private async checkRequirement(
    requirement: AchievementRequirement,
    sessionData: SessionData,
    userStats: UserStats
  ): Promise<boolean> {
    switch (requirement.type) {
      case 'session_completion':
        return this.checkSessionCompletion(requirement.conditions, sessionData, userStats);
      
      case 'performance_streak':
        return this.checkPerformanceStreak(requirement.conditions, sessionData, userStats);
      
      case 'total_sessions':
        return this.checkTotalSessions(requirement.conditions, userStats);
      
      case 'relationship_milestone':
        return this.checkRelationshipMilestone(requirement.conditions, sessionData, userStats);
      
      default:
        return false;
    }
  }

  /**
   * Check session completion achievements
   */
  private checkSessionCompletion(
    conditions: Record<string, any>,
    sessionData: SessionData,
    userStats: UserStats
  ): boolean {
    // First session completion
    if (conditions.firstSession && userStats.completedSessions === 1) {
      return true;
    }

    // Score threshold
    if (conditions.minScore && sessionData.finalScore >= conditions.minScore) {
      return true;
    }

    // Speed achievements (complete in X rounds or less)
    if (conditions.maxRounds && sessionData.moves.length <= conditions.maxRounds) {
      return true;
    }

    // Perfect score
    if (conditions.perfectScore && sessionData.finalScore >= 10.0) {
      return true;
    }

    // Specific character completion
    if (conditions.characterId && sessionData.characterId === conditions.characterId) {
      return true;
    }

    // Difficulty achievements
    if (conditions.minDifficulty) {
      // Would need character difficulty from sessionData
      return true; // Placeholder
    }

    // Win-win achievements (both parties satisfied)
    if (conditions.winWin && 
        sessionData.metrics.relationshipBuilding >= 8.0 && 
        sessionData.finalScore >= 8.0) {
      return true;
    }

    // Communication excellence
    if (conditions.communicationExcellence && 
        sessionData.metrics.communicationEffectiveness >= 9.0) {
      return true;
    }

    return false;
  }

  /**
   * Check performance streak achievements
   */
  private checkPerformanceStreak(
    conditions: Record<string, any>,
    sessionData: SessionData,
    userStats: UserStats
  ): boolean {
    // Win streak
    if (conditions.winStreak && userStats.currentStreak >= conditions.winStreak) {
      return true;
    }

    // Score streak (consecutive sessions above threshold)
    if (conditions.scoreStreak && conditions.minScore) {
      const recentSessions = userStats.recentSessions
        .slice(0, conditions.scoreStreak)
        .reverse(); // Most recent first
      
      if (recentSessions.length >= conditions.scoreStreak) {
        return recentSessions.every(session => session.finalScore >= conditions.minScore);
      }
    }

    // Improvement streak (each session better than the last)
    if (conditions.improvementStreak) {
      const recentSessions = userStats.recentSessions
        .slice(0, conditions.improvementStreak)
        .reverse();
      
      if (recentSessions.length >= conditions.improvementStreak) {
        for (let i = 1; i < recentSessions.length; i++) {
          if (recentSessions[i].finalScore <= recentSessions[i - 1].finalScore) {
            return false;
          }
        }
        return true;
      }
    }

    return false;
  }

  /**
   * Check total sessions achievements
   */
  private checkTotalSessions(
    conditions: Record<string, any>,
    userStats: UserStats
  ): boolean {
    if (conditions.count && userStats.completedSessions >= conditions.count) {
      return true;
    }

    return false;
  }

  /**
   * Check relationship milestone achievements
   */
  private checkRelationshipMilestone(
    conditions: Record<string, any>,
    sessionData: SessionData,
    userStats: UserStats
  ): boolean {
    const relationship = userStats.characterRelationships[sessionData.characterId];
    
    if (!relationship) return false;

    // Trust level achievements
    if (conditions.trustLevel && relationship.trustLevel >= conditions.trustLevel) {
      return true;
    }

    // Respect level achievements
    if (conditions.respectLevel && relationship.respectLevel >= conditions.respectLevel) {
      return true;
    }

    // Total interactions with character
    if (conditions.totalInteractions && 
        relationship.totalInteractions >= conditions.totalInteractions) {
      return true;
    }

    // Relationship status achievements
    if (conditions.relationshipStatus && 
        relationship.relationshipStatus === conditions.relationshipStatus) {
      return true;
    }

    return false;
  }

  /**
   * Get achievement progress for a user
   */
  async getAchievementProgress(
    userStats: UserStats,
    unlockedAchievements: string[]
  ): Promise<Record<string, any>> {
    const progress: Record<string, any> = {};

    for (const achievement of this.achievements) {
      if (unlockedAchievements.includes(achievement.id)) {
        progress[achievement.id] = { completed: true, progress: 100 };
        continue;
      }

      // Calculate progress based on achievement type
      const progressData = await this.calculateProgress(achievement, userStats);
      progress[achievement.id] = progressData;
    }

    return progress;
  }

  /**
   * Calculate progress towards an achievement
   */
  private async calculateProgress(
    achievement: Achievement,
    userStats: UserStats
  ): Promise<{ completed: boolean; progress: number; current?: number; target?: number }> {
    const { type, conditions } = achievement.requirements;

    switch (type) {
      case 'total_sessions':
        const target = conditions.count || 1;
        const current = userStats.completedSessions;
        return {
          completed: current >= target,
          progress: Math.min((current / target) * 100, 100),
          current,
          target
        };

      case 'performance_streak':
        if (conditions.winStreak) {
          const target = conditions.winStreak;
          const current = userStats.currentStreak;
          return {
            completed: current >= target,
            progress: Math.min((current / target) * 100, 100),
            current,
            target
          };
        }
        break;

      case 'relationship_milestone':
        if (conditions.characterId && conditions.trustLevel) {
          const relationship = userStats.characterRelationships[conditions.characterId];
          const target = conditions.trustLevel;
          const current = relationship?.trustLevel || 0;
          return {
            completed: current >= target,
            progress: Math.min((current / target) * 100, 100),
            current,
            target
          };
        }
        break;

      default:
        return { completed: false, progress: 0 };
    }

    return { completed: false, progress: 0 };
  }

  /**
   * Get recommended next achievements for a user
   */
  getRecommendedAchievements(
    userStats: UserStats,
    unlockedAchievements: string[],
    limit: number = 5
  ): Achievement[] {
    const available = this.achievements.filter(
      achievement => 
        !unlockedAchievements.includes(achievement.id) && 
        achievement.isActive
    );

    // Sort by difficulty/progress and return top recommendations
    return available
      .sort((a, b) => {
        // Prioritize achievements that are closer to completion
        // This would need progress calculation
        return 0; // Placeholder
      })
      .slice(0, limit);
  }
}

// Default achievement definitions
export const DEFAULT_ACHIEVEMENTS: Partial<Achievement>[] = [
  {
    id: 'first_steps',
    title: 'First Steps',
    description: 'Complete your first negotiation session',
    badge: '🎯',
    rarity: 'common',
    category: 'milestone',
    requirements: {
      type: 'session_completion',
      conditions: { firstSession: true }
    },
    rewards: { xp: 100 }
  },
  {
    id: 'speed_demon',
    title: 'Speed Demon',
    description: 'Close a deal in 3 rounds or less',
    badge: '⚡',
    rarity: 'rare',
    category: 'performance',
    requirements: {
      type: 'session_completion',
      conditions: { maxRounds: 3, minScore: 7.0 }
    },
    rewards: { xp: 250 }
  },
  {
    id: 'win_win_master',
    title: 'Win-Win Master',
    description: 'Achieve mutual satisfaction (8.0+ score with 8.0+ relationship)',
    badge: '🤝',
    rarity: 'epic',
    category: 'relationship',
    requirements: {
      type: 'session_completion',
      conditions: { winWin: true }
    },
    rewards: { xp: 500 }
  },
  {
    id: 'veteran_negotiator',
    title: 'Veteran Negotiator',
    description: 'Complete 50 negotiation sessions',
    badge: '🏆',
    rarity: 'legendary',
    category: 'milestone',
    requirements: {
      type: 'total_sessions',
      conditions: { count: 50 }
    },
    rewards: { xp: 1000, unlocks: ['expert_scenarios'] }
  },
  {
    id: 'streak_master',
    title: 'Streak Master',
    description: 'Win 10 negotiations in a row',
    badge: '🔥',
    rarity: 'epic',
    category: 'performance',
    requirements: {
      type: 'performance_streak',
      conditions: { winStreak: 10 }
    },
    rewards: { xp: 750 }
  }
];

export const achievementEngine = new AchievementEngine([]);
