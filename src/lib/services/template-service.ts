import { useMutation, useQuery } from "@tanstack/react-query";
import { getStoredAuth } from "@/lib/auth/auth-service";
import { apiClient } from "@/lib/config";
import { AxiosError } from "axios";

interface Template {
  id: string;
  name: string;
  description?: string;
  schema: {
    fields: Array<{
      name: string;
      type: string;
      required: boolean;
    }>;
    layout: Record<string, unknown>;
  };
  createdAt: string;
  updatedAt: string;
}

interface CreateTemplateDto {
  name: string;
  description?: string;
  schema: {
    fields: Array<{
      name: string;
      type: string;
      required: boolean;
    }>;
    layout: Record<string, unknown>;
  };
}

interface AnnotationSchema {
  name: string;
  fields: Array<{
    name: string;
    type: 'text' | 'number' | 'date' | 'select';
    required: boolean;
    options?: string[];
  }>;
}

interface CreateAnnotationDto {
  documentId: string;
  schemaId: string;
  data: Record<string, unknown>;
}

export class TemplateError extends Error {
  constructor(
    message: string,
    public code: "CREATE_FAILED" | "FETCH_FAILED" | "INVALID_SCHEMA",
    public status?: number
  ) {
    super(message);
    this.name = "TemplateError";
  }
}

function getAuthHeaders() {
  const { accessToken, organizationId } = getStoredAuth();
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (accessToken) {
    headers["Authorization"] = `Bearer ${accessToken}`;
  }
  
  if (organizationId) {
    headers["X-Tenant-Id"] = organizationId;
  }

  return headers;
}

// API functions
async function createTemplate(data: CreateTemplateDto): Promise<Template> {
  try {
    const response = await apiClient.post<Template>(
      `/report-templates`,
      data,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new TemplateError(
        error.response?.data?.message || error.message || 'Failed to create template',
        "CREATE_FAILED",
        error.response?.status
      );
    }
    throw error;
  }
}

async function getTemplates(): Promise<Template[]> {
  try {
    const response = await apiClient.get<Template[] | { templates: Template[] }>(
      `/report-templates`,
      { headers: getAuthHeaders() }
    );
    // Ensure we always return an array, even if empty
    const data = response.data;
    return Array.isArray(data) ? data : data.templates || [];
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new TemplateError(
        error.response?.data?.message || error.message || 'Failed to fetch templates',
        "FETCH_FAILED",
        error.response?.status
      );
    }
    throw error;
  }
}

async function createAnnotationSchema(schema: AnnotationSchema): Promise<void> {
  try {
    await apiClient.post<void>(
      `/annotations/schemas`,
      schema,
      { headers: getAuthHeaders() }
    );
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new TemplateError(
        error.response?.data?.message || error.message || 'Failed to create annotation schema',
        "INVALID_SCHEMA",
        error.response?.status
      );
    }
    throw error;
  }
}

async function createAnnotation(data: CreateAnnotationDto): Promise<void> {
  try {
    await apiClient.post<void>(
      `/annotations`,
      data,
      { headers: getAuthHeaders() }
    );
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new TemplateError(
        error.response?.data?.message || error.message || 'Failed to create annotation',
        "CREATE_FAILED",
        error.response?.status
      );
    }
    throw error;
  }
}

// Query hooks
export function useTemplates() {
  return useQuery<Template[], TemplateError>({
    queryKey: ['templates'],
    queryFn: getTemplates,
  });
}

export function useCreateTemplate() {
  return useMutation<Template, TemplateError, CreateTemplateDto>({
    mutationFn: createTemplate,
  });
}

export function useCreateAnnotationSchema() {
  return useMutation<void, TemplateError, AnnotationSchema>({
    mutationFn: createAnnotationSchema,
  });
}

export function useCreateAnnotation() {
  return useMutation<void, TemplateError, CreateAnnotationDto>({
    mutationFn: createAnnotation,
  });
}