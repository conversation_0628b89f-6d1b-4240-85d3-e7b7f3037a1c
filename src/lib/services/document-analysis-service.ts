import { useMutation, useQuery, type Query } from "@tanstack/react-query";
import { apiClient, BASE_API_URL } from "@/lib/config";
import { AxiosError } from "axios";

export interface AnalysisOptions {
  includeAnnotations?: boolean;
  generateReport?: boolean;
}

export interface AnalysisResult {
  status: 'queued' | 'processing' | 'completed' | 'failed';
  analysis?: {
    text: string;
    segments: string[];
    metrics: Record<string, unknown>;
  };
  report?: {
    jobId: string;
    downloadUrl: string;
    estimatedCompletion: string;
  };
  error?: string;
}

export interface ProcessingStatus {
  status: 'queued' | 'processing' | 'completed' | 'failed';
  currentStage: 'upload' | 'preprocessing' | 'analysis' | 'annotation' | 'report';
  progress: number;
  error?: string;
  result?: {
    reportUrl?: string;
    analysis?: {
      text: string;
      segments: string[];
      metrics: Record<string, unknown>;
    };
  };
}

export class AnalysisError extends Error {
  constructor(
    message: string,
    public code: "ANALYSIS_FAILED" | "STATUS_CHECK_FAILED" | "INVALID_DOCUMENT",
    public status?: number
  ) {
    super(message);
    this.name = "AnalysisError";
  }
}

// API functions
async function analyze(documentId: string, options: AnalysisOptions): Promise<AnalysisResult> {
  try {
    const response = await apiClient.post<AnalysisResult>(
      `/documents/${documentId}/analyze`,
      options
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new AnalysisError(
        error.response?.data?.message || error.message || 'Analysis failed',
        "ANALYSIS_FAILED",
        error.response?.status
      );
    }
    throw error;
  }
}

async function getAnalysisStatus(documentId: string): Promise<ProcessingStatus> {
  try {
    const response = await apiClient.get<ProcessingStatus>(
      `/documents/${documentId}/analysis-status`
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new AnalysisError(
        error.response?.data?.message || error.message || 'Failed to check status',
        "STATUS_CHECK_FAILED",
        error.response?.status
      );
    }
    throw error;
  }
}

// WebSocket setup for real-time updates
const statusSubscriptions = new Map<string, Set<(status: ProcessingStatus) => void>>();

function setupWebSocket(documentId: string) {
  const wsUrl = BASE_API_URL.replace('http', 'ws');
  const ws = new WebSocket(`${wsUrl}/documents/${documentId}/analysis-ws`);
  
  ws.onmessage = (event) => {
    const status = JSON.parse(event.data) as ProcessingStatus;
    const callbacks = statusSubscriptions.get(documentId);
    callbacks?.forEach(callback => callback(status));
  };

  return () => ws.close();
}

// React Query hooks
export function useAnalyzeDocument() {
  return useMutation<AnalysisResult, AnalysisError, { documentId: string; options: AnalysisOptions }>({
    mutationFn: ({ documentId, options }) => analyze(documentId, options),
  });
}

export function useAnalysisStatus(documentId: string, pollInterval = 5000) {
  return useQuery<ProcessingStatus, AnalysisError>({
    queryKey: ['analysis', documentId],
    queryFn: () => getAnalysisStatus(documentId),
    enabled: !!documentId,
    refetchInterval: (query: Query<ProcessingStatus, AnalysisError>) => {
      const data = query.state.data;
      return data && (data.status === 'completed' || data.status === 'failed') 
        ? false 
        : pollInterval;
    },
    refetchOnWindowFocus: true,
    staleTime: 0,
  });
}

export function subscribeToStatus(
  documentId: string,
  callback: (status: ProcessingStatus) => void
): () => void {
  if (!statusSubscriptions.has(documentId)) {
    statusSubscriptions.set(documentId, new Set());
  }

  const callbacks = statusSubscriptions.get(documentId);
  if (callbacks) {
    callbacks.add(callback);
  }

  const cleanup = setupWebSocket(documentId);

  return () => {
    const cbs = statusSubscriptions.get(documentId);
    if (cbs) {
      cbs.delete(callback);
      if (cbs.size === 0) {
        statusSubscriptions.delete(documentId);
        cleanup();
      }
    }
  };
}