import { apiClient } from "../config";
import { 
  CreditBalance, 
  CreditTransaction, 
  CreditPackage, 
  FeatureCost,
  CREDIT_PACKAGES,
  FEATURE_COSTS 
} from "../types/subscription";

export interface CreditUsageRequest {
  organizationId: string;
  featureName: string;
  transactionId?: string;
}

export interface CreditCheckResponse {
  hasCredits: boolean;
  currentBalance: number;
  requiredCredits: number;
  featureCost: FeatureCost;
}

export interface CreditHistoryParams {
  limit?: number;
  offset?: number;
  type?: 'allocation' | 'purchase' | 'usage' | 'refund' | 'expiration';
}

class CreditService {
  /**
   * Get current credit balance for an organization
   */
  async getCreditBalance(organizationId: string): Promise<CreditBalance> {
    try {
      const response = await apiClient.get<CreditBalance>(`/credits/balance?organizationId=${organizationId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching credit balance:", error);
      throw error;
    }
  }

  /**
   * Get credit transaction history
   */
  async getCreditHistory(organizationId: string, params?: CreditHistoryParams): Promise<CreditTransaction[]> {
    try {
      const queryParams = new URLSearchParams({
        organizationId,
        limit: (params?.limit || 50).toString(),
        offset: (params?.offset || 0).toString(),
        ...(params?.type && { type: params.type })
      });

      const response = await apiClient.get<{
        transactions: CreditTransaction[];
        total: number;
        count: number;
        offset: number;
      }>(`/credits/history?${queryParams}`);

      // Return the transactions array from the response
      return response.data.transactions;
    } catch (error) {
      console.error("Error fetching credit history:", error);
      throw error;
    }
  }

  /**
   * Check if organization has enough credits for a feature
   */
  async checkCreditsForFeature(organizationId: string, featureName: string): Promise<CreditCheckResponse> {
    try {
      const response = await apiClient.get<CreditCheckResponse>(`/credits/check/${featureName}?organizationId=${organizationId}`);
      return response.data;
    } catch (error) {
      console.error("Error checking credits for feature:", error);
      throw error;
    }
  }

  /**
   * Use credits for a feature
   */
  async useCreditsForFeature(request: CreditUsageRequest): Promise<{ success: boolean; newBalance: number; transaction: CreditTransaction }> {
    try {
      const response = await apiClient.post<{ success: boolean; newBalance: number; transaction: CreditTransaction }>('/credits/use', request);
      return response.data;
    } catch (error) {
      console.error("Error using credits for feature:", error);
      throw error;
    }
  }

  /**
   * Allocate monthly credits (typically called by system)
   */
  async allocateMonthlyCredits(organizationId: string): Promise<{ success: boolean; creditsAllocated: number; newBalance: number }> {
    try {
      const response = await apiClient.post<{ success: boolean; creditsAllocated: number; newBalance: number }>('/credits/allocate-monthly', {
        organizationId
      });
      return response.data;
    } catch (error) {
      console.error("Error allocating monthly credits:", error);
      throw error;
    }
  }

  /**
   * Get available feature costs
   */
  async getFeatureCosts(): Promise<FeatureCost[]> {
    try {
      const response = await apiClient.get<FeatureCost[]>('/credits/features');
      return response.data;
    } catch (error) {
      console.error("Error fetching feature costs:", error);
      // Fallback to local configuration
      return Object.values(FEATURE_COSTS);
    }
  }

  /**
   * Get feature costs by category
   */
  async getFeatureCostsByCategory(category: 'free' | 'basic' | 'advanced' | 'premium'): Promise<FeatureCost[]> {
    try {
      const response = await apiClient.get<FeatureCost[]>(`/credits/features/category/${category}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching feature costs by category:", error);
      // Fallback to local configuration
      return Object.values(FEATURE_COSTS).filter(cost => cost.category === category);
    }
  }

  /**
   * Get available credit packages for purchase
   */
  async getCreditPackages(): Promise<CreditPackage[]> {
    // For now, use local configuration directly to ensure consistency
    // TODO: Implement API endpoint for credit packages when backend is ready
    return CREDIT_PACKAGES;
  }

  /**
   * Create checkout session for credit purchase
   */
  async createCreditCheckoutSession(_organizationId: string, packageId: string): Promise<{sessionUrl?: string, sessionId?: string}> {
    try {
      const response = await apiClient.post<{sessionUrl?: string, sessionId?: string}>('/credits/purchase', {
        package: packageId,
        successUrl: `${window.location.origin}/credits/success?session_id={CHECKOUT_SESSION_ID}`,
        cancelUrl: `${window.location.origin}/credits/cancel`
      });
      return response.data;
    } catch (error) {
      console.error("Error creating credit checkout session:", error);
      throw error;
    }
  }

  /**
   * Get feature cost for a specific feature (local lookup)
   */
  getFeatureCost(featureName: string): FeatureCost | null {
    return FEATURE_COSTS[featureName] || null;
  }

  /**
   * Calculate total cost for multiple features
   */
  calculateTotalCost(featureNames: string[]): number {
    return featureNames.reduce((total, featureName) => {
      const cost = this.getFeatureCost(featureName);
      return total + (cost?.credits || 0);
    }, 0);
  }

  /**
   * Check if user can afford multiple features
   */
  canAffordFeatures(currentBalance: number, featureNames: string[]): boolean {
    const totalCost = this.calculateTotalCost(featureNames);
    return currentBalance >= totalCost;
  }
}

export const creditService = new CreditService();
