/**
 * Negotiation Integration Service
 * Bridges Chat Negotiation with Playbook and Simulator systems
 */

import { apiClient } from '@/lib/config';
import { negotiationPlaybookService } from './negotiation-playbook-service';
import { negotiationSimulatorService } from './negotiation-simulator-service';
import { chatNegotiationService } from './chat-negotiation-service';
import type { NegotiationPlaybook } from '@/lib/types/negotiation-playbook';
import type { NegotiationScenario, CreateScenarioFromPlaybookRequest } from '@/lib/types/negotiation-simulator';

export interface PlaybookToChatScenario {
  playbookId: string;
  documentId: string;
  scenarioType: 'contract_review' | 'term_negotiation' | 'risk_mitigation';
  extractedContext: {
    contractType: string;
    keyTerms: string[];
    riskAreas: string[];
    leveragePoints: string[];
    constraints: string[];
  };
  aiPersonality: {
    characterId: string;
    aggressiveness: number;
    flexibility: number;
    riskTolerance: number;
    communicationStyle: 'DIRECT' | 'DIPLOMATIC' | 'ANALYTICAL' | 'EMOTIONAL';
  };
  suggestedStrategies: string[];
}

export interface ChatToSimulatorBridge {
  chatSessionId: string;
  extractedMoves: Array<{
    strategy: string;
    terms: Record<string, any>;
    effectiveness: number;
  }>;
  relationshipMetrics: {
    trust: number;
    respect: number;
    pressure: number;
  };
  recommendedScenarios: string[];
}

class NegotiationIntegrationService {

  /**
   * Create a chat negotiation scenario from a playbook
   */
  async createChatScenarioFromPlaybook(
    playbookId: string,
    options?: {
      focusAreas?: string[];
      difficulty?: 'beginner' | 'intermediate' | 'advanced';
      aiPersonality?: Partial<PlaybookToChatScenario['aiPersonality']>;
    }
  ): Promise<PlaybookToChatScenario> {
    try {
      // 1. Get the playbook data
      const playbook = await negotiationPlaybookService.getPlaybook(playbookId);
      
      // 2. Extract scenario context from playbook
      const extractedContext = this.extractScenarioContext(playbook);
      
      // 3. Configure AI personality based on playbook analysis
      const aiPersonality = this.configureAIPersonality(playbook, options?.aiPersonality);
      
      // 4. Generate scenario
      const scenario: PlaybookToChatScenario = {
        playbookId,
        documentId: playbook.documentId,
        scenarioType: this.determineScenarioType(playbook),
        extractedContext,
        aiPersonality,
        suggestedStrategies: this.extractSuggestedStrategies(playbook)
      };

      return scenario;
    } catch (error) {
      console.error('Failed to create chat scenario from playbook:', error);
      throw error;
    }
  }

  /**
   * Start a chat negotiation session from a playbook
   */
  async startChatNegotiationFromPlaybook(
    playbookId: string,
    options?: {
      focusAreas?: string[];
      difficulty?: 'beginner' | 'intermediate' | 'advanced';
    }
  ): Promise<{
    chatSession: any;
    playbookContext: PlaybookToChatScenario;
  }> {
    try {
      // 1. Create scenario from playbook
      const playbookContext = await this.createChatScenarioFromPlaybook(playbookId, options);
      
      // 2. Start chat negotiation session
      const chatSession = await chatNegotiationService.startChatNegotiation(
        playbookContext.scenarioType,
        playbookContext.aiPersonality
      );

      return {
        chatSession,
        playbookContext
      };
    } catch (error) {
      console.error('Failed to start chat negotiation from playbook:', error);
      throw error;
    }
  }

  /**
   * Create a simulator scenario from chat negotiation results
   */
  async createSimulatorScenarioFromChat(
    chatSessionId: string,
    options?: {
      difficulty?: 'beginner' | 'intermediate' | 'advanced';
      focusAreas?: string[];
    }
  ): Promise<NegotiationScenario> {
    try {
      // 1. Get chat session data
      const chatSession = await chatNegotiationService.getChatNegotiationSession(chatSessionId);
      
      // 2. Analyze chat performance
      const chatAnalysis = this.analyzeChatPerformance(chatSession);
      
      // 3. Create simulator scenario based on chat insights
      const scenarioRequest = {
        contractType: this.mapChatToContractType(chatSession.scenarioId),
        difficulty: options?.difficulty || this.recommendDifficulty(chatAnalysis),
        focusAreas: options?.focusAreas || chatAnalysis.weakAreas,
        aiPersonality: this.adaptAIPersonality(chatSession.aiPersonality, chatAnalysis),
        initialConditions: {
          userPosition: chatSession.extractedTerms,
          relationshipState: chatSession.relationshipMetrics
        }
      };

      const scenario = await negotiationSimulatorService.createScenario(scenarioRequest);
      return scenario;
    } catch (error) {
      console.error('Failed to create simulator scenario from chat:', error);
      throw error;
    }
  }

  /**
   * Get integrated learning path recommendations
   */
  async getIntegratedLearningPath(
    userId: string,
    documentId?: string
  ): Promise<{
    currentStep: 'playbook' | 'chat' | 'simulator' | 'advanced';
    recommendations: Array<{
      type: 'playbook' | 'chat' | 'simulator';
      title: string;
      description: string;
      estimatedTime: string;
      difficulty: string;
      action: {
        type: string;
        params: Record<string, any>;
      };
    }>;
    skillGaps: string[];
    nextMilestones: string[];
  }> {
    try {
      // 1. Get user's negotiation profile
      const userProfile = await negotiationSimulatorService.getUserNegotiationProfile();
      
      // 2. Get performance history across all systems
      const performanceHistory = await negotiationSimulatorService.getPerformanceHistory(userId);
      
      // 3. Analyze skill gaps
      const skillGapAnalysis = await negotiationSimulatorService.generateSkillGapAnalysis({
        userId,
        includePlaybookData: true,
        includeChatData: true,
        includeSimulatorData: true
      });

      // 4. Generate personalized recommendations
      const recommendations = this.generateIntegratedRecommendations(
        userProfile,
        performanceHistory,
        skillGapAnalysis,
        documentId
      );

      return {
        currentStep: this.determineCurrentStep(userProfile, performanceHistory),
        recommendations,
        skillGaps: skillGapAnalysis.identifiedGaps,
        nextMilestones: skillGapAnalysis.recommendedMilestones
      };
    } catch (error) {
      console.error('Failed to get integrated learning path:', error);
      throw error;
    }
  }

  /**
   * Track cross-system usage and performance
   */
  async trackCrossSystemUsage(
    userId: string,
    activity: {
      system: 'playbook' | 'chat' | 'simulator';
      action: string;
      sessionId: string;
      performance?: Record<string, any>;
      timeSpent: number;
    }
  ): Promise<void> {
    try {
      await apiClient.post('/negotiation-integration/track-usage', {
        userId,
        activity,
        timestamp: new Date().toISOString()
      });
    } catch (error) {
      console.error('Failed to track cross-system usage:', error);
      // Don't throw - tracking is non-critical
    }
  }

  // Private helper methods

  private extractScenarioContext(playbook: NegotiationPlaybook) {
    return {
      contractType: this.inferContractType(playbook),
      keyTerms: playbook.keyNegotiationPoints.map(point => point.term),
      riskAreas: playbook.riskAssessment.risks.map(risk => risk.category),
      leveragePoints: playbook.strategicRecommendations
        .filter(rec => rec.priority === 'high')
        .map(rec => rec.strategy),
      constraints: playbook.riskAssessment.risks
        .filter(risk => risk.severity === 'high')
        .map(risk => risk.description)
    };
  }

  private configureAIPersonality(
    playbook: NegotiationPlaybook, 
    overrides?: Partial<PlaybookToChatScenario['aiPersonality']>
  ) {
    // Base personality on playbook complexity and risk assessment
    const riskLevel = playbook.riskAssessment.overallRiskLevel;
    const complexity = playbook.keyNegotiationPoints.length;

    const basePersonality = {
      characterId: this.selectCharacterForPlaybook(playbook),
      aggressiveness: riskLevel === 'high' ? 0.7 : 0.4,
      flexibility: complexity > 5 ? 0.3 : 0.6,
      riskTolerance: riskLevel === 'low' ? 0.8 : 0.3,
      communicationStyle: 'DIPLOMATIC' as const
    };

    return { ...basePersonality, ...overrides };
  }

  private determineScenarioType(playbook: NegotiationPlaybook): PlaybookToChatScenario['scenarioType'] {
    const hasHighRiskTerms = playbook.riskAssessment.risks.some(risk => risk.severity === 'high');
    const hasComplexTerms = playbook.keyNegotiationPoints.length > 5;

    if (hasHighRiskTerms) return 'risk_mitigation';
    if (hasComplexTerms) return 'term_negotiation';
    return 'contract_review';
  }

  private extractSuggestedStrategies(playbook: NegotiationPlaybook): string[] {
    return playbook.strategicRecommendations
      .filter(rec => rec.priority === 'high' || rec.priority === 'medium')
      .map(rec => rec.strategy)
      .slice(0, 5);
  }

  private analyzeChatPerformance(chatSession: any) {
    // Analyze chat session performance to identify strengths and weaknesses
    const relationshipMetrics = chatSession.relationshipMetrics;
    const score = chatSession.score;
    
    const weakAreas = [];
    if (relationshipMetrics.trust < 50) weakAreas.push('relationship_building');
    if (relationshipMetrics.pressure > 70) weakAreas.push('pressure_management');
    if (score < 6) weakAreas.push('strategic_thinking');

    return {
      overallScore: score,
      relationshipEffectiveness: (relationshipMetrics.trust + relationshipMetrics.respect) / 2,
      pressureManagement: 100 - relationshipMetrics.pressure,
      weakAreas,
      strongAreas: this.identifyStrongAreas(chatSession)
    };
  }

  private generateIntegratedRecommendations(
    userProfile: any,
    performanceHistory: any[],
    skillGapAnalysis: any,
    documentId?: string
  ) {
    const recommendations = [];

    // Add playbook recommendation if user has documents
    if (documentId) {
      recommendations.push({
        type: 'playbook' as const,
        title: 'Generate Strategic Analysis',
        description: 'Create a comprehensive negotiation playbook for your document',
        estimatedTime: '5-10 minutes',
        difficulty: 'Beginner',
        action: {
          type: 'generate_playbook',
          params: { documentId }
        }
      });
    }

    // Add chat negotiation recommendations based on skill gaps
    if (skillGapAnalysis.identifiedGaps.includes('communication')) {
      recommendations.push({
        type: 'chat' as const,
        title: 'Practice Conversational Negotiation',
        description: 'Improve communication skills through natural chat practice',
        estimatedTime: '15-20 minutes',
        difficulty: 'Intermediate',
        action: {
          type: 'start_chat_negotiation',
          params: { scenarioType: 'communication_focused' }
        }
      });
    }

    // Add simulator recommendations for advanced practice
    if (userProfile?.experienceLevel === 'intermediate') {
      recommendations.push({
        type: 'simulator' as const,
        title: 'Advanced Scenario Practice',
        description: 'Test your skills in complex negotiation scenarios',
        estimatedTime: '30-45 minutes',
        difficulty: 'Advanced',
        action: {
          type: 'start_simulator_session',
          params: { difficulty: 'advanced' }
        }
      });
    }

    return recommendations;
  }

  private determineCurrentStep(userProfile: any, performanceHistory: any[]) {
    if (!performanceHistory.length) return 'playbook';
    
    const hasPlaybookExperience = performanceHistory.some(h => h.system === 'playbook');
    const hasChatExperience = performanceHistory.some(h => h.system === 'chat');
    const hasSimulatorExperience = performanceHistory.some(h => h.system === 'simulator');

    if (!hasPlaybookExperience) return 'playbook';
    if (!hasChatExperience) return 'chat';
    if (!hasSimulatorExperience) return 'simulator';
    return 'advanced';
  }

  // Additional helper methods...
  private inferContractType(playbook: NegotiationPlaybook): string {
    // Logic to infer contract type from playbook content
    return 'service_agreement'; // Simplified
  }

  private selectCharacterForPlaybook(playbook: NegotiationPlaybook): string {
    // Select appropriate AI character based on playbook analysis
    return 'analytical_negotiator'; // Simplified
  }

  private identifyStrongAreas(chatSession: any): string[] {
    // Identify user's strong areas from chat performance
    return ['strategic_thinking']; // Simplified
  }

  private mapChatToContractType(scenarioId: string): string {
    const mapping: Record<string, string> = {
      'software_licensing': 'SOFTWARE_LICENSE',
      'real_estate': 'REAL_ESTATE_PURCHASE',
      'salary': 'EMPLOYMENT_CONTRACT',
      'service_contract': 'SERVICE_AGREEMENT'
    };
    return mapping[scenarioId] || 'SERVICE_AGREEMENT';
  }

  private recommendDifficulty(chatAnalysis: any): 'beginner' | 'intermediate' | 'advanced' {
    if (chatAnalysis.overallScore >= 8) return 'advanced';
    if (chatAnalysis.overallScore >= 6) return 'intermediate';
    return 'beginner';
  }

  private adaptAIPersonality(chatPersonality: any, chatAnalysis: any) {
    // Adapt AI personality based on chat performance
    return {
      ...chatPersonality,
      aggressiveness: chatAnalysis.pressureManagement < 50 ? 
        Math.max(0.2, chatPersonality.aggressiveness - 0.2) : 
        chatPersonality.aggressiveness
    };
  }
}

export const negotiationIntegrationService = new NegotiationIntegrationService();
