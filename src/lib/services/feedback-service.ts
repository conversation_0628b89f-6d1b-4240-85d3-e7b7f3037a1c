import {
	Feedback,
	CreateFeedbackRequest,
	UpdateFeedbackRequest,
	FeedbackVoteRequest,
	FeedbackCategory,
	FeedbackCorrection,
	CreateCorrectionRequest,
	FeedbackAnalyticsSummary,
	FeedbackQueryParams,
	CorrectionQueryParams,
	FeedbackResponse,
} from "@/lib/types/user-feedback";
import { apiClient } from "../config";

// URL prefix for feedback API endpoints
const feedbackApiBaseUrl = "/user-feedback";

export const FeedbackService = {
	// Feedback endpoints
	async createFeedback(data: CreateFeedbackRequest): Promise<Feedback> {
		const response = await apiClient.post<Feedback>(
			`${feedbackApiBaseUrl}/feedback`,
			data
		);
		return response.data;
	},

	async getFeedback(
		params?: FeedbackQueryParams
	): Promise<FeedbackResponse<Feedback>> {
		const response = await apiClient.get<FeedbackResponse<Feedback>>(
			`${feedbackApiBaseUrl}/feedback`,
			{ params }
		);
		return response.data;
	},

	async getFeedbackById(id: string): Promise<Feedback> {
		const response = await apiClient.get<Feedback>(
			`${feedbackApiBaseUrl}/feedback/${id}`
		);
		return response.data;
	},

	async updateFeedback(
		id: string,
		data: UpdateFeedbackRequest
	): Promise<Feedback> {
		const response = await apiClient.patch<Feedback>(
			`${feedbackApiBaseUrl}/feedback/${id}`,
			data
		);
		return response.data;
	},

	async deleteFeedback(id: string): Promise<void> {
		await apiClient.delete(`${feedbackApiBaseUrl}/feedback/${id}`);
	},

	async voteFeedback(id: string, data: FeedbackVoteRequest): Promise<Feedback> {
		const response = await apiClient.post<Feedback>(
			`${feedbackApiBaseUrl}/feedback/${id}/vote`,
			data
		);
		return response.data;
	},

	// Category endpoints
	async createCategory(
		data: Omit<
			FeedbackCategory,
			| "id"
			| "isDefault"
			| "isActive"
			| "organizationId"
			| "createdBy"
			| "createdAt"
			| "updatedAt"
		>
	): Promise<FeedbackCategory> {
		const response = await apiClient.post<FeedbackCategory>(
			`${feedbackApiBaseUrl}/categories`,
			data
		);
		return response.data;
	},

	async getCategories(): Promise<FeedbackCategory[]> {
		const response = await apiClient.get<FeedbackCategory[]>(
			`${feedbackApiBaseUrl}/categories`
		);
		return response.data;
	},

	// Correction endpoints
	async createCorrection(
		data: CreateCorrectionRequest
	): Promise<FeedbackCorrection> {
		const response = await apiClient.post<FeedbackCorrection>(
			`${feedbackApiBaseUrl}/corrections`,
			data
		);
		return response.data;
	},

	async getCorrections(
		params?: CorrectionQueryParams
	): Promise<FeedbackResponse<FeedbackCorrection>> {
		const response = await apiClient.get<FeedbackResponse<FeedbackCorrection>>(
			`${feedbackApiBaseUrl}/corrections`,
			{ params }
		);
		return response.data;
	},

	// Analytics endpoints
	async getFeedbackAnalytics(
		params?: CorrectionQueryParams
	): Promise<FeedbackAnalyticsSummary> {
		const response = await apiClient.get<FeedbackAnalyticsSummary>(
			`${feedbackApiBaseUrl}/analytics/summary`,
			{ params }
		);
		return response.data;
	},
};
