import { apiClient } from "../config"
import type { <PERSON>older, CreateFolderDto, UpdateFolderDto, FolderDocument } from "@/lib/types/document-organization"

class FolderService {
  async createFolder(folderData: CreateFolderDto): Promise<Folder> {
    try {
      const response = await apiClient.post<Folder>('/document-organization/folders', folderData)
      return response.data
    } catch (error) {
      console.error("Error creating folder:", error)
      throw error
    }
  }

  async getFolders(): Promise<Folder[]> {
    try {
      console.log("Fetching all folders");
      const response = await apiClient.get<Folder[]>('/document-organization/folders')
      console.log("Folders data received:", response.data);
      
      // Ensure each folder has a documents array
      const folders = response.data.map(folder => ({
        ...folder,
        documents: folder.documents || []
      }));
      
      return folders;
    } catch (error) {
      console.error("Error fetching folders:", error)
      throw error
    }
  }

  async getFolder(id: string): Promise<Folder> {
    try {
      console.log(`Fetching folder with ID: ${id}`);
      const response = await apiClient.get<Folder>(`/document-organization/folders/${id}`)
      console.log("Folder data received:", response.data);
      
      // Ensure the documents array is always defined
      if (!response.data.documents) {
        response.data.documents = [];
      }
      
      return response.data
    } catch (error) {
      console.error(`Error fetching folder ${id}:`, error)
      throw error
    }
  }

  async updateFolder(id: string, folderData: UpdateFolderDto): Promise<Folder> {
    try {
      const response = await apiClient.patch<Folder>(`/document-organization/folders/${id}`, folderData)
      return response.data
    } catch (error) {
      console.error(`Error updating folder ${id}:`, error)
      throw error
    }
  }

  async deleteFolder(id: string): Promise<void> {
    try {
      await apiClient.delete(`/document-organization/folders/${id}`)
    } catch (error) {
      console.error(`Error deleting folder ${id}:`, error)
      throw error
    }
  }

  async addDocumentToFolder(folderId: string, documentId: string | string[]): Promise<Folder> {
    try {
      console.log(`Attempting to add document(s) to folder ${folderId}`);
      
      // Convert single documentId to array if needed
      const documentIds = Array.isArray(documentId) ? documentId : [documentId];
      console.log('Document IDs:', documentIds);
      
      const response = await apiClient.post<Folder>(
        `/document-organization/folders/${folderId}/documents`,
        { documentIds },
        { 
          timeout: 10000 // Add timeout to prevent hanging requests
        },
      )
      
      console.log('API response:', response.data);
      return response.data
    } catch (error: unknown) {
      console.error(`Error adding document(s) to folder ${folderId}:`, error)
      
      // Add more detailed error logging
      if (error && typeof error === 'object' && 'response' in error) {
        // The request was made and the server responded with a status code
        // that falls out of the range of 2xx
        const axiosError = error as { 
          response: { 
            data: unknown; 
            status: number; 
            headers: unknown; 
          } 
        };
        console.error('Error response data:', axiosError.response.data);
        console.error('Error response status:', axiosError.response.status);
        console.error('Error response headers:', axiosError.response.headers);
      } else if (error && typeof error === 'object' && 'request' in error) {
        // The request was made but no response was received
        console.error('Error request:', (error as { request: unknown }).request);
      } else if (error instanceof Error) {
        // Something happened in setting up the request that triggered an Error
        console.error('Error message:', error.message);
      }
      
      throw error
    }
  }

  async removeDocumentsFromFolder(folderId: string, documentIds: string[]): Promise<Folder> {
    try {
      console.log(`Removing documents from folder ${folderId}:`, documentIds);
      
      const response = await apiClient.delete<Folder>(
        `/document-organization/folders/${folderId}/documents`,
        { 
          data: { documentIds }
        }
      )
      return response.data
    } catch (error: unknown) {
      console.error(`Error removing documents from folder ${folderId}:`, error)
      throw error
    }
  }

  async getFoldersForDocument(documentId: string): Promise<Folder[]> {
    try {
      const response = await apiClient.get<Folder[]>(`/document-organization/folders/documents/${documentId}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching folders for document ${documentId}:`, error)
      throw error
    }
  }

  async getFolderDocuments(folderId: string): Promise<FolderDocument[]> {
    try {
      console.log(`Fetching documents for folder ${folderId}`);
      const response = await apiClient.get<FolderDocument[]>(
        `/document-organization/folders/${folderId}/documents`
      );
      console.log("Folder documents received:", response.data);
      return response.data;
    } catch (error) {
      console.error(`Error fetching documents for folder ${folderId}:`, error);
      throw error;
    }
  }
}

export const folderService = new FolderService()
