import { AxiosError } from 'axios';
import { apiClient } from '../config';
import { ClauseTemplate, ClauseMatch, GenerateTemplateRequest } from '../types/clause-library';

export interface ClauseMatchingOptions {
  similarityThreshold?: number;
  categories?: string[];
  matchingPreferences?: {
    ignoreNumbering?: boolean;
    useKeywordMatching?: boolean;
    handleSpecialFormats?: boolean;
    boostSimilarContent?: boolean;
  };
}

class ClauseLibraryService {
  async getTemplates(categorySearch?: string): Promise<ClauseTemplate[]> {
    try {
      const response = await apiClient.get<ClauseTemplate[]>('/documents/clause-library/templates', {
        params: categorySearch ? { categorySearch } : undefined
      });
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new Error(error.response?.data?.message || 'Failed to fetch clause templates');
      }
      throw error;
    }
  }

  async createTemplate(template: Omit<ClauseTemplate, 'id' | 'createdAt' | 'updatedAt'>): Promise<ClauseTemplate> {
    try {
      const response = await apiClient.post<ClauseTemplate>(
        '/documents/clause-library/templates',
        template
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new Error(error.response?.data?.message || 'Failed to create clause template');
      }
      throw error;
    }
  }

  async identifyClauses(
    documentId: string,
    options: ClauseMatchingOptions = {}
  ): Promise<ClauseMatch[]> {
    try {
      const response = await apiClient.post<ClauseMatch[]>('/documents/clause-library/identify', {
        documentId,
        categories: options.categories,
        similarityThreshold: options.similarityThreshold,
        matchingPreferences: {
          ignoreNumbering: options.matchingPreferences?.ignoreNumbering ?? true,
          useKeywordMatching: options.matchingPreferences?.useKeywordMatching ?? true,
          handleSpecialFormats: options.matchingPreferences?.handleSpecialFormats ?? true,
          boostSimilarContent: options.matchingPreferences?.boostSimilarContent ?? true
        }
      });
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new Error(error.response?.data?.message || 'Failed to identify clauses');
      }
      throw error;
    }
  }

  async generateTemplate(request: GenerateTemplateRequest): Promise<ClauseTemplate> {
    try {
      const response = await apiClient.post<ClauseTemplate>(
        '/documents/clause-library/generate-template',
        request
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        throw new Error(error.response?.data?.message || 'Failed to generate template');
      }
      throw error;
    }
  }

  /**
   * Helper method to search templates by partial category match
   * @param categoryQuery Partial category name to search for
   */
  async searchTemplatesByCategory(categoryQuery: string): Promise<ClauseTemplate[]> {
    return this.getTemplates(categoryQuery);
  }
}

export const clauseLibraryService = new ClauseLibraryService();