import { apiClient } from "../config"

// Request Types
export interface AnalyzeCitationDto {
  citation: string
  includeRelationships?: boolean
  includePrecedentChains?: boolean
  includeImpact?: boolean
  maxRelationshipDepth?: number
  maxPrecedentChainLength?: number
}

export interface AnalyzeDocumentCitationsDto {
  documentText: string
  includeRelationships?: boolean
  includePrecedentChains?: boolean
  includeImpact?: boolean
  maxRelationshipDepth?: number
  maxPrecedentChainLength?: number
}

// Response Types
export interface CitationMetadata {
  judges?: string[]
  docketNumber?: string
  precedentialStatus?: string
  summary?: string
  procedural_history?: string
  disposition?: string
  classification?: string[]
  subject_matter?: string[]
  attorneys?: string[]
}

export interface Citation {
  id: string
  citation: string
  type: string
  title?: string
  court?: string
  year?: number
  jurisdiction?: string
  courtListenerId?: string
  metadata?: CitationMetadata
}

export interface RelationshipMetadata {
  // For 'cites' relationships
  citedCase?: {
    name: string
    citation: string
    court: string
    year?: number
    jurisdiction: string
  }
  relationshipDescription?: string
  citedCaseUrl?: string

  // For 'citedBy' relationships
  citingCase?: {
    name: string
    citation: string
    court: string
    year?: number
    jurisdiction: string
  }
  citingCaseUrl?: string
}

export interface CitationRelationship {
  id: string
  sourceCitationId: string
  targetCitationId: string
  relationshipType: string
  strength: string // 'weak', 'moderate', 'strong'
  metadata: RelationshipMetadata
}


export interface CitationImpact {
  citationId: string
  totalCitations: number
  recentCitations: number
  influentialCitations: number
  negativeReferences: number
  positiveReferences: number
  impactScore: number // 0-100
}

export interface PrecedentChainItem {
  citationId: string
  relationshipToParent: string // 'follows', 'distinguishes', etc.
  significance: string // 'low', 'medium', 'high'
}

export interface PrecedentChain {
  id: string
  rootCitationId: string
  chain: PrecedentChainItem[]
}


export interface CitationAnalysisResponse {
  citation: Citation
  relationships?: CitationRelationship[]
  impact?: CitationImpact
  precedentChains?: PrecedentChain[]
}

export interface NetworkGraphData {
  nodes: Array<{
    id: string
    label: string
    type: string
    year?: number
    size?: number
  }>
  links: Array<{
    source: string
    target: string
    type: string
    strength: number
  }>
}

class CitationAnalysisService {
  // Analyze a single citation
  async analyzeCitation(dto: AnalyzeCitationDto): Promise<CitationAnalysisResponse | null> {
    try {
      const response = await apiClient.post<CitationAnalysisResponse>(`/legal-research/citations/analyze`, dto)
      return response.data
    } catch (error) {
      console.error("Error analyzing citation:", error)
      return null
    }
  }

  // Analyze all citations in a document
  async analyzeDocumentCitations(dto: AnalyzeDocumentCitationsDto): Promise<CitationAnalysisResponse[]> {
    try {
      const response = await apiClient.post<CitationAnalysisResponse[]>(`/legal-research/citations/analyze-document`, dto)
      return response.data
    } catch (error) {
      console.error("Error analyzing document citations:", error)
      throw error
    }
  }

  // Get citation relationships
  async getCitationRelationships(citation: string, depth = 1): Promise<CitationAnalysisResponse> {
    try {
      const response = await apiClient.get<CitationAnalysisResponse>(
        `/legal-research/citations/relationships/${encodeURIComponent(citation)}?depth=${depth}`,
      )
      return response.data
    } catch (error) {
      console.error("Error getting citation relationships:", error)
      throw error
    }
  }

  // Get citation impact
  async getCitationImpact(citation: string): Promise<CitationAnalysisResponse> {
    try {
      const response = await apiClient.get<CitationAnalysisResponse>(
        `/legal-research/citations/impact/${encodeURIComponent(citation)}`,
      )
      return response.data
    } catch (error) {
      console.error("Error getting citation impact:", error)
      throw error
    }
  }

  // Get citation precedent chains
  async getCitationPrecedent(citation: string, chainLength = 3): Promise<CitationAnalysisResponse> {
    try {
      const response = await apiClient.get<CitationAnalysisResponse>(
        `/legal-research/citations/precedent/${encodeURIComponent(citation)}?chainLength=${chainLength}`,
      )
      return response.data
    } catch (error) {
      console.error("Error getting citation precedent:", error)
      throw error
    }
  }

  // Get citation network graph data
  async getCitationNetwork(citation: string, depth = 2): Promise<NetworkGraphData> {
    try {
      const response = await apiClient.get<NetworkGraphData>(
        `/legal-research/citations/network/${encodeURIComponent(citation)}?depth=${depth}`,
      )
      return response.data
    } catch (error) {
      console.error("Error getting citation network:", error)
      throw error
    }
  }

  // Use specific endpoints for basic analysis
  async analyzeCitationBasic(dto: AnalyzeCitationDto): Promise<CitationAnalysisResponse | null> {
    try {
      const response = await apiClient.post<CitationAnalysisResponse>(`/legal-research/citations/analyze/basic`, dto)
      return response.data
    } catch (error) {
      console.error("Error analyzing citation (basic):", error)
      throw error
    }
  }

  // Use specific endpoints for enhanced analysis
  async analyzeCitationEnhanced(dto: AnalyzeCitationDto): Promise<CitationAnalysisResponse | null> {
    try {
      const response = await apiClient.post<CitationAnalysisResponse>(`/legal-research/citations/analyze/enhanced`, dto)
      return response.data
    } catch (error) {
      console.error("Error analyzing citation (enhanced):", error)
      throw error
    }
  }

  // Use specific endpoints for basic document analysis
  async analyzeDocumentCitationsBasic(dto: AnalyzeDocumentCitationsDto): Promise<CitationAnalysisResponse[]> {
    try {
      const response = await apiClient.post<CitationAnalysisResponse[]>(`/legal-research/citations/analyze-document/basic`, dto)
      return response.data
    } catch (error) {
      console.error("Error analyzing document citations (basic):", error)
      throw error
    }
  }

  // Use specific endpoints for enhanced document analysis
  async analyzeDocumentCitationsEnhanced(dto: AnalyzeDocumentCitationsDto): Promise<CitationAnalysisResponse[]> {
    try {
      const response = await apiClient.post<CitationAnalysisResponse[]>(`/legal-research/citations/analyze-document/enhanced`, dto)
      return response.data
    } catch (error) {
      console.error("Error analyzing document citations (enhanced):", error)
      throw error
    }
  }
}

export const citationAnalysisService = new CitationAnalysisService()
