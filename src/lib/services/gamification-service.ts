import { AxiosError } from "axios";
import { apiClient } from "@/lib/config";

// Types
export interface UserGamificationProfile {
  userId: string;
  organizationId: string;
  level: {
    current: number;
    title: string;
    currentXP: number;
    totalXP: number;
    xpToNext: number;
    progress: number;
  };
  achievements: UserAchievement[];
  statistics: {
    totalSessions: number;
    completedSessions: number;
    averageScore: number;
    winRate: number;
    currentStreak: number;
    bestStreak: number;
    achievementsCount: number;
  };
  unlockedContent: {
    characters: string[];
    scenarios: string[];
    features: string[];
  };
  createdAt: Date;
  updatedAt: Date;
}

export interface UserAchievement {
  achievementId: string;
  unlockedAt: Date;
  sessionId?: string;
  metadata?: Record<string, any>;
}

export interface Achievement {
  id: string;
  title: string;
  description: string;
  badge: string;
  rarity: 'common' | 'rare' | 'epic' | 'legendary';
  category: string;
  requirements: AchievementRequirement;
  rewards: AchievementReward;
  statistics: {
    totalUnlocked: number;
    unlockRate: number;
  };
  isActive: boolean;
  createdAt: Date;
}

export interface AchievementRequirement {
  type: 'session_completion' | 'performance_streak' | 'total_sessions' | 'relationship_milestone';
  conditions: Record<string, any>;
}

export interface AchievementReward {
  xp: number;
  credits?: number;
  unlocks?: string[];
}

export interface AICharacter {
  id: string;
  name: string;
  title: string;
  company: string;
  avatar: string;
  difficulty: number;
  specialties: string[];
  backstory: string;
  personality: AIPersonalityProfile;
  unlockRequirements?: UnlockRequirement;
  behaviorPatterns: {
    openingStrategy: string;
    concessionTriggers: string[];
    relationshipFactors: string[];
    weaknesses: string[];
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface AIPersonalityProfile {
  aggressiveness: number;
  flexibility: number;
  riskTolerance: number;
  communicationStyle: string;
  decisionSpeed: string;
  concessionPattern: string;
}

export interface UnlockRequirement {
  level?: number;
  achievements?: string[];
  sessionsCompleted?: number;
}

export interface CharacterRelationship {
  userId: string;
  characterId: string;
  relationship: {
    respectLevel: number;
    trustLevel: number;
    relationshipStatus: string;
    totalInteractions: number;
    lastInteraction: Date;
  };
  interactionHistory: InteractionHistoryEntry[];
  bonuses: RelationshipBonuses;
  notes?: string;
  createdAt: Date;
  updatedAt: Date;
}

export interface InteractionHistoryEntry {
  sessionId: string;
  outcome: string;
  userApproach: string;
  aiSatisfaction: number;
  userSatisfaction: number;
  respectChange: number;
  trustChange: number;
  date: Date;
}

export interface RelationshipBonuses {
  betterStartingTerms: boolean;
  increasedFlexibility: number;
  insiderInformation: boolean;
  relationshipDiscount?: number;
}

export interface LeaderboardEntry {
  rank: number;
  userId: string;
  score: number;
  totalDeals: number;
  winRate: number;
  averageRounds: number;
  totalXP: number;
  metadata: {
    name: string;
    title: string;
    organization: string;
    avatar?: string;
  };
}

export interface LeaderboardResponse {
  rankings: LeaderboardEntry[];
  userRank?: number;
  totalParticipants: number;
  timeframe: string;
  scope: string;
  lastUpdated: Date;
}

export interface PressureEvent {
  id: string;
  type: 'stakeholder' | 'market' | 'time' | 'competitor';
  title: string;
  category: string;
  templates: PressureEventTemplate[];
  impact: PressureEventImpact;
  triggerConditions: PressureEventTriggerConditions;
  cooldown: number;
  probability: number;
  isActive: boolean;
  createdAt: Date;
}

export interface PressureEventTemplate {
  message: string;
  intensity: 'low' | 'medium' | 'high';
  variables: string[];
}

export interface PressureEventImpact {
  userStress: number;
  timeMultiplier: number;
  leverageChange: number;
  aiAggressiveness?: number;
}

export interface PressureEventTriggerConditions {
  minRound: number;
  maxRound: number;
  sessionStatus: string;
  timeRemaining?: { max?: number };
  userScore?: { min?: number };
  dealGap?: { min?: number };
}

export interface LevelUpdate {
  previousLevel: number;
  newLevel: number;
  xpGained: number;
  totalXP: number;
  leveledUp: boolean;
  newTitle: string;
  newUnlocks: string[];
}

export interface SessionGamificationUpdate {
  xpEarned: number;
  achievementsUnlocked: Achievement[];
  levelUpdate?: LevelUpdate;
  pressureEvents: PressureEvent[];
  liveScore: number;
  relationshipChanges?: Record<string, Partial<CharacterRelationship>>;
}

export interface GamificationApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

export class GamificationError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public details?: unknown
  ) {
    super(message);
    this.name = "GamificationError";
  }
}

class GamificationService {
  private baseUrl = "/api/gamification";

  /**
   * Transform backend response to frontend structure
   */
  private transformResponse<T>(response: any): T {
    // Handle both wrapped and direct response formats
    if (response.data) {
      return response.data;
    }
    return response;
  }

  // User Profile Management
  async getUserProfile(userId: string): Promise<UserGamificationProfile> {
    try {
      const response = await apiClient.get<GamificationApiResponse<UserGamificationProfile>>(
        `${this.baseUrl}/users/${userId}/profile`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get user gamification profile");
    }
  }

  async updateUserProfile(
    userId: string, 
    updates: Partial<UserGamificationProfile>
  ): Promise<UserGamificationProfile> {
    try {
      const response = await apiClient.put<GamificationApiResponse<UserGamificationProfile>>(
        `${this.baseUrl}/users/${userId}/profile`,
        updates
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to update user gamification profile");
    }
  }

  // Experience and Leveling
  async awardExperience(
    userId: string, 
    amount: number, 
    source: string, 
    metadata?: any
  ): Promise<LevelUpdate> {
    try {
      const response = await apiClient.post<GamificationApiResponse<LevelUpdate>>(
        `${this.baseUrl}/users/${userId}/experience`,
        { amount, source, metadata }
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to award experience");
    }
  }

  async getLevelRequirements(): Promise<any[]> {
    try {
      const response = await apiClient.get<GamificationApiResponse<any[]>>(
        `${this.baseUrl}/level-requirements`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get level requirements");
    }
  }

  async getUserLevel(userId: string): Promise<UserGamificationProfile['level']> {
    try {
      const response = await apiClient.get<GamificationApiResponse<UserGamificationProfile['level']>>(
        `${this.baseUrl}/users/${userId}/level`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get user level");
    }
  }

  async getUnlockedContent(userId: string): Promise<UserGamificationProfile['unlockedContent']> {
    try {
      const response = await apiClient.get<GamificationApiResponse<UserGamificationProfile['unlockedContent']>>(
        `${this.baseUrl}/users/${userId}/unlocked-content`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get unlocked content");
    }
  }

  // Achievements
  async getUserAchievements(
    userId: string,
    filters?: { category?: string; rarity?: string }
  ): Promise<{ achievements: (Achievement & { unlocked: boolean; unlockedAt?: Date; progress?: any })[], totalUnlocked: number, totalAvailable: number }> {
    try {
      const params = new URLSearchParams();
      if (filters?.category) params.append('category', filters.category);
      if (filters?.rarity) params.append('rarity', filters.rarity);

      const response = await apiClient.get<GamificationApiResponse<any>>(
        `${this.baseUrl}/users/${userId}/achievements?${params}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get user achievements");
    }
  }

  async getAllAchievements(): Promise<Achievement[]> {
    try {
      const response = await apiClient.get<GamificationApiResponse<Achievement[]>>(
        `${this.baseUrl}/achievements`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get all achievements");
    }
  }

  async getAchievementProgress(userId: string): Promise<any> {
    try {
      const response = await apiClient.get<GamificationApiResponse<any>>(
        `${this.baseUrl}/users/${userId}/achievement-progress`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get achievement progress");
    }
  }

  async checkAchievements(
    userId: string,
    sessionId: string,
    sessionData: any
  ): Promise<Achievement[]> {
    try {
      const response = await apiClient.post<GamificationApiResponse<{ unlockedAchievements: Achievement[] }>>(
        `${this.baseUrl}/users/${userId}/check-achievements`,
        { sessionId, sessionData }
      );
      return this.transformResponse(response.data).unlockedAchievements;
    } catch (error) {
      this.handleError(error, "Failed to check achievements");
    }
  }

  // Characters
  async getCharacters(
    userId: string,
    filters?: { difficulty?: number; unlocked?: boolean }
  ): Promise<(AICharacter & { unlocked: boolean; relationship?: CharacterRelationship })[]> {
    try {
      const params = new URLSearchParams({ userId });
      if (filters?.difficulty) params.append('difficulty', filters.difficulty.toString());
      if (filters?.unlocked !== undefined) params.append('unlocked', filters.unlocked.toString());

      const response = await apiClient.get<GamificationApiResponse<{ characters: any[] }>>(
        `${this.baseUrl}/characters?${params}`
      );
      return this.transformResponse(response.data).characters;
    } catch (error) {
      this.handleError(error, "Failed to get characters");
    }
  }

  async getCharacter(
    characterId: string,
    userId: string
  ): Promise<AICharacter & { unlocked: boolean; relationship?: CharacterRelationship }> {
    try {
      const response = await apiClient.get<GamificationApiResponse<any>>(
        `${this.baseUrl}/characters/${characterId}?userId=${userId}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get character");
    }
  }

  async getUnlockedCharacters(userId: string): Promise<string[]> {
    try {
      const response = await apiClient.get<GamificationApiResponse<{ characters: string[] }>>(
        `${this.baseUrl}/users/${userId}/unlocked-characters`
      );
      return this.transformResponse(response.data).characters;
    } catch (error) {
      this.handleError(error, "Failed to get unlocked characters");
    }
  }

  // Character Relationships
  async getCharacterRelationships(userId: string): Promise<Record<string, CharacterRelationship>> {
    try {
      const response = await apiClient.get<GamificationApiResponse<{ relationships: Record<string, CharacterRelationship> }>>(
        `${this.baseUrl}/users/${userId}/character-relationships`
      );
      return this.transformResponse(response.data).relationships;
    } catch (error) {
      this.handleError(error, "Failed to get character relationships");
    }
  }

  async updateCharacterRelationship(
    userId: string,
    characterId: string,
    updates: Partial<CharacterRelationship>
  ): Promise<CharacterRelationship> {
    try {
      const response = await apiClient.put<GamificationApiResponse<CharacterRelationship>>(
        `${this.baseUrl}/users/${userId}/character-relationships/${characterId}`,
        updates
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to update character relationship");
    }
  }

  async getRelationshipEffects(characterId: string): Promise<any> {
    try {
      const response = await apiClient.get<GamificationApiResponse<any>>(
        `${this.baseUrl}/characters/${characterId}/relationship-effects`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get relationship effects");
    }
  }

  // Leaderboards
  async getLeaderboard(
    timeframe: 'weekly' | 'monthly' | 'all_time' = 'weekly',
    scope: 'global' | 'organization' | 'industry' = 'global',
    scopeId?: string,
    limit: number = 50,
    userId?: string
  ): Promise<LeaderboardResponse> {
    try {
      const params = new URLSearchParams({
        timeframe,
        scope,
        limit: limit.toString()
      });

      if (scopeId) params.append('scopeId', scopeId);
      if (userId) params.append('userId', userId);

      const response = await apiClient.get<GamificationApiResponse<LeaderboardResponse>>(
        `${this.baseUrl}/leaderboards/negotiation?${params}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get leaderboard");
    }
  }

  async getOrganizationLeaderboard(
    organizationId: string,
    timeframe: 'weekly' | 'monthly' | 'all_time' = 'weekly'
  ): Promise<LeaderboardResponse> {
    try {
      const response = await apiClient.get<GamificationApiResponse<LeaderboardResponse>>(
        `${this.baseUrl}/leaderboards/organization/${organizationId}?timeframe=${timeframe}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get organization leaderboard");
    }
  }

  async getIndustryLeaderboard(
    industry: string,
    timeframe: 'weekly' | 'monthly' | 'all_time' = 'weekly'
  ): Promise<LeaderboardResponse> {
    try {
      const response = await apiClient.get<GamificationApiResponse<LeaderboardResponse>>(
        `${this.baseUrl}/leaderboards/by-industry?industry=${industry}&timeframe=${timeframe}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get industry leaderboard");
    }
  }

  async getFriendsLeaderboard(userId: string): Promise<LeaderboardResponse> {
    try {
      const response = await apiClient.get<GamificationApiResponse<LeaderboardResponse>>(
        `${this.baseUrl}/users/${userId}/friends/leaderboard`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get friends leaderboard");
    }
  }

  // Pressure Events
  async getPressureEvents(): Promise<PressureEvent[]> {
    try {
      const response = await apiClient.get<GamificationApiResponse<{ events: PressureEvent[] }>>(
        `${this.baseUrl}/pressure-events`
      );
      return this.transformResponse(response.data).events;
    } catch (error) {
      this.handleError(error, "Failed to get pressure events");
    }
  }

  async triggerPressureEvent(
    sessionId: string,
    eventId: string,
    customData?: any
  ): Promise<void> {
    try {
      await apiClient.post(
        `${this.baseUrl}/sessions/${sessionId}/pressure-events`,
        { eventId, customData }
      );
    } catch (error) {
      this.handleError(error, "Failed to trigger pressure event");
    }
  }

  async getLiveSessionUpdates(sessionId: string): Promise<any> {
    try {
      const response = await apiClient.get<GamificationApiResponse<any>>(
        `${this.baseUrl}/sessions/${sessionId}/live-updates`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get live session updates");
    }
  }

  async sendStakeholderMessage(
    sessionId: string,
    messageData: any
  ): Promise<void> {
    try {
      await apiClient.post(
        `${this.baseUrl}/sessions/${sessionId}/stakeholder-messages`,
        messageData
      );
    } catch (error) {
      this.handleError(error, "Failed to send stakeholder message");
    }
  }

  // Session Integration
  async initializeSessionGamification(
    sessionId: string,
    userId: string,
    characterId: string
  ): Promise<any> {
    try {
      const response = await apiClient.post<GamificationApiResponse<any>>(
        `${this.baseUrl}/sessions/${sessionId}/initialize`,
        { userId, characterId }
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to initialize session gamification");
    }
  }

  async updateSessionGamification(
    sessionId: string,
    moveData: any
  ): Promise<SessionGamificationUpdate> {
    try {
      const response = await apiClient.post<GamificationApiResponse<SessionGamificationUpdate>>(
        `${this.baseUrl}/sessions/${sessionId}/update`,
        moveData
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to update session gamification");
    }
  }

  async finalizeSessionGamification(sessionId: string): Promise<{
    finalXP: number;
    finalAchievements: Achievement[];
    relationshipUpdates: Record<string, CharacterRelationship>;
    levelUpdate?: LevelUpdate;
  }> {
    try {
      const response = await apiClient.post<GamificationApiResponse<any>>(
        `${this.baseUrl}/sessions/${sessionId}/finalize`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to finalize session gamification");
    }
  }

  // Dashboard and Analytics
  async getGamificationDashboard(userId: string): Promise<{
    overview: {
      level: number;
      totalXP: number;
      achievementsUnlocked: number;
      currentStreak: number;
      globalRank: number;
    };
    recentActivity: any[];
    recommendations: any[];
    upcomingUnlocks: any[];
  }> {
    try {
      const response = await apiClient.get<GamificationApiResponse<any>>(
        `${this.baseUrl}/users/${userId}/dashboard`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get gamification dashboard");
    }
  }

  async getPerformanceAnalytics(
    userId: string,
    timeframe?: string
  ): Promise<any> {
    try {
      const params = timeframe ? `?timeframe=${timeframe}` : '';
      const response = await apiClient.get<GamificationApiResponse<any>>(
        `${this.baseUrl}/users/${userId}/performance-analytics${params}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get performance analytics");
    }
  }

  // Social Features
  async shareAchievement(
    userId: string,
    achievementId: string,
    platform: string,
    message?: string
  ): Promise<void> {
    try {
      await apiClient.post(
        `${this.baseUrl}/users/${userId}/share-achievement`,
        { achievementId, platform, message, includeStats: true }
      );
    } catch (error) {
      this.handleError(error, "Failed to share achievement");
    }
  }

  async shareSessionResults(sessionId: string, shareData: any): Promise<void> {
    try {
      await apiClient.post(
        `${this.baseUrl}/sessions/${sessionId}/share`,
        shareData
      );
    } catch (error) {
      this.handleError(error, "Failed to share session results");
    }
  }

  async getAchievementFeed(
    organizationId?: string,
    limit: number = 20
  ): Promise<any[]> {
    try {
      const params = new URLSearchParams({ limit: limit.toString() });
      if (organizationId) params.append('organizationId', organizationId);

      const response = await apiClient.get<GamificationApiResponse<any[]>>(
        `${this.baseUrl}/feed/achievements?${params}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get achievement feed");
    }
  }

  async getTeamPerformance(teamId: string): Promise<any> {
    try {
      const response = await apiClient.get<GamificationApiResponse<any>>(
        `${this.baseUrl}/teams/${teamId}/performance`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get team performance");
    }
  }

  private handleError(error: unknown, defaultMessage: string): never {
    if (error instanceof AxiosError) {
      const message = error.response?.data?.message || defaultMessage;
      const code = error.response?.data?.code || "GAMIFICATION_ERROR";
      const statusCode = error.response?.status;
      const details = error.response?.data?.details;

      throw new GamificationError(message, code, statusCode, details);
    }

    throw new GamificationError(
      defaultMessage,
      "UNKNOWN_ERROR",
      undefined,
      error
    );
  }
}

export const gamificationService = new GamificationService();
