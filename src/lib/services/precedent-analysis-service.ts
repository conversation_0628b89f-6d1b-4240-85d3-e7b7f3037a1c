import { apiClient } from "../config";

// Request Types
export interface AIFocusOptions {
  prioritizeImpact?: boolean;
  detailLevel?: "concise" | "standard" | "detailed";
}

export interface PrecedentAnalysisOptions {
  includeRecommendations?: boolean;
  maxRelatedCases?: number;
  minRelevanceScore?: number;
  categorize?: boolean;
  assessImpact?: boolean;
  useAIAnalysis?: boolean;
  aiFocus?: AIFocusOptions;
}

// Response Types
export interface RelatedCase {
  caseName: string;
  citation: string;
  court: string;
  year: number;
  relevance: number;
  relationship: string; // "cites", "citedBy", "overruled", "companion", etc.
  summary?: string;
}

export interface KeyPoint {
  text: string;
  type?: "fact" | "holding" | "reasoning" | "dissent" | "concurrence";
}

export interface AIAnalysisDetails {
  reasoning?: string;
  source: "AI" | "RuleBasedHybrid";
}

export interface PrecedentAnalysisResult {
  citation: string;
  relevanceScore: number;
  impact: "positive" | "negative" | "neutral" | "unknown";
  category: string;
  keyPoints: KeyPoint[];
  recommendation?: string;
  relatedCases: RelatedCase[];
  aiAnalysisDetails?: AIAnalysisDetails;
}

class PrecedentAnalysisService {
  /**
   * Analyzes precedents cited in a document
   * @param documentId The ID of the document to analyze
   * @param options Options for the precedent analysis
   * @returns Array of precedent analysis results
   */
  async analyzePrecedents(
    documentId: string,
    options?: PrecedentAnalysisOptions
  ): Promise<PrecedentAnalysisResult[]> {
    try {
      const response = await apiClient.post<PrecedentAnalysisResult[]>(
        `/documents/precedents/${documentId}/analyze`,
        options || {}
      );
      return response.data;
    } catch (error) {
      console.error("Error analyzing precedents:", error);
      throw error;
    }
  }
}

export const precedentAnalysisService = new PrecedentAnalysisService();
