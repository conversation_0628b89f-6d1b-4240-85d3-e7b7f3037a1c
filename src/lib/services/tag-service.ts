import { apiClient } from "../config"
import type { Tag, CreateTagDto, UpdateTagDto } from "@/lib/types/document-organization"

class TagService {
  async createTag(tagData: CreateTagDto): Promise<Tag> {
    try {
      const response = await apiClient.post<Tag>('/document-organization/tags', tagData)
      return response.data
    } catch (error) {
      console.error("Error creating tag:", error)
      throw error
    }
  }

  async getTags(): Promise<Tag[]> {
    try {
      const response = await apiClient.get<Tag[]>('/document-organization/tags')
      return response.data
    } catch (error) {
      console.error("Error fetching tags:", error)
      throw error
    }
  }

  async getTag(id: string): Promise<Tag> {
    try {
      const response = await apiClient.get<Tag>(`/document-organization/tags/${id}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching tag ${id}:`, error)
      throw error
    }
  }

  async updateTag(id: string, tagData: UpdateTagDto): Promise<Tag> {
    try {
      const response = await apiClient.patch<Tag>(`/document-organization/tags/${id}`, tagData)
      return response.data
    } catch (error) {
      console.error(`Error updating tag ${id}:`, error)
      throw error
    }
  }

  async deleteTag(id: string): Promise<void> {
    try {
      await apiClient.delete(`/document-organization/tags/${id}`)
    } catch (error) {
      console.error(`Error deleting tag ${id}:`, error)
      throw error
    }
  }

  async addDocumentToTag(tagId: string, documentId: string): Promise<Tag> {
    try {
      const response = await apiClient.post<Tag>(
        `/document-organization/tags/${tagId}/documents`,
        { documentId },
      )
      return response.data
    } catch (error) {
      console.error(`Error adding document to tag ${tagId}:`, error)
      throw error
    }
  }

  async removeDocumentFromTag(tagId: string, documentId: string): Promise<void> {
    try {
      await apiClient.delete(`/document-organization/tags/${tagId}/documents/${documentId}`)
    } catch (error) {
      console.error(`Error removing document from tag ${tagId}:`, error)
      throw error
    }
  }

  async getTagsForDocument(documentId: string): Promise<Tag[]> {
    try {
      const response = await apiClient.get<Tag[]>(`/document-organization/tags/documents/${documentId}`)
      return response.data
    } catch (error) {
      console.error(`Error fetching tags for document ${documentId}:`, error)
      throw error
    }
  }
}

export const tagService = new TagService()
