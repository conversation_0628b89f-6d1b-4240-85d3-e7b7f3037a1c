/**
 * Campaign Mode Service
 * Handles story-driven negotiation campaigns with progression
 */

import { AxiosError } from "axios";
import { apiClient } from "@/lib/config";

export interface Campaign {
  id: string;
  title: string;
  description: string;
  storyline: string;
  difficulty: number;
  estimatedDuration: string; // e.g., "2-3 hours"
  industry: string;
  thumbnail: string;
  chapters: CampaignChapter[];
  prerequisites: CampaignPrerequisites;
  rewards: CampaignRewards;
  statistics: {
    totalPlayers: number;
    completionRate: number;
    averageScore: number;
  };
  isActive: boolean;
  createdAt: Date;
  updatedAt: Date;
}

export interface CampaignChapter {
  id: string;
  campaignId: string;
  chapterNumber: number;
  title: string;
  description: string;
  storyText: string;
  objectives: ChapterObjective[];
  scenarios: CampaignScenario[];
  unlockRequirements?: ChapterUnlockRequirements;
  rewards: ChapterRewards;
  isOptional: boolean;
}

export interface ChapterObjective {
  id: string;
  description: string;
  type: 'score_threshold' | 'relationship_building' | 'time_limit' | 'specific_outcome';
  target: any;
  isRequired: boolean;
  points: number;
}

export interface CampaignScenario {
  id: string;
  chapterId: string;
  scenarioId: string;
  order: number;
  contextualSetup: string; // Story context for this scenario
  modifiedObjectives?: any; // Campaign-specific objectives
  characterModifications?: any; // Character behavior changes for story
}

export interface CampaignPrerequisites {
  minLevel?: number;
  requiredAchievements?: string[];
  completedCampaigns?: string[];
  skillRequirements?: Record<string, number>;
}

export interface ChapterUnlockRequirements {
  previousChapterCompletion: boolean;
  minScore?: number;
  specificObjectives?: string[];
}

export interface CampaignRewards {
  xp: number;
  credits?: number;
  achievements: string[];
  unlocks: string[];
  title?: string;
  badge?: string;
}

export interface ChapterRewards {
  xp: number;
  credits?: number;
  unlocks?: string[];
}

export interface UserCampaignProgress {
  userId: string;
  campaignId: string;
  status: 'not_started' | 'in_progress' | 'completed' | 'abandoned';
  currentChapter: number;
  completedChapters: string[];
  chapterProgress: Record<string, ChapterProgress>;
  totalScore: number;
  startedAt: Date;
  lastPlayedAt: Date;
  completedAt?: Date;
  timeSpent: number; // in minutes
}

export interface ChapterProgress {
  chapterId: string;
  status: 'locked' | 'available' | 'in_progress' | 'completed';
  score: number;
  completedObjectives: string[];
  completedScenarios: string[];
  attempts: number;
  bestScore: number;
  timeSpent: number;
  completedAt?: Date;
}

export interface CampaignSession {
  id: string;
  userId: string;
  campaignId: string;
  chapterId: string;
  scenarioId: string;
  sessionId: string; // Reference to negotiation session
  storyContext: any;
  objectives: ChapterObjective[];
  status: 'active' | 'completed' | 'failed';
  score: number;
  startedAt: Date;
  completedAt?: Date;
}

export interface CampaignApiResponse<T> {
  success: boolean;
  data: T;
  message?: string;
  timestamp: string;
}

export class CampaignError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number,
    public details?: unknown
  ) {
    super(message);
    this.name = "CampaignError";
  }
}

class CampaignService {
  private baseUrl = "/api/campaigns";

  private transformResponse<T>(response: any): T {
    if (response.data) {
      return response.data;
    }
    return response;
  }

  // Campaign Management
  async getCampaigns(
    filters?: {
      difficulty?: number;
      industry?: string;
      status?: string;
      userId?: string;
    }
  ): Promise<(Campaign & { userProgress?: UserCampaignProgress })[]> {
    try {
      const params = new URLSearchParams();
      if (filters?.difficulty) params.append('difficulty', filters.difficulty.toString());
      if (filters?.industry) params.append('industry', filters.industry);
      if (filters?.status) params.append('status', filters.status);
      if (filters?.userId) params.append('userId', filters.userId);

      const response = await apiClient.get<CampaignApiResponse<{ campaigns: any[] }>>(
        `${this.baseUrl}?${params}`
      );
      return this.transformResponse(response.data).campaigns;
    } catch (error) {
      this.handleError(error, "Failed to get campaigns");
    }
  }

  async getCampaign(
    campaignId: string,
    userId?: string
  ): Promise<Campaign & { userProgress?: UserCampaignProgress }> {
    try {
      const params = userId ? `?userId=${userId}` : '';
      const response = await apiClient.get<CampaignApiResponse<any>>(
        `${this.baseUrl}/${campaignId}${params}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get campaign");
    }
  }

  async getAvailableCampaigns(userId: string): Promise<Campaign[]> {
    try {
      const response = await apiClient.get<CampaignApiResponse<{ campaigns: Campaign[] }>>(
        `${this.baseUrl}/available?userId=${userId}`
      );
      return this.transformResponse(response.data).campaigns;
    } catch (error) {
      this.handleError(error, "Failed to get available campaigns");
    }
  }

  // User Progress Management
  async getUserCampaignProgress(
    userId: string,
    campaignId: string
  ): Promise<UserCampaignProgress> {
    try {
      const response = await apiClient.get<CampaignApiResponse<UserCampaignProgress>>(
        `${this.baseUrl}/${campaignId}/users/${userId}/progress`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get user campaign progress");
    }
  }

  async startCampaign(
    userId: string,
    campaignId: string
  ): Promise<UserCampaignProgress> {
    try {
      const response = await apiClient.post<CampaignApiResponse<UserCampaignProgress>>(
        `${this.baseUrl}/${campaignId}/users/${userId}/start`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to start campaign");
    }
  }

  async updateCampaignProgress(
    userId: string,
    campaignId: string,
    updates: Partial<UserCampaignProgress>
  ): Promise<UserCampaignProgress> {
    try {
      const response = await apiClient.put<CampaignApiResponse<UserCampaignProgress>>(
        `${this.baseUrl}/${campaignId}/users/${userId}/progress`,
        updates
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to update campaign progress");
    }
  }

  // Chapter Management
  async getChapter(
    campaignId: string,
    chapterId: string,
    userId?: string
  ): Promise<CampaignChapter & { userProgress?: ChapterProgress }> {
    try {
      const params = userId ? `?userId=${userId}` : '';
      const response = await apiClient.get<CampaignApiResponse<any>>(
        `${this.baseUrl}/${campaignId}/chapters/${chapterId}${params}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get chapter");
    }
  }

  async startChapter(
    userId: string,
    campaignId: string,
    chapterId: string
  ): Promise<ChapterProgress> {
    try {
      const response = await apiClient.post<CampaignApiResponse<ChapterProgress>>(
        `${this.baseUrl}/${campaignId}/chapters/${chapterId}/users/${userId}/start`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to start chapter");
    }
  }

  async completeChapter(
    userId: string,
    campaignId: string,
    chapterId: string,
    results: {
      score: number;
      completedObjectives: string[];
      timeSpent: number;
    }
  ): Promise<ChapterProgress> {
    try {
      const response = await apiClient.post<CampaignApiResponse<ChapterProgress>>(
        `${this.baseUrl}/${campaignId}/chapters/${chapterId}/users/${userId}/complete`,
        results
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to complete chapter");
    }
  }

  // Session Management
  async startCampaignSession(
    userId: string,
    campaignId: string,
    chapterId: string,
    scenarioId: string
  ): Promise<CampaignSession> {
    try {
      const response = await apiClient.post<CampaignApiResponse<CampaignSession>>(
        `${this.baseUrl}/${campaignId}/chapters/${chapterId}/sessions`,
        { userId, scenarioId }
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to start campaign session");
    }
  }

  async completeCampaignSession(
    sessionId: string,
    results: {
      score: number;
      completedObjectives: string[];
      sessionData: any;
    }
  ): Promise<CampaignSession> {
    try {
      const response = await apiClient.post<CampaignApiResponse<CampaignSession>>(
        `${this.baseUrl}/sessions/${sessionId}/complete`,
        results
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to complete campaign session");
    }
  }

  // Analytics and Leaderboards
  async getCampaignLeaderboard(
    campaignId: string,
    timeframe: 'weekly' | 'monthly' | 'all_time' = 'all_time'
  ): Promise<any[]> {
    try {
      const response = await apiClient.get<CampaignApiResponse<any[]>>(
        `${this.baseUrl}/${campaignId}/leaderboard?timeframe=${timeframe}`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get campaign leaderboard");
    }
  }

  async getCampaignAnalytics(campaignId: string): Promise<any> {
    try {
      const response = await apiClient.get<CampaignApiResponse<any>>(
        `${this.baseUrl}/${campaignId}/analytics`
      );
      return this.transformResponse(response.data);
    } catch (error) {
      this.handleError(error, "Failed to get campaign analytics");
    }
  }

  private handleError(error: unknown, defaultMessage: string): never {
    if (error instanceof AxiosError) {
      const message = error.response?.data?.message || defaultMessage;
      const code = error.response?.data?.code || "CAMPAIGN_ERROR";
      const statusCode = error.response?.status;
      const details = error.response?.data?.details;

      throw new CampaignError(message, code, statusCode, details);
    }

    throw new CampaignError(
      defaultMessage,
      "UNKNOWN_ERROR",
      undefined,
      error
    );
  }
}

export const campaignService = new CampaignService();

// Mock campaign data for development
export const MOCK_CAMPAIGNS: Partial<Campaign>[] = [
  {
    id: 'startup_journey',
    title: 'The Startup Journey',
    description: 'Navigate the challenges of building a startup from seed funding to IPO',
    storyline: 'You are the founder of a promising tech startup. Guide your company through critical negotiations that will determine its fate.',
    difficulty: 2,
    estimatedDuration: '3-4 hours',
    industry: 'Technology',
    thumbnail: '/campaigns/startup-journey.jpg',
    prerequisites: {
      minLevel: 1
    },
    rewards: {
      xp: 2000,
      achievements: ['startup_founder', 'deal_maker'],
      unlocks: ['investor_characters', 'startup_scenarios'],
      title: 'Startup Founder'
    }
  },
  {
    id: 'corporate_ladder',
    title: 'Climbing the Corporate Ladder',
    description: 'Master internal negotiations and office politics to reach the C-suite',
    storyline: 'Start as a junior employee and negotiate your way to the top of a Fortune 500 company.',
    difficulty: 3,
    estimatedDuration: '4-5 hours',
    industry: 'Corporate',
    thumbnail: '/campaigns/corporate-ladder.jpg',
    prerequisites: {
      minLevel: 5,
      requiredAchievements: ['veteran_negotiator']
    },
    rewards: {
      xp: 3500,
      achievements: ['corporate_climber', 'executive_presence'],
      unlocks: ['executive_scenarios', 'board_room_characters'],
      title: 'Corporate Executive'
    }
  },
  {
    id: 'international_deals',
    title: 'International Deal Maker',
    description: 'Navigate cross-cultural negotiations across global markets',
    storyline: 'Represent your company in high-stakes international negotiations across different cultures and business practices.',
    difficulty: 4,
    estimatedDuration: '5-6 hours',
    industry: 'International Business',
    thumbnail: '/campaigns/international-deals.jpg',
    prerequisites: {
      minLevel: 10,
      completedCampaigns: ['startup_journey', 'corporate_ladder']
    },
    rewards: {
      xp: 5000,
      achievements: ['global_negotiator', 'cultural_bridge'],
      unlocks: ['international_characters', 'cultural_scenarios'],
      title: 'Global Deal Maker'
    }
  }
];
