import { APIClient } from "@/lib/config";
import type {
	CreateDepositionPreparationDto,
	UpdateDepositionPreparationDto,
	CreateDepositionQuestionDto,
	UpdateDepositionQuestionDto,
	GenerateQuestionsDto,
	AnalyzeDepositionDto,
	DepositionPreparation,
	QuestionGenerationResult,
	DepositionQuestion,
	QuestionCategory,
	QuestionPriority,
	CategoryInputFormat,
	DepositionAnalysisResponse,
} from "@/lib/types/deposition";
import { CATEGORY_MAPPINGS } from "@/lib/types/deposition";

class DepositionService {
	private readonly baseUrl = `/depositions`;
	private apiClient = new APIClient();

	/**
	 * Create a new deposition preparation
	 */
	async createDepositionPreparation(
		data: CreateDepositionPreparationDto
	): Promise<DepositionPreparation> {
		try {
			const response = await this.apiClient.post<DepositionPreparation>(
				this.baseUrl,
				data
			);
			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error
					? error.message
					: "Failed to create deposition preparation";
			throw new Error(message);
		}
	}

	/**
	 * Get all deposition preparations
	 */
	async getAllDepositionPreparations(): Promise<DepositionPreparation[]> {
		try {
			const response = await this.apiClient.get<DepositionPreparation[]>(
				this.baseUrl
			);
			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error
					? error.message
					: "Failed to fetch deposition preparations";
			throw new Error(message);
		}
	}

	/**
	 * Get a specific deposition preparation by ID
	 */
	async getDepositionPreparation(id: string): Promise<DepositionPreparation> {
		try {
			const response = await this.apiClient.get<DepositionPreparation>(
				`${this.baseUrl}/${id}`
			);
			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error
					? error.message
					: "Failed to fetch deposition preparation";
			throw new Error(message);
		}
	}

	/**
	 * Update a deposition preparation
	 */
	async updateDepositionPreparation(
		id: string,
		data: UpdateDepositionPreparationDto
	): Promise<DepositionPreparation> {
		try {
			const response = await this.apiClient.put<DepositionPreparation>(
				`${this.baseUrl}/${id}`,
				data
			);
			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error
					? error.message
					: "Failed to update deposition preparation";
			throw new Error(message);
		}
	}

	/**
	 * Delete a deposition preparation
	 */
	async deleteDepositionPreparation(id: string): Promise<void> {
		try {
			await this.apiClient.delete(`${this.baseUrl}/${id}`);
		} catch (error: unknown) {
			const message =
				error instanceof Error
					? error.message
					: "Failed to delete deposition preparation";
			throw new Error(message);
		}
	}

	/**
	 * Get deposition analysis by ID
	 */
	async getDepositionAnalysis(id: string): Promise<DepositionAnalysisResponse> {
		try {
			const response = await this.apiClient.get<DepositionAnalysisResponse>(
				`/depositions/analysis/${id}`
			);
			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error
					? error.message
					: "Failed to get deposition analysis";
			throw new Error(message);
		}
	}

	/**
	 * Analyze deposition transcript
	 */
	async analyzeDepositionTranscript(
		data: AnalyzeDepositionDto
	): Promise<DepositionAnalysisResponse> {
		try {
			const response = await this.apiClient.post<DepositionAnalysisResponse>(
				`/depositions/analyze-transcript`,
				data
			);
			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error
					? error.message
					: "Failed to analyze deposition transcript";
			if (message.includes("403")) {
				throw new Error("Access to AI question generation feature is required");
			}
			throw new Error(message);
		}
	}

	/**
	 * Get analysis history for a deposition
	 */
	async getAnalysisHistory(
		depositionId: string
	): Promise<DepositionAnalysisResponse[]> {
		try {
			const response = await this.apiClient.get<DepositionAnalysisResponse[]>(
				`/depositions/${depositionId}/analyses`
			);
			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error
					? error.message
					: "Failed to get analysis history";
			throw new Error(message);
		}
	}

	/**
	 * Add a question to a deposition preparation
	 */
	async addQuestion(
		id: string,
		data: CreateDepositionQuestionDto
	): Promise<DepositionQuestion> {
		try {
			const response = await this.apiClient.post<DepositionQuestion>(
				`${this.baseUrl}/${id}/questions`,
				data
			);
			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error ? error.message : "Failed to add question";
			throw new Error(message);
		}
	}

	/**
	 * Update a question in a deposition preparation
	 */
	async updateQuestion(
		id: string,
		questionId: string,
		data: UpdateDepositionQuestionDto
	): Promise<DepositionQuestion> {
		try {
			const response = await this.apiClient.put<DepositionQuestion>(
				`${this.baseUrl}/${id}/questions/${questionId}`,
				data
			);
			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error ? error.message : "Failed to update question";
			throw new Error(message);
		}
	}

	/**
	 * Delete a question from a deposition preparation
	 */
	async deleteQuestion(id: string, questionId: string): Promise<void> {
		try {
			await this.apiClient.delete(
				`${this.baseUrl}/${id}/questions/${questionId}`
			);
		} catch (error: unknown) {
			const message =
				error instanceof Error ? error.message : "Failed to delete question";
			throw new Error(message);
		}
	}

	/**
	 * Generate questions using AI (global)
	 * @param data The data needed to generate questions
	 * @returns A promise that resolves to the generated questions
	 */
	async generateQuestions(
		data: GenerateQuestionsDto
	): Promise<QuestionGenerationResult> {
		try {
			const response = await this.apiClient.post<QuestionGenerationResult>(
				`${this.baseUrl}/generate-questions`,
				data
			);

			// Ensure the response has the correct structure
			if (!response.data.questions) {
				throw new Error(
					"Invalid response format from question generation service"
				);
			}

			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error ? error.message : "Failed to generate questions";
			if (message.includes("403")) {
				throw new Error("Access to AI question generation feature is required");
			}
			throw new Error(message);
		}
	}

	/**
	 * Generate questions for a specific deposition
	 * @param depositionId The ID of the deposition to generate questions for
	 * @param data The data needed to generate questions
	 * @returns A promise that resolves to the generated questions
	 */
	async generateQuestionsForDeposition(
		depositionId: string,
		data: Partial<GenerateQuestionsDto> & {
			questionCategories?: (CategoryInputFormat | QuestionCategory)[];
			priorityLevel?: QuestionPriority | "all";
		}
	): Promise<QuestionGenerationResult> {
		try {
			const mappedData = {
				...data,
				questionCategories: data.questionCategories
					? this.mapQuestionCategories(
							data.questionCategories as (
								| CategoryInputFormat
								| QuestionCategory
							)[]
					  )
					: undefined,
			};
			const response = await this.apiClient.post<QuestionGenerationResult>(
				`${this.baseUrl}/${depositionId}/generate-questions`,
				mappedData
			);
			return response.data;
		} catch (error: unknown) {
			const message =
				error instanceof Error
					? error.message
					: "Failed to generate questions for deposition";
			if (message.includes("403")) {
				throw new Error("Access to AI question generation feature is required");
			}
			throw new Error(message);
		}
	}

	/**
	 * Normalize and map input category to standard QuestionCategory
	 */
	private mapQuestionCategories(
		categories: (string | QuestionCategory)[]
	): QuestionCategory[] {
		if (!categories || categories.length === 0) {
			return [];
		}

		return categories.map((category) => {
			// If it's already a standard category, return it
			if (
				Object.values<string>({
					GENERAL: "GENERAL",
					CREDIBILITY: "CREDIBILITY",
					CONSISTENCY: "CONSISTENCY",
					DOCUMENTATION: "DOCUMENTATION",
					EXPERT_QUALIFICATION: "EXPERT_QUALIFICATION",
					IMPEACHMENT: "IMPEACHMENT",
				}).includes(category as string)
			) {
				return category as QuestionCategory;
			}

			// Otherwise, try to map from input variations to standard categories
			const matchedCategory = Object.entries(CATEGORY_MAPPINGS).find(
				([, config]) => config.inputVariations.includes(category as string)
			);

			return matchedCategory
				? (matchedCategory[0] as QuestionCategory)
				: ("GENERAL" as QuestionCategory);
		});
	}
}

export const depositionService = new DepositionService();
