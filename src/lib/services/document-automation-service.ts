import { AxiosError } from "axios";
import { apiClient } from "../config";
import type {
  AIAssistedDraftingRequest,
  AIAssistedDraftingResponse,
  RelatedDocumentsRequest,
  RelatedDocumentsResponse,
  ClauseIntelligenceRequest,
  ClauseIntelligenceResponse,
  ClauseLibraryBuildRequest,
  ClauseLibraryBuildResponse,
  DocumentAutomationError,
} from "../types/document-automation";

export class DocumentAutomationServiceError extends Error {
  constructor(
    message: string,
    public code: string,
    public statusCode?: number
  ) {
    super(message);
    this.name = "DocumentAutomationServiceError";
  }
}

class DocumentAutomationService {
  private readonly baseUrl = "/api/documents/automation";

  /**
   * Generate new legal documents using AI-assisted drafting
   */
  async aiAssistedDrafting(
    request: AIAssistedDraftingRequest
  ): Promise<AIAssistedDraftingResponse> {
    try {
      const response = await apiClient.post<AIAssistedDraftingResponse>(
        `${this.baseUrl}/ai-assisted-drafting`,
        request
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorData = error.response?.data as DocumentAutomationError;
        if (error.response?.status === 403) {
          throw new DocumentAutomationServiceError(
            errorData?.message || "Feature not available in current subscription",
            "FEATURE_NOT_AVAILABLE",
            403
          );
        }
        if (error.response?.status === 400) {
          throw new DocumentAutomationServiceError(
            errorData?.message || "Invalid request parameters",
            "VALIDATION_ERROR",
            400
          );
        }
        throw new DocumentAutomationServiceError(
          errorData?.message || "Failed to generate document",
          "DRAFTING_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Generate related documents (schedules, exhibits, etc.) from a primary document
   */
  async generateRelatedDocuments(
    request: RelatedDocumentsRequest
  ): Promise<RelatedDocumentsResponse> {
    try {
      const response = await apiClient.post<RelatedDocumentsResponse>(
        `${this.baseUrl}/generate-related-documents`,
        request
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorData = error.response?.data as DocumentAutomationError;
        if (error.response?.status === 404) {
          throw new DocumentAutomationServiceError(
            errorData?.message || "Primary document not found",
            "DOCUMENT_NOT_FOUND",
            404
          );
        }
        if (error.response?.status === 403) {
          throw new DocumentAutomationServiceError(
            errorData?.message || "Feature not available in current subscription",
            "FEATURE_NOT_AVAILABLE",
            403
          );
        }
        throw new DocumentAutomationServiceError(
          errorData?.message || "Failed to generate related documents",
          "GENERATION_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Get intelligent clause suggestions and auto-population recommendations
   */
  async getClauseIntelligence(
    request: ClauseIntelligenceRequest
  ): Promise<ClauseIntelligenceResponse> {
    try {
      const response = await apiClient.post<ClauseIntelligenceResponse>(
        `${this.baseUrl}/clause-intelligence`,
        request
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorData = error.response?.data as DocumentAutomationError;
        if (error.response?.status === 403) {
          throw new DocumentAutomationServiceError(
            errorData?.message || "Feature not available in current subscription",
            "FEATURE_NOT_AVAILABLE",
            403
          );
        }
        throw new DocumentAutomationServiceError(
          errorData?.message || "Failed to get clause suggestions",
          "CLAUSE_INTELLIGENCE_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }

  /**
   * Auto-build clause library from organization's document corpus
   */
  async buildClauseLibrary(
    request: ClauseLibraryBuildRequest = {}
  ): Promise<ClauseLibraryBuildResponse> {
    try {
      const response = await apiClient.post<ClauseLibraryBuildResponse>(
        `${this.baseUrl}/build-clause-library`,
        request
      );
      return response.data;
    } catch (error) {
      if (error instanceof AxiosError) {
        const errorData = error.response?.data as DocumentAutomationError;
        if (error.response?.status === 403) {
          throw new DocumentAutomationServiceError(
            errorData?.message || "Feature not available in current subscription",
            "FEATURE_NOT_AVAILABLE",
            403
          );
        }
        throw new DocumentAutomationServiceError(
          errorData?.message || "Failed to build clause library",
          "LIBRARY_BUILD_FAILED",
          error.response?.status
        );
      }
      throw error;
    }
  }
}

export const documentAutomationService = new DocumentAutomationService();
export default documentAutomationService;
