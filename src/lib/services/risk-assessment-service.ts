import { useMutation, useQuery } from "@tanstack/react-query";
import { getStoredAuth } from "@/lib/auth/auth-service";
import { apiClient } from "@/lib/config";
import { AxiosError } from "axios";

interface RiskAnalysis {
  overallRisk: 'low' | 'medium' | 'high';
  categories: Array<{
    name: string;
    level: 'low' | 'medium' | 'high';
    findings: string[];
    recommendations: string[];
  }>;
}

interface RiskCategory {
  id: string;
  name: string;
  description: string;
}

interface AnalyzeRiskParams {
  documentId: string;
  categories?: string[];
}

export class RiskAssessmentError extends Error {
  constructor(
    message: string,
    public code: "ANALYSIS_FAILED" | "INVALID_CATEGORY",
    public status?: number
  ) {
    super(message);
    this.name = "RiskAssessmentError";
  }
}

function getAuthHeaders() {
  const { accessToken, organizationId } = getStoredAuth();
  const headers: Record<string, string> = {
    'Content-Type': 'application/json',
  };
  
  if (accessToken) {
    headers["Authorization"] = `Bearer ${accessToken}`;
  }
  
  if (organizationId) {
    headers["X-Tenant-Id"] = organizationId;
  }

  return headers;
}

// API functions
async function analyzeRisks(params: AnalyzeRiskParams): Promise<RiskAnalysis> {
  try {
    const response = await apiClient.post<RiskAnalysis>(
      `/risk-assessment/analyze`,
      params,
      { headers: getAuthHeaders() }
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new RiskAssessmentError(
        error.response?.data?.message || error.message || 'Risk analysis failed',
        "ANALYSIS_FAILED",
        error.response?.status
      );
    }
    throw error;
  }
}

async function getRiskTypes(): Promise<RiskCategory[]> {
  try {
    const response = await apiClient.get<{ categories: RiskCategory[] }>(
      `/risk-assessment/types`,
      { headers: getAuthHeaders() }
    );
    return response.data.categories;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new RiskAssessmentError(
        error.response?.data?.message || error.message || 'Failed to fetch risk types',
        "ANALYSIS_FAILED",
        error.response?.status
      );
    }
    throw error;
  }
}

// Query hooks
export function useAnalyzeRisks() {
  return useMutation<RiskAnalysis, RiskAssessmentError, AnalyzeRiskParams>({
    mutationFn: analyzeRisks,
  });
}

export function useRiskTypes() {
  return useQuery<RiskCategory[], RiskAssessmentError>({
    queryKey: ['riskTypes'],
    queryFn: getRiskTypes,
  });
}