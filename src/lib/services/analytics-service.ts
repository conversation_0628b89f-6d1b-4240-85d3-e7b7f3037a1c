import { apiClient } from "../config";
import { useEffect, useState } from 'react';

export function useDashboardData(params?: AnalyticsParams) {
  const [data, setData] = useState<OrganizationAnalytics | null>(null);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<Error | null>(null);

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await analyticsService.getDashboardAnalytics(params);
        setData(result);
        setError(null);
      } catch (err) {
        setError(err as Error);
      } finally {
        setLoading(false);
      }
    };

    fetchData();
  }, [params]);

  return { data, loading, error };
}

export function formatStorageSize(bytes: number): string {
  const units = ['B', 'KB', 'MB', 'GB', 'TB'];
  let size = bytes;
  let unitIndex = 0;

  while (size >= 1024 && unitIndex < units.length - 1) {
    size /= 1024;
    unitIndex++;
  }

  return `${size.toFixed(2)} ${units[unitIndex]}`;
}

export function formatPercentage(value: number): string {
  return `${(value * 100).toFixed(1)}%`;
}

export function formatCurrency(amount: number): string {
  return new Intl.NumberFormat('en-US', {
    style: 'currency',
    currency: 'USD'
  }).format(amount);
}

export function getUsageTrend(timeSeriesData: TimeSeriesData) {
  if (!timeSeriesData?.documentUploads?.length) {
    return { trend: 0, percentage: 0 };
  }

  const current = timeSeriesData.documentUploads[timeSeriesData.documentUploads.length - 1].count;
  const previous = timeSeriesData.documentUploads[0].count;
  
  const trend = current - previous;
  const percentage = previous ? (trend / previous) * 100 : 0;

  return {
    trend,
    percentage
  };
}


export interface TimeRange {
	start: string;
	end: string;
}

// Update the DocumentAnalysis interface to include the new fields
export interface DocumentAnalysis {
	totalDocuments: number;
	analyzedDocuments: number;
	comparedDocuments: number;
	documentsByType: Array<{
		type: string;
		count: number;
	}>;
	averageAnalysisTime: number;
	classificationMetrics?: {
		automaticClassifications: number;
		manualClassifications: number;
		unclassifiedDocuments: number;
		classificationAccuracy: number;
	};
	comparisonMetrics?: {
		totalComparisons: number;
		basicComparisons: number;
		enhancedComparisons: number;
		comparisonsByType: Array<{
			type: string;
			count: number;
		}>;
	};
}

export interface UserEngagement {
	totalUsers: number;
	activeUsers: number;
	totalQueries: number;
	averageQueriesPerUser: number;
	averageResponseTime: number;
	userActivity?: Array<{
		userId: string;
		name: string;
		queryCount: number;
		documentCount: number;
		lastActive: string;
	}>;
}

export interface TopicAnalysis {
	topTopics: Array<{
		topic: string;
		count: number;
	}>;
	topicTrends: Array<{
		date: string;
		topics: Array<{
			topic: string;
			count: number;
		}>;
	}>;
}

export interface CitationMetrics {
	totalCitations: number;
	topCitedCases: Array<{
		caseName: string;
		count: number;
		url?: string;
		jurisdiction?: string;
	}>;
	citationsByJurisdiction: Array<{
		jurisdiction: string;
		count: number;
	}>;
	citationsByYear?: Array<{
		year: number;
		count: number;
	}>;
	citationNetwork?: {
		nodes: Array<{
			id: string;
			name: string;
			year: number;
			weight: number;
		}>;
		links: Array<{
			source: string;
			target: string;
			weight: number;
		}>;
	};
}

// Update the TimeSeriesData interface to include the new fields
export interface TimeSeriesData {
	documentUploads: Array<{
		date: string;
		count: number;
	}>;
	documentAnalyses: Array<{
		date: string;
		count: number;
	}>;
	documentComparisons: Array<{
		date: string;
		count: number;
		basic?: number;
		enhanced?: number;
	}>;
	documentClassifications?: Array<{
		date: string;
		count: number;
		automatic?: number;
		manual?: number;
	}>;
	userQueries: Array<{
		date: string;
		count: number;
	}>;
	averageResponseTimes: Array<{
		date: string;
		value: number;
	}>;
}

export interface OrganizationAnalytics {
	organizationId: string;
	timeRange: TimeRange;
	documentAnalysis: DocumentAnalysis;
	userEngagement: UserEngagement;
	topicAnalysis: TopicAnalysis;
	citationMetrics: CitationMetrics;
	timeSeriesData: TimeSeriesData;
	lastUpdated: string;
}

export interface DocumentAnalytics {
	organizationId: string;
	timeRange: TimeRange;
	documentAnalysis: DocumentAnalysis;
	timeSeriesData: {
		documentClassifications: TimeSeriesData["documentClassifications"];
		documentUploads: TimeSeriesData["documentUploads"];
		documentAnalyses: TimeSeriesData["documentAnalyses"];
		documentComparisons: TimeSeriesData["documentComparisons"];
	};
	lastUpdated: string;
}

export interface UserAnalytics {
	organizationId: string;
	timeRange: TimeRange;
	userEngagement: UserEngagement;
	topicAnalysis: TopicAnalysis;
	timeSeriesData: {
		userQueries: TimeSeriesData["userQueries"];
		averageResponseTimes: TimeSeriesData["averageResponseTimes"];
	};
	lastUpdated: string;
}

export interface CitationAnalytics {
	organizationId: string;
	timeRange: TimeRange;
	citationMetrics: CitationMetrics;
	lastUpdated: string;
}

export interface AnalyticsParams {
	startDate?: string;
	endDate?: string;
	granularity?: "day" | "week" | "month";
	includeTimeSeriesData?: boolean;
	includeCitationMetrics?: boolean;
	includeTopicAnalysis?: boolean;
}

class AnalyticsService {

	async getDashboardAnalytics(
		params?: AnalyticsParams
	): Promise<OrganizationAnalytics> {
		try {
			const response = await apiClient.get<OrganizationAnalytics>(
				`/analytics/dashboard`,
				{
					params,
				}
			);
			return response.data;
		} catch (error) {
			console.error("Error fetching dashboard analytics:", error);
			throw error;
		}
	}

	async getOrganizationAnalytics(
		params?: AnalyticsParams
	): Promise<OrganizationAnalytics> {
		try {
			const response = await apiClient.get<OrganizationAnalytics>(
				`/analytics/organization`,
				{
					params,
				}
			);
			return response.data;
		} catch (error) {
			console.error("Error fetching organization analytics:", error);
			throw error;
		}
	}

	async getDocumentAnalytics(
		params?: AnalyticsParams
	): Promise<DocumentAnalytics> {
		try {
			const response = await apiClient.get<DocumentAnalytics>(
				`/analytics/documents`,
				{
					params,
				}
			);
			return response.data;
		} catch (error) {
			console.error("Error fetching document analytics:", error);
			throw error;
		}
	}

	async getUserAnalytics(params?: AnalyticsParams): Promise<UserAnalytics> {
		try {
			const response = await apiClient.get<UserAnalytics>(`/analytics/users`, {
				params,
			});
			return response.data;
		} catch (error) {
			console.error("Error fetching user analytics:", error);
			throw error;
		}
	}

	async getCitationAnalytics(
		params?: AnalyticsParams
	): Promise<CitationAnalytics> {
		try {
			const response = await apiClient.get<CitationAnalytics>(
				`/analytics/citations`,
				{
					params,
				}
			);
			return response.data;
		} catch (error) {
			console.error("Error fetching citation analytics:", error);
			throw error;
		}
	}
}

export const analyticsService = new AnalyticsService();
