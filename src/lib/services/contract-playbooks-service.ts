import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query";
import { apiClient } from "@/lib/config";
import { AxiosError } from "axios";
import type {
	ContractPlaybook,
	ContractAnalysis,
	PlaybookSearchParams,
	PlaybooksResponse,
	CreatePlaybookRequest,
	UpdatePlaybookRequest,
	AnalyzeContractRequest,
	AnalysisSearchParams,
	AnalysesResponse,
	PlaybookAnalytics,
	DuplicatePlaybookRequest,
	ExportPlaybookResponse,
	ImportPlaybookRequest,
	ContractPlaybookError,
} from "@/lib/types/contract-playbooks";
import type {
	SampleContractPlaybook,
	SampleContractPlaybookFilters,
	SamplePlaybooksResponse,
	SamplePlaybookStatsResponse,
	ClonedContractPlaybook,
} from "@/lib/types/sample-playbooks";

// Custom error class for contract playbook operations
export class ContractPlaybookServiceError extends Error {
	constructor(
		message: string,
		public code: string,
		public statusCode?: number,
		public details?: any
	) {
		super(message);
		this.name = "ContractPlaybookServiceError";
	}
}

// API Service Class
class ContractPlaybooksService {
	private baseUrl = "/contract-playbooks";

	// Playbook Management
	async getPlaybooks(
		params?: PlaybookSearchParams
	): Promise<PlaybooksResponse> {
		try {
			const response = await apiClient.get<PlaybooksResponse>(this.baseUrl, {
				params,
			});
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch playbooks");
		}
	}

	async createPlaybook(data: CreatePlaybookRequest): Promise<ContractPlaybook> {
		try {
			const response = await apiClient.post<ContractPlaybook>(
				this.baseUrl,
				data
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to create playbook");
		}
	}

	async getPlaybook(id: string): Promise<ContractPlaybook> {
		try {
			const response = await apiClient.get<ContractPlaybook>(
				`${this.baseUrl}/${id}`
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch playbook");
		}
	}

	async updatePlaybook(
		id: string,
		data: UpdatePlaybookRequest
	): Promise<ContractPlaybook> {
		try {
			const response = await apiClient.put<ContractPlaybook>(
				`${this.baseUrl}/${id}`,
				data
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to update playbook");
		}
	}

	async deletePlaybook(id: string): Promise<void> {
		try {
			await apiClient.delete(`${this.baseUrl}/${id}`);
		} catch (error) {
			this.handleError(error, "Failed to delete playbook");
		}
	}

	// Contract Analysis
	async analyzeContract(
		data: AnalyzeContractRequest
	): Promise<ContractAnalysis> {
		try {
			const response = await apiClient.post<ContractAnalysis>(
				`${this.baseUrl}/analyze`,
				data
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to analyze contract");
		}
	}

	async getAnalyses(params?: AnalysisSearchParams): Promise<AnalysesResponse> {
		try {
			const response = await apiClient.get<AnalysesResponse>(
				`${this.baseUrl}/analyses`,
				{ params }
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch analyses");
		}
	}

	async getAnalysis(id: string): Promise<ContractAnalysis> {
		try {
			const response = await apiClient.get<ContractAnalysis>(
				`${this.baseUrl}/analyses/${id}`
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch analysis");
		}
	}

	async deleteAnalysis(id: string): Promise<void> {
		try {
			await apiClient.delete(`${this.baseUrl}/analyses/${id}`);
		} catch (error) {
			this.handleError(error, "Failed to delete analysis");
		}
	}

	// Utility Methods
	async getPlaybookAnalytics(id: string): Promise<PlaybookAnalytics> {
		try {
			const response = await apiClient.get<PlaybookAnalytics>(
				`${this.baseUrl}/${id}/analytics`
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch playbook analytics");
		}
	}

	async duplicatePlaybook(
		id: string,
		data: DuplicatePlaybookRequest
	): Promise<ContractPlaybook> {
		try {
			const response = await apiClient.post<ContractPlaybook>(
				`${this.baseUrl}/${id}/duplicate`,
				data
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to duplicate playbook");
		}
	}

	async exportPlaybook(id: string): Promise<ExportPlaybookResponse> {
		try {
			const response = await apiClient.get<ExportPlaybookResponse>(
				`${this.baseUrl}/${id}/export`
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to export playbook");
		}
	}

	async importPlaybook(data: ImportPlaybookRequest): Promise<ContractPlaybook> {
		try {
			const response = await apiClient.post<ContractPlaybook>(
				`${this.baseUrl}/import`,
				data
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to import playbook");
		}
	}

	// Sample Contract Playbooks
	async getSamplePlaybooks(
		filters?: SampleContractPlaybookFilters
	): Promise<SampleContractPlaybook[]> {
		try {
			const response = await apiClient.get<SampleContractPlaybook[]>(
				"/sample-contract-playbooks",
				{ params: filters }
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch sample contract playbooks");
		}
	}

	async getSamplePlaybook(id: string): Promise<SampleContractPlaybook> {
		try {
			const response = await apiClient.get<SampleContractPlaybook>(
				`/sample-contract-playbooks/${id}`
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch sample contract playbook");
		}
	}

	async getSamplePlaybookStats(): Promise<SamplePlaybookStatsResponse> {
		try {
			const response = await apiClient.get<SamplePlaybookStatsResponse>(
				"/sample-contract-playbooks/stats/overview"
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch sample playbook statistics");
		}
	}

	async cloneSamplePlaybook(id: string): Promise<ClonedContractPlaybook> {
		try {
			const response = await apiClient.post<ClonedContractPlaybook>(
				`/sample-contract-playbooks/${id}/clone`
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to clone sample contract playbook");
		}
	}

	async getAvailableContractTypes(): Promise<string[]> {
		try {
			const response = await apiClient.get<string[]>(
				"/sample-contract-playbooks/contract-types/available"
			);
			return response.data;
		} catch (error) {
			this.handleError(error, "Failed to fetch available contract types");
		}
	}

	// Error handling
	private handleError(error: unknown, defaultMessage: string): never {
		if (error instanceof AxiosError) {
			const errorData = error.response?.data as ContractPlaybookError;

			// Handle specific error cases
			if (error.response?.status === 403) {
				throw new ContractPlaybookServiceError(
					"Contract playbooks feature requires PRO subscription",
					"FEATURE_NOT_AVAILABLE",
					403,
					{ upgradeUrl: "/subscription" }
				);
			}

			if (error.response?.status === 404) {
				throw new ContractPlaybookServiceError(
					"Resource not found",
					"NOT_FOUND",
					404
				);
			}

			if (error.response?.status === 400) {
				throw new ContractPlaybookServiceError(
					errorData?.message || "Invalid request parameters",
					"VALIDATION_ERROR",
					400,
					errorData?.details
				);
			}

			throw new ContractPlaybookServiceError(
				errorData?.message || defaultMessage,
				"API_ERROR",
				error.response?.status,
				errorData?.details
			);
		}

		throw new ContractPlaybookServiceError(defaultMessage, "UNKNOWN_ERROR");
	}
}

// Export singleton instance
export const contractPlaybooksService = new ContractPlaybooksService();

// Query Keys
export const contractPlaybookKeys = {
	all: ["contract-playbooks"] as const,
	lists: () => [...contractPlaybookKeys.all, "list"] as const,
	list: (params?: PlaybookSearchParams) =>
		[...contractPlaybookKeys.lists(), params] as const,
	details: () => [...contractPlaybookKeys.all, "detail"] as const,
	detail: (id: string) => [...contractPlaybookKeys.details(), id] as const,
	analytics: (id: string) =>
		[...contractPlaybookKeys.all, "analytics", id] as const,
	analyses: () => [...contractPlaybookKeys.all, "analyses"] as const,
	analysesList: (params?: AnalysisSearchParams) =>
		[...contractPlaybookKeys.analyses(), params] as const,
	analysisDetail: (id: string) =>
		[...contractPlaybookKeys.analyses(), id] as const,
	// Sample playbook keys
	samples: () => [...contractPlaybookKeys.all, "samples"] as const,
	samplesList: (filters?: SampleContractPlaybookFilters) =>
		[...contractPlaybookKeys.samples(), filters] as const,
	sampleDetail: (id: string) => [...contractPlaybookKeys.samples(), id] as const,
	sampleStats: () => [...contractPlaybookKeys.samples(), "stats"] as const,
	availableTypes: () => [...contractPlaybookKeys.samples(), "types"] as const,
};

// React Query Hooks
export function usePlaybooks(params?: PlaybookSearchParams) {
	return useQuery({
		queryKey: contractPlaybookKeys.list(params),
		queryFn: () => contractPlaybooksService.getPlaybooks(params),
		staleTime: 1000 * 60 * 5, // 5 minutes
	});
}

export function usePlaybook(id: string) {
	return useQuery({
		queryKey: contractPlaybookKeys.detail(id),
		queryFn: () => contractPlaybooksService.getPlaybook(id),
		enabled: !!id,
		staleTime: 1000 * 60 * 5, // 5 minutes
	});
}

export function useCreatePlaybook() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: contractPlaybooksService.createPlaybook,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: contractPlaybookKeys.lists() });
		},
	});
}

export function useUpdatePlaybook() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({ id, data }: { id: string; data: UpdatePlaybookRequest }) =>
			contractPlaybooksService.updatePlaybook(id, data),
		onSuccess: (_, { id }) => {
			queryClient.invalidateQueries({
				queryKey: contractPlaybookKeys.detail(id),
			});
			queryClient.invalidateQueries({ queryKey: contractPlaybookKeys.lists() });
		},
	});
}

export function useDeletePlaybook() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: contractPlaybooksService.deletePlaybook,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: contractPlaybookKeys.lists() });
		},
	});
}

// Analysis Hooks
export function useAnalyzeContract() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: contractPlaybooksService.analyzeContract,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: contractPlaybookKeys.analyses(),
			});
		},
	});
}

export function useAnalyses(params?: AnalysisSearchParams) {
	return useQuery({
		queryKey: contractPlaybookKeys.analysesList(params),
		queryFn: () => contractPlaybooksService.getAnalyses(params),
		staleTime: 1000 * 60 * 2, // 2 minutes
	});
}

export function useAnalysis(id: string) {
	return useQuery({
		queryKey: contractPlaybookKeys.analysisDetail(id),
		queryFn: () => contractPlaybooksService.getAnalysis(id),
		enabled: !!id,
		staleTime: 1000 * 60 * 5, // 5 minutes
	});
}

export function useDeleteAnalysis() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: contractPlaybooksService.deleteAnalysis,
		onSuccess: () => {
			queryClient.invalidateQueries({
				queryKey: contractPlaybookKeys.analyses(),
			});
		},
	});
}

// Utility Hooks
export function usePlaybookAnalytics(id: string) {
	return useQuery({
		queryKey: contractPlaybookKeys.analytics(id),
		queryFn: () => contractPlaybooksService.getPlaybookAnalytics(id),
		enabled: !!id,
		staleTime: 1000 * 60 * 10, // 10 minutes
	});
}

export function useDuplicatePlaybook() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: ({
			id,
			data,
		}: {
			id: string;
			data: DuplicatePlaybookRequest;
		}) => contractPlaybooksService.duplicatePlaybook(id, data),
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: contractPlaybookKeys.lists() });
		},
	});
}

export function useExportPlaybook() {
	return useMutation({
		mutationFn: contractPlaybooksService.exportPlaybook,
	});
}

export function useImportPlaybook() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: contractPlaybooksService.importPlaybook,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: contractPlaybookKeys.lists() });
		},
	});
}

// Sample Contract Playbook Hooks
export function useSamplePlaybooks(filters?: SampleContractPlaybookFilters) {
	return useQuery({
		queryKey: contractPlaybookKeys.samplesList(filters),
		queryFn: () => contractPlaybooksService.getSamplePlaybooks(filters),
		staleTime: 1000 * 60 * 10, // 10 minutes - samples don't change often
	});
}

export function useSamplePlaybook(id: string) {
	return useQuery({
		queryKey: contractPlaybookKeys.sampleDetail(id),
		queryFn: () => contractPlaybooksService.getSamplePlaybook(id),
		enabled: !!id,
		staleTime: 1000 * 60 * 10, // 10 minutes
	});
}

export function useSamplePlaybookStats() {
	return useQuery({
		queryKey: contractPlaybookKeys.sampleStats(),
		queryFn: () => contractPlaybooksService.getSamplePlaybookStats(),
		staleTime: 1000 * 60 * 15, // 15 minutes
	});
}

export function useCloneSamplePlaybook() {
	const queryClient = useQueryClient();

	return useMutation({
		mutationFn: contractPlaybooksService.cloneSamplePlaybook,
		onSuccess: () => {
			queryClient.invalidateQueries({ queryKey: contractPlaybookKeys.lists() });
		},
	});
}

export function useAvailableContractTypes() {
	return useQuery({
		queryKey: contractPlaybookKeys.availableTypes(),
		queryFn: () => contractPlaybooksService.getAvailableContractTypes(),
		staleTime: 1000 * 60 * 30, // 30 minutes - rarely changes
	});
}
