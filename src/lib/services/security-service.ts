import { useMutation } from "@tanstack/react-query";
import { getStoredAuth } from "@/lib/auth/auth-service";
import { apiClient } from "@/lib/config";
import { AxiosError } from "axios";

interface ScanResult {
  status: 'clean' | 'infected' | 'error';
  scanId: string;
  timestamp: string;
  threat?: {
    name: string;
    severity: 'low' | 'medium' | 'high';
  };
}

export class SecurityError extends Error {
  constructor(
    message: string,
    public code: "SCAN_FAILED" | "INVALID_FILE",
    public status?: number
  ) {
    super(message);
    this.name = "SecurityError";
  }
}

// API functions
async function scanFile(file: File): Promise<ScanResult> {
  try {
    const formData = new FormData();
    formData.append('file', file);

    const { accessToken } = getStoredAuth();
    const headers: Record<string, string> = {};
    if (accessToken) {
      headers['Authorization'] = `Bearer ${accessToken}`;
    }

    const response = await apiClient.post<ScanResult>(
      `/security/virus-scan/scan`,
      formData,
      {
        headers: {
          ...headers,
          'Content-Type': 'multipart/form-data',
        },
      }
    );
    return response.data;
  } catch (error) {
    if (error instanceof AxiosError) {
      throw new SecurityError(
        error.response?.data?.message || error.message || 'Scan failed',
        "SCAN_FAILED",
        error.response?.status
      );
    }
    throw error;
  }
}

// Query hooks
export function useScanFile() {
  return useMutation<ScanResult, SecurityError, File>({
    mutationFn: scanFile,
  });
}