import { apiClient } from "../config";
import { Subscription, SubscriptionTier } from "../types/subscription";

class SubscriptionService {
  async getSubscription(organizationId: string): Promise<Subscription> {
    try {
      const response = await apiClient.get<Subscription>(`/subscriptions/${organizationId}`);
      return response.data;
    } catch (error) {
      console.error("Error fetching subscription:", error);
      throw error;
    }
  }

  async createSubscription(organizationId: string, email: string, tier: SubscriptionTier = 'free'): Promise<Subscription> {
    try {
      const response = await apiClient.post<Subscription>('/subscriptions', {
        organizationId,
        email,
        tier
      });
      return response.data;
    } catch (error) {
      console.error("Error creating subscription:", error);
      throw error;
    }
  }

  async cancelSubscription(organizationId: string): Promise<Subscription> {
    try {
      const response = await apiClient.delete<Subscription>(`/subscriptions/${organizationId}`);
      return response.data;
    } catch (error) {
      console.error("Error canceling subscription:", error);
      throw error;
    }
  }

  // 🎯 Analysis limits removed - only document limits remain
  async checkUsageLimits(organizationId: string, action: 'document_upload'): Promise<{allowed: boolean}> {
    try {
      const response = await apiClient.get<{allowed: boolean}>(`/subscriptions/${organizationId}/usage?action=${action}`);
      return response.data;
    } catch (error) {
      console.error("Error checking usage limits:", error);
      throw error;
    }
  }

  async createCheckoutSession(organizationId: string, tier: SubscriptionTier): Promise<{sessionUrl?: string, sessionId?: string}> {
    try {
      const response = await apiClient.post<{sessionUrl?: string, sessionId?: string}>('/subscriptions/checkout', {
        organizationId,
        tier
      });
      return response.data;
    } catch (error) {
      console.error("Error creating checkout session:", error);
      throw error;
    }
  }

  async getBillingPortalUrl(organizationId: string): Promise<{url: string}> {
    try {
      const response = await apiClient.get<{url: string}>(`/subscriptions/${organizationId}/billing-portal`);
      return response.data;
    } catch (error) {
      console.error("Error getting billing portal URL:", error);
      throw error;
    }
  }

  async downgradeToFreeTier(organizationId: string): Promise<Subscription> {
    try {
      const response = await apiClient.patch<Subscription>(`/subscriptions/${organizationId}/tier`, {
        tier: "free"
      });
      return response.data;
    } catch (error) {
      console.error("Error downgrading subscription:", error);
      throw error;
    }
  }
}

export const subscriptionService = new SubscriptionService();
