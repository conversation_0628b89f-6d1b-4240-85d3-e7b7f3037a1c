"use client"

import type React from "react"
import { createContext, useContext, useState, useCallback, useEffect } from "react"
import { useParams } from "next/navigation"
import { type ChatMessage, type ChatSession, type Thread, chatService } from "../services/chat-service"
import { documentService } from "../services/document-service"
import type { DocumentAnalysis } from "@/lib/types/analysis"
import { v4 as uuidv4 } from "uuid"

interface ChatContextType {
  currentSession: ChatSession | null
  messages: ChatMessage[]
  threads: Thread[]
  isLoading: boolean
  error: Error | null
  documentUploaded: boolean
  documentId: string | null
  uploadDocument: (file: File) => Promise<void>
  createSession: (documentId?: string, title?: string) => Promise<void>
  sendMessage: (content: string, relatedDocumentIds?: string[]) => Promise<void>
  loadMoreMessages: () => Promise<void>
  createThread: (messageId: string, title: string, parentThreadId?: string) => Promise<void>
  setError: (error: Error | null) => void
  clearSession: () => void
  currentPage: number
  hasNextPage: boolean
  analyzeDocument: (
    documentId: string,
    options?: { documentType?: string; query?: string },
  ) => Promise<DocumentAnalysis>
  currentAnalysis: DocumentAnalysis | null
  isAnalysisPanelOpen: boolean
  openAnalysisPanel: () => void
  closeAnalysisPanel: () => void
  toggleAnalysisPanel: () => void
}

const ChatContext = createContext<ChatContextType | undefined>(undefined)

export function ChatProvider({ children }: { children: React.ReactNode }) {
  const [currentSession, setCurrentSession] = useState<ChatSession | null>(null)
  const [messages, setMessages] = useState<ChatMessage[]>([])
  const [threads, setThreads] = useState<Thread[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<Error | null>(null)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasNextPage, setHasNextPage] = useState(false)
  const [documentUploaded, setDocumentUploaded] = useState(false)
  const [documentId, setDocumentId] = useState<string | null>(null)

   // New state for analysis panel
   const [currentAnalysis, setCurrentAnalysis] = useState<DocumentAnalysis | null>(null)
   const [isAnalysisPanelOpen, setIsAnalysisPanelOpen] = useState(false)
 
   const openAnalysisPanel = useCallback(() => {
     setIsAnalysisPanelOpen(true)
   }, [])
 
   const closeAnalysisPanel = useCallback(() => {
     setIsAnalysisPanelOpen(false)
   }, [])
 
   const toggleAnalysisPanel = useCallback(() => {
     setIsAnalysisPanelOpen((prev) => !prev)
   }, [])
  const createSession = useCallback(async (documentId?: string, title?: string) => {
    try {
      setIsLoading(true)
      let session: ChatSession | null = null

      if (title) {
        // Create new session
        session = await chatService.createSession(documentId, title)
        setThreads([])
        setMessages([])
        setCurrentPage(1)
        setHasNextPage(false)
      } else if (documentId) {
        // Switch to existing session
        const sessions = await chatService.getSessions()
        session = sessions.find((s) => s.id === documentId) || null
        if (!session) {
          throw new Error("Session not found")
        }
        // Load existing messages
        const messagesResponse = await chatService.getSessionMessages(session.id, 1)
        setMessages(messagesResponse.items)
        setCurrentPage(1)
        setHasNextPage(messagesResponse.meta.hasNextPage)
      }

      setCurrentSession(session)
      if (session?.documentId) {
        setDocumentId(session.documentId)
        setDocumentUploaded(true)
      }
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Failed to create session"))
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Load initial session from URL parameter
  const params = useParams()
  useEffect(() => {
    const initializeSession = async () => {
      const sessionId = params?.sessionId
      if (sessionId && typeof sessionId === "string" && !currentSession) {
        await createSession(sessionId)
      }
    }

    initializeSession()
  }, [params?.sessionId, createSession, currentSession])

  const uploadDocument = useCallback(
    async (file: File) => {
      try {
        setIsLoading(true)
        setError(null)

        const formData = new FormData()
        formData.append("file", file)

        const result = await documentService.uploadDocument(formData)
        console.log("Document uploaded:", result)
        setDocumentId(result.id)
        setDocumentUploaded(true)

        // Create a new chat session with this document
        await createSession(result.id, `Chat about ${file.name}`)
      } catch (err) {
        setError(err instanceof Error ? err : new Error("Failed to upload document"))
        setDocumentUploaded(false)
        setDocumentId(null)
      } finally {
        setIsLoading(false)
      }
    },
    [createSession],
  )

  const sendMessage = useCallback(
    async (content: string, relatedDocumentIds?: string[]) => {
      if (!currentSession || !documentUploaded) {
        throw new Error("No active chat session or document not uploaded")
      }

      // Create optimistic message conforming to ChatMessage interface
      const optimisticMessage: ChatMessage = {
        id: `temp-${Date.now()}`,
        content,
        role: "user",
        timestamp: new Date().toISOString(),
        attachments: [],
        references: [],
      }

      // Add optimistic message immediately
      setMessages((prev) => [optimisticMessage, ...prev])

      try {
        setIsLoading(true) // Indicate background activity
        // Send the message to the backend
        await chatService.sendMessage(currentSession.id, content, relatedDocumentIds || [documentId!])

        // Refetch the latest messages to get the confirmed user message + assistant response
        const messagesResponse = await chatService.getSessionMessages(currentSession.id, 1)

        // Update state with the latest messages from the server
        setMessages(messagesResponse.items)
        setCurrentPage(1) // Reset to page 1 as we fetched the latest
        setHasNextPage(messagesResponse.meta.hasNextPage)
      } catch (err) {
        // Remove optimistic message on error
        setMessages((prev) => prev.filter((msg) => msg.id !== optimisticMessage.id))
        setError(err instanceof Error ? err : new Error("Failed to send message"))
      } finally {
        setIsLoading(false) // Stop loading indicator
      }
    },
    [currentSession, documentId, documentUploaded],
  )

  const loadMoreMessages = useCallback(async () => {
    if (!currentSession || isLoading || !hasNextPage) return

    try {
      setIsLoading(true)
      const nextPage = currentPage + 1
      const response = await chatService.getSessionMessages(currentSession.id, nextPage)

      setMessages((prev) => [...prev, ...response.items])
      setCurrentPage(nextPage)
      setHasNextPage(response.meta.hasNextPage)
    } catch (err) {
      setError(err instanceof Error ? err : new Error("Failed to load more messages"))
    } finally {
      setIsLoading(false)
    }
  }, [currentSession, currentPage, hasNextPage, isLoading])

  const createThread = useCallback(
    async (messageId: string, title: string, parentThreadId?: string) => {
      if (!currentSession) {
        throw new Error("No active chat session")
      }

      try {
        setIsLoading(true)
        const thread = await chatService.createThread(currentSession.id, messageId, title, parentThreadId)
        setThreads((prev) => [thread, ...prev])
      } catch (err) {
        setError(err instanceof Error ? err : new Error("Failed to create thread"))
      } finally {
        setIsLoading(false)
      }
    },
    [currentSession],
  )

  const clearSession = useCallback(() => {
    setCurrentSession(null)
    setMessages([])
    setThreads([])
    setCurrentPage(1)
    setHasNextPage(false)
    setDocumentId(null)
    setDocumentUploaded(false)
    setError(null)
    setCurrentAnalysis(null)
    setIsAnalysisPanelOpen(false)
  }, [])

  // New function to analyze documents
  const analyzeDocument = useCallback(
    async (docId: string) => {
      if (!docId) {
        throw new Error("Document ID is required for analysis")
      }

      try {
        setIsLoading(true)
        setError(null)

        // First check if an analysis already exists for this document
        try {
          const existingAnalyses = await documentService.getExistingAnalyses(docId)
          
          if (existingAnalyses && existingAnalyses.length > 0) {
            // Use the most recent analysis if available
            const latestAnalysis = existingAnalyses[0]
            
            // Set the current analysis and open the panel
            setCurrentAnalysis(latestAnalysis)
            setIsAnalysisPanelOpen(true)
            
            if (currentSession) {
              const newMessage: ChatMessage = {
                id: uuidv4(),
                content: "I've found an existing analysis for your document. You can view it in the panel on the right.",
                role: "assistant",
                timestamp: new Date().toISOString(),
                attachments: [],
                references: [],
              }
              
              // Add the message to the UI immediately
              setMessages((prev) => [newMessage, ...prev])
            }
            
            return latestAnalysis
          }
        } catch (error) {
          // If checking for existing analyses fails, continue with creating a new analysis
          console.warn("Failed to check for existing analyses:", error)
        }

        // If no existing analysis was found, create a new one
        const analysisData = await documentService.analyzeDocument(docId)

        // Set the current analysis and open the panel
        setCurrentAnalysis(analysisData)
        setIsAnalysisPanelOpen(true)

        if (currentSession) {
          const newMessage: ChatMessage = {
            id: uuidv4(),
            content: "I've analyzed your document. You can view the analysis in the panel on the right.",
            role: "assistant",
            timestamp: new Date().toISOString(),
            attachments: [],
            references: [],
          }

          // Add the message to the UI immediately
          setMessages((prev) => [newMessage, ...prev])
        }

        return analysisData
      } catch (err) {
        const error = err instanceof Error ? err : new Error("Failed to analyze document")
        setError(error)
        throw error
      } finally {
        setIsLoading(false)
      }
    },
    [currentSession],
  )

  const value = {
    currentSession,
    messages,
    threads,
    isLoading,
    error,
    documentUploaded,
    documentId,
    uploadDocument,
    createSession,
    sendMessage,
    loadMoreMessages,
    createThread,
    setError,
    currentPage,
    hasNextPage,
    clearSession,
    analyzeDocument,
    currentAnalysis,
    isAnalysisPanelOpen,
    openAnalysisPanel,
    closeAnalysisPanel,
    toggleAnalysisPanel,
  }

  return <ChatContext.Provider value={value}>{children}</ChatContext.Provider>
}

export const useChatContext = () => {
  const context = useContext(ChatContext)
  if (context === undefined) {
    throw new Error("useChatContext must be used within a ChatProvider")
  }
  return context
}
