/**
 * Map between the frontend contract types and backend document types
 */

// Frontend to backend document type mapping
export function mapContractTypeToDocumentType(contractType: string): string {
  const mapping: Record<string, string> = {
    "msa": "SERVICE AGREEMENT",     // Master Service Agreement -> SERVICE AGREEMENT
    "nda": "NDA",                  // Non-Disclosure Agreement -> NDA 
    "sow": "SERVICE AGREEMENT",     // Statement of Work -> SERVICE AGREEMENT
    "employment": "EMPLOYMENT_CONTRACT",
    "vendor": "CONTRACTOR AGREEMENT",
    "licensing": "LICENSING_AGREEMENT",
    "partnership": "AGREEMENT",     // Partnership Agreement -> AGREEMENT
    "custom": "CONTRACT",          // Custom Contract -> CONTRACT
  };

  return mapping[contractType] || "CONTRACT"; // Default to CONTRACT if no mapping found
}

// Backend to frontend contract type mapping
export function mapDocumentTypeToContractType(documentType: string): string {
  const mapping: Record<string, string> = {
    "SERVICE AGREEMENT": "msa",    // Maps both MSA and SOW to service agreement type
    "NDA": "nda",
    "EMPLOYMENT_CONTRACT": "employment",
    "CONTRACTOR AGREEMENT": "vendor",
    "LICENSING_AGREEMENT": "licensing",
    "AGREEMENT": "partnership",    // General agreements map to partnership type
    "CONTRACT": "custom",         // Generic contracts map to custom type
  };

  return mapping[documentType] || "custom"; // Default to custom if no mapping found
}