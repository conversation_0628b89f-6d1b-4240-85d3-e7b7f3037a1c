/**
 * Move Context Engine
 * Determines the appropriate move form based on negotiation context
 */

import { NegotiationContext, DynamicField } from '@/components/negotiation-simulator/dynamic-move-form';

export interface ScenarioConfig {
  id: string;
  title: string;
  type: NegotiationContext['type'];
  description: string;
  complexity: NegotiationContext['complexity'];
  estimatedRounds: number;
  keyTerms: string[];
  commonStrategies: string[];
  aiPersonalities: {
    detailOriented: boolean;
    riskAverse: boolean;
    timeConstrained: boolean;
    relationshipFocused: boolean;
  };
}

export const SCENARIO_CONFIGS: Record<string, ScenarioConfig> = {
  software_licensing: {
    id: 'software_licensing',
    title: 'Software Licensing Agreement',
    type: 'software_licensing',
    description: 'Negotiate terms for enterprise software licensing',
    complexity: 'moderate',
    estimatedRounds: 4,
    keyTerms: ['license_fee', 'user_count', 'contract_duration', 'support_level'],
    commonStrategies: ['COLLABORATIVE', 'COMPETITIVE'],
    aiPersonalities: {
      detailOriented: true,
      riskAverse: true,
      timeConstrained: false,
      relationshipFocused: true
    }
  },
  
  real_estate_purchase: {
    id: 'real_estate_purchase',
    title: 'Real Estate Purchase',
    type: 'real_estate',
    description: 'Negotiate the purchase of residential property',
    complexity: 'complex',
    estimatedRounds: 6,
    keyTerms: ['purchase_price', 'down_payment', 'closing_date', 'inspection_period'],
    commonStrategies: ['COMPETITIVE', 'COMPROMISING'],
    aiPersonalities: {
      detailOriented: true,
      riskAverse: true,
      timeConstrained: true,
      relationshipFocused: false
    }
  },
  
  salary_negotiation: {
    id: 'salary_negotiation',
    title: 'Salary Negotiation',
    type: 'salary',
    description: 'Negotiate compensation package for new role',
    complexity: 'moderate',
    estimatedRounds: 3,
    keyTerms: ['base_salary', 'bonus_target', 'start_date', 'remote_work'],
    commonStrategies: ['COLLABORATIVE', 'ACCOMMODATING'],
    aiPersonalities: {
      detailOriented: false,
      riskAverse: true,
      timeConstrained: true,
      relationshipFocused: true
    }
  },
  
  service_contract: {
    id: 'service_contract',
    title: 'Service Contract',
    type: 'service_contract',
    description: 'Negotiate terms for professional services',
    complexity: 'simple',
    estimatedRounds: 3,
    keyTerms: ['service_fee', 'deliverables', 'timeline', 'payment_terms'],
    commonStrategies: ['COLLABORATIVE', 'COMPETITIVE'],
    aiPersonalities: {
      detailOriented: true,
      riskAverse: false,
      timeConstrained: false,
      relationshipFocused: true
    }
  },
  
  partnership_deal: {
    id: 'partnership_deal',
    title: 'Strategic Partnership',
    type: 'partnership',
    description: 'Negotiate strategic business partnership terms',
    complexity: 'complex',
    estimatedRounds: 8,
    keyTerms: ['revenue_split', 'exclusivity', 'territory', 'duration'],
    commonStrategies: ['COLLABORATIVE', 'COMPROMISING'],
    aiPersonalities: {
      detailOriented: true,
      riskAverse: true,
      timeConstrained: false,
      relationshipFocused: true
    }
  },
  
  acquisition_deal: {
    id: 'acquisition_deal',
    title: 'Company Acquisition',
    type: 'acquisition',
    description: 'Negotiate terms for company acquisition',
    complexity: 'complex',
    estimatedRounds: 10,
    keyTerms: ['valuation', 'payment_structure', 'due_diligence', 'closing_conditions'],
    commonStrategies: ['COMPETITIVE', 'COLLABORATIVE'],
    aiPersonalities: {
      detailOriented: true,
      riskAverse: true,
      timeConstrained: false,
      relationshipFocused: false
    }
  }
};

export class MoveContextEngine {
  
  /**
   * Determine the negotiation context based on scenario and current state
   */
  static determineContext(
    scenarioId: string,
    currentRound: number,
    previousMoves: any[],
    aiPersonality?: any
  ): NegotiationContext {
    const config = SCENARIO_CONFIGS[scenarioId];
    if (!config) {
      throw new Error(`Unknown scenario: ${scenarioId}`);
    }

    // Determine negotiation phase
    const phase = this.determinePhase(currentRound, config.estimatedRounds, previousMoves);
    
    return {
      type: config.type,
      phase,
      complexity: config.complexity,
      aiPersonality: aiPersonality || config.aiPersonalities,
      previousMoves: previousMoves.length,
      currentTerms: this.extractCurrentTerms(previousMoves)
    };
  }

  /**
   * Determine the current negotiation phase
   */
  private static determinePhase(
    currentRound: number,
    estimatedRounds: number,
    previousMoves: any[]
  ): NegotiationContext['phase'] {
    const progress = currentRound / estimatedRounds;
    
    if (currentRound === 1) {
      return 'opening';
    } else if (progress < 0.4) {
      return 'counter_offer';
    } else if (progress < 0.8) {
      return 'concessions';
    } else if (progress < 0.95) {
      return 'final_terms';
    } else {
      return 'closing';
    }
  }

  /**
   * Extract current terms from previous moves
   */
  private static extractCurrentTerms(previousMoves: any[]): Record<string, any> {
    if (previousMoves.length === 0) return {};
    
    // Get the most recent move's terms
    const lastMove = previousMoves[previousMoves.length - 1];
    return lastMove.terms || {};
  }

  /**
   * Get suggested strategies based on context
   */
  static getSuggestedStrategies(context: NegotiationContext): Array<{
    strategy: string;
    label: string;
    description: string;
    effectiveness: number;
  }> {
    const strategies = [
      {
        strategy: 'COLLABORATIVE',
        label: 'Collaborative',
        description: 'Work together to find win-win solutions',
        effectiveness: 0.8
      },
      {
        strategy: 'COMPETITIVE',
        label: 'Competitive',
        description: 'Assert your position strongly',
        effectiveness: 0.6
      },
      {
        strategy: 'ACCOMMODATING',
        label: 'Accommodating',
        description: 'Show flexibility to build goodwill',
        effectiveness: 0.7
      },
      {
        strategy: 'COMPROMISING',
        label: 'Compromising',
        description: 'Meet in the middle on key terms',
        effectiveness: 0.7
      },
      {
        strategy: 'AVOIDING',
        label: 'Avoiding',
        description: 'Sidestep difficult issues for now',
        effectiveness: 0.4
      }
    ];

    // Adjust effectiveness based on context
    return strategies.map(strategy => {
      let adjustedEffectiveness = strategy.effectiveness;
      
      // AI personality adjustments
      if (context.aiPersonality.relationshipFocused && strategy.strategy === 'COLLABORATIVE') {
        adjustedEffectiveness += 0.2;
      }
      if (context.aiPersonality.timeConstrained && strategy.strategy === 'AVOIDING') {
        adjustedEffectiveness -= 0.3;
      }
      if (context.aiPersonality.riskAverse && strategy.strategy === 'COMPETITIVE') {
        adjustedEffectiveness -= 0.2;
      }
      
      // Phase adjustments
      if (context.phase === 'opening' && strategy.strategy === 'COLLABORATIVE') {
        adjustedEffectiveness += 0.1;
      }
      if (context.phase === 'final_terms' && strategy.strategy === 'COMPETITIVE') {
        adjustedEffectiveness -= 0.2;
      }
      if (context.phase === 'closing' && strategy.strategy === 'ACCOMMODATING') {
        adjustedEffectiveness += 0.2;
      }

      return {
        ...strategy,
        effectiveness: Math.max(0.1, Math.min(1.0, adjustedEffectiveness))
      };
    }).sort((a, b) => b.effectiveness - a.effectiveness);
  }

  /**
   * Get contextual tips for the current move
   */
  static getContextualTips(context: NegotiationContext): string[] {
    const tips: string[] = [];
    
    // Phase-specific tips
    switch (context.phase) {
      case 'opening':
        tips.push('Start with your ideal terms but show willingness to negotiate');
        tips.push('Establish rapport and understand their priorities');
        break;
      case 'counter_offer':
        tips.push('Address their concerns while maintaining your key requirements');
        tips.push('Look for areas where you can create mutual value');
        break;
      case 'concessions':
        tips.push('Make strategic concessions to move toward agreement');
        tips.push('Ask for something in return for each concession');
        break;
      case 'final_terms':
        tips.push('Focus on closing the remaining gaps');
        tips.push('Emphasize the benefits of reaching agreement now');
        break;
      case 'closing':
        tips.push('Confirm all terms and next steps clearly');
        tips.push('Express enthusiasm about working together');
        break;
    }

    // AI personality tips
    if (context.aiPersonality.detailOriented) {
      tips.push('Provide specific details and justifications for your terms');
    }
    if (context.aiPersonality.relationshipFocused) {
      tips.push('Emphasize long-term partnership benefits');
    }
    if (context.aiPersonality.timeConstrained) {
      tips.push('Acknowledge time pressures and propose efficient solutions');
    }
    if (context.aiPersonality.riskAverse) {
      tips.push('Address potential risks and offer guarantees where possible');
    }

    // Complexity tips
    if (context.complexity === 'complex') {
      tips.push('Break down complex terms into manageable components');
      tips.push('Consider phased implementation or pilot programs');
    }

    return tips.slice(0, 4); // Return top 4 tips
  }

  /**
   * Validate move data based on context
   */
  static validateMove(context: NegotiationContext, moveData: any): {
    isValid: boolean;
    errors: string[];
    warnings: string[];
  } {
    const errors: string[] = [];
    const warnings: string[] = [];

    // Check required fields based on scenario
    const config = SCENARIO_CONFIGS[Object.keys(SCENARIO_CONFIGS).find(key => 
      SCENARIO_CONFIGS[key].type === context.type
    ) || ''];

    if (config) {
      config.keyTerms.forEach(term => {
        if (!moveData.terms[term] && moveData.terms[term] !== 0) {
          warnings.push(`Consider including ${term.replace('_', ' ')} in your offer`);
        }
      });
    }

    // Message validation
    if (!moveData.message || moveData.message.trim().length < 10) {
      errors.push('Please provide a more detailed message explaining your position');
    }

    // Context-specific validations
    if (context.phase === 'final_terms' && Object.keys(moveData.terms).length < 2) {
      warnings.push('Consider addressing more terms to finalize the agreement');
    }

    if (context.aiPersonality.detailOriented && moveData.message.length < 50) {
      warnings.push('This AI prefers detailed explanations - consider expanding your message');
    }

    return {
      isValid: errors.length === 0,
      errors,
      warnings
    };
  }
}

export default MoveContextEngine;
