"use client";

import React, { createContext, useContext, useState, useCallback, useEffect } from "react";
import { useSubscription } from "@/lib/subscription/subscription-context";
import { legalResearchService } from "@/lib/services/legal-research-service";
import type {
  ResearchSession,
  ResearchMessage,
  ResearchOptions,
  LegalResearchError,
  RESEARCH_FEATURE_COSTS,
} from "@/lib/types/legal-research";

interface ResearchContextType {
  // Session state
  currentSession: ResearchSession | null;
  messages: ResearchMessage[];
  isLoading: boolean;
  error: Error | null;

  // Session management
  createSession: (title?: string) => Promise<void>;
  loadSession: (sessionId: string) => Promise<void>;
  clearSession: () => void;

  // Research operations
  sendResearchQuery: (query: string, options?: ResearchOptions) => Promise<void>;
  askFollowUp: (question: string) => Promise<void>;

  // UI state
  isSearching: boolean;
  isSynthesizing: boolean;
  showFilters: boolean;
  setShowFilters: (show: boolean) => void;

  // Credit management
  checkCreditsForQuery: (includeSynthesis: boolean) => Promise<boolean>;
  
  // Error handling
  setError: (error: Error | null) => void;
}

const ResearchContext = createContext<ResearchContextType | undefined>(undefined);

export function useResearchContext() {
  const context = useContext(ResearchContext);
  if (context === undefined) {
    throw new Error("useResearchContext must be used within a ResearchProvider");
  }
  return context;
}

export function ResearchProvider({ children }: { children: React.ReactNode }) {
  // Core state
  const [currentSession, setCurrentSession] = useState<ResearchSession | null>(null);
  const [messages, setMessages] = useState<ResearchMessage[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const [error, setError] = useState<Error | null>(null);

  // UI state
  const [isSearching, setIsSearching] = useState(false);
  const [isSynthesizing, setIsSynthesizing] = useState(false);
  const [showFilters, setShowFilters] = useState(false);

  // Subscription context for credit management
  const { checkCreditsForFeature, useCreditsForFeature } = useSubscription();

  /**
   * Create a new research session
   */
  const createSession = useCallback(async (title?: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const sessionTitle = title || `Research Session ${new Date().toLocaleDateString()}`;
      const session = await legalResearchService.createSession({
        title: sessionTitle,
        tags: [],
        isShared: false,
      });

      setCurrentSession(session);
      setMessages([]);
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Failed to create session");
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Load an existing research session
   */
  const loadSession = useCallback(async (sessionId: string) => {
    try {
      setIsLoading(true);
      setError(null);

      const session = await legalResearchService.getSession(sessionId);
      setCurrentSession(session);

      // Convert session queries to messages
      const sessionMessages: ResearchMessage[] = [];
      
      if (session.queries) {
        for (const query of session.queries) {
          // Add user message
          sessionMessages.push({
            id: `${query.queryId}-user`,
            role: 'user',
            content: query.query || query.question || '',
            timestamp: query.timestamp,
            queryType: query.queryType,
            queryId: query.queryId,
            sessionId: session.sessionId,
          });

          // Add assistant response (placeholder - would need to fetch actual response)
          sessionMessages.push({
            id: `${query.queryId}-assistant`,
            role: 'assistant',
            content: query.resultSummary,
            timestamp: query.timestamp,
            creditsUsed: query.creditsUsed,
            queryType: query.queryType,
            queryId: query.queryId,
            sessionId: session.sessionId,
          });
        }
      }

      setMessages(sessionMessages.reverse()); // Most recent first
    } catch (err) {
      const error = err instanceof Error ? err : new Error("Failed to load session");
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
    }
  }, []);

  /**
   * Clear current session and messages
   */
  const clearSession = useCallback(() => {
    setCurrentSession(null);
    setMessages([]);
    setError(null);
    setIsSearching(false);
    setIsSynthesizing(false);
  }, []);

  /**
   * Check if user has sufficient credits for a query
   */
  const checkCreditsForQuery = useCallback(async (includeSynthesis: boolean): Promise<boolean> => {
    try {
      const featureName = includeSynthesis ? 'legal_research_synthesis' : 'legal_research_basic';
      return await checkCreditsForFeature(featureName);
    } catch (error) {
      console.error("Error checking credits:", error);
      return false;
    }
  }, [checkCreditsForFeature]);

  /**
   * Send a research query
   */
  const sendResearchQuery = useCallback(async (query: string, options?: ResearchOptions) => {
    if (!currentSession) {
      throw new Error("No active research session");
    }

    // Check credits first
    const hasCredits = await checkCreditsForQuery(options?.includeSynthesis ?? true);
    if (!hasCredits) {
      throw new LegalResearchError(
        "Insufficient credits for this research query",
        "INSUFFICIENT_CREDITS"
      );
    }

    // Create optimistic user message
    const optimisticMessage: ResearchMessage = {
      id: `temp-${Date.now()}`,
      content: query,
      role: "user",
      timestamp: new Date().toISOString(),
      queryType: "research",
      sessionId: currentSession.sessionId,
    };

    // Add optimistic message immediately
    setMessages((prev) => [optimisticMessage, ...prev]);

    try {
      setIsLoading(true);
      setIsSearching(true);
      if (options?.includeSynthesis) {
        setIsSynthesizing(true);
      }

      // Send the research query
      const response = await legalResearchService.performResearch({
        query,
        options,
        sessionId: currentSession.sessionId,
      });

      // Use credits
      const featureName = options?.includeSynthesis ? 'legal_research_synthesis' : 'legal_research_basic';
      await useCreditsForFeature(featureName, response.data.queryId);

      // Create assistant response message
      const assistantMessage: ResearchMessage = {
        id: response.data.queryId,
        role: "assistant",
        content: response.data.aiSynthesis?.summary || "Research completed",
        timestamp: response.data.metadata.timestamp,
        searchResults: response.data.searchResults,
        aiSynthesis: response.data.aiSynthesis,
        followUpSuggestions: response.data.followUpSuggestions,
        creditsUsed: response.data.metadata.creditsUsed,
        queryType: "research",
        queryId: response.data.queryId,
        sessionId: response.data.sessionId,
      };

      // Update messages - remove optimistic and add real messages
      setMessages((prev) => [
        assistantMessage,
        {
          ...optimisticMessage,
          id: `${response.data.queryId}-user`,
          queryId: response.data.queryId,
        },
        ...prev.filter((msg) => msg.id !== optimisticMessage.id),
      ]);

      // Update current session
      setCurrentSession((prev) => prev ? {
        ...prev,
        queryCount: prev.queryCount + 1,
        totalCreditsUsed: prev.totalCreditsUsed + response.data.metadata.creditsUsed,
        updatedAt: response.data.metadata.timestamp,
      } : null);

    } catch (err) {
      // Remove optimistic message on error
      setMessages((prev) => prev.filter((msg) => msg.id !== optimisticMessage.id));
      
      const error = err instanceof Error ? err : new Error("Failed to send research query");
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
      setIsSearching(false);
      setIsSynthesizing(false);
    }
  }, [currentSession, checkCreditsForQuery, useCreditsForFeature]);

  /**
   * Ask a follow-up question
   */
  const askFollowUp = useCallback(async (question: string) => {
    if (!currentSession) {
      throw new Error("No active research session");
    }

    // Check credits for follow-up
    const hasCredits = await checkCreditsForQuery(false); // Follow-ups are basic queries
    if (!hasCredits) {
      throw new LegalResearchError(
        "Insufficient credits for follow-up question",
        "INSUFFICIENT_CREDITS"
      );
    }

    // Create optimistic user message
    const optimisticMessage: ResearchMessage = {
      id: `temp-${Date.now()}`,
      content: question,
      role: "user",
      timestamp: new Date().toISOString(),
      queryType: "followup",
      sessionId: currentSession.sessionId,
    };

    setMessages((prev) => [optimisticMessage, ...prev]);

    try {
      setIsLoading(true);
      setIsSearching(true);

      const response = await legalResearchService.askFollowUp({
        sessionId: currentSession.sessionId,
        question,
        options: {
          includeSynthesis: true,
          focusOnPrevious: true,
        },
      });

      // Use credits
      await useCreditsForFeature('legal_research_followup', response.data.queryId);

      // Create assistant response
      const assistantMessage: ResearchMessage = {
        id: response.data.queryId,
        role: "assistant",
        content: response.data.aiSynthesis?.summary || "Follow-up completed",
        timestamp: response.data.metadata.timestamp,
        searchResults: response.data.searchResults,
        aiSynthesis: response.data.aiSynthesis,
        followUpSuggestions: response.data.followUpSuggestions,
        creditsUsed: response.data.metadata.creditsUsed,
        queryType: "followup",
        queryId: response.data.queryId,
        sessionId: response.data.sessionId,
      };

      // Update messages
      setMessages((prev) => [
        assistantMessage,
        {
          ...optimisticMessage,
          id: `${response.data.queryId}-user`,
          queryId: response.data.queryId,
        },
        ...prev.filter((msg) => msg.id !== optimisticMessage.id),
      ]);

      // Update session
      setCurrentSession((prev) => prev ? {
        ...prev,
        queryCount: prev.queryCount + 1,
        totalCreditsUsed: prev.totalCreditsUsed + response.data.metadata.creditsUsed,
        updatedAt: response.data.metadata.timestamp,
      } : null);

    } catch (err) {
      setMessages((prev) => prev.filter((msg) => msg.id !== optimisticMessage.id));
      
      const error = err instanceof Error ? err : new Error("Failed to ask follow-up question");
      setError(error);
      throw error;
    } finally {
      setIsLoading(false);
      setIsSearching(false);
    }
  }, [currentSession, checkCreditsForQuery, useCreditsForFeature]);

  const value: ResearchContextType = {
    currentSession,
    messages,
    isLoading,
    error,
    createSession,
    loadSession,
    clearSession,
    sendResearchQuery,
    askFollowUp,
    isSearching,
    isSynthesizing,
    showFilters,
    setShowFilters,
    checkCreditsForQuery,
    setError,
  };

  return (
    <ResearchContext.Provider value={value}>
      {children}
    </ResearchContext.Provider>
  );
}
