"use client";

import React, { createContext, useContext, useState, useEffect } from "react";
import { useAuth } from "../auth/auth-context";

interface Organization {
  id: string;
  name: string;
  createdAt: string;
  ownerId: string;
}

interface OrganizationContextType {
  organization: Organization | null;
  isLoading: boolean;
  error: Error | null;
  setOrganization: (org: Organization | null) => void;
}

const OrganizationContext = createContext<OrganizationContextType | undefined>(undefined);

export function OrganizationProvider({ children }: { children: React.ReactNode }) {
  const [organization, setOrganization] = useState<Organization | null>(null);
  const [isLoading, setIsLoading] = useState<boolean>(true);
  const [error, setError] = useState<Error | null>(null);
  
  const { user } = useAuth();

  useEffect(() => {
    const fetchOrganization = async () => {
      if (!user) {
        setOrganization(null);
        setIsLoading(false);
        return;
      }

      try {
        setIsLoading(true);
        setError(null);
        
        // For now, we'll use a mock organization
        // In a real implementation, you would fetch this from your API
        const mockOrg: Organization = {
          id: user.organizationId || "org-123",
          name: "Docgic",
          createdAt: new Date().toISOString(),
          ownerId: user.id,
        };
        
        setOrganization(mockOrg);
      } catch (err) {
        console.error("Error fetching organization:", err);
        setError(err instanceof Error ? err : new Error("Failed to fetch organization"));
      } finally {
        setIsLoading(false);
      }
    };

    fetchOrganization();
  }, [user]);

  return (
    <OrganizationContext.Provider
      value={{
        organization,
        isLoading,
        error,
        setOrganization,
      }}
    >
      {children}
    </OrganizationContext.Provider>
  );
}

export function useOrganization() {
  const context = useContext(OrganizationContext);
  if (context === undefined) {
    throw new Error("useOrganization must be used within an OrganizationProvider");
  }
  return context;
}
