import { useCallback, useState } from "react";
import { captureError } from "./error-service";

interface ErrorState {
  message: string | null;
  isError: boolean;
  retry?: () => Promise<void>;
}

export function useErrorHandler() {
  const [error, setError] = useState<ErrorState>({
    message: null,
    isError: false,
  });

  const handleError = useCallback((error: unknown, retry?: () => Promise<void>) => {
    const message = error instanceof Error ? error.message : "An unexpected error occurred";
    setError({ message, isError: true, retry });
    captureError(error);
  }, []);

  const clearError = useCallback(() => {
    setError({ message: null, isError: false });
  }, []);

  const withErrorHandling = useCallback(<TArgs extends unknown[], TReturn>(
    fn: (...args: TArgs) => Promise<TReturn>,
    options: { retry?: boolean } = {}
  ) => {
    return async (...args: TArgs): Promise<TReturn> => {
      try {
        return await fn(...args);
      } catch (err) {
        const retryFn = options.retry 
          ? async () => { 
              await fn(...args); 
              clearError(); 
            }
          : undefined;
        handleError(err, retryFn);
        throw err;
      }
    };
  }, [handleError, clearError]);

  return {
    error,
    handleError,
    clearError,
    withErrorHandling,
  };
}