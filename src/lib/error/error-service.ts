interface ErrorMetadata {
  componentStack?: string;
  url?: string;
  timestamp: string;
  userAgent?: string;
  userId?: string;
  tenantId?: string;
}

interface ErrorReport {
  message: string;
  stack?: string;
  metadata: ErrorMetadata;
}

// In production, this would send to your error reporting service
export async function reportError(
  error: Error,
  metadata?: Partial<ErrorMetadata>
): Promise<void> {
  const errorReport: ErrorReport = {
    message: error.message,
    stack: error.stack,
    metadata: {
      timestamp: new Date().toISOString(),
      url: typeof window !== "undefined" ? window.location.href : undefined,
      userAgent: typeof window !== "undefined" ? window.navigator.userAgent : undefined,
      ...metadata,
    },
  };

  // In development, just log to console
  if (process.env.NODE_ENV === "development") {
    console.error("Error Report:", errorReport);
    return;
  }

  // In production, would send to error reporting service
  try {
    // Mock API call
    await fetch("/api/error-reporting", {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
      },
      body: JSON.stringify(errorReport),
    });
  } catch (reportingError) {
    // Fallback to console in case reporting fails
    console.error("Failed to report error:", reportingError);
    console.error("Original error:", errorReport);
  }
}

export function captureError(error: unknown, metadata?: Partial<ErrorMetadata>): void {
  if (error instanceof Error) {
    reportError(error, metadata);
  } else {
    reportError(new Error(String(error)), metadata);
  }
}

export function withErrorCapture<TArgs extends unknown[], TReturn>(
  fn: (...args: TArgs) => Promise<TReturn>,
  metadata?: Partial<ErrorMetadata>
): (...args: TArgs) => Promise<TReturn> {
  return async (...args: TArgs) => {
    try {
      return await fn(...args);
    } catch (error) {
      captureError(error, metadata);
      throw error;
    }
  };
}