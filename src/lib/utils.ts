import { clsx, type ClassValue } from "clsx";
import { twMerge } from "tailwind-merge";

export function cn(...inputs: ClassValue[]) {
	return twMerge(clsx(inputs));
}

export function formatBytes(bytes: number, decimals = 2) {
	if (bytes === 0) return "0 Bytes";

	const k = 1024;
	const dm = decimals < 0 ? 0 : decimals;
	const sizes = ["Bytes", "KB", "MB", "GB", "TB"];

	const i = Math.floor(Math.log(bytes) / Math.log(k));

	return `${parseFloat((bytes / Math.pow(k, i)).toFixed(dm))} ${sizes[i]}`;
}

/**
 * Determines if a document type can be viewed directly in the browser
 */
export function isWebViewable(type?: string): boolean {
	if (!type) return false;

	const webViewableTypes = [
		"application/pdf",
		"text/plain",
		"text/html",
		"image/jpeg",
		"image/png",
		"image/gif",
		"image/svg+xml",
	];

	return webViewableTypes.includes(type);
}

export function formatDate(date: string | number | Date): string {
	return new Date(date).toLocaleDateString(undefined, {
		year: "numeric",
		month: "long",
		day: "numeric",
	});
}

export function formatDateTime(date: string | number | Date): string {
	return new Date(date).toLocaleString(undefined, {
		year: "numeric",
		month: "short",
		day: "numeric",
		hour: "2-digit",
		minute: "2-digit",
	});
}
