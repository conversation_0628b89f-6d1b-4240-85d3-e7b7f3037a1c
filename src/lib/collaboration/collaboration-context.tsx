"use client";

import React, { createContext, useContext, useEffect, useState, useCallback, useRef } from 'react';
import io from 'socket.io-client';
import { AuthContext } from '@/lib/auth/auth-context';
import type {
  CollaborationSession,
  SessionParticipant,
  DocumentOperation,
  CursorPosition,
  SessionJoinEvent,
  SessionLeaveEvent,
  DocumentOperationEvent,
  CursorUpdateEvent,
  PresenceChangeEvent,
  CommentCreatedEvent,
  TaskAssignedEvent,
  WorkflowStatusChangedEvent
} from '@/lib/types/collaboration';

interface CollaborationContextType {
  // Connection state
  isConnected: boolean;
  socket: any;
  
  // Session management
  currentSession: CollaborationSession | null;
  participants: SessionParticipant[];
  joinSession: (sessionId: string) => Promise<void>;
  leaveSession: () => Promise<void>;
  
  // Real-time operations
  sendOperation: (operation: Omit<DocumentOperation, 'id' | 'timestamp' | 'applied'>) => void;
  updateCursor: (cursor: CursorPosition) => void;
  
  // Event handlers
  onOperationReceived: (handler: (operation: DocumentOperation) => void) => void;
  onCursorUpdate: (handler: (userId: string, cursor: CursorPosition) => void) => void;
  onParticipantJoin: (handler: (participant: SessionParticipant) => void) => void;
  onParticipantLeave: (handler: (userId: string) => void) => void;
  onPresenceChange: (handler: (userId: string, presence: string) => void) => void;
  onCommentCreated: (handler: (threadId: string, comment: any) => void) => void;
  onTaskAssigned: (handler: (task: any) => void) => void;
  onWorkflowStatusChanged: (handler: (workflowInstanceId: string, status: string) => void) => void;
  
  // Cleanup
  removeEventHandler: (event: string, handler: Function) => void;
}

const CollaborationContext = createContext<CollaborationContextType | null>(null);

interface CollaborationProviderProps {
  children: React.ReactNode;
}

export function CollaborationProvider({ children }: CollaborationProviderProps) {
  const authContext = useContext(AuthContext);
  const user = authContext?.user;
  const [socket, setSocket] = useState<any>(null);
  const [isConnected, setIsConnected] = useState(false);
  const [currentSession, setCurrentSession] = useState<CollaborationSession | null>(null);
  const [participants, setParticipants] = useState<SessionParticipant[]>([]);

  // Event handlers storage
  const eventHandlers = useRef<Map<string, Set<Function>>>(new Map());

  // Initialize WebSocket connection
  useEffect(() => {
    // Temporarily disable socket connection to fix the error
    if (!user) return;

    const socketInstance = io(process.env.NEXT_PUBLIC_WS_URL || 'http://localhost:4000', {
      auth: {
        token: localStorage.getItem('token') // Assuming JWT token is stored in localStorage
      },
      transports: ['websocket']
    });

    socketInstance.on('connect', () => {
      console.log('Connected to collaboration server');
      setIsConnected(true);
    });

    socketInstance.on('disconnect', () => {
      console.log('Disconnected from collaboration server');
      setIsConnected(false);
    });

    // Session events
    socketInstance.on('session:join', (event: SessionJoinEvent) => {
      const handlers = eventHandlers.current.get('session:join');
      if (handlers) {
        handlers.forEach(handler => handler(event.data.participant));
      }
      
      // Update participants list
      setParticipants(prev => {
        const existing = prev.find(p => p.userId === event.data.participant.userId);
        if (existing) return prev;
        return [...prev, event.data.participant];
      });
    });

    socketInstance.on('session:leave', (event: SessionLeaveEvent) => {
      const handlers = eventHandlers.current.get('session:leave');
      if (handlers) {
        handlers.forEach(handler => handler(event.data.userId));
      }
      
      // Remove participant from list
      setParticipants(prev => prev.filter(p => p.userId !== event.data.userId));
    });

    // Document operation events
    socketInstance.on('document:operation', (event: DocumentOperationEvent) => {
      const handlers = eventHandlers.current.get('document:operation');
      if (handlers) {
        handlers.forEach(handler => handler(event.data.operation));
      }
    });

    // Cursor update events
    socketInstance.on('cursor:update', (event: CursorUpdateEvent) => {
      const handlers = eventHandlers.current.get('cursor:update');
      if (handlers) {
        handlers.forEach(handler => handler(event.data.userId, event.data.cursor));
      }
    });

    // Presence change events
    socketInstance.on('user:presence_changed', (event: PresenceChangeEvent) => {
      const handlers = eventHandlers.current.get('user:presence_changed');
      if (handlers) {
        handlers.forEach(handler => handler(event.data.userId, event.data.presence));
      }
      
      // Update participant presence
      setParticipants(prev => prev.map(p => 
        p.userId === event.data.userId 
          ? { ...p, presence: event.data.presence }
          : p
      ));
    });

    // Comment events
    socketInstance.on('comment:created', (event: CommentCreatedEvent) => {
      const handlers = eventHandlers.current.get('comment:created');
      if (handlers) {
        handlers.forEach(handler => handler(event.data.threadId, event.data.comment));
      }
    });

    // Task events
    socketInstance.on('task:assigned', (event: TaskAssignedEvent) => {
      const handlers = eventHandlers.current.get('task:assigned');
      if (handlers) {
        handlers.forEach(handler => handler(event.data.task));
      }
    });

    // Workflow events
    socketInstance.on('workflow:status_changed', (event: WorkflowStatusChangedEvent) => {
      const handlers = eventHandlers.current.get('workflow:status_changed');
      if (handlers) {
        handlers.forEach(handler => handler(event.data.workflowInstanceId, event.data.status));
      }
    });

    setSocket(socketInstance);

    return () => {
      socketInstance.disconnect();
    };
  }, [user]);

  // Join collaboration session
  const joinSession = useCallback(async (sessionId: string) => {
    if (!socket || !isConnected) {
      throw new Error('Not connected to collaboration server');
    }

    return new Promise<void>((resolve, reject) => {
      socket.emit('session:join', { sessionId }, (response: any) => {
        if (response.success) {
          setCurrentSession(response.session);
          setParticipants(response.session.participants);
          resolve();
        } else {
          reject(new Error(response.error));
        }
      });
    });
  }, [socket, isConnected]);

  // Leave collaboration session
  const leaveSession = useCallback(async () => {
    if (!socket || !currentSession) return;

    return new Promise<void>((resolve) => {
      socket.emit('session:leave', { sessionId: currentSession.id }, () => {
        setCurrentSession(null);
        setParticipants([]);
        resolve();
      });
    });
  }, [socket, currentSession]);

  // Send document operation
  const sendOperation = useCallback((operation: Omit<DocumentOperation, 'id' | 'timestamp' | 'applied'>) => {
    if (!socket || !currentSession) return;

    socket.emit('document:operation', {
      sessionId: currentSession.id,
      operation
    });
  }, [socket, currentSession]);

  // Update cursor position
  const updateCursor = useCallback((cursor: CursorPosition) => {
    if (!socket || !currentSession) return;

    socket.emit('cursor:update', {
      sessionId: currentSession.id,
      cursor
    });
  }, [socket, currentSession]);

  // Event handler registration
  const onOperationReceived = useCallback((handler: (operation: DocumentOperation) => void) => {
    const handlers = eventHandlers.current.get('document:operation') || new Set();
    handlers.add(handler);
    eventHandlers.current.set('document:operation', handlers);
  }, []);

  const onCursorUpdate = useCallback((handler: (userId: string, cursor: CursorPosition) => void) => {
    const handlers = eventHandlers.current.get('cursor:update') || new Set();
    handlers.add(handler);
    eventHandlers.current.set('cursor:update', handlers);
  }, []);

  const onParticipantJoin = useCallback((handler: (participant: SessionParticipant) => void) => {
    const handlers = eventHandlers.current.get('session:join') || new Set();
    handlers.add(handler);
    eventHandlers.current.set('session:join', handlers);
  }, []);

  const onParticipantLeave = useCallback((handler: (userId: string) => void) => {
    const handlers = eventHandlers.current.get('session:leave') || new Set();
    handlers.add(handler);
    eventHandlers.current.set('session:leave', handlers);
  }, []);

  const onPresenceChange = useCallback((handler: (userId: string, presence: string) => void) => {
    const handlers = eventHandlers.current.get('user:presence_changed') || new Set();
    handlers.add(handler);
    eventHandlers.current.set('user:presence_changed', handlers);
  }, []);

  const onCommentCreated = useCallback((handler: (threadId: string, comment: any) => void) => {
    const handlers = eventHandlers.current.get('comment:created') || new Set();
    handlers.add(handler);
    eventHandlers.current.set('comment:created', handlers);
  }, []);

  const onTaskAssigned = useCallback((handler: (task: any) => void) => {
    const handlers = eventHandlers.current.get('task:assigned') || new Set();
    handlers.add(handler);
    eventHandlers.current.set('task:assigned', handlers);
  }, []);

  const onWorkflowStatusChanged = useCallback((handler: (workflowInstanceId: string, status: string) => void) => {
    const handlers = eventHandlers.current.get('workflow:status_changed') || new Set();
    handlers.add(handler);
    eventHandlers.current.set('workflow:status_changed', handlers);
  }, []);

  // Remove event handler
  const removeEventHandler = useCallback((event: string, handler: Function) => {
    const handlers = eventHandlers.current.get(event);
    if (handlers) {
      handlers.delete(handler);
    }
  }, []);

  const value: CollaborationContextType = {
    isConnected,
    socket,
    currentSession,
    participants,
    joinSession,
    leaveSession,
    sendOperation,
    updateCursor,
    onOperationReceived,
    onCursorUpdate,
    onParticipantJoin,
    onParticipantLeave,
    onPresenceChange,
    onCommentCreated,
    onTaskAssigned,
    onWorkflowStatusChanged,
    removeEventHandler
  };

  return (
    <CollaborationContext.Provider value={value}>
      {children}
    </CollaborationContext.Provider>
  );
}

export function useCollaboration() {
  const context = useContext(CollaborationContext);
  if (!context) {
    throw new Error('useCollaboration must be used within a CollaborationProvider');
  }
  return context;
}
