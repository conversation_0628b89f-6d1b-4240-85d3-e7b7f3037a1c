import axios, { AxiosInstance, AxiosRequestConfig, AxiosResponse } from 'axios';
import { deleteCookie } from 'cookies-next';
import { getStoredAuth } from './auth/auth-service';

// Compute BASE_API_URL and export for backward compatibility
let baseUrl = process.env.NEXT_PUBLIC_API_URL || "http://localhost:4000";
if (baseUrl.endsWith('/api')) {
  baseUrl = baseUrl.slice(0, -4); // Remove trailing '/api'
}
export const BASE_API_URL = `${baseUrl}/api`;
export const API_URL = BASE_API_URL; // For backward compatibility
export const getBaseUrl = () => BASE_API_URL; // For backward compatibility

// Define common response and request types
export interface ApiResponse<T = unknown> {
  data: T;
  status: number;
  statusText: string;
}

export interface ApiRequest {
  params?: Record<string, unknown>;
  data?: unknown;
  headers?: Record<string, string>;
}

// Define APIClient class
export class APIClient {
  private axiosInstance: AxiosInstance;

  constructor() {
    this.axiosInstance = axios.create({
      baseURL: BASE_API_URL,
      withCredentials: true,
    });

    // Add response interceptor
    this.axiosInstance.interceptors.response.use(
      (response) => response,
      (error) => {
        console.error("API Error:", error);
        if (error.response?.status === 401) {
          
          // Delete access token
          deleteCookie(AUTH_CONFIG.accessTokenKey);
          // Only redirect to login if we're not already on the login page
          if (typeof window !== 'undefined' && !window.location.pathname.includes(AUTH_CONFIG.routes.login)) {
            window.location.href = AUTH_CONFIG.routes.login;
          }
        }
        return Promise.reject(error);
      }
    );
  }

  private getAuthHeaders() {
    const { accessToken } = getStoredAuth();
    return {
      Authorization: `Bearer ${accessToken}`,
    };
  }

  public async get<T = unknown>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.axiosInstance.get<T>(url, { ...config, headers: { ...config?.headers, ...this.getAuthHeaders() } });
  }

  public async post<T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.axiosInstance.post<T>(url, data, { ...config, headers: { ...config?.headers, ...this.getAuthHeaders() } });
  }

  public async put<T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.axiosInstance.put<T>(url, data, { ...config, headers: { ...config?.headers, ...this.getAuthHeaders() } });
  }

  public async delete<T = unknown>(
    url: string,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.axiosInstance.delete<T>(url, { ...config, headers: { ...config?.headers, ...this.getAuthHeaders() } });
  }

  public async patch<T = unknown>(
    url: string,
    data?: unknown,
    config?: AxiosRequestConfig
  ): Promise<AxiosResponse<T>> {
    return this.axiosInstance.patch<T>(url, data, { ...config, headers: { ...config?.headers, ...this.getAuthHeaders() } });
  }
}

// Export configuration objects
export const COOKIE_OPTIONS: {
  maxAge: number;
  path: string;
  secure: boolean;
  sameSite: 'lax' | 'strict' | 'none';
  domain?: string;
} = {
  maxAge: 60 * 60 * 24 * 7, // 7 days
  path: "/",
  secure: process.env.NODE_ENV === "production",
  sameSite: "lax",
  ...(process.env.NODE_ENV === "production" && process.env.COOKIE_DOMAIN && { domain: process.env.COOKIE_DOMAIN }),
};

export const AUTH_CONFIG = {
  accessTokenKey: "accessToken",
  userKey: "user",
  organizationKey: "organizationId",
  routes: {
    login: "/login",
    register: "/register",
    chat: "/chat",
    dashboard: "/dashboard",
    unauthorized: "/unauthorized",
  },
  allowedOrigins: process.env.ALLOWED_ORIGINS?.split(',') ?? ["http://localhost:3000"],
};

export const UPLOAD_CONFIG = {
  maxFileSize: 100 * 1024 * 1024, // 100 MB
  allowedFileTypes: [
    "application/pdf",
    "application/msword",
    "application/vnd.openxmlformats-officedocument.wordprocessingml.document", // .docx
    "text/plain", // .txt
    "image/jpeg", // .jpg, .jpeg
    "image/png", // .png
  ],
};

export const REQUIRED_ENV_VARS = [
  "NEXT_PUBLIC_API_URL",
  "COOKIE_DOMAIN",
  "ALLOWED_ORIGINS",
];

// Optional: Export an instance of the API client for convenience
export const apiClient = new APIClient();