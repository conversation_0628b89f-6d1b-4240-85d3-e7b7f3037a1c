# Document Analysis Platform - Frontend

A Next.js-based frontend application for the Document Analysis Platform.

## Features

- 🔐 Authentication with MFA support
- 📄 Document upload and management
- 🔄 Real-time processing status
- 👥 Multi-tenant support
- 🎨 Modern UI with Tailwind CSS

## Prerequisites

- Node.js 18 or higher
- Backend API running (default: http://localhost:4000)
- npm or yarn package manager

## Getting Started

1. Clone the repository:
```bash
git clone https://github.com/your-org/doc-analysis-web.git
cd doc-analysis-web
```

2. Install dependencies:
```bash
npm install
# or
yarn install
```

3. Copy the environment example file:
```bash
cp .env.example .env.local
```

4. Update the environment variables in `.env.local`:
```env
# Backend API URL
NEXT_PUBLIC_API_URL=http://localhost:4000/api

# Cookie Settings (for production)
COOKIE_DOMAIN=your-domain.com

# CORS Settings (comma-separated list of allowed origins)
ALLOWED_ORIGINS=https://your-domain.com,https://app.your-domain.com
```

5. Run the development server:
```bash
npm run dev
# or
yarn dev
```

The application will be available at [http://localhost:3000](http://localhost:3000).

## Authentication Flow

The application uses a token-based authentication system with the following features:

- JWT tokens stored in secure HTTP-only cookies
- Multi-factor authentication support
- Automatic token refresh
- Role-based access control

### Protected Routes

All routes under `/dashboard` are protected and require authentication. The auth flow:

1. User attempts to access protected route
2. Middleware checks for valid token
3. If no token, redirects to login with return URL
4. After successful login, redirects back to original route

## API Integration

The frontend communicates with the backend API using:

- Fetch API with credentials
- TanStack Query for data fetching and caching
- Automatic token refresh
- Tenant ID forwarding

### Setting Up Backend Connection

1. Ensure the backend API is running
2. Update `NEXT_PUBLIC_API_URL` in `.env.local`
3. Configure CORS settings on the backend to allow frontend origin

## Document Upload

Documents can be uploaded with:

- Drag and drop support
- File type validation
- Size limits (configurable)
- Progress tracking
- Error handling

### Supported File Types

- PDF (.pdf)
- Word (.doc, .docx)
- Text (.txt)
- Images (.jpg, .png)

## Development

### Project Structure

```
src/
├── app/              # Next.js app router
│   ├── (auth)/      # Authentication pages
│   ├── (dashboard)/ # Protected dashboard pages
│   └── (marketing)/ # Public marketing pages
├── components/      # React components
├── lib/            # Utility functions and services
└── middleware.ts   # Auth middleware
```

### Key Technologies

- Next.js 14+
- React 18+
- TanStack Query
- Tailwind CSS
- TypeScript
- cookies-next

## Production Deployment

1. Build the application:
```bash
npm run build
# or
yarn build
```

2. Set production environment variables:
- `NODE_ENV=production`
- `NEXT_PUBLIC_API_URL`
- `COOKIE_DOMAIN`
- `ALLOWED_ORIGINS`

3. Configure secure cookie settings:
- Enable secure flag
- Set proper domain
- Configure SameSite policy

4. Deploy the build output:
```bash
npm start
# or
yarn start
```

## Security Considerations

- Uses HTTP-only cookies for tokens
- Implements CORS protection
- Validates file uploads
- Sanitizes user input
- Implements rate limiting
- Supports MFA

## License

[MIT License](LICENSE)
