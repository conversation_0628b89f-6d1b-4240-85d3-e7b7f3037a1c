# Dependencies
node_modules
.pnp
.pnp.js

# Testing
/coverage
.nyc_output

# Next.js
/.next/
/out/
.next
next-env.d.ts

# Production
/build
/dist

# Environment files
.env
.env.local
.env.development.local
.env.test.local
.env.production.local

# Debug logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# IDE
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

# OS
.DS_Store
*.pem

# TypeScript
*.tsbuildinfo

# Vercel
.vercel

# PWA
public/sw.js
public/workbox-*.js
public/worker-*.js
public/sw.js.map
public/workbox-*.js.map
public/worker-*.js.map

# Cache
.cache
.npm
.eslintcache

# Optional
.env.example
*.log
.history
.qodo
