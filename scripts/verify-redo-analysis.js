#!/usr/bin/env node

/**
 * Verification script for the redo analysis feature
 * 
 * This script verifies that:
 * 1. Document service has redoAnalysis method
 * 2. Analysis panel accepts and passes documentId
 * 3. Analysis artifact router passes documentId to all components
 * 4. All artifact components accept documentId prop
 * 5. General artifact has full redo implementation
 * 6. Document card passes documentId to analysis panel
 */

const fs = require('fs');
const path = require('path');

console.log('🔍 Verifying Redo Analysis Feature Implementation...\n');

// Test 1: Check document service has redoAnalysis method
console.log('✅ Test 1: Document service redoAnalysis method');
const documentServicePath = path.join(__dirname, '../src/lib/services/document-service.ts');
const documentServiceContent = fs.readFileSync(documentServicePath, 'utf8');

if (!documentServiceContent.includes('redoAnalysis') || 
    !documentServiceContent.includes('forceNew')) {
  console.log('❌ FAIL: Document service missing redoAnalysis method');
  process.exit(1);
}

console.log('✅ PASS: Document service has redoAnalysis method');

// Test 2: Check analysis panel accepts documentId
console.log('\n✅ Test 2: Analysis panel documentId integration');
const analysisPanelPath = path.join(__dirname, '../src/components/analysis/analysis-panel.tsx');
const analysisPanelContent = fs.readFileSync(analysisPanelPath, 'utf8');

if (!analysisPanelContent.includes('documentId?: string') ||
    !analysisPanelContent.includes('documentId={documentId}')) {
  console.log('❌ FAIL: Analysis panel missing documentId integration');
  process.exit(1);
}

console.log('✅ PASS: Analysis panel properly handles documentId');

// Test 3: Check analysis artifact router passes documentId
console.log('\n✅ Test 3: Analysis artifact router documentId passing');
const analysisArtifactPath = path.join(__dirname, '../src/components/analysis/analysis-artifact.tsx');
const analysisArtifactContent = fs.readFileSync(analysisArtifactPath, 'utf8');

if (!analysisArtifactContent.includes('documentId?: string') ||
    !analysisArtifactContent.includes('documentId={documentId}')) {
  console.log('❌ FAIL: Analysis artifact router missing documentId passing');
  process.exit(1);
}

console.log('✅ PASS: Analysis artifact router properly passes documentId');

// Test 4: Check general artifact has redo implementation
console.log('\n✅ Test 4: General artifact redo implementation');
const generalArtifactPath = path.join(__dirname, '../src/components/analysis/artifact/general-artifact.tsx');
const generalArtifactContent = fs.readFileSync(generalArtifactPath, 'utf8');

const generalArtifactChecks = [
  { pattern: /handleRedoAnalysis/, description: 'redo analysis handler' },
  { pattern: /useCreditUsage/, description: 'credit usage integration' },
  { pattern: /documentService\.redoAnalysis/, description: 'service method call' },
  { pattern: /Redo Analysis/, description: 'redo button text' },
  { pattern: /RefreshCw/, description: 'refresh icon' },
  { pattern: /isRedoing/, description: 'loading state' }
];

let generalArtifactPassed = true;
generalArtifactChecks.forEach(check => {
  if (!check.pattern.test(generalArtifactContent)) {
    console.log(`❌ FAIL: General artifact missing ${check.description}`);
    generalArtifactPassed = false;
  }
});

if (generalArtifactPassed) {
  console.log('✅ PASS: General artifact has complete redo implementation');
} else {
  process.exit(1);
}

// Test 5: Check document card passes documentId
console.log('\n✅ Test 5: Document card documentId passing');
const documentCardPath = path.join(__dirname, '../src/components/documents/document-card.tsx');
const documentCardContent = fs.readFileSync(documentCardPath, 'utf8');

if (!documentCardContent.includes('documentId={document.id}')) {
  console.log('❌ FAIL: Document card does not pass documentId to AnalysisPanel');
  process.exit(1);
}

console.log('✅ PASS: Document card properly passes documentId');

// Test 6: Check all artifact components accept documentId
console.log('\n✅ Test 6: All artifact components accept documentId');
const artifactComponents = [
  'contract-artifact.tsx',
  'legal-opinion-artifact.tsx',
  'policy-artifact.tsx',
  'legislation-artifact.tsx',
  'court-filing-artifact.tsx',
  'corporate-document-artifact.tsx',
  'estate-planning-artifact.tsx',
  'intellectual-property-artifact.tsx',
  'real-estate-artifact.tsx',
  'financial-document-artifact.tsx'
];

let allArtifactsPassed = true;
artifactComponents.forEach(component => {
  const componentPath = path.join(__dirname, `../src/components/analysis/artifact/${component}`);
  if (fs.existsSync(componentPath)) {
    const componentContent = fs.readFileSync(componentPath, 'utf8');
    
    if (!componentContent.includes('documentId?: string') ||
        (!componentContent.includes('documentId,') && !componentContent.includes('documentId }'))) {
      console.log(`❌ FAIL: ${component} missing documentId prop`);
      allArtifactsPassed = false;
    }
  }
});

if (allArtifactsPassed) {
  console.log('✅ PASS: All artifact components accept documentId prop');
} else {
  process.exit(1);
}

// Test 7: Check API endpoint format
console.log('\n✅ Test 7: API endpoint format validation');
if (!documentServiceContent.includes('forceNew: options?.forceNew ?? true') ||
    !documentServiceContent.includes('documentType: options?.documentType || "CONTRACT"')) {
  console.log('❌ FAIL: API endpoint format not properly implemented');
  process.exit(1);
}

console.log('✅ PASS: API endpoint format properly implemented');

console.log('\n🎉 All tests passed! Redo Analysis feature is properly implemented.');
console.log('\n📋 Summary of implementation:');
console.log('   • Document service has redoAnalysis method with proper API format');
console.log('   • Analysis panel accepts and passes documentId to artifacts');
console.log('   • Analysis artifact router distributes documentId to all components');
console.log('   • All artifact components updated to accept documentId prop');
console.log('   • General artifact has complete redo implementation with credit integration');
console.log('   • Document card properly passes document.id to analysis panel');
console.log('   • API calls use forceNew: true and proper document type');
console.log('\n🚀 Users can now redo analysis with proper credit usage and error handling!');
