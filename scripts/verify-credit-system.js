#!/usr/bin/env node

/**
 * Verification script for the hybrid subscription + credit system updates
 * 
 * This script verifies that:
 * 1. Analysis limits have been removed from subscription plans
 * 2. CRUD operations are free (0 credits)
 * 3. Only AI-powered features consume credits
 * 4. Credit allocations are properly configured
 */

const fs = require('fs');
const path = require('path');

// Read the subscription types file
const subscriptionTypesPath = path.join(__dirname, '../src/lib/types/subscription.ts');
const subscriptionContent = fs.readFileSync(subscriptionTypesPath, 'utf8');

console.log('🔍 Verifying Hybrid Subscription + Credit System Updates...\n');

// Test 1: Check that analysisPerMonth is removed from subscription plans
console.log('✅ Test 1: Analysis limits removed from subscription plans');
if (subscriptionContent.includes('analysisPerMonth')) {
  console.log('❌ FAIL: Found references to analysisPerMonth in subscription plans');
  process.exit(1);
} else {
  console.log('✅ PASS: No analysisPerMonth references found');
}

// Test 2: Check that unlimited AI analysis is mentioned in descriptions
console.log('\n✅ Test 2: Subscription plans mention unlimited AI analysis');
// Match multiline descriptions that may span multiple lines
const planDescriptions = subscriptionContent.match(/description:\s*(?:"([^"]+)"|"([^"]*\n[^"]*)")/g) || [];
let hasUnlimitedAnalysis = true;

// Also check for the specific pattern across multiple lines
const unlimitedAnalysisCount = (subscriptionContent.match(/unlimited ai analysis/gi) || []).length;

if (unlimitedAnalysisCount >= 3) {
  console.log('✅ PASS: All plan descriptions mention unlimited AI analysis');
} else {
  console.log(`❌ FAIL: Only found ${unlimitedAnalysisCount} references to unlimited AI analysis (expected 3)`);
  process.exit(1);
}

// Test 3: Check that CRUD operations are free (0 credits)
console.log('\n✅ Test 3: CRUD operations are free (0 credits)');
const freeOperations = [
  'document_upload',
  'document_organization',
  'document_view',
  'document_edit',
  'document_delete',
  'bulk_upload',
  'chat_sessions',
  'collaboration_join',
  'playbook_crud'
];

let allCrudFree = true;
freeOperations.forEach(operation => {
  const regex = new RegExp(`${operation}:[^}]*credits:\\s*0`, 's');
  if (!regex.test(subscriptionContent)) {
    console.log(`❌ FAIL: ${operation} is not free (0 credits)`);
    allCrudFree = false;
  }
});

if (allCrudFree) {
  console.log('✅ PASS: All CRUD operations are free (0 credits)');
} else {
  process.exit(1);
}

// Test 4: Check that AI features consume credits
console.log('\n✅ Test 4: AI-powered features consume credits');
const aiFeatures = [
  'basic_analysis',
  'chat',
  'advanced_analysis',
  'ai_assisted_drafting',
  'clause_intelligence'
];

let allAiPaid = true;
aiFeatures.forEach(feature => {
  const regex = new RegExp(`${feature}:[^}]*credits:\\s*[1-9]`, 's');
  if (!regex.test(subscriptionContent)) {
    console.log(`❌ FAIL: ${feature} does not consume credits`);
    allAiPaid = false;
  }
});

if (allAiPaid) {
  console.log('✅ PASS: All AI features consume credits');
} else {
  process.exit(1);
}

// Test 5: Check credit allocations
console.log('\n✅ Test 5: Credit allocations are properly configured');
const creditAllocations = {
  LAW_STUDENT: 50,
  LAWYER: 500,
  LAW_FIRM: 2000
};

let allocationsCorrect = true;
Object.entries(creditAllocations).forEach(([tier, expectedCredits]) => {
  const regex = new RegExp(`\\[SubscriptionTierEnum\\.${tier}\\]:\\s*${expectedCredits}`);
  if (!regex.test(subscriptionContent)) {
    console.log(`❌ FAIL: ${tier} does not have ${expectedCredits} credits allocated`);
    allocationsCorrect = false;
  }
});

if (allocationsCorrect) {
  console.log('✅ PASS: All credit allocations are correct');
} else {
  process.exit(1);
}

// Test 6: Check that feature access hook removes analysis limits
const featureAccessPath = path.join(__dirname, '../src/hooks/use-feature-access.ts');
const featureAccessContent = fs.readFileSync(featureAccessPath, 'utf8');

console.log('\n✅ Test 6: Feature access hook removes analysis limits');
if (featureAccessContent.includes('return true') && 
    featureAccessContent.includes('Analysis limits have been REMOVED')) {
  console.log('✅ PASS: Feature access hook properly removes analysis limits');
} else {
  console.log('❌ FAIL: Feature access hook still enforces analysis limits');
  process.exit(1);
}

console.log('\n🎉 All tests passed! The hybrid subscription + credit system is properly implemented.');
console.log('\n📋 Summary of changes:');
console.log('   • Analysis limits completely removed from all subscription plans');
console.log('   • All CRUD operations are now FREE (0 credits)');
console.log('   • Only AI-powered features consume credits');
console.log('   • Credit allocations: Law Student (50), Lawyer (500), Law Firm (2000)');
console.log('   • Unlimited AI analysis within credit allocation for all tiers');
console.log('\n🚀 The system now provides fair pricing where users only pay for AI intelligence, not basic data management!');
