/**
 * Collaboration Features Demo Script
 * 
 * This script demonstrates all collaboration features and can be used
 * for testing and validation of the collaboration suite.
 */

import { collaborationService } from '../src/lib/services/collaboration-service';

// Demo data
const DEMO_DOCUMENT_ID = 'demo-doc-123';
const DEMO_USER_ID = 'user-123';
const DEMO_TEMPLATE_ID = 'review-template-1';

/**
 * Demo: Real-time Collaboration Session
 */
async function demoCollaborationSession() {
  console.log('🚀 Testing Real-time Collaboration...');
  
  try {
    // Create a collaboration session
    const session = await collaborationService.createSession(DEMO_DOCUMENT_ID, {
      maxParticipants: 5,
      allowAnonymous: false,
      recordChanges: true
    });
    console.log('✅ Session created:', session.id);

    // Join the session
    const participant = await collaborationService.joinSession(session.id);
    console.log('✅ Joined session as:', participant.user.name);

    // Apply a document operation
    const operation = await collaborationService.applyOperation(session.id, {
      type: 'insert',
      position: 100,
      content: 'This is a collaborative edit!',
      userId: DEMO_USER_ID
    });
    console.log('✅ Applied operation:', operation.id);

    // Get recent operations
    const operations = await collaborationService.getOperations(session.id);
    console.log('✅ Retrieved operations:', operations.length);

    // End the session
    await collaborationService.endSession(session.id);
    console.log('✅ Session ended successfully');

  } catch (error) {
    console.error('❌ Collaboration session error:', error);
  }
}

/**
 * Demo: Workflow Management
 */
async function demoWorkflowManagement() {
  console.log('🔄 Testing Workflow Management...');
  
  try {
    // Create a workflow template
    const template = await collaborationService.createWorkflowTemplate({
      name: 'Document Review Workflow',
      description: 'Standard legal document review process',
      steps: [
        {
          id: 'step-1',
          name: 'Initial Review',
          description: 'First pass review by junior associate',
          type: 'review',
          assigneeId: 'junior-associate',
          dueInHours: 24,
          required: true
        },
        {
          id: 'step-2',
          name: 'Senior Review',
          description: 'Senior partner review and approval',
          type: 'approval',
          assigneeId: 'senior-partner',
          dueInHours: 48,
          required: true
        }
      ]
    });
    console.log('✅ Workflow template created:', template.id);

    // Create a workflow instance
    const instance = await collaborationService.createWorkflowInstance(
      template.id,
      DEMO_DOCUMENT_ID,
      { priority: 'high', client: 'ABC Corp' }
    );
    console.log('✅ Workflow instance created:', instance.id);

    // Execute first step
    const updatedInstance = await collaborationService.executeWorkflowStep(
      instance.id,
      'step-1',
      { reviewNotes: 'Initial review completed, looks good' }
    );
    console.log('✅ Step executed, status:', updatedInstance.status);

    // Get all workflow instances
    const instances = await collaborationService.getWorkflowInstances(DEMO_DOCUMENT_ID);
    console.log('✅ Retrieved workflow instances:', instances.length);

  } catch (error) {
    console.error('❌ Workflow management error:', error);
  }
}

/**
 * Demo: Task Management
 */
async function demoTaskManagement() {
  console.log('✅ Testing Task Management...');
  
  try {
    // Get tasks for the document
    const tasks = await collaborationService.getTasks({
      documentId: DEMO_DOCUMENT_ID,
      status: 'pending'
    });
    console.log('✅ Retrieved tasks:', tasks.length);

    if (tasks.length > 0) {
      const task = tasks[0];
      
      // Update task status
      const updatedTask = await collaborationService.updateTaskStatus(
        task.id,
        'in_progress',
        'Started working on this task'
      );
      console.log('✅ Task status updated:', updatedTask.status);

      // Add a comment to the task
      const taskWithComment = await collaborationService.addTaskComment(
        task.id,
        'Making good progress on the review'
      );
      console.log('✅ Task comment added:', taskWithComment.comments.length);

      // Assign task to another user
      const assignedTask = await collaborationService.assignTask(
        task.id,
        'another-user-id'
      );
      console.log('✅ Task reassigned to:', assignedTask.assigneeId);
    }

  } catch (error) {
    console.error('❌ Task management error:', error);
  }
}

/**
 * Demo: Threaded Comments
 */
async function demoThreadedComments() {
  console.log('💬 Testing Threaded Comments...');
  
  try {
    // Create a comment thread
    const thread = await collaborationService.createCommentThread(DEMO_DOCUMENT_ID, {
      title: 'Review Section 3',
      type: 'review',
      anchor: {
        type: 'text',
        context: 'Payment terms section',
        startPosition: 200,
        endPosition: 250
      },
      initialComment: 'This section needs clarification on payment schedules'
    });
    console.log('✅ Comment thread created:', thread.id);

    // Add a reply to the thread
    const comment = await collaborationService.addComment(
      thread.id,
      'I agree, we should specify net 30 terms explicitly'
    );
    console.log('✅ Comment added:', comment.id);

    // Add a reaction to the comment
    const reactionComment = await collaborationService.addReaction(
      comment.id,
      '👍'
    );
    console.log('✅ Reaction added:', reactionComment.reactions.length);

    // Resolve the thread
    const resolvedThread = await collaborationService.resolveThread(thread.id);
    console.log('✅ Thread resolved:', resolvedThread.status);

    // Get all comment threads for the document
    const threads = await collaborationService.getCommentThreads(DEMO_DOCUMENT_ID);
    console.log('✅ Retrieved comment threads:', threads.length);

  } catch (error) {
    console.error('❌ Threaded comments error:', error);
  }
}

/**
 * Demo: Document Sharing
 */
async function demoDocumentSharing() {
  console.log('🔐 Testing Document Sharing...');
  
  try {
    // Share document with external user
    const shareResult = await collaborationService.shareDocument(DEMO_DOCUMENT_ID, {
      emails: ['<EMAIL>'],
      permissions: {
        canView: true,
        canEdit: false,
        canComment: true,
        canShare: false
      },
      expiresAt: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(), // 7 days
      message: 'Please review this contract and provide feedback'
    });
    console.log('✅ Document shared:', shareResult.shareUrl);

    // Get document shares
    const shares = await collaborationService.getDocumentShares(DEMO_DOCUMENT_ID);
    console.log('✅ Retrieved document shares:', shares.length);

  } catch (error) {
    console.error('❌ Document sharing error:', error);
  }
}

/**
 * Demo: Analytics and Metrics
 */
async function demoAnalytics() {
  console.log('📊 Testing Analytics...');
  
  try {
    // Get collaboration metrics
    const metrics = await collaborationService.getCollaborationMetrics({
      start: '2024-01-01',
      end: '2024-01-31'
    });
    console.log('✅ Collaboration metrics:', {
      activeCollaborations: metrics.activeCollaborations,
      totalParticipants: metrics.totalParticipants,
      totalComments: metrics.totalComments
    });

    // Get team analytics
    const teamAnalytics = await collaborationService.getTeamAnalytics({
      start: '2024-01-01',
      end: '2024-01-31'
    });
    console.log('✅ Team analytics:', {
      teamProductivity: teamAnalytics.teamProductivity,
      collaborationEfficiency: teamAnalytics.collaborationEfficiency
    });

  } catch (error) {
    console.error('❌ Analytics error:', error);
  }
}

/**
 * Demo: Notifications
 */
async function demoNotifications() {
  console.log('🔔 Testing Notifications...');
  
  try {
    // Get unread notifications
    const notifications = await collaborationService.getNotifications({
      isRead: false
    });
    console.log('✅ Unread notifications:', notifications.length);

    if (notifications.length > 0) {
      // Mark first notification as read
      const readNotification = await collaborationService.markNotificationAsRead(
        notifications[0].id
      );
      console.log('✅ Notification marked as read:', readNotification.isRead);
    }

    // Mark all notifications as read
    await collaborationService.markAllNotificationsAsRead();
    console.log('✅ All notifications marked as read');

  } catch (error) {
    console.error('❌ Notifications error:', error);
  }
}

/**
 * Run all collaboration demos
 */
async function runAllDemos() {
  console.log('🎯 Starting Collaboration Features Demo...\n');
  
  const demos = [
    demoCollaborationSession,
    demoWorkflowManagement,
    demoTaskManagement,
    demoThreadedComments,
    demoDocumentSharing,
    demoAnalytics,
    demoNotifications
  ];

  for (const demo of demos) {
    try {
      await demo();
      console.log(''); // Add spacing between demos
    } catch (error) {
      console.error('Demo failed:', error);
      console.log(''); // Add spacing even on failure
    }
  }

  console.log('🎉 Collaboration Features Demo Complete!');
}

/**
 * Run specific demo by name
 */
async function runDemo(demoName: string) {
  const demos = {
    'session': demoCollaborationSession,
    'workflow': demoWorkflowManagement,
    'tasks': demoTaskManagement,
    'comments': demoThreadedComments,
    'sharing': demoDocumentSharing,
    'analytics': demoAnalytics,
    'notifications': demoNotifications
  };

  const demo = demos[demoName as keyof typeof demos];
  if (demo) {
    await demo();
  } else {
    console.error('Unknown demo:', demoName);
    console.log('Available demos:', Object.keys(demos).join(', '));
  }
}

// Export for use in other scripts
export {
  runAllDemos,
  runDemo,
  demoCollaborationSession,
  demoWorkflowManagement,
  demoTaskManagement,
  demoThreadedComments,
  demoDocumentSharing,
  demoAnalytics,
  demoNotifications
};

// Run if called directly
if (require.main === module) {
  const demoName = process.argv[2];
  if (demoName) {
    runDemo(demoName);
  } else {
    runAllDemos();
  }
}
